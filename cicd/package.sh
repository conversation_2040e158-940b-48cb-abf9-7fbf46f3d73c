#!/usr/bin/env bash

APP_NAME=$(grep '"name":' package.json | sed 's/^.*\"name\": \"\([a-zA-Z0-9_\.\-]*\)\".*$/\1/')
APP_VERSION=$(grep '"version":' package.json | sed 's/^.*\"version\": \"\([a-zA-Z0-9_\.\-]*\)\".*$/\1/')

BUILD_RESOURCE="$APP_NAME:$APP_VERSION"

echo "Building Application for '$BUILD_RESOURCE'"

docker build --tag=vidmob/$BUILD_RESOURCE --build-arg "APP_VERSION=$APP_VERSION" . || exit $?

echo "Successfully packaged - '$BUILD_RESOURCE'"
