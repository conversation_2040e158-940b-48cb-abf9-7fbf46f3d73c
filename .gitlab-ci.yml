## Please refer to https://vidmob.atlassian.net/wiki/spaces/ATLAS/pages/3724509193/Setting+up+CI+CD for default CI/CD variable overrides
include:
  - project: 'vidmob/repos/recently-migrated/vidmob-operations'
    ref: $OPS_REPO_VERSION
    file: '/gitlab/ci/pipelines/gitflow/soa.nest.docker.ecs.pipeline.yml'

variables:
  JIRA_PROJECT_ID: 'VID'
  JIRA_SUB_PROJECT_ID: 'SOA-ORGANIZATION-SERVICE'
  ENABLE_PLAYWRIGHT_API_TESTS: 'true'
  PLAYWRIGHT_TEST_TAGS_API_CUSTOM: '@organizationUser @organizationMediaValidate'

deploy:dev:
  variables:
    AWS_CLI_IAM_ROLE_ARN: 'arn:aws:iam::812471470063:role/gitlabUser'
    # Comma seperated list of ecs service objects defined as space seperated bash array, defined in the same order as "<Cluster1,Service1,Task1,Task_File1,ASG1,ASG_Size1><space><Cluster2,Service2,Task2,Task_File2,ASG2,ASG_Size2>"
    ECS_DEPLOY_LIST: 'vidmob-nestjs-cluster,vidmob-organization-service,vidmob-organization-service,ecs/vidmob-organization-service-dev.json'

deploy:stage:
  variables:
    AWS_CLI_IAM_ROLE_ARN: 'arn:aws:iam::840000716985:role/gitlabUser'
    # Comma seperated list of ecs service objects defined as space seperated bash array, defined in the same order as "<Cluster1,Service1,Task1,Task_File1,ASG1,ASG_Size1><space><Cluster2,Service2,Task2,Task_File2,ASG2,ASG_Size2>"
    ECS_DEPLOY_LIST: 'vidmob-nestjs-cluster,vidmob-organization-service,vidmob-organization-service,ecs/vidmob-organization-service-stage.json'

deploy:prod:
  variables:
    AWS_CLI_IAM_ROLE_ARN: 'arn:aws:iam::560638139269:role/gitlabUser'
    # Comma seperated list of ecs service objects defined as space seperated bash array, defined in the same order as "<Cluster1,Service1,Task1,Task_File1,ASG1,ASG_Size1><space><Cluster2,Service2,Task2,Task_File2,ASG2,ASG_Size2>"
    ECS_DEPLOY_LIST: 'vidmob-nestjs-cluster,vidmob-organization-service,vidmob-organization-service,ecs/vidmob-organization-service-prod.json'
