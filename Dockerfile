# NodeJS Base from https://gitlab.com/vidmob/repos/infrastructure/vidmob-docker-templates
FROM 451690304815.dkr.ecr.us-east-1.amazonaws.com/vidmob/nodejs-base:20.9.0-buster-slim-202407232122

# Copy application dependency manifests to the container image.
# A wildcard is used to ensure both package.json AND package-lock.json are copied.
# Copying this separately prevents re-running npm install on every code change.
COPY --chown=nodejs:nodejs  package*.json ./

# Copy node_modules and prune to just production dependencies.
COPY --chown=nodejs:nodejs  node_modules/ node_modules
RUN  npm prune --omit=dev

# Copy dist and config folders
COPY --chown=nodejs:nodejs  dist ./dist
COPY --chown=nodejs:nodejs  config ./config

EXPOSE 3000

# Run the application from dist folder
CMD [ "node", "dist/main.js" ]
