{"name": "vidmob-organization-service", "version": "0.0.0-SNAPSHOT", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@automapper/classes": "8.7.7", "@automapper/core": "8.7.7", "@automapper/nestjs": "8.7.7", "@automapper/types": "6.3.1", "@nestjs/common": "9.4.2", "@nestjs/config": "2.3.2", "@nestjs/core": "9.4.2", "@nestjs/mapped-types": "1.2.2", "@nestjs/platform-express": "9.4.2", "@nestjs/swagger": "6.3.0", "@nestjs/typeorm": "9.0.1", "@vidmob/vidmob-nestjs-common": "1.0.94", "@vidmob/vidmob-soa-notification-service-sdk": "0.0.0-SNAPSHOT-2aab4ee6-20240110114954", "aws-sdk": "2.1691.0", "axios": "1.7.4", "class-transformer": "0.5.1", "class-validator": "0.14.0", "js-yaml": "4.1.0", "mysql2": "3.10.3", "reflect-metadata": "0.1.13", "rxjs": "7.8.1", "typeorm": "0.3.17"}, "devDependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.2", "@types/express": "^4.17.17", "@types/jest": "29.5.0", "@types/js-yaml": "^4.0.5", "@types/node": "18.15.11", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.9", "@typescript-eslint/parser": "^5.59.9", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.20", "supertest": "^6.3.3", "ts-jest": "29.0.5", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "4.2.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}, "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}