NODE_ENV: prod
AWS_REGION: us-east-1

# this will need to be moved to SecretsManager
branchIo:
  url: https://api.branch.io/v1/url
  env: prod
  key:
    secret: branch-io-prod

databricks:
  secret: prod/databricks

dataExport:
  exportTypes:
    CREATIVE_SCORING:
      enabled: true
      jobId: 283504814921263
    CREATIVE_ELEMENTS:
      enabled: true
      jobId: 600536991616430
    CREATIVE_ANALYTICS:
      enabled: true
      jobId: ***************

sqs:
  producers:
    - name: 'dataExportUpdate'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-data-exports-update-prod'
    - name: 'importStartQueueIAv3'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vmsls-import-management-start-import-prod'
    - name: 'importStartQueueLegacy'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/analyticsStoreRequest-prod'
    - name: 'accountPostImportCompleteQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-account-post-import-complete-prod'
    - name: 'accountsPreImportTasksQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-accounts-pre-import-tasks-prod'

  consumers:
    - name: 'dataExportUpdate'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-data-exports-update-prod'
    - name: 'accountPostImportCompleteQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-account-post-import-complete-prod'
    - name: 'accountsPreImportTasksQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-accounts-pre-import-tasks-prod'

database:
  default:
    secret: prod/mysql/soa-rw

snowflake:
  ANALYTICS_READ_WRITE:
    secret: prod/snowflake/soa_rw

scheduled-features-changes:
  start-delay: 3Day

spendDisabled:
  databricks:
    jobId: ***************

temporal:
  host: temporal-server-prod.vidmob.com
  port: 7233
  tls: Yes
  retryWorkerCountThreshold: 5
  namespaces:
    scheduled-features-changes: scheduled-features-changes-prod
    spend-disabled: spend-disabled-prod
  schedules:
    scheduled-features-changes-schedule:
      intervals:
        - every: 1Hour
