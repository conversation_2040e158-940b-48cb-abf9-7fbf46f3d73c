NODE_ENV: dev
AWS_REGION: us-east-1

# this will need to be moved to SecretsManager
branchIo:
  url: https://api.branch.io/v1/url
  env: dev
  key:
    secret: branch-io-dev

databricks:
  secret: dev/databricks

dataExport:
  exportTypes:
    CREATIVE_SCORING:
      enabled: true
      jobId: 829466064815702
    CREATIVE_ELEMENTS:
      enabled: true
      jobId: 3068930985589
    CREATIVE_ANALYTICS:
      enabled: true
      jobId: ***************

sqs:
  producers:
    - name: 'dataExportUpdate'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-data-exports-update-dev'
    - name: 'importStartQueueIAv3'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vmsls-import-management-start-import-dev'
    - name: 'importStartQueueLegacy'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/analyticsStoreRequest-dev'
    - name: 'accountPostImportCompleteQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-account-post-import-complete-dev'
    - name: 'accountsPreImportTasksQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-accounts-pre-import-tasks-dev'

  consumers:
    - name: 'dataExportUpdate'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-data-exports-update-dev'
    - name: 'accountPostImportCompleteQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-account-post-import-complete-dev'
    - name: 'accountsPreImportTasksQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-accounts-pre-import-tasks-dev'

database:
  default:
    secret: dev/mysql/soa-rw

snowflake:
  ANALYTICS_READ_WRITE:
    secret: dev/snowflake/soa_rw

scheduled-features-changes:
  start-delay: 3Day

spendDisabled:
  databricks:
    jobId: ****************

temporal:
  host: temporal-server-dev.vidmob.com
  port: 7233
  tls: Yes
  retryWorkerCountThreshold: 5
  namespaces:
    scheduled-features-changes: scheduled-features-changes-dev
    spend-disabled: spend-disabled-dev
  schedules:
    scheduled-features-changes-schedule:
      intervals:
        - every: 1Hour
