NODE_ENV: stage
AWS_REGION: us-east-1

# this will need to be moved to SecretsManager
branchIo:
  url: https://api.branch.io/v1/url
  env: stage
  key:
    secret: branch-io-stage

databricks:
  secret: stage/databricks

dataExport:
  exportTypes:
    CREATIVE_SCORING:
      enabled: true
      jobId: 932386317475321
    CREATIVE_ELEMENTS:
      enabled: true
      jobId: 777864401469874
    CREATIVE_ANALYTICS:
      enabled: true
      jobId: ***************

sqs:
  producers:
    - name: 'dataExportUpdate'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-data-exports-update-stage'
    - name: 'importStartQueueIAv3'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vmsls-import-management-start-import-stage'
    - name: 'importStartQueueLegacy'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/analyticsStoreRequest-stage'
    - name: 'accountPostImportCompleteQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-account-post-import-complete-stage'
    - name: 'accountsPreImportTasksQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-accounts-pre-import-tasks-stage'

  consumers:
    - name: 'dataExportUpdate'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-data-exports-update-stage'
    - name: 'accountPostImportCompleteQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-account-post-import-complete-stage'
    - name: 'accountsPreImportTasksQueue'
      queueUrl: 'https://sqs.us-east-1.amazonaws.com/************/vidmob-ad-accounts-pre-import-tasks-stage'

database:
  default:
    secret: stage/mysql/soa-rw

snowflake:
  ANALYTICS_READ_WRITE:
    secret: stage/snowflake/soa_rw

scheduled-features-changes:
  start-delay: 3Day

spendDisabled:
  databricks:
    jobId: ***************

temporal:
  host: temporal-server-stage.vidmob.com
  port: 7233
  tls: Yes
  retryWorkerCountThreshold: 5
  namespaces:
    scheduled-features-changes: scheduled-features-changes-stage
    spend-disabled: spend-disabled-stage
  schedules:
    scheduled-features-changes-schedule:
      intervals:
        - every: 1Hour
