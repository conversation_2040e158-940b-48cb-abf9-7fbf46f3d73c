import { Provider, InjectionToken } from '@nestjs/common';
import { SecretsConfigurationService } from '@vidmob/vidmob-nestjs-common';

export function branchIoSecretKeyProvider(
  injectionToken: InjectionToken,
  path: string,
): Provider<string> {
  return {
    inject: [SecretsConfigurationService],
    provide: injectionToken,

    async useFactory(secrets: SecretsConfigurationService): Promise<string> {
      const val = await secrets.getString(path);
      if (!val) {
        throw new Error(`No secret value for '${path}'`);
      }
      return val;
    },
  };
}
