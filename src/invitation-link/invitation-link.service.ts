import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

interface IData {
  title?: string;
  image?: string;
  video?: string;
  description?: string;
}

interface IBranchIoData {
  environment: string;
  type: string;
  $always_deeplink: boolean;
  $publicly_indexable: number;
  $og_title?: string;
  $og_image_url?: string;
  $og_video?: string;
  $og_description?: string;
}

interface IBranchIoBody {
  data: IBranchIoData;
  branch_key: string;
  channel: string;
  feature: string;
}

interface IBranchIoResponse {
  data: {
    url: string;
  };
}

@Injectable()
export class InvitationLinkService {
  private readonly branchIoUrl: string;
  private readonly branchIoEnv: string;

  constructor(
    private readonly configService: ConfigService,
    @Inject('BRANCH_IO_KEY') private branchIoKey: string,
  ) {
    this.branchIoUrl = this.configService.getOrThrow('branchIo.url');
    this.branchIoEnv = this.configService.getOrThrow('branchIo.env');
  }

  async createUrl(
    metadata: Object,
    invitationType: string,
    channel: string,
    data: IData,
  ) {
    const branchIoData: IBranchIoData = {
      environment: this.branchIoEnv,
      $always_deeplink: true,
      $publicly_indexable: 0,
      type: invitationType,
      ...metadata,
    };

    if (data?.title?.length > 0) {
      // sanitizes to include only alphanumeric chars, excluding underscore
      branchIoData.$og_title = data.title.replace(/[^0-9a-z]/gi, '');
    }
    if (data?.description?.length > 0) {
      // sanitizes to include only alphanumeric chars, excluding underscore
      branchIoData.$og_description = data.description.replace(
        /[^0-9a-z]/gi,
        '',
      );
    }

    if (data?.image?.length > 0) branchIoData.$og_image_url = data.image;
    if (data?.video?.length > 0) branchIoData.$og_video = data.video;

    return await this.postBranchIoRequest(
      branchIoData,
      channel,
      invitationType,
    );
  }

  async postBranchIoRequest(
    branchIoData: IBranchIoData,
    channel: string,
    feature: string,
  ) {
    const branchIoBody: IBranchIoBody = {
      data: branchIoData,
      branch_key: this.branchIoKey,
      channel,
      feature,
    };

    const response: IBranchIoResponse = await axios.post(
      this.branchIoUrl,
      branchIoBody,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    return response.data.url;
  }
}
