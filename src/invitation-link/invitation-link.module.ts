import { Module } from '@nestjs/common';
import { InvitationLinkService } from './invitation-link.service';
import {
  SecretsConfigurationService,
  SecretsManagerService,
} from '@vidmob/vidmob-nestjs-common';
import { branchIoSecretKeyProvider } from './branch-io-secret-key.provider';

@Module({
  exports: [InvitationLinkService],
  providers: [
    branchIoSecretKeyProvider('BRANCH_IO_KEY', 'branchIo.key'),
    InvitationLinkService,
    SecretsConfigurationService,
    SecretsManagerService,
  ],
})
export class InvitationLinkModule {}
