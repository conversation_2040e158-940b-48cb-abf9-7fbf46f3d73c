import { Test, TestingModule } from '@nestjs/testing';
import { InvitationLinkService } from './invitation-link.service';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosResponse } from 'axios';
import { INVITATION_TYPE, SHARE_CHANNEL } from 'src/common/constants/constants';
import { branchIoSecretKeyProvider } from './branch-io-secret-key.provider';

jest.mock('axios');
const mockedAxios = axios as jest.MockedFunction<typeof axios>;

describe('InvitationLinkService', () => {
  let service: InvitationLinkService;

  beforeEach(async () => {
    jest.mock('axios');
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InvitationLinkService,
        {
          provide: ConfigService,
          useValue: {
            getOrThrow: jest.fn().mockImplementation(() => 'dummy-value'),
          },
        },
        {
          provide: 'BRANCH_IO_KEY',
          useValue: 'DUMMY',
        },
      ],
    }).compile();

    service = module.get<InvitationLinkService>(InvitationLinkService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createUrl', () => {
    beforeEach(() => {
      const mockResponse = {
        data: {
          url: 'https://bt.vidmob.io',
        },
        status: 200,
        statusText: 'ok',
      } as AxiosResponse;

      (
        mockedAxios.post as jest.MockedFunction<typeof mockedAxios.post>
      ).mockResolvedValue(mockResponse);
    });

    it('should return the branch io url', async () => {
      await expect(
        service.createUrl(
          {},
          INVITATION_TYPE.ORGANIZATION_INVITE,
          SHARE_CHANNEL.OTHER,
          {},
        ),
      ).resolves.toBe('https://bt.vidmob.io');
    });
  });
});
