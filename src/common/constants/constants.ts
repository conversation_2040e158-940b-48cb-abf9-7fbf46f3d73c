export const DATA_SOURCE = 'DATA_SOURCE';

export const ENABLED = 'ENABLED';
export const DISABLED = 'DISABLED';
export const FACEBOOK_PLATFORM_NAME = 'BACKGROUND_FACEBOOK_AD_ACCOUNT';
export const SELF_MANAGED_IDENTIFIER = 'Self Managed';
export const ENTERPRISE_IDENTIFIER = 'Enterprise';
export const DUPLICATE_NAME_ERROR = 'DUPLICATE_NAME_ERROR';

export const EMAIL_REGEX =
  /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/i;

/**
 * Regex to find URLs globally.
 * It will find http, https and www urls (even splited by whitespace or new line character).
 * @example: 'testing https://testing.com'
 * @example: 'testing http://testing.com'
 * @example: 'testing www.testing.com'
 * @example: 'testing www. testing .com'
 */
export const URL_SEARCH_REGEX = /(?:((https?):\/\/)|www\.)[\n\S\s]+/g;

/**
 * Regex to validate filenames.
 * It will allow letters, numbers, spaces, dots, hyphens and accents.
 */
export const FILENAME_REGEX = /^[^.](?:[a-zA-Z0-9\-_:.,' \u00C0-\u017F])+$/;

export const DEFAULT_HISTORIC_IMPORT_DAYS = 395; // 13 months
export const DEFAULT_HISTORIC_IMPORT_DAYS_OFFSET = 7;
export const MAX_ACCOUNT_IMPORT_FAILURE_THRESHOLD = 5;

export enum Platform {
  PLATFORM_FACEBOOK = 'FACEBOOK',
  PLATFORM_SNAPCHAT = 'SNAPCHAT',
  PLATFORM_TWITTER = 'TWITTER',
  PLATFORM_ADWORDS = 'ADWORDS',
  PLATFORM_DV360 = 'DV360',
  PLATFORM_LINKEDIN = 'LINKEDIN',
  PLATFORM_PINTEREST = 'PINTEREST',
  PLATFORM_VERIZON_NATIVE = 'VERIZONNATIVE',
  PLATFORM_YAHOO = 'YAHOO',
  PLATFORM_FACEBOOK_PAGE = 'FACEBOOKPAGE',
  PLATFORM_INSTAGRAM_PAGE = 'INSTAGRAMPAGE',
  PLATFORM_TIKTOK = 'TIKTOK',
  PLATFORM_AMAZON = 'AMAZON',
  PLATFORM_AMAZON_ATTRIBUTION = 'AMAZONATTRIBUTION',
  PLATFORM_AMAZON_ADVERTISING = 'AMAZONADVERTISING',
  PLATFORM_AMAZON_ADVERTISING_DSP = 'AMAZONADVERTISINGDSP',
  PLATFORM_REDDIT = 'REDDIT',
}

export const IAV3_ENABLED_PLATFORMS: string[] = [
  Platform.PLATFORM_PINTEREST,
  Platform.PLATFORM_REDDIT,
  Platform.PLATFORM_LINKEDIN,
  Platform.PLATFORM_TIKTOK,
  Platform.PLATFORM_SNAPCHAT,
  Platform.PLATFORM_DV360,
];

export enum Status {
  ENABLED = 'ENABLED',
  DISABLED = 'DISABLED',
}

export enum Permission {
  ALLOW = 'ALLOW',
  DENY = 'DENY',
  UNKNOWN = 'UNKNOWN',
}

export enum ProjectScope {
  ENTERPRISE = 'ENTERPRISE',
  PUBLIC = 'PUBLIC',
}

export enum ProjectManagementMethod {
  MANAGED = 'MANAGED',
  CLIENT_SELF_SERVICE = 'CLIENT_SELF_SERVICE',
}

export enum WorkspaceManagerRole {
  ACCOUNT_MANAGER = 'ACCOUNT_MANAGER',
  PROJECT_MANAGER = 'PROJECT_MANAGER',
  EXTERNAL_CREATIVE_OPS_MANAGER = 'EXTERNAL_CREATIVE_OPS_MANAGER',
}

export enum PermissionResource {
  ORGANIZATION = 'organization',
}

export enum PermissionSubResource {
  WORKSPACE_ALL = 'workspace_all',
}

export enum PermissionType {
  UPDATE = 'update',
  READ = 'read',
}

export enum AdAccountConnectionStatus {
  CONNECTED = 'Connected',
  MISSING_PERMISSIONS = 'Missing permissions',
  DISCONNECTED = 'Disconnected',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum SortBy {
  ACCOUNT_NAME = 'account_name',
  CHANNEL = 'channel',
  PLATFORM = 'platform',
  PLATFORM_ACCOUNT_NAME = 'platform_account_name',
  CONNECTION_STATUS = 'connection_status',
  DATE_CREATED_COLUMN = 'dateCreated',
  PLATFORM_ACCOUNT_NAME_COLUMN = 'platformAccountName',
}

export enum PlatformAdAccountHealthSortBy {
  CHANNEL = 'channel',
  ACCOUNT_NAME = 'account_name',
  CONNECTION_STATUS = 'connection_status',
  IMPORT_STATUS = 'import_status',
  LAST_CONNECTED_ON = 'last_connected_on',
  LAST_IMPORTED_ON = 'last_import_success_date',
}

export enum PlatformAdAccountSortBy {
  PLATFORM = 'platform',
  PLATFORM_ACCOUNT_NAME = 'platformAccountName',
  PLATFORM_ACCOUNT_ID = 'platformAccountId',
  DATE_CREATED_COLUMN = 'dateCreated',
  PROCESSING_COMPLETED = 'processingCompleted',
  LAST_SUCCESSFUL_PROCESSING_DATE = 'lastSuccessfulProcessingDate',
  CONNECTED = 'connected',
  IMPORT_STATUS = 'importStatus',
}

export enum AccountTypeScope {
  ENTERPRISE = 'ENTERPRISE',
  PUBLIC = 'PUBLIC',
}

export enum OrganizationUserRoles {
  ORG_STANDARD = 'ORG_STANDARD',
  ORG_ADMIN = 'ORG_ADMIN',
}

export enum RoleType {
  ORGANIZATION_ROLE_TYPE = 'organization_entity',
  WORKSPACE_ROLE_TYPE = 'business_entity',
}

export enum EMAIL_IDENTIFIERS {
  ORGANIZATION_INVITE = 'ORGANIZATION_INVITE',
}

export enum INVITATION_TYPE {
  ORGANIZATION_INVITE = 'ORGANIZATION_INVITE',
}

export enum ORGANIZATION_INVITE_STATUS {
  PENDING = 'PENDING',
  REVOKED = 'REVOKED',
  ACCEPTED = 'ACCEPTED',
}

export enum SHARE_CHANNEL {
  OTHER = 'OTHER',
}

export enum WORKSPACE_USER_ROLE {
  ADMIN = 'ADMIN',
  ADMIN_OVERRIDE_REQUEST_REVIEWER = 'ADMIN_OVERRIDE_REQUEST_REVIEWER',
  STANDARD = 'STANDARD',
  CREATOR = 'CREATOR',
}

/**
 * Saved to the Database
 */
export enum ImportStatus {
  ENABLED = 'ENABLED',
  PROCESSING = 'PROCESSING',
  NOT_IMPORTING = 'NOT_IMPORTING',
  FAILED = 'FAILED',
  SUSPENDED = 'SUSPENDED',
}

/**
 * Exposed to users through BFF
 */
export enum ImportStatusForBFF {
  ENABLED = 'Enabled',
  PROCESSING = 'Processing',
  NOT_IMPORTING = 'Not Importing',
  FAILED = 'Failed',
  SUSPENDED = 'Suspended',
}

export enum ImportState {
  STARTED = 'STARTED',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

export enum ImportRebuildType {
  HISTORIC = 'HISTORIC',
  INCREMENTAL = 'INCREMENTAL',
}

export enum FilterType {
  MULTI_SELECT = 'MULTI_SELECT',
}

export enum FeatureState {
  ENABLED = 'ENABLED',
  DISABLED = 'DISABLED',
}

export enum FeatureChangeJobStatus {
  SCHEDULED = 'SCHEDULED',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}
