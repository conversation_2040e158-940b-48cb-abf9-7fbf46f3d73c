export const CONFIG_PORT = 'PORT';

export const CONFIG_SCHEDULED_FEATURE_CHANGES_START_DELAY =
  'scheduled-features-changes.start-delay';

export const CONFIG_TEMPORAL_WORKER_TIMER_MILISECONDS =
  'temporal.workerTimerMiliseconds';

export const CONFIG_TEMPORAL_SCHEDULED_FEATURE_CHANGES_NAMESPACE =
  'temporal.namespaces.scheduled-features-changes';
export const CONFIG_TEMPORAL_SCHEDULED_FEATURE_CHANGES_SCHEDULE =
  'temporal.schedules.scheduled-features-changes-schedule';

export const CONFIG_TEMPORAL_SPEND_DISABLED_NAMESPACE =
  'temporal.namespaces.spend-disabled';

export const CONFIG_TEMPORAL_DISABLED = 'temporal.disabled';

export const CONFIG_SNOWFLAKE_RW_PROVIDER_NAME = 'ANALYTICS_READ_WRITE';

export const DEFAULT_SNOWFLAKE_CONNECTION_POOL_OPTIONS = { max: 10, min: 2 };

export const CONFIG_SPEND_DISABLED_DATABRICK_JOB_ID =
  'spendDisabled.databricks.jobId';
