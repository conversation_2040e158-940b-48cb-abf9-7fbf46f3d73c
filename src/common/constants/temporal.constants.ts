import { ScheduledFeatureChangesWorkflow } from 'src/features/scheduled-feature-changes/scheduled-features-changes.workflow';
import { SpendDisabledWorkflow } from 'src/features/spend-disabled/spend-disabled.workflow';

export enum TEMPORAL_TASK_NAME {
  SCHEDULED_FEATURE_CHANGES = 'scheduled_feature_changes',
  SPEND_DISABLED = 'spend_disabled',
}

// Maps a task name type to its workflow function handler
export const TEMPORAL_WORKFLOW_FUNCTION: {
  [key in keyof typeof TEMPORAL_TASK_NAME]: string;
} = {
  SCHEDULED_FEATURE_CHANGES: ScheduledFeatureChangesWorkflow.name,
  SPEND_DISABLED: SpendDisabledWorkflow.name,
};
