import { ObjectLiteral, Repository } from 'typeorm';
import {
  EMAIL_REGEX,
  URL_SEARCH_REGEX,
  FILENAME_REGEX,
  ImportStatus,
  ImportStatusForBFF,
} from '../constants/constants';

export const formatDateToLocalDateString = (stringDate: string) => {
  if (!stringDate) return stringDate;
  const date = new Date(stringDate);
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };
  return date.toLocaleDateString(undefined, options);
};

export const startTransaction = async (
  repository: Repository<ObjectLiteral>,
) => {
  const manager = repository.manager;
  const queryRunner =
    repository.queryRunner ?? manager.connection.createQueryRunner();

  await queryRunner.connect();
  await queryRunner.startTransaction();

  return queryRunner;
};

export const isEmailValid = (email: string) => {
  return email.match(EMAIL_REGEX) !== null;
};

export const removeClickableUrlsFromString = (text: string) => {
  return text.replace(URL_SEARCH_REGEX, '');
};

export const isDateFormatValid = (dateStr: string) => {
  const timestamp = Date.parse(dateStr);
  return !isNaN(timestamp);
};

export const isFilenameValid = (filename: string) => {
  return filename?.length <= 255 && FILENAME_REGEX.test(filename);
};

export const getImportStatusForBff = (
  importStatus: ImportStatus,
): ImportStatusForBFF => {
  return ImportStatusForBFF[
    ImportStatus[importStatus] as keyof typeof ImportStatusForBFF
  ];
};

export const getImportStatusFromBff = (
  importStatusForBff: string,
): ImportStatus => {
  const importstatus = Object.entries(ImportStatusForBFF).find(
    ([_, value]) => value === importStatusForBff,
  );

  if (!importstatus) {
    return null;
  }
  return ImportStatus[importstatus[0]];
};
