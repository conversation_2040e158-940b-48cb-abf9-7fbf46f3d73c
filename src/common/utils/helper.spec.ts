import {
  formatDateToLocalDateString,
  isFilenameValid,
  removeClickableUrlsFromString,
} from './helper';

describe('Test helper class functions', () => {
  it('test formatDateToLocalDateString method with regular date string', () => {
    expect(formatDateToLocalDateString('2023-08-31 20:38:54')).toEqual(
      'Aug 31, 2023',
    );
  });
  it('test formatDateToLocalDateString method with ISO date string', () => {
    expect(formatDateToLocalDateString('2023-10-18T11:08:14.859831')).toEqual(
      'Oct 18, 2023',
    );
  });

  it('test formatDateToLocalDateString method with null', () => {
    expect(formatDateToLocalDateString(null)).toEqual(null);
  });

  describe('removeClickableUrlsFromString', () => {
    it('should remove string with www urls', () => {
      expect(
        removeClickableUrlsFromString('some string with www.fake.test.com'),
      ).toBe('some string with ');
    });

    it('should remove string with http urls', () => {
      expect(
        removeClickableUrlsFromString('some string with http://fake.test.com'),
      ).toBe('some string with ');
    });

    it('should remove string with https urls', () => {
      expect(
        removeClickableUrlsFromString('some string with https://fake.test.com'),
      ).toBe('some string with ');
    });

    it('should remove string with https urls splited by whitespace', () => {
      expect(
        removeClickableUrlsFromString(
          'some string with https:// fake .test .com',
        ),
      ).toBe('some string with ');
    });

    it('should remove string with https urls splited by whitespace', () => {
      expect(
        removeClickableUrlsFromString(
          `some string with https://
          fake .test .com`,
        ),
      ).toBe('some string with ');
    });

    it('should not change string without clickable urls', () => {
      expect(removeClickableUrlsFromString('some string with ')).toBe(
        'some string with ',
      );
    });
  });

  describe('is filename valid', () => {
    it('should return true for valid filename', () => {
      expect(isFilenameValid('filename')).toBe(true);
      expect(isFilenameValid('file_name - 3:55')).toBe(true);
      expect(isFilenameValid("L'Oréal")).toBe(true);
      expect(isFilenameValid(new Array(256).join('a'))).toBe(true);
    });

    it('should return false for filename with invalid characters', () => {
      expect(isFilenameValid('.file-name')).toBe(false);
      expect(isFilenameValid('file-name@')).toBe(false);
      expect(isFilenameValid('file/name')).toBe(false);
      expect(isFilenameValid('file*name')).toBe(false);
      expect(isFilenameValid('file?name')).toBe(false);
      expect(isFilenameValid('file!name')).toBe(false);
      expect(isFilenameValid('I ❤️ files')).toBe(false);
      expect(isFilenameValid('I 😍 files')).toBe(false);
    });

    it('should return false for filename with invalid length', () => {
      expect(isFilenameValid(null)).toBe(false);
      expect(isFilenameValid(undefined)).toBe(false);
      expect(isFilenameValid('')).toBe(false);
      expect(isFilenameValid(new Array(257).join('a'))).toBe(false);
    });
  });
});
