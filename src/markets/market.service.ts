import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { Market } from './entities/market.entity';
import { WorkspaceMarket } from 'src/workspaces/entities/workspace-market.entity';
import { CreateWorkspaceMarketDto } from './dto/create-workspace-market.dto';
import { DeleteMarketAssignResponseDto } from './dto/delete-market-assign-response.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadMarketDto } from './dto/read-market.dto';
import { PlatformAdAccountMarketMap } from 'src/ad-accounts/entities/platform-ad-account-market-map.entity';
import { PlatformAdAccount } from 'src/ad-accounts/entities/ad-account.entity';
import { WorkspaceAdAccountMap } from '../workspaces/workspace-ad-account/entities/workspace-ad-account-map.entities';

@Injectable()
export class MarketService {
  constructor(
    @InjectRepository(WorkspaceMarket)
    private readonly workspaceCountryMapRepository: Repository<WorkspaceMarket>,

    @InjectRepository(Market)
    private marketRepository: Repository<Market>,

    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  /**
   * Finds a list of markets by their ids
   */
  public async findMarketsByCodes(marketCodes: string[]): Promise<Market[]> {
    if (!marketCodes?.length) return [];
    return this.marketRepository.find({ where: { isoCode: In(marketCodes) } });
  }

  /**
   * Create a new market assignment to a workspace.
   */
  async createMarketToWorkspace(workspaceId: number, isoCode: string) {
    const workspaceMarket = new WorkspaceMarket();
    workspaceMarket.workspaceId = workspaceId;
    workspaceMarket.isoCode = isoCode;

    return this.classMapper.map(
      await this.workspaceCountryMapRepository.save(workspaceMarket),
      WorkspaceMarket,
      CreateWorkspaceMarketDto,
    );
  }

  /**
   * Remove the assignment of a market to a workspace.
   */
  async removeMarketFromWorkspace(workspaceId: number, isoCode: string) {
    const marketAssignmentToBeDeleted =
      await this.workspaceCountryMapRepository.findOne({
        where: { workspaceId: workspaceId, isoCode: isoCode },
      });

    if (!marketAssignmentToBeDeleted) {
      throw new NotFoundException(
        `Assignment of workspace ${workspaceId} to market ${isoCode} not found`,
      );
    }

    const deletedAssignment = await this.workspaceCountryMapRepository.remove(
      marketAssignmentToBeDeleted,
    );

    return this.classMapper.map(
      deletedAssignment,
      WorkspaceMarket,
      DeleteMarketAssignResponseDto,
    );
  }

  /**
   * List All Markets assigned to a workspace.
   */
  async getWorkspaceMarkets(
    workspaceId: number,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadMarketDto>> {
    const [result, total] = await this.marketRepository
      .createQueryBuilder('market')
      .innerJoin(
        'market.workspaces',
        'workspace',
        'workspace.id = :workspaceId',
        { workspaceId },
      )
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .getManyAndCount();

    const ReadWorkspaceMarketMapDtos: ReadMarketDto[] =
      this.classMapper.mapArray(result, Market, ReadMarketDto);

    return new PaginatedResultArray(ReadWorkspaceMarketMapDtos, total);
  }

  async getWorkspaceAdAccountMarkets(
    workspaceIds: number[],
    organizationId: string,
    paginationOptions?: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadMarketDto>> {
      const [result, total] = await this.marketRepository
          .createQueryBuilder('market')
          .innerJoin(
              PlatformAdAccountMarketMap,
              'platformAdAccountMarketMap',
              'platformAdAccountMarketMap.country_iso_code = market.iso_code',
          )
          .innerJoin(
              WorkspaceAdAccountMap,
              'workspaceAdAccountMap',
              'workspaceAdAccountMap.platform_ad_account_id = platformAdAccountMarketMap.platform_ad_account_id',
          )
          .innerJoin(
              'workspaceAdAccountMap.workspace',
              'workspace',
              'workspace.organization_id = :organizationId AND workspace.id IN (:...workspaceIds)',
              { organizationId, workspaceIds },
          )
          .orderBy('market.name', 'ASC')
          .skip(paginationOptions.offset || 0)
          .take(paginationOptions.perPage || 10)
          .getManyAndCount();

      const ReadWorkspaceMarketMapDtos: ReadMarketDto[] =
        this.classMapper.mapArray(result, Market, ReadMarketDto);

    return new PaginatedResultArray(ReadWorkspaceMarketMapDtos, total);
  }

  async findMarketsByPlatformAccountId(
    platformAccountId: string,
  ): Promise<Market[]> {
    return await this.marketRepository
      .createQueryBuilder('market')
      .select('market.isoCode')
      .addSelect('market.name')
      .innerJoin(
        PlatformAdAccountMarketMap,
        'platformAdAccountMarketMap',
        'platformAdAccountMarketMap.country_iso_code = market.iso_code',
      )
      .innerJoin(
        PlatformAdAccount,
        'platformAdAccount',
        'platformAdAccount.id = platformAdAccountMarketMap.platform_ad_account_id',
      )
      .where('platformAdAccount.platform_account_id = :platformAccountId', {
        platformAccountId,
      })
      .getMany();
  }

  async findMarketsByWorkspaceIds(
    workspaceIds: number[],
    paginationOptions: PaginationOptions,
  ) {
    return await this.marketRepository
      .createQueryBuilder('market')
      .select('market.isoCode')
      .addSelect('market.name')
      .innerJoin(
        PlatformAdAccountMarketMap,
        'platformAdAccountMarketMap',
        'platformAdAccountMarketMap.country_iso_code = market.iso_code',
      )
      .innerJoin(
        WorkspaceAdAccountMap,
        'workspaceAdAccountMap',
        'workspaceAdAccountMap.platform_ad_account_id = platformAdAccountMarketMap.platform_ad_account_id',
      )
      .where('workspaceAdAccountMap.partner_id IN (:...workspaceIds)', {
        workspaceIds,
      })
      .distinct(true)
      .orderBy('market.name', 'ASC')
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .getManyAndCount();
  }
}
