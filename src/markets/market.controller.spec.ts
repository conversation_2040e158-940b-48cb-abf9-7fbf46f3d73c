import { Test, TestingModule } from '@nestjs/testing';
import { MarketController } from './market.controller';
import { MarketService } from './market.service';
import { Market } from './entities/market.entity';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { listMarketDto } from './dto/list-market.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { CreateWorkspaceMarketMapDto } from './dto/create-workspce-country-map.dto';

describe('MarketController', () => {
  let controller: MarketController;
  let marketService: MarketService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MarketController],
      providers: [
        {
          provide: MarketService,
          useValue: {
            createMarketToWorkspace: jest.fn().mockResolvedValue(undefined),
            removeMarketFromWorkspace: jest.fn().mockResolvedValue(undefined),
            getWorkspaceMarkets: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    controller = module.get<MarketController>(MarketController);
    marketService = module.get<MarketService>(MarketService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should call service with valid CreateWorkspaceMarketMapDto', async () => {
    const workspaceId = 1;
    const validDto: CreateWorkspaceMarketMapDto = {
      isoCode: 'XYZ',
    };

    await controller.createMarketWorkspace(workspaceId, validDto);

    expect(marketService.createMarketToWorkspace).toHaveBeenCalledWith(
      workspaceId,
      validDto.isoCode,
    );
  });

  it('should return paginated list of markets associated with the workspace', async () => {
    const workspaceId = 1;
    const paginationOptions: PaginationOptions = {
      offset: 0,
      perPage: 10,
    };
    const listMarketDto: listMarketDto = {
      workspaceId,
    };

    const market1 = new Market();
    market1.isoCode = 'usa';
    market1.name = 'USA Market';

    const market2 = new Market();
    market2.isoCode = 'can';
    market2.name = 'Canada Market';

    const result = [market1, market2];
    const total = result.length;

    jest
      .spyOn(marketService, 'getWorkspaceMarkets')
      .mockResolvedValueOnce(new PaginatedResultArray(result, total));

    const paginatedResult = await controller.listMarkets(
      listMarketDto.workspaceId,
      paginationOptions,
    );

    expect(marketService.getWorkspaceMarkets).toHaveBeenCalledWith(
      listMarketDto.workspaceId,
      paginationOptions,
    );
    expect(paginatedResult).toBeInstanceOf(PaginatedResultArray);
    expect(paginatedResult.totalCount).toBe(total);
    expect(paginatedResult.items.length).toBe(result.length);
  });
});
