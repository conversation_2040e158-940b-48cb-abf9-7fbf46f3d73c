import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, Mapper } from '@automapper/core';
import { DeleteMarketAssignResponseDto } from '../dto/delete-market-assign-response.dto';
import { ReadMarketDto } from '../dto/read-market.dto';
import { Market } from '../entities/market.entity';
import { WorkspaceMarket } from 'src/workspaces/entities/workspace-market.entity';
import { CreateWorkspaceMarketDto } from '../dto/create-workspace-market.dto';

/**
 * Define the mapping between criteria DTOs and entities.
 */
@Injectable()
export class MarketProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  /**
   * A custom profile to define how the automapper should transform criteria. The primary function is to convert the
   * parameters from a string to an object.
   */
  override get profile() {
    return (mapper: Mapper) => {
      // Map to read markets
      createMap(mapper, Market, ReadMarketDto);

      // Map to remove markets to a workspace.
      createMap(mapper, WorkspaceMarket, DeleteMarketAssignResponseDto);

      // Map to create markets to a workspace.
      createMap(mapper, WorkspaceMarket, CreateWorkspaceMarketDto);
    };
  }
}
