import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryColumn,
  ManyToMany,
  <PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
} from 'typeorm';
import { Workspace } from '../../workspaces/entities/workspace.entity';
import { AutoMap } from '@automapper/classes';
import { WorkspaceMarket } from '../../workspaces/entities/workspace-market.entity';

@Entity('country')
export class Market {
  @AutoMap()
  @PrimaryColumn({ name: 'iso_code', length: 3 })
  isoCode: string;

  @AutoMap()
  @Column({ name: 'name', length: 100, unique: true })
  name: string;

  @AutoMap()
  @ManyToMany(() => Workspace, (workspace) => workspace.markets)
  @JoinTable({
    name: 'workspace_country_map',
    joinColumns: [{ name: 'iso_code' }],
    inverseJoinColumns: [{ name: 'partner_id' }],
  })
  workspaces: Workspace[];

  @OneToMany(() => WorkspaceMarket, (workspaceMarket) => workspaceMarket.market)
  workspaceMarkets: WorkspaceMarket[];
}
