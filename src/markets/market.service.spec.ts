import { Test, TestingModule } from '@nestjs/testing';
import { MarketService } from './market.service';
import { Repository } from 'typeorm';
import { WorkspaceMarket } from 'src/workspaces/entities/workspace-market.entity';
import { Market } from './entities/market.entity';
import { Mapper } from '@automapper/core';
// import { CreateWorkspaceMarketDto } from '../dto/create-workspace-market.dto';
// import { ReadMarketDto } from '../dto/read-market.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getMapperToken } from '@automapper/nestjs';
import { NotFoundException } from '@nestjs/common';
import { CreateWorkspaceMarketDto } from './dto/create-workspace-market.dto';
import { ReadMarketDto } from './dto/read-market.dto';
import {organization} from "../organizations/organization-user/mock/organization-user-mock";

const createQueryBuilder: any = {
  select: () => createQueryBuilder,
  addSelect: () => createQueryBuilder,
  innerJoin: () => createQueryBuilder,
  where: () => createQueryBuilder,
  getMany: () => jest.fn(),
};

describe('MarketService', () => {
  let marketService: MarketService;
  let workspaceCountryMapRepo: Repository<WorkspaceMarket>;
  let marketRepo: Repository<Market>;
  let classMapper: Mapper;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      providers: [
        MarketService,
        {
          provide: getRepositoryToken(WorkspaceMarket),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(Market),
          useValue: {
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getMapperToken(),
          useValue: {
            map: jest.fn(),
            mapArray: jest.fn(),
          },
        },
      ],
    }).compile();

    marketService = moduleRef.get<MarketService>(MarketService);
    workspaceCountryMapRepo = moduleRef.get<Repository<WorkspaceMarket>>(
      getRepositoryToken(WorkspaceMarket),
    );
    marketRepo = moduleRef.get<Repository<Market>>(getRepositoryToken(Market));
    classMapper = moduleRef.get<Mapper>(getMapperToken());
  });

  it('should be defined', () => {
    expect(marketService).toBeDefined();
  });

  describe('createMarketToWorkspace', () => {
    it('should assign a market to a workspace', async () => {
      // Mock the required data
      const workspaceId = 123;
      const isoCode = 'bra';
      const savedRecord = new WorkspaceMarket();
      const mappedResponse = new CreateWorkspaceMarketDto();

      jest
        .spyOn(workspaceCountryMapRepo, 'save')
        .mockResolvedValue(savedRecord);

      (classMapper.map as jest.Mock).mockReturnValue(mappedResponse);

      const result = await marketService.createMarketToWorkspace(
        workspaceId,
        isoCode,
      );

      expect(workspaceCountryMapRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          workspaceId,
          isoCode,
        }),
      );
      expect(classMapper.map).toHaveBeenCalledWith(
        savedRecord,
        WorkspaceMarket,
        CreateWorkspaceMarketDto,
      );
      expect(result).toEqual(mappedResponse);
    });
  });

  describe('getWorkspaceMarkets', () => {
    it('should return paginated results of ReadMarketDto', async () => {
      // Mock the required data
      const workspaceId = 123;
      const paginationOptions: PaginationOptions = {
        offset: 0,
        perPage: 10,
      };

      // Mock the result from the QueryBuilder
      const queryResult: Market[] = [
        // Array of Market entities
      ];
      const totalResult = 20;

      // Mock the mapped response
      const mappedResponse: ReadMarketDto[] = [
        // Array of ReadMarketDto
      ];

      // Mock the findAndCount method of MarketRepository
      jest.spyOn(marketRepo, 'createQueryBuilder').mockReturnValue({
        innerJoin: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest
          .fn()
          .mockResolvedValue([queryResult, totalResult]),
      } as any);

      (classMapper.mapArray as jest.Mock).mockReturnValue(mappedResponse);

      // Call the method to be tested
      const result = await marketService.getWorkspaceMarkets(
        workspaceId,
        paginationOptions,
      );

      // Assertions
      expect(marketRepo.createQueryBuilder).toHaveBeenCalled();
      expect(classMapper.mapArray).toHaveBeenCalledWith(
        queryResult,
        Market,
        ReadMarketDto,
      );
      expect(result).toBeInstanceOf(PaginatedResultArray);
      expect(result.items).toEqual(mappedResponse);
      expect(result.totalCount).toEqual(totalResult);
    });
  });

  describe('removeMarketFromWorkspace', () => {
    it('should delete the market assignment from the workspace', async () => {
      const workspaceId = 123;
      const isoCode = 'bra';
      const marketAssignment = new WorkspaceMarket();

      jest
        .spyOn(workspaceCountryMapRepo, 'findOne')
        .mockResolvedValue(marketAssignment);

      jest
        .spyOn(workspaceCountryMapRepo, 'remove')
        .mockResolvedValue(marketAssignment);

      const result = await marketService.removeMarketFromWorkspace(
        workspaceId,
        isoCode,
      );

      expect(workspaceCountryMapRepo.findOne).toHaveBeenCalledWith({
        where: { workspaceId, isoCode },
      });
      expect(workspaceCountryMapRepo.remove).toHaveBeenCalledWith(
        marketAssignment,
      );
      expect(result).toEqual(undefined);
    });

    it('should throw an exception if the market assignment is not found', async () => {
      const workspaceId = 123;
      const isoCode = 'bra';

      jest.spyOn(workspaceCountryMapRepo, 'findOne').mockResolvedValue(null);

      await expect(
        marketService.removeMarketFromWorkspace(workspaceId, isoCode),
      ).rejects.toThrowError(NotFoundException);
    });
  });

  describe('findMarketsByPlatformAdAccountId', () => {
    const markets = [
      {
        isoCode: 'usa',
        name: 'United States',
      },
      {
        isoCode: 'bra',
        name: 'Brazil',
      },
    ];

    it('should return an array of markets', async () => {
      jest
        .spyOn(marketRepo, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      jest.spyOn(createQueryBuilder, 'getMany').mockResolvedValueOnce(markets);

      await expect(
        marketService.findMarketsByPlatformAccountId('xxxxxx'),
      ).resolves.toEqual(markets);
    });
  });

  describe('getOrganizationMarkets', () => {
    it('should return a paginated list of markets', async () => {
      const mockMarkets = [{ isoCode: 'a', name: 'Market1' }, { isoCode: 'b', name: 'Market2' }];
      const mockTotal = 2;

      jest.spyOn(marketRepo, 'createQueryBuilder').mockReturnValue({
        innerJoin: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getManyAndCount: jest
            .fn().mockResolvedValue([mockMarkets, mockTotal]),
      } as any);
      const result = await marketService.getWorkspaceAdAccountMarkets([23142],'org-id', { offset: 0, perPage: 10 });

      expect(marketRepo.createQueryBuilder).toHaveBeenCalled();
      expect(result).toBeInstanceOf(PaginatedResultArray);
      expect(result.totalCount).toBe(2);
    });
  });
});
