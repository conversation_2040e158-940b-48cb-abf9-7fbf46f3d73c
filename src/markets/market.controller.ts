import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  ValidationPipe,
} from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { MarketService } from './market.service';
import {
  GetPagination,
  PaginationOptions,
  VmApiCreatedResponse,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { CreateWorkspaceMarketDto } from './dto/create-workspace-market.dto';
import { CreateWorkspaceMarketMapDto } from './dto/create-workspce-country-map.dto';
import { DeleteMarketAssignResponseDto } from './dto/delete-market-assign-response.dto';
import { ReadMarketDto } from './dto/read-market.dto';
import { RemoveWorkspaceMarketMapDto } from './dto/remove-workspace-country-map.dto';
import {WorkspaceAdAccountMarketRequestDto} from "./dto/workspace-ad-account-market-request.dto";

@ApiTags('Market')
@Controller('market')
export class MarketController {
  constructor(private marketService: MarketService) {}

  /**
   * Create a new assignment to a workspace.
   * @param workspaceId - The id of the workspace (Partner).
   * @param isoCode - The id of Market (Country).
   */
  @VmApiCreatedResponse({
    type: CreateWorkspaceMarketDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @ApiParam({ name: 'isoCode', description: 'The id of Market' })
  @Post(':isoCode/workspace/:workspaceId')
  async createMarketWorkspace(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Param(new ValidationPipe()) param: CreateWorkspaceMarketMapDto,
  ) {
    return this.marketService.createMarketToWorkspace(
      workspaceId,
      param.isoCode,
    );
  }

  /**
   * Remove the assignment of a market to a workspace.
   * @param workspaceId - The id of the workspace.
   * @param isoCode - The id of the market.
   */
  @VmApiOkResponse({
    type: DeleteMarketAssignResponseDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @ApiParam({ name: 'isoCode', description: 'The id of the market' })
  @Delete(':isoCode/workspace/:workspaceId')
  async removeMarketFromWorkspace(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Param(new ValidationPipe()) param: RemoveWorkspaceMarketMapDto,
  ) {
    return await this.marketService.removeMarketFromWorkspace(
      workspaceId,
      param.isoCode,
    );
  }

  /**
   * List All Markets assigned to a workspace.
   * @param workspaceId - The id of the workspace (Partner).
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadMarketDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Get('workspace/:workspaceId')
  async listMarkets(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadMarketDto>> {
    return this.marketService.getWorkspaceMarkets(
      workspaceId,
      paginationOptions,
    );
  }

  @VmApiOkPaginatedArrayResponse({
    type: ReadMarketDto,
  })
  @ApiParam({ name: 'organizationId', description: 'The id of the organization' })
  @Post('organization/:organizationId')
  async getWorkspaceAdAccountMarkets(
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Body() body: WorkspaceAdAccountMarketRequestDto,
  ): Promise<PaginatedResultArray<ReadMarketDto>> {
    return this.marketService.getWorkspaceAdAccountMarkets(
      body.workspaceIds,
      organizationId,
      paginationOptions,
    );
  }
}
