import { MarketService } from './market.service';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Market } from '../markets/entities/market.entity';
import { WorkspaceMarket } from 'src/workspaces/entities/workspace-market.entity';
import { MarketController } from './market.controller';
import { MarketProfile } from './mappers/market.profile';

@Module({
  imports: [TypeOrmModule.forFeature([Market, WorkspaceMarket])],
  exports: [MarketService],
  controllers: [MarketController],
  providers: [MarketService, MarketProfile],
})
export class MarketModule {}
