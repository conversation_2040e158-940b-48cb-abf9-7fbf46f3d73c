import { Test, TestingModule } from '@nestjs/testing';
import { DatabricksApiService } from './databricks-api.service';
import { SecretsConfigurationService } from '@vidmob/vidmob-nestjs-common';

describe('DatabricksApiService', () => {
  let service: DatabricksApiService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: SecretsConfigurationService,
          useValue: {
            get: jest.fn().mockReturnValue({ salt: 'salty' }),
          },
        },
        DatabricksApiService,
      ],
    }).compile();

    service = module.get<DatabricksApiService>(DatabricksApiService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
