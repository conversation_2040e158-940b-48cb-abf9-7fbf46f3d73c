export type DatabricksParamMap = { [key: string]: string };
export type DatabricksParamList = string[];

export interface DatabricksPipelineParams {
  full_refresh?: boolean;
}

export interface DatabricksQueueParams {
  enabled?: boolean;
}

export interface DatabricksJobRunNowRequest {
  job_id: number;
  jar_params?: DatabricksParamList;
  notebook_params?: DatabricksParamMap;
  python_params?: DatabricksParamList;
  spark_submit_params?: DatabricksParamList;
  python_named_params?: DatabricksParamMap;
  sql_params?: DatabricksParamMap;
  dbt_params?: DatabricksParamList;
  pipeline_params?: DatabricksPipelineParams;
  idempotency_token?: string;
  queue?: DatabricksQueueParams;
  job_parameters?: DatabricksParamMap;
}

export interface DatabricksJobRunNowResponse {
  run_id: number;
}

export enum DatabricksState {
  BLOCKED = 'BLOCKED',
  PENDING = 'PENDING',
  QUEUED = 'QUEUED',
  RUNNING = 'RUNNING',
  TERMINATING = 'TERMINATING',
  TERMINATED = 'TERMINATED',
  WAITING = 'WAITING',
}

export enum DatabricksTerminationDetailsType {
  SUCCESS = 'SUCCESS',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  CLIENT_ERROR = 'CLIENT_ERROR',
  CLOUD_FAILURE = 'CLOUD_FAILURE',
}

export interface DatabricksTerminationDetails {
  code: string;
  message: string;
  type: DatabricksTerminationDetailsType;
}

export interface DatabricksStatus {
  state: DatabricksState;
  termination_details: DatabricksTerminationDetails;
}

export interface DatabricksJobGetResponse {
  // hiding other fields, since it's not needed
  status: DatabricksStatus;
}
