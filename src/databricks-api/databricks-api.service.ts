import { Injectable, Logger } from '@nestjs/common';
import { SecretsConfigurationService } from '@vidmob/vidmob-nestjs-common';
import axios, { AxiosResponse } from 'axios';
import {
  DatabricksJobGetResponse,
  DatabricksJobRunNowRequest,
  DatabricksJobRunNowResponse,
} from './databricks-api.types';
import {
  DATABRICKS_ENDPOINT_AUTH_TOKEN,
  DATABRICKS_ENDPOINT_JOB_GET,
  DATABRICKS_ENDPOINT_JOB_RUN_NOW,
} from './databricks-api.constants';
import * as querystring from 'querystring';

interface DatabricksSecretConfig {
  url: string;
  token?: string;
  clientId?: string;
  clientSecret?: string;
}

interface TokenCache {
  token: string;
  expiresAt: number;
}

@Injectable()
export class DatabricksApiService {
  private readonly logger: Logger = new Logger(DatabricksApiService.name);
  private config: DatabricksSecretConfig | undefined = undefined;

  // keeps copy of token and expiry time in memory
  private tokenCache: TokenCache | undefined = undefined;

  constructor(
    private readonly secretsConfigurationService: SecretsConfigurationService,
  ) {}

  private async getConfig() {
    if (this.config) return this.config;

    this.config =
      await this.secretsConfigurationService.get<DatabricksSecretConfig>(
        'databricks',
      );
    return this.config;
  }

  async postJobRunNow(
    request: DatabricksJobRunNowRequest,
  ): Promise<DatabricksJobRunNowResponse> {
    const resp = await this.callApiPost<DatabricksJobRunNowResponse>(
      DATABRICKS_ENDPOINT_JOB_RUN_NOW,
      request,
    );
    return resp.data;
  }

  async getJob(runId: number): Promise<DatabricksJobGetResponse> {
    const resp = await this.callApiGet<DatabricksJobGetResponse>(
      `${DATABRICKS_ENDPOINT_JOB_GET}?run_id=${runId}`,
    );
    return resp.data;
  }

  async callApiGet<T>(
    endpoint: string,
    params?: { [key: string]: string },
  ): Promise<AxiosResponse<T>> {
    const config = await this.getConfig();
    const token = await this.getToken();

    return axios.get(`${config.url}${endpoint}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params,
    });
  }

  async callApiPost<T>(
    endpoint: string,
    body: any,
    params?: { [key: string]: string },
  ): Promise<AxiosResponse<T>> {
    const config = await this.getConfig();
    const token = await this.getToken();

    return axios.post(`${config.url}${endpoint}`, body, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params,
    });
  }

  async getToken(): Promise<string> {
    const config = await this.getConfig();
    if (config.token) {
      return config.token;
    }

    const now = Date.now();
    if (this.tokenCache && this.tokenCache.expiresAt > now) {
      return this.tokenCache.token;
    }

    return await this.refreshToken();
  }

  async refreshToken() {
    const databricksConfig = await this.getConfig();
    if (!databricksConfig.clientId || !databricksConfig.clientSecret) {
      throw new Error('Databricks oauth credentials not available');
    }
    const data = {
      grant_type: 'client_credentials',
      scope: 'all-apis',
    };
    const dataUrlEncoded = querystring.stringify(data);
    const requestConfig = {
      auth: {
        username: databricksConfig.clientId,
        password: databricksConfig.clientSecret,
      },
    };

    const now = Date.now();

    const resp = await axios.post(
      `${databricksConfig.url}${DATABRICKS_ENDPOINT_AUTH_TOKEN}`,
      dataUrlEncoded,
      requestConfig,
    );

    this.tokenCache = {
      token: resp.data.access_token,
      expiresAt: now + resp.data.expires_in * 1000,
    };

    this.logger.log(
      `Databricks token refreshed, expires at ${this.tokenCache.expiresAt} (in ${resp.data.expires_in} seconds)`,
    );

    return this.tokenCache.token;
  }
}
