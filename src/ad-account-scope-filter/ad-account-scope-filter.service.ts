import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PlatformAdAccountMarketMap } from 'src/ad-accounts/entities/platform-ad-account-market-map.entity';
import { Market } from 'src/markets/entities/market.entity';
import { WorkspaceAdAccountMap } from 'src/workspaces/workspace-ad-account/entities/workspace-ad-account-map.entities';
import { PlatformAdAccount } from '../ad-accounts/entities/ad-account.entity';
import { Repository } from 'typeorm';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { ScopeFilterResponseDto } from './dto/scope-filter-response.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { Brand } from 'src/brands/entities/brand.entity';
import { PlatformAdAccountBrandMap } from 'src/ad-accounts/entities/platform-ad-account-brand-map.entity';
import { ScopeFilterRequestDto } from './dto/scope-filter-request.dto';
import {BrandByAdAccountRequestDto} from "./dto/brand-by-ad-account-request.dto";

@Injectable()
export class AdAccountScopeFilterService {
  constructor(
    @InjectMapper() private readonly classMapper: Mapper,
    @InjectRepository(Brand)
    private readonly brandRepository: Repository<Brand>,
    @InjectRepository(Market)
    private readonly marketRepository: Repository<Market>,
    @InjectRepository(PlatformAdAccount)
    private readonly platformAdAccountRepository: Repository<PlatformAdAccount>,
  ) {}

  async findMarketsByPlatformAndWorkspaceIds(
    scopeFiltersDto: ScopeFilterRequestDto,
    paginationOptions,
  ) {
    const { platform, workspaceIds } = scopeFiltersDto;
    const [markets, totalCount] = await this.marketRepository
      .createQueryBuilder('market')
      .select('market.isoCode')
      .addSelect('market.name')
      .innerJoin(
        PlatformAdAccountMarketMap,
        'platformAdAccountMarketMap',
        'platformAdAccountMarketMap.countryIsoCode = market.isoCode',
      )
      .innerJoin(
        WorkspaceAdAccountMap,
        'workspaceAdAccountMap',
        'workspaceAdAccountMap.platformAdAccountId = platformAdAccountMarketMap.platformAdAccountId',
      )
      .innerJoin(
        PlatformAdAccount,
        'platformAdAccount',
        'platformAdAccount.id = platformAdAccountMarketMap.platformAdAccountId',
      )
      .where('workspaceAdAccountMap.workspaceId in (:workspaceIds)', {
        workspaceIds,
      })
      .andWhere('platformAdAccount.platform = (:platform)', {
        platform,
      })
      .andWhere('platformAdAccount.canAccess = :canAccess', {
        canAccess: true,
      })
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .getManyAndCount();
    const mappedMarkets = this.classMapper.mapArray(
      markets,
      Market,
      ScopeFilterResponseDto,
    );
    return new PaginatedResultArray(mappedMarkets, totalCount);
  }

  async findBrandsByPlatformAndWorkspaceIds(
    scopeFiltersDto: ScopeFilterRequestDto,
    paginationOptions,
  ) {
    const { platform, workspaceIds } = scopeFiltersDto;
    const [brands, totalCount] = await this.brandRepository
      .createQueryBuilder('brand')
      .select('brand.id')
      .addSelect('brand.name')
      .innerJoin(
        PlatformAdAccountBrandMap,
        'platformAdAccountBrandMap',
        'platformAdAccountBrandMap.brand_id = brand.id',
      )
      .innerJoin(
        PlatformAdAccount,
        'platformAdAccount',
        'platformAdAccount.id = platformAdAccountBrandMap.platformAdAccountId',
      )
      .innerJoin(
        WorkspaceAdAccountMap,
        'workspaceAdAccountMap',
        'workspaceAdAccountMap.platformAdAccountId = platformAdAccount.id',
      )
      .where('workspaceAdAccountMap.workspaceId in (:workspaceIds)', {
        workspaceIds,
      })
      .andWhere('platformAdAccount.platform = :platform', {
        platform,
      })
      .andWhere('platformAdAccount.canAccess = :canAccess', {
        canAccess: true,
      })
      .andWhere('brand.deleted = :deleted', { deleted: false })
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .getManyAndCount();
    const mappedBrands = this.classMapper.mapArray(
      brands,
      Brand,
      ScopeFilterResponseDto,
    );
    return new PaginatedResultArray(mappedBrands, totalCount);
  }

  async findAdAccountsByScopeFilters(
    scopeFiltersDto: ScopeFilterRequestDto,
    paginationOptions,
  ) {
    const { platform, workspaceIds, marketIds, brandIds } = scopeFiltersDto;
    const queryBuilder = this.platformAdAccountRepository
      .createQueryBuilder('platformAdAccount')
      .innerJoin(
        'platformAdAccount.workspaceAdAccountMap',
        'workspaceAdAccountMap',
      )
      .leftJoinAndSelect(
        'platformAdAccount.platformAdAccountMarketMap',
        'platformAdAccountMarketMap',
      )
      .leftJoinAndSelect('platformAdAccountMarketMap.market', 'market')
      .leftJoinAndSelect(
        'platformAdAccount.platformAdAccountBrandMap',
        'platformAdAccountBrandMap',
      )
      .leftJoinAndSelect('platformAdAccountBrandMap.brand', 'brand')
      .where('workspaceAdAccountMap.workspaceId in (:workspaceIds)', {
        workspaceIds,
      })
      .andWhere('platformAdAccount.platform = :platform', {
        platform,
      })
      .andWhere('platformAdAccount.canAccess = :canAccess', {
        canAccess: true,
      });

    if (marketIds?.length) {
      queryBuilder.andWhere('market.isoCode in (:marketIds)', {
        marketIds: marketIds,
      });
    }
    if (brandIds?.length) {
      queryBuilder
        .andWhere('brand.id in (:brandIds)', { brandIds: brandIds })
        .andWhere('brand.deleted = :deleted', { deleted: false });
    }

    const [adAccounts, totalCount] = await queryBuilder
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .getManyAndCount();

    const mappedAdAccounts = this.classMapper.mapArray(
      adAccounts,
      PlatformAdAccount,
      ScopeFilterResponseDto,
    );
    return new PaginatedResultArray(mappedAdAccounts, totalCount);
  }

  async getBrandAndMarketsByAdAccounts(brandByAdAccountRequestDto: BrandByAdAccountRequestDto) {
    const brandIds = await this.getBrandIdsByAdAccounts(brandByAdAccountRequestDto);
    const marketIds = await this.getMarketIdsByAdAccounts(brandByAdAccountRequestDto);

    return {
      brandIds,
      marketIds,
    }

  }
  async getBrandIdsByAdAccounts(brandByAdAccountRequestDto: BrandByAdAccountRequestDto) {
    const { adAccountIds } = brandByAdAccountRequestDto;
    const result = await this.brandRepository.createQueryBuilder('brand')
      .select('brand.id')
        .innerJoin(
            PlatformAdAccountBrandMap,
            'platformAdAccountBrandMap',
            'platformAdAccountBrandMap.brand_id = brand.id',
        )
        .innerJoin(
            PlatformAdAccount,
            'platformAdAccount',
            'platformAdAccount.id = platformAdAccountBrandMap.platformAdAccountId',
        )
        .andWhere('platformAdAccount.canAccess = :canAccess', {
          canAccess: true,
        })
        .andWhere('brand.deleted = :deleted', { deleted: false })
        .andWhere('platformAdAccount.platformAccountId IN (:platformAdAccountIds)', {
          platformAdAccountIds: adAccountIds,
      }).getMany();

    return result.map((brand) => brand.id);
  }

  async getMarketIdsByAdAccounts(brandByAdAccountRequestDto: BrandByAdAccountRequestDto) {
    const { adAccountIds} = brandByAdAccountRequestDto;
    const result = await this.marketRepository.createQueryBuilder('market')
        .innerJoin(
            PlatformAdAccountMarketMap,
            'platformAdAccountMarketMap',
            'platformAdAccountMarketMap.countryIsoCode = market.isoCode',
        )
        .innerJoin(
            PlatformAdAccount,
            'platformAdAccount',
            'platformAdAccount.id = platformAdAccountMarketMap.platformAdAccountId',
        )
        .andWhere('platformAdAccount.canAccess = :canAccess', {
          canAccess: true,
        })
        .andWhere('platformAdAccount.platformAccountId IN (:platformAdAccountIds)', {
          platformAdAccountIds: adAccountIds,
        }).getMany();

    return result.map((market) => market.isoCode);
  }
}
