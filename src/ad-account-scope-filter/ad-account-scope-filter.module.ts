import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdAccountScopeFilterController } from './ad-account-scope-filter.controller';
import { Market } from 'src/markets/entities/market.entity';
import { PlatformAdAccountMarketMap } from 'src/ad-accounts/entities/platform-ad-account-market-map.entity';
import { PlatformAdAccount } from 'src/ad-accounts/entities/ad-account.entity';
import { WorkspaceAdAccountMap } from 'src/workspaces/workspace-ad-account/entities/workspace-ad-account-map.entities';
import { AdAccountScopeFilterService } from './ad-account-scope-filter.service';
import { ScopeFilterProfile } from './mapper/scope-filter.profile';
import { Brand } from 'src/brands/entities/brand.entity';
import { PlatformAdAccountBrandMap } from 'src/ad-accounts/entities/platform-ad-account-brand-map.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Market,
      PlatformAdAccountMarketMap,
      WorkspaceAdAccountMap,
      PlatformAdAccount,
      Brand,
      PlatformAdAccountBrandMap,
    ]),
  ],
  exports: [AdAccountScopeFilterService],
  providers: [AdAccountScopeFilterService, ScopeFilterProfile],
  controllers: [AdAccountScopeFilterController],
})
export class AdAccountScopeFilterModule {}
