import { Body, Controller, Post } from '@nestjs/common';
import { ScopeFilterRequestDto } from './dto/scope-filter-request.dto';
import { AdAccountScopeFilterService } from './ad-account-scope-filter.service';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { ScopeFilterEnum } from './enums/ScopeFilter.enum';
import { ScopeFilterResponseDto } from './dto/scope-filter-response.dto';
import { ApiTags } from '@nestjs/swagger';
import {BrandByAdAccountRequestDto} from "./dto/brand-by-ad-account-request.dto";

@ApiTags('Scope Filter')
@Controller('scope-filter')
export class AdAccountScopeFilterController {
  constructor(
    private readonly adAccountScopeFilterService: AdAccountScopeFilterService,
  ) {}
  @VmApiOkResponse({
    description: 'Scope filters successfully retrieved',
    type: ScopeFilterResponseDto,
  })
  @Post('')
  filterAdAccountsByScope(
    @Body() dto: ScopeFilterRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    if (dto.type === ScopeFilterEnum.MARKETS) {
      return this.adAccountScopeFilterService.findMarketsByPlatformAndWorkspaceIds(
        dto,
        paginationOptions,
      );
    }

    if (dto.type === ScopeFilterEnum.BRANDS) {
      return this.adAccountScopeFilterService.findBrandsByPlatformAndWorkspaceIds(
        dto,
        paginationOptions,
      );
    }

    if (dto.type === ScopeFilterEnum.AD_ACCOUNTS) {
      return this.adAccountScopeFilterService.findAdAccountsByScopeFilters(
        dto,
        paginationOptions,
      );
    }
  }

  @Post('brand')
  getBrandsAndMarketsByAdAccounts(@Body() brandByAdAccountRequestDto: BrandByAdAccountRequestDto) {
    return this.adAccountScopeFilterService.getBrandAndMarketsByAdAccounts(brandByAdAccountRequestDto);
  }
}
