import { Mapper, createMap, forMember, mapFrom } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { Market } from 'src/markets/entities/market.entity';
import { ScopeFilterResponseDto } from '../dto/scope-filter-response.dto';
import { Brand } from 'src/brands/entities/brand.entity';
import { PlatformAdAccount } from 'src/ad-accounts/entities/ad-account.entity';

@Injectable()
export class ScopeFilterProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        Market,
        ScopeFilterResponseDto,
        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.isoCode),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.name),
        ),
      );

      createMap(
        mapper,
        Brand,
        ScopeFilterResponseDto,
        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.id),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.name),
        ),
      );

      createMap(
        mapper,
        PlatformAdAccount,
        ScopeFilterResponseDto,
        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.platformAccountId),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.platformAccountName),
        ),
        forMember(
          (dest) => dest.processingCompleted,
          mapFrom((src) => Boolean(src.processingCompleted)),
        ),
        forMember(
          (dest) => dest.brands,
          mapFrom((src) =>
            src.platformAdAccountBrandMap.map((brand) => ({
              id: brand.brandId,
              name: brand.brand.name,
            })),
          ),
        ),
        forMember(
          (dest) => dest.markets,
          mapFrom((src) =>
            src.platformAdAccountMarketMap.map((market) => ({
              id: market.countryIsoCode,
              name: market.market.name,
            })),
          ),
        ),
      );
    };
  }
}
