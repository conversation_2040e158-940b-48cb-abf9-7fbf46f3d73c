import { Test, TestingModule } from '@nestjs/testing';
import { AdAccountScopeFilterController } from './ad-account-scope-filter.controller';
import { AdAccountScopeFilterService } from './ad-account-scope-filter.service';
import { ScopeFilterRequestDto } from './dto/scope-filter-request.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { ScopeFilterEnum } from './enums/ScopeFilter.enum';

describe('AdAccountScopeFilterController', () => {
  let controller: AdAccountScopeFilterController;
  let service: AdAccountScopeFilterService;

  const mockService = {
    findMarketsByPlatformAndWorkspaceIds: jest.fn(),
    findBrandsByPlatformAndWorkspaceIds: jest.fn(),
    findAdAccountsByScopeFilters: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdAccountScopeFilterController],
      providers: [
        { provide: AdAccountScopeFilterService, useValue: mockService },
      ],
    }).compile();

    controller = module.get<AdAccountScopeFilterController>(
      AdAccountScopeFilterController,
    );
    service = module.get<AdAccountScopeFilterService>(
      AdAccountScopeFilterService,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should call findMarketsByPlatformAndWorkspaceIds when dto type is MARKETS', async () => {
    const dto: ScopeFilterRequestDto = {
      type: ScopeFilterEnum.MARKETS,
      platform: 'FACEBOOK',
      workspaceIds: [12345],
    };
    const paginationOptions: PaginationOptions = {};

    await controller.filterAdAccountsByScope(dto, paginationOptions);

    expect(
      mockService.findMarketsByPlatformAndWorkspaceIds,
    ).toHaveBeenCalledWith(dto, paginationOptions);
  });

  it('should call findBrandsByPlatformAndWorkspaceIds when dto type is BRANDS', async () => {
    const dto: ScopeFilterRequestDto = {
      type: ScopeFilterEnum.BRANDS,
      platform: 'FACEBOOK',
      workspaceIds: [12345],
    };
    const paginationOptions: PaginationOptions = {};

    await controller.filterAdAccountsByScope(dto, paginationOptions);

    expect(
      mockService.findBrandsByPlatformAndWorkspaceIds,
    ).toHaveBeenCalledWith(dto, paginationOptions);
  });

  it('should call findAdAccountsByScopeFilters when dto type is AD_ACCOUNTS', async () => {
    const dto: ScopeFilterRequestDto = {
      type: ScopeFilterEnum.AD_ACCOUNTS,
      platform: 'FACEBOOK',
      workspaceIds: [12345],
    };
    const paginationOptions: PaginationOptions = {};

    await controller.filterAdAccountsByScope(dto, paginationOptions);

    expect(mockService.findAdAccountsByScopeFilters).toHaveBeenCalledWith(
      dto,
      paginationOptions,
    );
  });
});
