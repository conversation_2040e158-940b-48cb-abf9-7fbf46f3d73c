import { AutoMap } from '@automapper/classes';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

type option = { id: string; name: string };

export class ScopeFilterResponseDto {
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  id: string;

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  name: string;

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  brands?: option[];

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  markets?: option[];

  @AutoMap()
  @IsOptional()
  @IsBoolean()
  processingCompleted?: boolean;
}
