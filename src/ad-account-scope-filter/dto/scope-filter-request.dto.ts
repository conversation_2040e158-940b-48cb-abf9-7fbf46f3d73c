import { AutoMap } from '@automapper/classes';
import {
  IsString,
  IsNotEmpty,
  IsArray,
  IsOptional,
  IsNumber,
  IsEnum,
  ArrayNotEmpty,
} from 'class-validator';
import { ScopeFilterEnum } from '../enums/ScopeFilter.enum';

export class ScopeFilterRequestDto {
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  platform: string;

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  workspaceIds: number[];

  @AutoMap()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  marketIds?: string[];

  @AutoMap()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  brandIds?: string[];

  @AutoMap()
  @IsNotEmpty()
  @IsEnum(ScopeFilterEnum, {
    each: true,
    message:
      'Scope to filter by. Acceptable values are ' +
      Object.values(ScopeFilterEnum).join(', '),
  })
  type: ScopeFilterEnum;
}
