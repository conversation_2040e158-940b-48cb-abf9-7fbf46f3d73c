import { classes } from '@automapper/classes';
import { Mapper } from '@automapper/core';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceUserProfile } from './workspace-user.profile';
import { CreateWorkspaceUserDto } from 'src/organization-invite/dto/create-workspace-user.dto';
import { WorkspaceUser } from 'src/workspaces/entities/workspace-user.entity';

describe('WorkspaceUserProfile', () => {
  let mapper: Mapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [WorkspaceUserProfile],
    }).compile();

    const workspaceUserProfile: WorkspaceUserProfile =
      module.get<WorkspaceUserProfile>(WorkspaceUserProfile);
    mapper = module.get<Mapper>(getMapperToken());
    workspaceUserProfile.profile(mapper);
  });

  it('should map CreateWorkspaceUserDto to WorkspaceUser', () => {
    const createWorkspaceUserDto: CreateWorkspaceUserDto = {
      userId: 1,
      workspaceId: 2,
      active: true,
      roleId: 3,
      dateCreated: new Date(Date.now()),
      lastUpdated: new Date(Date.now()),
    };

    const workspaceUserEntity = mapper.map(
      createWorkspaceUserDto,
      CreateWorkspaceUserDto,
      WorkspaceUser,
    );

    expect(workspaceUserEntity).toBeDefined();
    expect(workspaceUserEntity.active).toBe(true);
    expect(workspaceUserEntity.userId).toBe(1);
    expect(workspaceUserEntity.workspaceId).toBe(2);
    expect(workspaceUserEntity.roleId).toBe(3);
  });
});
