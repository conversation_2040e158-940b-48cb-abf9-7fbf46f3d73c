import { Mapper, createMap, forMember, mapFrom } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreateWorkspaceUserDto } from 'src/organization-invite/dto/create-workspace-user.dto';
import { WorkspaceUser } from 'src/workspaces/entities/workspace-user.entity';
import { ReadWorkspaceUserDto } from '../dto/read-workspace-user.dto';

@Injectable()
export class WorkspaceUserProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  get profile() {
    return (mapper) => {
      createMap(mapper, CreateWorkspaceUserDto, WorkspaceUser);
      createMap(
        mapper,
        WorkspaceUser,
        ReadWorkspaceUserDto,
        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.user.id),
        ),
        forMember(
          (dest) => dest.displayName,
          mapFrom((src) => src.user.displayName),
        ),
        forMember(
          (dest) => dest.firstName,
          mapFrom((src) => src.user.firstName),
        ),
        forMember(
          (dest) => dest.lastName,
          mapFrom((src) => src.user.lastName),
        ),
        forMember(
          (dest) => dest.photo,
          mapFrom((src) => src.user.photo),
        ),
        forMember(
          (dest) => dest.email,
          mapFrom((src) => src.user.email),
        ),
        forMember(
          (dest) => dest.jobTitle,
          mapFrom((src) => src.user.jobTitle),
        ),
        forMember(
          (dest) => dest.role,
          mapFrom((src) => src.role),
        ),
      );
    };
  }
}
