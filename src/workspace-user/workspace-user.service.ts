import { <PERSON>per } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WORKSPACE_USER_ROLE } from 'src/common/constants/constants';
import { User } from 'src/entities/user.entity';
import { CreateWorkspaceUserDto } from 'src/organization-invite/dto/create-workspace-user.dto';
import { WorkspaceOrganizationInvite } from 'src/organization-invite/entities/workspace-organization-invite.entity';
import { ORGANIZATION_USER_FILTER_BY } from 'src/organizations/utils/constants';
import { RoleService } from 'src/role/role.service';
import { WorkspaceManager } from 'src/workspaces/entities/workspace-manager.entity';
import { WorkspaceUser } from 'src/workspaces/entities/workspace-user.entity';
import { Delete<PERSON><PERSON><PERSON>, In, Query<PERSON><PERSON><PERSON>, Repository } from 'typeorm';
import { ReadWorkspaceUserDto } from './dto/read-workspace-user.dto';

/**
 * WorkspaceUserService is a service class responsible to interact with the relationship between Workspace and User.
 * This service, ideally, should be the one to interact with WorkspaceUser (a.k.a partner_person) and WorkspaceManager (a.k.a partner_manager) tables.
 */
@Injectable()
export class WorkspaceUserService {
  constructor(
    @InjectRepository(WorkspaceUser)
    private workspaceUserRepository: Repository<WorkspaceUser>,
    @InjectRepository(WorkspaceManager)
    private workspaceManagerRepository: Repository<WorkspaceManager>,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly roleService: RoleService,
  ) {}

  async findAllWorkspaceUsersByOrgIdAndUserIds(
    organizationId: string,
    userIds: number[],
  ) {
    return await this.workspaceUserRepository.find({
      where: {
        userId: In(userIds),
        active: true,
        workspace: {
          organizationId,
        },
      },
      relations: {
        workspace: true,
      },
    });
  }

  async findAllWorkspaceManagersByOrgIdAndUserIds(
    organizationId: string,
    userIds: number[],
  ) {
    return await this.workspaceManagerRepository.find({
      where: {
        managerId: In(userIds),
        workspace: {
          organizationId,
        },
      },
      relations: {
        workspace: true,
      },
    });
  }

  async findAllWorkspaceUsers(
    workspaceId: number,
    filterUserBy: ORGANIZATION_USER_FILTER_BY,
  ) {
    const queryBase = this.workspaceUserRepository
      .createQueryBuilder('workspaceUser')
      .innerJoin('workspaceUser.workspace', 'workspace')
      .innerJoinAndSelect('workspaceUser.user', 'user')
      .innerJoinAndSelect('workspaceUser.role', 'role')
      .where('workspace.id = :workspaceId', { workspaceId })
      .andWhere('workspaceUser.active = true')
      .andWhere('role.identifier IN (:...workspaceRoles)', {
        workspaceRoles: [
          WORKSPACE_USER_ROLE.ADMIN,
          WORKSPACE_USER_ROLE.ADMIN_OVERRIDE_REQUEST_REVIEWER,
          WORKSPACE_USER_ROLE.CREATOR,
          WORKSPACE_USER_ROLE.STANDARD,
        ],
      })
      .andWhere('user.enabled = true')
      .andWhere('user.accountLocked = false')
      .andWhere('user.accountExpired = false');

    if (filterUserBy === ORGANIZATION_USER_FILTER_BY.VIDMOB_ONLY) {
      queryBase.andWhere('user.email LIKE :vidmobUser', {
        vidmobUser: `%@vidmob.com`,
      });
    }

    if (filterUserBy === ORGANIZATION_USER_FILTER_BY.NO_VIDMOB) {
      queryBase.andWhere('user.email NOT LIKE :vidmobUser', {
        vidmobUser: `%@vidmob.com`,
      });
    }

    const results = await queryBase
      .orderBy({
        'user.displayName': 'ASC',
        'user.id': 'ASC',
      })
      .getMany();

    return this.classMapper.mapArray(
      results,
      WorkspaceUser,
      ReadWorkspaceUserDto,
    );
  }

  async userHasAccessToAllWorkspaces(
    userId: number,
    workspaceIds: number[],
  ): Promise<boolean> {
    const workspaceUsers = await this.workspaceUserRepository.findBy({
      userId,
      workspaceId: In(workspaceIds),
    });

    return workspaceUsers.length === workspaceIds.length;
  }

  /**
   * It will check if user has sufficient role inside workspace to do any action (e.g, invite) against "roleId".
   * Example:
   * 1 - if user is an ADMIN_OVERRIDE_REQUEST_REVIEWER, he can invite user with any workspace role;
   * 2 - if user is an ADMIN, he can invite user with any workspace role (except from ADMIN_OVERRIDE_REQUEST_REVIEWER role);
   * 3 - if user is an STANDARD, he cannot invite user with ADMIN_OVERRIDE_REQUEST_REVIEWER or ADMIN roles;
   * @param userId
   * @param workspaceId
   * @param roleId
   */
  async userCanUpdateWithRoleId(
    userId: number,
    workspaceId: number,
    roleId: number,
  ) {
    const role = await this.roleService.findWorkspaceRoleById(roleId);

    if (role === null)
      throw new BadRequestException(`Role id ${roleId} cannot be found`);

    if (
      role.identifier === WORKSPACE_USER_ROLE.STANDARD ||
      role.identifier === WORKSPACE_USER_ROLE.CREATOR
    )
      return true;

    const workspaceUser = await this.findUserRoleWithinWorkspace(
      userId,
      workspaceId,
    );

    if (workspaceUser === null)
      throw new BadRequestException(
        `User is not a member of workspace id ${workspaceId}`,
      );

    return this.userRoleHasHigherPermissionsThanRole(
      workspaceUser.role.identifier,
      role.identifier,
    );
  }

  /**
   * It will retrieve the workspace user
   * @param userId
   * @param workspaceId
   * @returns WorkspaceUser
   */
  async findUserRoleWithinWorkspace(
    userId: number,
    workspaceId: number,
  ): Promise<WorkspaceUser> {
    return await this.workspaceUserRepository.findOne({
      where: {
        userId,
        workspaceId,
      },
      relations: {
        role: true,
      },
    });
  }

  async addUserToWorkspaces(
    user: User,
    workspaceOrganizationInvites: WorkspaceOrganizationInvite[],
    roleId: number,
    queryRunner: QueryRunner,
  ) {
    const workspaceIds = workspaceOrganizationInvites.map(
      (workspaceOrgInvite) => workspaceOrgInvite.workspaceId,
    );

    return await this.addUsersToWorkspaces(
      [user],
      workspaceIds,
      roleId,
      queryRunner,
    );
  }

  async addUsersToWorkspaces(
    users: User[],
    workspaces: number[],
    roleId: number,
    queryRunner: QueryRunner,
  ) {
    if (users.length == 0) return;
    if (workspaces.length == 0) return;

    const workspaceUserEntities = this.mapUsersToWorkspaces(
      users,
      workspaces,
      roleId,
    );

    await this.workspaceUserRepository
      .createQueryBuilder('workspaceUser', queryRunner)
      .insert()
      .into(WorkspaceUser)
      .values(workspaceUserEntities)
      .orUpdate(['active', 'last_updated'], ['partner_id', 'person_id'])
      .execute();
  }

  /**
   * It will delete within a transaction all partner_person rows from workspaceIds that userId is in
   * @param workspaceIds
   * @param userId
   * @param queryRunner
   * @returns Promise<DeleteResult>
   */
  async deleteAllWorkspaceUser(
    workspaceIds: number[],
    userId: number,
    queryRunner: QueryRunner,
  ): Promise<DeleteResult> {
    if (workspaceIds.length == 0) return;

    return await this.workspaceUserRepository
      .createQueryBuilder('workspaceUser', queryRunner)
      .delete()
      .from(WorkspaceUser)
      .where({
        workspaceId: In(workspaceIds),
        userId,
      })
      .execute();
  }

  /**
   * It will delete within a transaction all partner_manager rows from workspaceIds that userId is in
   * @param workspaceIds
   * @param userId
   * @param queryRunner
   * @returns Promise<DeleteResult>
   */
  async deleteAllWorkspaceManager(
    workspaceIds: number[],
    userId: number,
    queryRunner: QueryRunner,
  ): Promise<DeleteResult> {
    if (workspaceIds.length == 0) return;

    return await this.workspaceManagerRepository
      .createQueryBuilder('workspaceManager', queryRunner)
      .delete()
      .from(WorkspaceManager)
      .where({
        workspaceId: In(workspaceIds),
        managerId: userId,
      })
      .execute();
  }

  private mapUsersToWorkspaces(
    users: User[],
    workspaces: number[],
    roleId: number,
  ): WorkspaceUser[] {
    return users
      .map((user) =>
        workspaces.map((workspaceId) =>
          this.classMapper.map(
            {
              userId: user.id,
              workspaceId,
              roleId,
              active: true,
              dateCreated: new Date(Date.now()),
              lastUpdated: new Date(Date.now()),
            },
            CreateWorkspaceUserDto,
            WorkspaceUser,
          ),
        ),
      )
      .flat();
  }

  /**
   * It will check if user has higher permissions against a role .
   * 1 - ADMIN_OVERRIDE_REQUEST_REVIEWER is the higher role inside an workspace
   * 2 - ADMIN is higher than STANDARD, but below ADMIN_OVERRIDE_REQUEST_REVIEWER
   * 3 - STANDARD (or PM and AMs) is below ADMIN_OVERRIDE_REQUEST_REVIEWER and ADMIN;
   * @param userId
   * @param workspaceId
   * @param roleId
   */
  private userRoleHasHigherPermissionsThanRole(
    userRoleIdentifier: string,
    roleIdentifier: string,
  ): boolean {
    switch (userRoleIdentifier) {
      case WORKSPACE_USER_ROLE.ADMIN_OVERRIDE_REQUEST_REVIEWER:
        return true;
      case WORKSPACE_USER_ROLE.ADMIN:
        return (
          roleIdentifier !== WORKSPACE_USER_ROLE.ADMIN_OVERRIDE_REQUEST_REVIEWER
        );
      default:
        return false;
    }
  }
}
