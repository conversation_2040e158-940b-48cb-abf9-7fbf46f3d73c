import { <PERSON>, Get, Param, ParseIntPipe, Query } from '@nestjs/common';
import { Api<PERSON>aram, ApiQ<PERSON>y, ApiTags } from '@nestjs/swagger';
import { VmApiOkUnPaginatedArrayResponse } from '@vidmob/vidmob-nestjs-common';
import { WorkspaceUserService } from './workspace-user.service';
import { ORGANIZATION_USER_FILTER_BY } from 'src/organizations/utils/constants';

@ApiTags('workspace-user')
@Controller('workspace')
export class WorkspaceUserController {
  constructor(private readonly workspaceUserService: WorkspaceUserService) {}

  /**
   * Return all users for the workspace.
   * @param workspaceId workspace id
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: Number,
  })
  @ApiParam({
    name: 'workspaceId',
    type: Number,
    description: 'Id of the workspace',
  })
  @ApiQuery({
    name: 'filterBy',
    type: 'string',
    description:
      'filter the results whether for vidmob employees or no vidmob employees. Example: VIDMOB_ONLY or NO_VIDMOB',
  })
  @Get(':workspaceId/user')
  /**
   * TODO This endpoint is migrated from legacy Grails API.
   * We didn't include pagination first (and also querying partner_manager), since the front-end will need
   * to do a lot of re-work to support that, but we should include a pagination (and search) in the next iteration.
   */
  async findAll(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('filterBy') filterBy?: ORGANIZATION_USER_FILTER_BY,
  ) {
    return await this.workspaceUserService.findAllWorkspaceUsers(
      workspaceId,
      filterBy,
    );
  }
}
