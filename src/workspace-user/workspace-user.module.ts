import { Modu<PERSON> } from '@nestjs/common';
import { WorkspaceUserService } from './workspace-user.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkspaceUser } from 'src/workspaces/entities/workspace-user.entity';
import { WorkspaceUserProfile } from './profile/workspace-user.profile';
import { WorkspaceManager } from 'src/workspaces/entities/workspace-manager.entity';
import { RoleModule } from 'src/role/role.module';
import { WorkspaceUserController } from './workspace-user.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([WorkspaceUser, WorkspaceManager]),
    RoleModule,
  ],
  controllers: [WorkspaceUserController],
  exports: [WorkspaceUserService],
  providers: [WorkspaceUserService, WorkspaceUserProfile],
})
export class WorkspaceUserModule {}
