import { AutoMap } from '@automapper/classes';
import { ReadWorkspaceUserRoleDto } from './read-workspace-user-role.dto';

export class ReadWorkspaceUserDto {
  /**
   * Id of the user
   */
  @AutoMap()
  id: number;

  /**
   * the display name of the user
   */
  @AutoMap()
  displayName: string;

  /**
   * the first name of the user
   */
  @AutoMap()
  firstName: string;

  /**
   * the last name of the user
   */
  @AutoMap()
  lastName: string;

  /**
   * the job title of the user
   */
  @AutoMap()
  jobTitle: string;

  /**
   * the photo of the user
   */
  @AutoMap()
  photo: string;

  /**
   * the email of the user
   */
  @AutoMap()
  email: string;

  /**
   * The role of the user
   */
  @AutoMap()
  role: ReadWorkspaceUserRoleDto;
}
