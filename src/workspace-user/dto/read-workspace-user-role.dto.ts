import { AutoMap } from '@automapper/classes';

export class ReadWorkspaceUserRoleDto {
  /**
   * Id of the role
   * @example 123
   */
  @AutoMap()
  id: number;

  /**
   * Name of the role
   * @example Admin
   */
  @AutoMap()
  name: string;

  /**
   * Type of the role
   * @example business_entity
   */
  @AutoMap()
  type: string;

  /**
   * Identifier of the role
   * @example ADMIN
   */
  @AutoMap()
  identifier: string;

  /**
   * Description of the role
   * @example Can manage this workspace, its members, and access apps.
   */
  @AutoMap()
  description: string;
}
