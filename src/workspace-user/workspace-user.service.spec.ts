import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceUserService } from './workspace-user.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { WorkspaceUser } from 'src/workspaces/entities/workspace-user.entity';
import { QueryRunner, Repository } from 'typeorm';
import { getMapperToken } from '@automapper/nestjs';
import { Mapper, createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { User } from 'src/entities/user.entity';
import { WorkspaceUserProfile } from './profile/workspace-user.profile';
import { WorkspaceManager } from 'src/workspaces/entities/workspace-manager.entity';
import { RoleService } from 'src/role/role.service';
import { Role } from 'src/entities/role.entity';
import { RoleType, WORKSPACE_USER_ROLE } from 'src/common/constants/constants';
import { ORGANIZATION_USER_FILTER_BY } from 'src/organizations/utils/constants';

const createQueryBuilder: any = {
  insert: () => createQueryBuilder,
  into: () => createQueryBuilder,
  values: () => createQueryBuilder,
  orUpdate: () => createQueryBuilder,
  execute: () => createQueryBuilder,
  delete: () => createQueryBuilder,
  from: () => createQueryBuilder,
  where: () => createQueryBuilder,
  innerJoin: () => createQueryBuilder,
  innerJoinAndSelect: () => createQueryBuilder,
  andWhere: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  orderBy: () => createQueryBuilder,
  getMany: () => createQueryBuilder,
};

const adminOverrideRequestReviewerRole: Role = {
  id: 1,
  name: 'Admin Override Request Reviewer',
  type: RoleType.WORKSPACE_ROLE_TYPE,
  identifier: WORKSPACE_USER_ROLE.ADMIN_OVERRIDE_REQUEST_REVIEWER,
  description: '',
};

const adminRole: Role = {
  id: 2,
  name: 'Admin',
  type: RoleType.WORKSPACE_ROLE_TYPE,
  identifier: WORKSPACE_USER_ROLE.ADMIN,
  description: '',
};

const standardRole: Role = {
  id: 3,
  name: 'Standard',
  type: RoleType.WORKSPACE_ROLE_TYPE,
  identifier: WORKSPACE_USER_ROLE.STANDARD,
  description: '',
};

const vidmobUser = {
  id: 1,
  email: '<EMAIL>',
  displayName: 'test user',
  firstName: 'test',
  lastName: 'user',
  photo: null,
  jobTitle: null,
} as User;

const externalUser = {
  id: 2,
  email: '<EMAIL>',
  displayName: 'external user',
  firstName: 'external',
  lastName: 'user',
  photo: null,
  jobTitle: null,
} as User;

const TEST_USER_ID = 1;
const TEST_WORKSPACE_ID = 2;

describe('WorkspaceUserService', () => {
  let mapper: Mapper;
  let service: WorkspaceUserService;
  let roleService: RoleService;
  let workspaceUserRepository: Repository<WorkspaceUser>;
  let workspaceManagerRepository: Repository<WorkspaceManager>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceUserProfile,
        WorkspaceUserService,
        {
          provide: RoleService,
          useValue: {
            findWorkspaceRoleById: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(WorkspaceUser),
          useValue: {
            createQueryBuilder: jest
              .fn()
              .mockImplementation(() => createQueryBuilder),
            findOne: jest.fn(),
            findBy: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(WorkspaceManager),
          useValue: {
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    const workspaceUserProfile: WorkspaceUserProfile =
      module.get<WorkspaceUserProfile>(WorkspaceUserProfile);
    mapper = module.get<Mapper>(getMapperToken());
    workspaceUserProfile.profile(mapper);

    service = module.get<WorkspaceUserService>(WorkspaceUserService);
    roleService = module.get<RoleService>(RoleService);

    workspaceUserRepository = module.get<Repository<WorkspaceUser>>(
      getRepositoryToken(WorkspaceUser),
    );
    workspaceManagerRepository = module.get<Repository<WorkspaceManager>>(
      getRepositoryToken(WorkspaceManager),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAllWorkspaceUsers', () => {
    it('should return a list of vidmob workspace users', async () => {
      jest.spyOn(createQueryBuilder, 'getMany').mockResolvedValueOnce([
        {
          user: vidmobUser,
          role: standardRole,
        },
      ]);

      await expect(
        service.findAllWorkspaceUsers(
          TEST_WORKSPACE_ID,
          ORGANIZATION_USER_FILTER_BY.VIDMOB_ONLY,
        ),
      ).resolves.toEqual([
        {
          ...vidmobUser,
          role: standardRole,
        },
      ]);

      expect(createQueryBuilder.andWhere.mock.calls).toStrictEqual([
        ['workspaceUser.active = true'],
        [
          'role.identifier IN (:...workspaceRoles)',
          {
            workspaceRoles: [
              WORKSPACE_USER_ROLE.ADMIN,
              WORKSPACE_USER_ROLE.ADMIN_OVERRIDE_REQUEST_REVIEWER,
              WORKSPACE_USER_ROLE.CREATOR,
              WORKSPACE_USER_ROLE.STANDARD,
            ],
          },
        ],
        ['user.enabled = true'],
        ['user.accountLocked = false'],
        ['user.accountExpired = false'],
        [
          'user.email LIKE :vidmobUser',
          {
            vidmobUser: `%@vidmob.com`,
          },
        ],
      ]);
    });

    it('should return a list of external workspace users', async () => {
      jest.spyOn(createQueryBuilder, 'getMany').mockResolvedValueOnce([
        {
          user: externalUser,
          role: standardRole,
        },
      ]);

      await expect(
        service.findAllWorkspaceUsers(
          TEST_WORKSPACE_ID,
          ORGANIZATION_USER_FILTER_BY.NO_VIDMOB,
        ),
      ).resolves.toEqual([
        {
          ...externalUser,
          role: standardRole,
        },
      ]);

      expect(createQueryBuilder.andWhere.mock.calls).toStrictEqual([
        ['workspaceUser.active = true'],
        [
          'role.identifier IN (:...workspaceRoles)',
          {
            workspaceRoles: [
              WORKSPACE_USER_ROLE.ADMIN,
              WORKSPACE_USER_ROLE.ADMIN_OVERRIDE_REQUEST_REVIEWER,
              WORKSPACE_USER_ROLE.CREATOR,
              WORKSPACE_USER_ROLE.STANDARD,
            ],
          },
        ],
        ['user.enabled = true'],
        ['user.accountLocked = false'],
        ['user.accountExpired = false'],
        [
          'user.email NOT LIKE :vidmobUser',
          {
            vidmobUser: `%@vidmob.com`,
          },
        ],
      ]);
    });

    it('should return a list of every workspace user', async () => {
      jest.spyOn(createQueryBuilder, 'getMany').mockResolvedValueOnce([
        {
          user: vidmobUser,
          role: standardRole,
        },
        {
          user: externalUser,
          role: standardRole,
        },
      ]);

      await expect(
        service.findAllWorkspaceUsers(TEST_WORKSPACE_ID, null),
      ).resolves.toEqual([
        {
          ...vidmobUser,
          role: standardRole,
        },
        {
          ...externalUser,
          role: standardRole,
        },
      ]);

      expect(createQueryBuilder.andWhere.mock.calls).toStrictEqual([
        ['workspaceUser.active = true'],
        [
          'role.identifier IN (:...workspaceRoles)',
          {
            workspaceRoles: [
              WORKSPACE_USER_ROLE.ADMIN,
              WORKSPACE_USER_ROLE.ADMIN_OVERRIDE_REQUEST_REVIEWER,
              WORKSPACE_USER_ROLE.CREATOR,
              WORKSPACE_USER_ROLE.STANDARD,
            ],
          },
        ],
        ['user.enabled = true'],
        ['user.accountLocked = false'],
        ['user.accountExpired = false'],
      ]);
    });
  });

  describe('userHasAccessToAllWorkspaces', () => {
    it('should return true if user has access to all three workspaces', async () => {
      jest
        .spyOn(workspaceUserRepository, 'findBy')
        .mockResolvedValueOnce([
          new WorkspaceUser(),
          new WorkspaceUser(),
          new WorkspaceUser(),
        ]);

      await expect(
        service.userHasAccessToAllWorkspaces(TEST_USER_ID, [1, 2, 3]),
      ).resolves.toBeTruthy();
    });

    it('should return false if user does not have access to all three workspaces', async () => {
      jest
        .spyOn(workspaceUserRepository, 'findBy')
        .mockResolvedValueOnce([new WorkspaceUser(), new WorkspaceUser()]);

      await expect(
        service.userHasAccessToAllWorkspaces(TEST_USER_ID, [1, 2, 3]),
      ).resolves.toBeFalsy();
    });
  });

  describe('userCanUpdateWithRoleId', () => {
    afterEach(() => jest.restoreAllMocks());

    const adminOverrideRequestReviewerWorkspaceUser = {
      role: adminOverrideRequestReviewerRole,
    } as WorkspaceUser;

    const adminWorkspaceUser = {
      role: adminRole,
    } as WorkspaceUser;

    const standardWorkspaceUser = {
      role: standardRole,
    } as WorkspaceUser;

    it('admin override requester review should be able to update any role id', async () => {
      jest
        .spyOn(roleService, 'findWorkspaceRoleById')
        .mockResolvedValueOnce(adminOverrideRequestReviewerRole);

      jest
        .spyOn(workspaceUserRepository, 'findOne')
        .mockResolvedValueOnce(adminOverrideRequestReviewerWorkspaceUser);

      await expect(
        service.userCanUpdateWithRoleId(TEST_USER_ID, TEST_WORKSPACE_ID, 1),
      ).resolves.toBeTruthy();
    });

    it('admin should be able to update an admin role id', async () => {
      jest
        .spyOn(roleService, 'findWorkspaceRoleById')
        .mockResolvedValueOnce(adminRole);

      jest
        .spyOn(workspaceUserRepository, 'findOne')
        .mockResolvedValueOnce(adminWorkspaceUser);

      await expect(
        service.userCanUpdateWithRoleId(TEST_USER_ID, TEST_WORKSPACE_ID, 2),
      ).resolves.toBeTruthy();
    });

    it('admin should be able to update an standard role id', async () => {
      jest
        .spyOn(roleService, 'findWorkspaceRoleById')
        .mockResolvedValueOnce(standardRole);

      jest
        .spyOn(workspaceUserRepository, 'findOne')
        .mockResolvedValueOnce(adminWorkspaceUser);

      await expect(
        service.userCanUpdateWithRoleId(TEST_USER_ID, TEST_WORKSPACE_ID, 3),
      ).resolves.toBeTruthy();
    });

    it('admin should not be able to update an admin override requester review role id', async () => {
      jest
        .spyOn(roleService, 'findWorkspaceRoleById')
        .mockResolvedValueOnce(adminOverrideRequestReviewerRole);

      jest
        .spyOn(workspaceUserRepository, 'findOne')
        .mockResolvedValueOnce(adminWorkspaceUser);

      await expect(
        service.userCanUpdateWithRoleId(TEST_USER_ID, TEST_WORKSPACE_ID, 1),
      ).resolves.toBeFalsy();
    });

    it('standard should not be able to update an admin override requester review role id', async () => {
      jest
        .spyOn(roleService, 'findWorkspaceRoleById')
        .mockResolvedValueOnce(adminOverrideRequestReviewerRole);

      jest
        .spyOn(workspaceUserRepository, 'findOne')
        .mockResolvedValueOnce(standardWorkspaceUser);

      await expect(
        service.userCanUpdateWithRoleId(TEST_USER_ID, TEST_WORKSPACE_ID, 1),
      ).resolves.toBeFalsy();
    });

    it('standard should not be able to update an admin role id', async () => {
      jest
        .spyOn(roleService, 'findWorkspaceRoleById')
        .mockResolvedValueOnce(adminRole);

      jest
        .spyOn(workspaceUserRepository, 'findOne')
        .mockResolvedValueOnce(standardWorkspaceUser);

      await expect(
        service.userCanUpdateWithRoleId(TEST_USER_ID, TEST_WORKSPACE_ID, 2),
      ).resolves.toBeFalsy();
    });

    it('standard should be able to update a standard role id', async () => {
      jest
        .spyOn(roleService, 'findWorkspaceRoleById')
        .mockResolvedValueOnce(standardRole);

      jest
        .spyOn(workspaceUserRepository, 'findOne')
        .mockResolvedValueOnce(standardWorkspaceUser);

      await expect(
        service.userCanUpdateWithRoleId(TEST_USER_ID, TEST_WORKSPACE_ID, 3),
      ).resolves.toBeTruthy();
    });

    it('any user with access should be able to update a standard role id', async () => {
      jest
        .spyOn(roleService, 'findWorkspaceRoleById')
        .mockResolvedValueOnce(standardRole);

      await expect(
        service.userCanUpdateWithRoleId(TEST_USER_ID, TEST_WORKSPACE_ID, 3),
      ).resolves.toBeTruthy();
    });
  });

  describe('addUsersToWorkspaces', () => {
    const users = [new User(), new User()];
    const workspaces = [123, 456];
    const roleId = 7;

    let executeSpy: jest.SpyInstance;

    beforeEach(() => {
      jest
        .spyOn(workspaceUserRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);
      executeSpy = jest.spyOn(createQueryBuilder, 'execute');
    });

    afterEach(() => jest.restoreAllMocks());

    it('should add users to workspaces', async () => {
      await service.addUsersToWorkspaces(
        users,
        workspaces,
        roleId,
        {} as QueryRunner,
      );
      expect(executeSpy).toBeCalled();
    });

    it('should not add users if users array is empty', async () => {
      await service.addUsersToWorkspaces(
        [],
        workspaces,
        roleId,
        {} as QueryRunner,
      );
      expect(executeSpy).not.toBeCalled();
    });

    it('should not add users if workspaces array is empty', async () => {
      await service.addUsersToWorkspaces(users, [], roleId, {} as QueryRunner);
      expect(executeSpy).not.toBeCalled();
    });
  });

  describe('deleteAllWorkspaceUser', () => {
    let executeSpy: jest.SpyInstance;
    const userId = 123;

    beforeEach(() => {
      jest
        .spyOn(workspaceUserRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);
      executeSpy = jest.spyOn(createQueryBuilder, 'execute');
    });

    afterEach(() => jest.restoreAllMocks());

    it('should delete all workspace user is in', async () => {
      await service.deleteAllWorkspaceUser(
        [123, 456],
        userId,
        {} as QueryRunner,
      );
      expect(executeSpy).toBeCalled();
    });

    it('should do nothing if workspace ids are empty', async () => {
      await service.deleteAllWorkspaceUser([], userId, {} as QueryRunner);
      expect(executeSpy).not.toBeCalled();
    });
  });

  describe('deleteAllWorkspaceManager', () => {
    let executeSpy: jest.SpyInstance;
    const userId = 123;

    beforeEach(() => {
      jest
        .spyOn(workspaceManagerRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);
      executeSpy = jest.spyOn(createQueryBuilder, 'execute');
    });

    afterEach(() => jest.restoreAllMocks());

    it('should delete all workspace manager the user is in', async () => {
      await service.deleteAllWorkspaceManager(
        [123, 456],
        userId,
        {} as QueryRunner,
      );
      expect(executeSpy).toBeCalled();
    });

    it('should do nothing if workspace ids are empty', async () => {
      await service.deleteAllWorkspaceManager([], userId, {} as QueryRunner);
      expect(executeSpy).not.toBeCalled();
    });
  });
});
