import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceUserController } from './workspace-user.controller';
import { WorkspaceUserService } from './workspace-user.service';
import { RoleType, WORKSPACE_USER_ROLE } from 'src/common/constants/constants';

const TEST_WORKSPACE_ID = 1;

const workspaceUsers = [
  {
    id: 2,
    email: '<EMAIL>',
    displayName: 'external user',
    firstName: 'external',
    lastName: 'user',
    photo: null,
    jobTitle: null,
    role: {
      id: 3,
      name: 'Standard',
      type: RoleType.WORKSPACE_ROLE_TYPE,
      identifier: WORKSPACE_USER_ROLE.STANDARD,
      description: '',
    },
  },
];

describe('WorkspaceUserController', () => {
  let controller: WorkspaceUserController;
  let workspaceUserService: WorkspaceUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceUserController,
        {
          provide: WorkspaceUserService,
          useValue: {
            findAllWorkspaceUsers: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<WorkspaceUserController>(WorkspaceUserController);
    workspaceUserService =
      module.get<WorkspaceUserService>(WorkspaceUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    beforeEach(() => {
      jest
        .spyOn(workspaceUserService, 'findAllWorkspaceUsers')
        .mockResolvedValue(workspaceUsers);
    });

    it('should return a list of workspace users', async () => {
      await expect(controller.findAll(TEST_WORKSPACE_ID)).resolves.toEqual(
        workspaceUsers,
      );
    });
  });
});
