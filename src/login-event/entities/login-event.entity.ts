import { AutoMap } from '@automapper/classes';
import { Type } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  VersionColumn,
} from 'typeorm';

@Entity('login_event')
export class LoginEvent {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @VersionColumn()
  version: number;

  @AutoMap()
  @Column({
    name: 'entered_email_address',
    type: 'varchar',
    length: 190,
    nullable: false,
  })
  enteredEmailAddress: string;

  @AutoMap()
  @Column({ name: 'ip_address', type: 'varchar', length: 15, nullable: true })
  ipAddress: string;

  @AutoMap()
  @Type(() => Number)
  @Column({
    name: 'person_id',
    type: 'bigint',
    nullable: true,
    transformer: {
      to(value) {
        // Do nothing when writing the value
        return value;
      },
      from(value) {
        // Convert to number
        return Number(value);
      },
    },
  })
  userId: number;

  @AutoMap()
  @Column({ name: 'is_login_successful', type: 'tinyint', nullable: false })
  isLoginSuccessful: boolean;

  @AutoMap()
  @CreateDateColumn({ name: 'date_created' })
  dateCreated: Date;
}
