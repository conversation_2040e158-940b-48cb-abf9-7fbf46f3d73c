import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LoginEvent } from './entities/login-event.entity';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class LoginEventService {
  constructor(
    @InjectRepository(LoginEvent)
    private readonly loginEventRepository: Repository<LoginEvent>,
  ) {}

  /**
   * It returns the last successful login event for provided user ids.
   * Note this method returns only the userId and dateCreated columns
   * @param userIds
   * @returns
   */
  async findLatestSuccessfulLoginForUsers(userIds: number[]) {
    if (userIds.length === 0) {
      return [];
    }

    // Getting raw since calculated values cannot be used as select
    const rawResults = await this.loginEventRepository
      .createQueryBuilder('loginEvent')
      .select('loginEvent.userId', 'userId')
      .addSelect('MAX(date_created)', 'dateCreated')
      .where('loginEvent.userId IN (:...userIds)', {
        userIds: userIds,
      })
      .andWhere('loginEvent.isLoginSuccessful = true')
      .groupBy('loginEvent.userId')
      .getRawMany();

    // Mapping raw results to login events
    return rawResults.map((rawResult) =>
      plainToInstance(LoginEvent, rawResult),
    );
  }
}
