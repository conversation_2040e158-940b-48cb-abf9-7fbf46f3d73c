import { Test, TestingModule } from '@nestjs/testing';
import { LoginEventService } from './login-event.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { LoginEvent } from './entities/login-event.entity';

describe('LoginEventService', () => {
  let service: LoginEventService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LoginEventService,
        {
          provide: getRepositoryToken(LoginEvent),
          useValue: {
            createQueryBuilder: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<LoginEventService>(LoginEventService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
