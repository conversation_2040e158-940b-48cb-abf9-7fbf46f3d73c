import { AutoMap } from '@automapper/classes';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class AcceptOrganizationInviteDto {
  /**
   * the user id who is accepting the invitation, i.e the invitee.
   * This info will be used by an already logged user trying to accept the invite
   */
  @AutoMap()
  @IsNumber()
  @IsOptional()
  inviteeId?: number;

  /**
   * the user email who is accepting the invitation, i.e the invitee.
   * This info will only be used through sso logins, anywhere else should use the inviteeId!
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  inviteeEmail?: string;
}
