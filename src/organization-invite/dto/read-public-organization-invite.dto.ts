import { AutoMap } from '@automapper/classes';
import { IsString, isString } from 'class-validator';
import { ORGANIZATION_INVITE_STATUS } from 'src/common/constants/constants';
import { ReadRoleInviteDto } from './read-role-invite.dto';

export class ReadPublicOrganizationInvite {
  @AutoMap()
  @IsString()
  id: string;

  @AutoMap()
  @IsString()
  email: string;

  @AutoMap()
  @IsString()
  organizationId: string;

  @AutoMap()
  @IsString()
  status: ORGANIZATION_INVITE_STATUS;

  @AutoMap()
  @IsString()
  role: ReadRoleInviteDto;
}
