import { AutoMap } from '@automapper/classes';
import { ArrayNotEmpty, IsArray, IsNumber, IsString } from 'class-validator';

export class CreateMultipleUserWorkspaceDto {
  /**
   * An array of users email
   */
  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  users: string[];

  /**
   * An array of workspaces ids
   */
  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  workspaces: number[];

  /**
   * the role id
   */
  @AutoMap()
  @IsNumber()
  roleId: number;

  /**
   * the user id who is making the request
   */
  @AutoMap()
  @IsNumber()
  inviterId: number;
}
