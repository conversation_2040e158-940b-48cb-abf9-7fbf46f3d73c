import { AutoMap } from '@automapper/classes';
import { ArrayNotEmpty, IsArray, IsNumber, IsString } from 'class-validator';

export class CreateMultipleUserSingleWorkspaceDto {
  /**
   * An array of users email
   */
  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  users: string[];

  /**
   * the role id
   */
  @AutoMap()
  @IsNumber()
  roleId: number;

  /**
   * the user id who is making the request
   */
  @AutoMap()
  @IsNumber()
  inviterId: number;
}
