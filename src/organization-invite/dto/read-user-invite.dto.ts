import { AutoMap } from "@automapper/classes";

export class ReadUserInviteDto {
  /**
   * Id of the User
   * @example 123
   */
  @AutoMap()
  id: number;

  /**
   * Username
   * @example <EMAIL>
   */
  @AutoMap()
  username: string;

  /**
   * First name of user
   * @example FirstName
   */
  @AutoMap()
  firstName: string;

  /**
   * Last name of user
   * @example LastName
   */
  @AutoMap()
  lastName: string;

  /**
   * Display name of user
   * @example FirstName LastName
   */
  @AutoMap()
  displayName: string;

  /**
   * Email of user
   * @example <EMAIL>
   */
  @AutoMap()
  email: string;

  /**
   * Job title of user
   * @example Pilot
   */
  @AutoMap()
  jobTitle: string;

  /**
   * Photo of user
   * @example https://d2mcrmdbaugu8j.cloudfront.net/ABC123DEFHIJ/user/avatar/user-avatar.png
   */
  @AutoMap()
  photo: string;
}
