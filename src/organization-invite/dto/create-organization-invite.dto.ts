import { AutoMap } from '@automapper/classes';

export class CreateOrganizationInviteDto {
  @AutoMap()
  organizationId: string;

  @AutoMap()
  email: string;

  @AutoMap()
  roleId: number;

  @AutoMap()
  inviteCode: string;

  @AutoMap()
  validationCode: string;

  @AutoMap()
  status: string;

  @AutoMap()
  inviterId: number;

  @AutoMap()
  dateCreated: Date;

  @AutoMap()
  lastUpdated: Date;
}
