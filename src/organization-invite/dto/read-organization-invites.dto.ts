import { AutoMap } from '@automapper/classes';
import { IsDate, IsString } from 'class-validator';
import { ReadUserInviteDto } from './read-user-invite.dto';
import { ReadRoleInviteDto } from './read-role-invite.dto';

export class ReadOrganizationInvitesDto {
  @AutoMap()
  @IsString()
  id: string;

  @AutoMap()
  @IsString()
  userEmail: string;

  @AutoMap()
  role: ReadRoleInviteDto;

  @AutoMap()
  @IsString()
  status: string;

  @AutoMap()
  @IsDate()
  lastUpdated: Date;

  @AutoMap()
  invitedBy: ReadUserInviteDto;
}
