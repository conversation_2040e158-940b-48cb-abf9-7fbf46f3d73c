import { IsEnum, IsOptional, IsString } from "class-validator";
import { SortOrder } from "src/common/constants/constants";

export class OrganizationInviteSearchParamsDto {
  /**
   * Search string to filter organization invites by email
   */
  @IsOptional()
  @IsString()
  search?: string;

  /**
   * Sort by string
   * @example "lastUpdated"
   */
  @IsOptional()
  @IsString()
  sortBy?: string;

  /**
   * Sort Order string
   * @example "ASC"
   */
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: string;
}
