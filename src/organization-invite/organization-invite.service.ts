import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { OrganizationInvite } from './entities/organization-invite.entity';
import { In, QueryRunner, Repository } from 'typeorm';
import { WorkspaceOrganizationInvite } from './entities/workspace-organization-invite.entity';
import { CreateMultipleUserWorkspaceDto } from './dto/create-multiple-user-workspace.dto';
import { startTransaction } from 'src/common/utils/helper';
import { OrganizationUserQueryService } from 'src/organizations/organization-user/organization-user-query.service';
import { User } from 'src/entities/user.entity';
import { WorkspaceUserService } from 'src/workspace-user/workspace-user.service';
import { randomUUID } from 'crypto';
import { OrganizationInviteSearchParamsDto } from './dto/organization-invite-search-params.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadOrganizationInvitesDto } from './dto/read-organization-invites.dto';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import {
  OrganizationUserRoles,
  SortOrder,
} from 'src/common/constants/constants';
import { CreateOrganizationInviteWithIdDto } from './dto/create-organization-invite-with-id.dto';
import { SuccessfulMessageResponseDto } from './dto/successful-message-response.dto';
import { ORGANIZATION_INVITE_STATUS } from 'src/common/constants/constants';
import { UpdateOrganizationInviteDto } from './dto/update-organization-invite.dto';
import { ReadPublicOrganizationInvite } from './dto/read-public-organization-invite.dto';
import { Organization } from 'src/organizations/entities/organization.entity';
import { RoleService } from 'src/role/role.service';

@Injectable()
export class OrganizationInviteService {
  private readonly INVITE_CODE_LENGTH = 100;
  private readonly VALIDATION_CODE_LENGTH = 32;
  private readonly CHARACTERS =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

  private readonly ORGANIZATION_INVITE_SORT_BY_FIELDS = {
    email: 'organizationInvite.email',
    lastUpdated: 'organizationInvite.lastUpdated',
  };
  private readonly RESET_THRESHOLD_TIME = 60 * 5 * 1000; // five minutes, millis

  constructor(
    @InjectRepository(OrganizationInvite)
    private organizationInviteRepository: Repository<OrganizationInvite>,
    @InjectRepository(WorkspaceOrganizationInvite)
    private workspaceOrganizationInviteRepository: Repository<WorkspaceOrganizationInvite>,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly organizationUserQueryService: OrganizationUserQueryService,
    private readonly workspaceUserService: WorkspaceUserService,
    private readonly roleService: RoleService,
  ) {}

  /**
   * To be able to correct bulk invite multiple users to multiple workspaces, we need to:
   * 1 - Know which users (from user's emails sent) are part or not from the organization;
   * 2 - Those who is already part of the organization, can be "easily" added to the workspaces with the role sent;
   * 3 - Those who are not part of the organization, needs to be invited by email and saving those invitation
   *  and organization/workspace invites saved into organization_invite and workspace_organization_invite
   */
  async create(organizationId: string, dto: CreateMultipleUserWorkspaceDto) {
    const usersInOrganization =
      await this.organizationUserQueryService.findOrganizationUserByEmails(
        organizationId,
        dto.users,
      );

    const usersToEmailInvite = dto.users.filter(
      (email: string) =>
        !usersInOrganization.some(
          (user: User) => user.email.toLowerCase() == email.toLowerCase(),
        ),
    );

    const queryRunner = await startTransaction(
      this.organizationInviteRepository,
    );

    try {
      await this.workspaceUserService.addUsersToWorkspaces(
        usersInOrganization,
        dto.workspaces,
        dto.roleId,
        queryRunner,
      );

      const organizationInvites = await this.inviteEmailUsers(
        organizationId,
        usersToEmailInvite,
        dto,
        queryRunner,
      );

      await queryRunner.commitTransaction();
      await queryRunner.release();

      return organizationInvites;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      await queryRunner.release();
      throw error;
    }
  }

  /**
   * To accept the invitation we need to do in a single transaction:
   * 1 - Add user to organization_person_role with standard role;
   * 2 - Add user to person_organization_map
   * 3 - Add user to the invited partner_person table with the role on the invite;
   * 4 - Set organization invite as accepted
   */
  async acceptOrganizationInvite(
    organization: Organization,
    invitee: User,
    organizationInvite: OrganizationInvite,
  ): Promise<SuccessfulMessageResponseDto> {
    if (
      !this.canOrganizationInviteBeUpdated(
        organizationInvite,
        ORGANIZATION_INVITE_STATUS.ACCEPTED,
      )
    ) {
      throw new BadRequestException(
        `Organization invite with status ${organizationInvite.status} cannot be accepted`,
      );
    }

    const organizationRole =
      await this.roleService.findOrganizationRoleByIdentifier(
        OrganizationUserRoles.ORG_STANDARD,
      );

    const workspaceOrganizationInvites =
      await this.workspaceOrganizationInviteRepository.findBy({
        organizationInviteId: organizationInvite.id,
      });

    const queryRunner = await startTransaction(
      this.organizationInviteRepository,
    );

    try {
      // Add user to organization_person_role with standard role;
      await this.organizationUserQueryService.createOrganizationUserRoleTransactional(
        invitee,
        organization,
        organizationRole,
        queryRunner,
      );

      // Add user to person_organization_map
      await this.organizationUserQueryService.createUserOrganizationMapTransactional(
        invitee,
        organization,
        queryRunner,
      );

      // Add user to the invited partner_person table with the role on the invite;
      await this.workspaceUserService.addUserToWorkspaces(
        invitee,
        workspaceOrganizationInvites,
        organizationInvite.roleId,
        queryRunner,
      );

      //  Set organization invite as accepted
      await this.updateOrganizationInvite(
        organizationInvite.id,
        organization.id,
        {
          status: ORGANIZATION_INVITE_STATUS.ACCEPTED,
        },
      );

      await queryRunner.commitTransaction();
      await queryRunner.release();

      return {
        message: 'Organization invitation accepted successfully',
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      await queryRunner.release();
      throw error;
    }
  }

  async getPendingInvites(
    organizationId: string,
    paginationOptions?: PaginationOptions,
    searchParams?: OrganizationInviteSearchParamsDto,
  ): Promise<PaginatedResultArray<ReadOrganizationInvitesDto>> {
    const query = this.organizationInviteRepository
      .createQueryBuilder('organizationInvite')
      .innerJoinAndSelect('organizationInvite.invitedBy', 'user')
      .innerJoinAndSelect('organizationInvite.role', 'role')
      .where('organizationInvite.organizationId = :organizationId', {
        organizationId,
      })
      .andWhere('organizationInvite.status = :status', {
        status: ORGANIZATION_INVITE_STATUS.PENDING,
      });

    if (searchParams?.search?.length > 0) {
      query.andWhere('organizationInvite.email LIKE :search', {
        search: `%${searchParams?.search}%`,
      });
    }

    const sortBy =
      this.ORGANIZATION_INVITE_SORT_BY_FIELDS[searchParams?.sortBy] ??
      'organizationInvite.lastUpdated';

    const [result, total] = await query
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .orderBy(sortBy, (searchParams?.sortOrder as SortOrder) ?? 'DESC')
      .getManyAndCount();

    const readOrganizationInvites = this.classMapper.mapArray(
      result,
      OrganizationInvite,
      ReadOrganizationInvitesDto,
    );
    return new PaginatedResultArray(readOrganizationInvites, total);
  }

  async getPendingInvitesEmailsByEmails(
    organizationId: string,
    emails: string[],
  ): Promise<string[]> {
    const organizationPendingInvites =
      await this.organizationInviteRepository.findBy({
        organizationId,
        email: In(emails),
        status: ORGANIZATION_INVITE_STATUS.PENDING,
      });

    return organizationPendingInvites.map(
      (pendingInvite) => pendingInvite.email,
    );
  }

  /**
   * This function retrieves an organization invite by its id and organization id
   * @param organizationId
   * @param id
   * @returns
   */
  async getInviteByIdAndOrganizationId(organizationId: string, id: string) {
    return await this.organizationInviteRepository.findOne({
      relations: {
        invitedBy: true,
      },
      where: {
        id,
        organizationId,
      },
    });
  }

  /**
   * This function will return the organization invite by its id and the user email
   * @param id - the organization invite id
   * @param email - the invite emaill
   * @returns
   */
  async getInviteCodeByIdAndEmail(id: string, email: string) {
    return await this.organizationInviteRepository.findOneBy({
      id,
      email,
    });
  }

  async getInviteByInviteCode(
    organizationId: string,
    inviteCode: string,
    validationCode: string,
  ): Promise<ReadPublicOrganizationInvite> {
    const organizationInvite = await this.organizationInviteRepository.findOne({
      relations: {
        role: true,
      },
      where: {
        organizationId,
        inviteCode,
        validationCode,
      },
    });

    if (organizationInvite === null) {
      throw new NotFoundException('Invitation not found');
    }

    return this.classMapper.map(
      organizationInvite,
      OrganizationInvite,
      ReadPublicOrganizationInvite,
    );
  }

  async cancel(
    organizationId: string,
    id: string,
  ): Promise<SuccessfulMessageResponseDto> {
    const organizationInvite =
      await this.organizationInviteRepository.findOneBy({
        organizationId,
        id,
      });

    if (
      !this.canOrganizationInviteBeUpdated(
        organizationInvite,
        ORGANIZATION_INVITE_STATUS.REVOKED,
      )
    ) {
      throw new BadRequestException(
        `Organization invite with status ${organizationInvite.status} cannot be revoked`,
      );
    }

    await this.updateOrganizationInvite(id, organizationId, {
      status: ORGANIZATION_INVITE_STATUS.REVOKED,
    });

    return {
      message: `Organization invite email ${organizationInvite.email} canceled`,
    };
  }

  async resend(
    organizationId: string,
    id: string,
  ): Promise<OrganizationInvite> {
    const organizationInvite =
      await this.organizationInviteRepository.findOneBy({
        organizationId,
        id,
      });

    if (
      !this.canOrganizationInviteBeUpdated(
        organizationInvite,
        ORGANIZATION_INVITE_STATUS.PENDING,
      )
    ) {
      throw new BadRequestException(
        `Organization invite with status ${organizationInvite.status} cannot be resent`,
      );
    }

    const updateOrganizationInviteParams = {
      status: ORGANIZATION_INVITE_STATUS.PENDING,
      inviteCode: this.generateInviteCode(),
      validationCode: this.generateValidationCode(),
    };

    await this.updateOrganizationInvite(
      id,
      organizationId,
      updateOrganizationInviteParams,
    );

    return {
      ...organizationInvite,
      ...updateOrganizationInviteParams,
    };
  }

  async inviteEmailUsers(
    organizationId: string,
    usersToEmailInvite: string[],
    dto: CreateMultipleUserWorkspaceDto,
    queryRunner: QueryRunner,
  ): Promise<CreateOrganizationInviteWithIdDto[]> {
    if (usersToEmailInvite.length == 0) return [];

    const organizationInvitesEntities = this.mapUsersToOrganizationInvites(
      organizationId,
      usersToEmailInvite,
      dto.roleId,
      dto.inviterId,
    );

    const organizationInvites = await this.bulkInsertUsersToOrganizationInvites(
      queryRunner,
      organizationInvitesEntities,
    );

    const workspaceOrganizationInvitesEntities =
      this.mapOrganizationInvitesToWorkspaces(
        organizationInvites,
        dto.workspaces,
      );

    await this.bulkInserWorkspaceOrganizationInvites(
      queryRunner,
      workspaceOrganizationInvitesEntities,
    );

    return organizationInvites;
  }

  async bulkInsertUsersToOrganizationInvites(
    queryRunner: QueryRunner,
    organizationInvites: CreateOrganizationInviteWithIdDto[],
  ): Promise<CreateOrganizationInviteWithIdDto[]> {
    const insertResult = await this.organizationInviteRepository
      .createQueryBuilder('organizationInvite', queryRunner)
      .insert()
      .into(OrganizationInvite)
      .values(organizationInvites)
      .orIgnore()
      .execute();

    // return only the organization invites that were added (not the ignored ones)
    return organizationInvites.filter((orgInvite) =>
      insertResult.identifiers.some(
        (insertedId) => insertedId.id === orgInvite.id,
      ),
    );
  }

  async bulkInserWorkspaceOrganizationInvites(
    queryRunner: QueryRunner,
    workspaceOrganizationInvites: WorkspaceOrganizationInvite[],
  ) {
    await this.workspaceOrganizationInviteRepository
      .createQueryBuilder('workspaceOrganizationInvite', queryRunner)
      .insert()
      .into(WorkspaceOrganizationInvite)
      .values(workspaceOrganizationInvites)
      .orIgnore()
      .execute();
  }

  mapUsersToOrganizationInvites(
    organizationId: string,
    userEmails: string[],
    roleId: number,
    inviterId: number,
  ): CreateOrganizationInviteWithIdDto[] {
    return userEmails.map((email: string) => {
      return {
        id: randomUUID(),
        organizationId,
        email,
        roleId,
        inviteCode: this.generateInviteCode(),
        validationCode: this.generateValidationCode(),
        inviterId,
        status: ORGANIZATION_INVITE_STATUS.PENDING,
        dateCreated: new Date(Date.now()),
        lastUpdated: new Date(Date.now()),
      };
    });
  }

  mapOrganizationInvitesToWorkspaces(
    organizationInvites: CreateOrganizationInviteWithIdDto[],
    workspaceIds: number[],
  ): WorkspaceOrganizationInvite[] {
    const workspaceOrgInvites: WorkspaceOrganizationInvite[] = [];

    organizationInvites.forEach((organizationInvite) => {
      workspaceIds.forEach((workspaceId) => {
        workspaceOrgInvites.push({
          organizationInviteId: organizationInvite.id,
          workspaceId,
          dateCreated: new Date(Date.now()),
        });
      });
    });

    return workspaceOrgInvites;
  }

  private canOrganizationInviteBeUpdated(
    organizationInvite: OrganizationInvite,
    newStatus?: ORGANIZATION_INVITE_STATUS,
  ): boolean {
    if (!organizationInvite) {
      throw new NotFoundException('Organization invite could not be found');
    }

    switch (newStatus) {
      case ORGANIZATION_INVITE_STATUS.REVOKED:
        return this.canOrganizationInviteBeRevokedOrAccepted(
          organizationInvite,
        );
      case ORGANIZATION_INVITE_STATUS.PENDING:
        return this.canOrganizationInviteBeResent(organizationInvite);
      case ORGANIZATION_INVITE_STATUS.ACCEPTED:
        return this.canOrganizationInviteBeRevokedOrAccepted(
          organizationInvite,
        );
      default:
        return false;
    }
  }

  private canOrganizationInviteBeResent(
    organizationInvite: OrganizationInvite,
  ): boolean {
    this.validateInviteRateLimitThreshold(organizationInvite);

    return organizationInvite.status !== ORGANIZATION_INVITE_STATUS.ACCEPTED;
  }

  private validateInviteRateLimitThreshold(
    organizationInvite: OrganizationInvite,
  ) {
    const timeNextResetIsAllowed = new Date(
      organizationInvite.lastUpdated.getTime() + this.RESET_THRESHOLD_TIME,
    );

    if (Date.now() < timeNextResetIsAllowed.getTime()) {
      throw new HttpException(
        'Invite resend rate limit exceeded, wait to resent the invite again',
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }
  }

  private canOrganizationInviteBeRevokedOrAccepted(
    organizationInvite: OrganizationInvite,
  ): boolean {
    return (
      organizationInvite.status !== ORGANIZATION_INVITE_STATUS.REVOKED &&
      organizationInvite.status !== ORGANIZATION_INVITE_STATUS.ACCEPTED
    );
  }

  private async updateOrganizationInvite(
    id: string,
    organizationId: string,
    updateParams: object,
  ) {
    return await this.organizationInviteRepository.update(
      {
        id,
        organizationId,
      },
      {
        ...updateParams,
        lastUpdated: new Date(Date.now()),
      },
    );
  }

  private generateInviteCode() {
    return this.generateCode(this.INVITE_CODE_LENGTH);
  }

  private generateValidationCode() {
    return this.generateCode(this.VALIDATION_CODE_LENGTH);
  }

  // TODO: check collisions with multiple user's invitation at the same time
  // maybe generates a mixin of random code + hashing (user's email and organization id)?
  private generateCode(codeLength: number) {
    let code = '';
    const charactersLength = this.CHARACTERS.length;
    for (let index = 0; index < codeLength; index++) {
      code += this.CHARACTERS.charAt(
        Math.floor(Math.random() * charactersLength),
      );
    }

    return code;
  }
}
