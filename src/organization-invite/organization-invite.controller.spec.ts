import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationInviteController } from './organization-invite.controller';
import { OrganizationInviteService } from './organization-invite.service';
import { CreateMultipleUserWorkspaceDto } from './dto/create-multiple-user-workspace.dto';
import { RoleService } from 'src/role/role.service';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { OrganizationUserQueryService } from 'src/organizations/organization-user/organization-user-query.service';
import { Mapper, createMapper } from '@automapper/core';
import { OrganizationInviteProfile } from './profile/organization-invite.profile';
import { getMapperToken } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadOrganizationInvitesDto } from './dto/read-organization-invites.dto';
import { OrganizationInvite } from './entities/organization-invite.entity';
import { User } from 'src/entities/user.entity';
import { Role } from 'src/entities/role.entity';
import { UpdateOrganizationInviteDto } from './dto/update-organization-invite.dto';
import { Organization } from 'src/organizations/entities/organization.entity';
import { ReadPublicOrganizationInvite } from './dto/read-public-organization-invite.dto';
import { ORGANIZATION_INVITE_STATUS } from 'src/common/constants/constants';
import { WorkspaceUserService } from 'src/workspace-user/workspace-user.service';
import { CreateMultipleUserSingleWorkspaceDto } from './dto/create-multiple-user-single-workspace.dto';

const TEST_ORG_ID = '1754c0a9-c620-4d34-8243-9c8b49cbcaa6';
const TEST_WORKSPACE_ID = 123;

const createMultipleUserWorkspaceDto: CreateMultipleUserWorkspaceDto = {
  users: ['<EMAIL>', '<EMAIL>'],
  roleId: 7,
  inviterId: 1,
  workspaces: [123, 456],
};

const organizationInvite: OrganizationInvite = {
  id: 'xxxx',
  organizationId: TEST_ORG_ID,
  email: '<EMAIL>',
  roleId: 5,
  inviteCode: 'xx',
  validationCode: 'yy',
  status: 'PENDING',
  inviterId: 1234,
  dateCreated: new Date(Date.now()),
  lastUpdated: new Date(Date.now()),
  invitedBy: new User(),
  role: new Role(),
};

const organization = {
  name: 'Test org',
  id: TEST_ORG_ID,
} as Organization;

const inviter = {
  id: 5678,
} as User;

const updateOrganizationInvite: UpdateOrganizationInviteDto = {
  inviterId: inviter.id,
};

describe('OrganizationInviteController', () => {
  let mapper: Mapper;
  let controller: OrganizationInviteController;
  let organizationInviteService: OrganizationInviteService;
  let organizationUserQueryService: OrganizationUserQueryService;
  let workspaceUserService: WorkspaceUserService;
  let roleService: RoleService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationInviteProfile,
        {
          provide: OrganizationInviteService,
          useValue: {
            create: jest.fn(),
            getPendingInvites: jest.fn(),
            getPendingInvitesEmailsByEmails: jest.fn(),
            getInviteByInviteCode: jest.fn(),
            getInviteCodeByIdAndEmail: jest.fn(),
            cancel: jest.fn(),
            resend: jest.fn(),
            acceptOrganizationInvite: jest.fn(),
            getInviteByIdAndOrganizationId: jest.fn(),
          },
        },
        {
          provide: OrganizationUserQueryService,
          useValue: {
            areWorkspacesBelongsToOrganization: jest.fn(),
            findOrganization: jest.fn(),
            findUser: jest.fn(),
            findInactivePeopleByEmail: jest.fn(),
            isUserOrganizationAdmin: jest.fn(),
          },
        },
        {
          provide: RoleService,
          useValue: {
            isRoleValidWorkspaceRole: jest.fn(),
            isRoleIdWorkspaceStandardRole: jest.fn(),
          },
        },
        {
          provide: EmailTemplateService,
          useValue: {
            dispatchOrganizationInvites: jest.fn(),
          },
        },
        {
          provide: WorkspaceUserService,
          useValue: {
            userCanUpdateWithRoleId: jest.fn(),
            userHasAccessToAllWorkspaces: jest.fn(),
          },
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
      controllers: [OrganizationInviteController],
    }).compile();

    controller = module.get<OrganizationInviteController>(
      OrganizationInviteController,
    );

    organizationInviteService = module.get<OrganizationInviteService>(
      OrganizationInviteService,
    );

    organizationUserQueryService = module.get<OrganizationUserQueryService>(
      OrganizationUserQueryService,
    );

    roleService = module.get<RoleService>(RoleService);
    workspaceUserService =
      module.get<WorkspaceUserService>(WorkspaceUserService);

    const organizationInviteProfile: OrganizationInviteProfile =
      module.get<OrganizationInviteProfile>(OrganizationInviteProfile);
    mapper = module.get<Mapper>(getMapperToken());
    organizationInviteProfile.profile(mapper);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('bulkInviteUsersToWorkspaces', () => {
    describe('as organization admin', () => {
      beforeEach(() => {
        jest
          .spyOn(organizationUserQueryService, 'isUserOrganizationAdmin')
          .mockResolvedValueOnce(true);
      });

      it('should bulk invite users to multiple workspaces successfully', async () => {
        jest.spyOn(organizationInviteService, 'create').mockResolvedValue([]);

        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(true);

        jest
          .spyOn(roleService, 'isRoleValidWorkspaceRole')
          .mockResolvedValueOnce(true);
        jest
          .spyOn(organizationUserQueryService, 'findInactivePeopleByEmail')
          .mockResolvedValueOnce([]);
        jest
          .spyOn(organizationInviteService, 'getPendingInvitesEmailsByEmails')
          .mockResolvedValueOnce([]);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserWorkspaceDto,
          ),
        ).resolves.toEqual({
          message: 'Successfully assigned user(s) to 2 workspace(s)',
          inactiveEmails: [],
          pendingInviteEmails: [],
        });
      });

      it('should bulk invite users to single workspace successfully', async () => {});

      it('should bulk invite users partially successfully', async () => {
        const partialInviteRequestDto = {
          ...createMultipleUserWorkspaceDto,
          users: [
            ...createMultipleUserWorkspaceDto.users,
            '<EMAIL>',
          ],
        };
        jest.spyOn(organizationInviteService, 'create').mockResolvedValue([]);

        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(true);

        jest
          .spyOn(roleService, 'isRoleValidWorkspaceRole')
          .mockResolvedValueOnce(true);
        jest
          .spyOn(organizationUserQueryService, 'findInactivePeopleByEmail')
          .mockResolvedValueOnce([partialInviteRequestDto.users[0]]);
        jest
          .spyOn(organizationInviteService, 'getPendingInvitesEmailsByEmails')
          .mockResolvedValueOnce([partialInviteRequestDto.users[1]]);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            partialInviteRequestDto,
          ),
        ).resolves.toEqual({
          message: 'Successfully assigned user(s) to 2 workspace(s)',
          inactiveEmails: [partialInviteRequestDto.users[0]],
          pendingInviteEmails: [partialInviteRequestDto.users[1]],
        });
      });

      it('should throw invalid workspace error', async () => {
        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(false);

        jest
          .spyOn(organizationUserQueryService, 'isUserOrganizationAdmin')
          .mockResolvedValueOnce(true);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserWorkspaceDto,
          ),
        ).rejects.toThrow(
          new BadRequestException(
            `One or more workspaces in ${createMultipleUserWorkspaceDto.workspaces} doesn't belong to the organization ${TEST_ORG_ID}`,
          ),
        );
      });

      it('should throw invalid role error', async () => {
        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(true);
        jest
          .spyOn(roleService, 'isRoleValidWorkspaceRole')
          .mockResolvedValueOnce(false);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserWorkspaceDto,
          ),
        ).rejects.toThrow(
          new BadRequestException(
            `Role ${createMultipleUserWorkspaceDto.roleId} is not a valid workspace role`,
          ),
        );
      });

      it('should throw no valid user selected', async () => {
        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(true);
        jest
          .spyOn(roleService, 'isRoleValidWorkspaceRole')
          .mockResolvedValueOnce(true);
        jest
          .spyOn(organizationUserQueryService, 'findInactivePeopleByEmail')
          .mockResolvedValueOnce([
            createMultipleUserWorkspaceDto.users[0],
            createMultipleUserWorkspaceDto.users[1],
          ]);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserWorkspaceDto,
          ),
        ).rejects.toThrow(
          new BadRequestException({
            inactiveEmails: createMultipleUserWorkspaceDto.users,
            pendingInviteEmails: [],
          }),
        );
      });
    });

    describe('as organization standard', () => {
      const createMultipleUserSingleWorkspaceDto = {
        ...createMultipleUserWorkspaceDto,
        workspaces: [123],
      };

      beforeEach(() => {
        jest
          .spyOn(organizationUserQueryService, 'isUserOrganizationAdmin')
          .mockResolvedValueOnce(false);
      });

      it('should bulk invite users to multiple workspaces successfully', async () => {
        jest.spyOn(organizationInviteService, 'create').mockResolvedValue([]);

        jest
          .spyOn(roleService, 'isRoleIdWorkspaceStandardRole')
          .mockResolvedValueOnce(true);

        jest
          .spyOn(workspaceUserService, 'userHasAccessToAllWorkspaces')
          .mockResolvedValueOnce(true);
        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(true);

        jest
          .spyOn(roleService, 'isRoleValidWorkspaceRole')
          .mockResolvedValueOnce(true);
        jest
          .spyOn(organizationUserQueryService, 'findInactivePeopleByEmail')
          .mockResolvedValueOnce([]);
        jest
          .spyOn(organizationInviteService, 'getPendingInvitesEmailsByEmails')
          .mockResolvedValueOnce([]);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserWorkspaceDto,
          ),
        ).resolves.toEqual({
          message: 'Successfully assigned user(s) to 2 workspace(s)',
          inactiveEmails: [],
          pendingInviteEmails: [],
        });
      });

      it('should throw error if are inviting to multiple workspaces and is inviting with a role different than workspace standard', async () => {
        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(true);

        jest
          .spyOn(roleService, 'isRoleValidWorkspaceRole')
          .mockResolvedValueOnce(true);

        jest
          .spyOn(roleService, 'isRoleIdWorkspaceStandardRole')
          .mockResolvedValueOnce(false);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserWorkspaceDto,
          ),
        ).rejects.toThrow(
          new BadRequestException(
            `User cannot invite users with role ${createMultipleUserWorkspaceDto.roleId} to multiple workspaces`,
          ),
        );
      });

      it('should throw error if user is not org admin and is inviting with to workspaces they do not have access to', async () => {
        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(true);

        jest
          .spyOn(roleService, 'isRoleValidWorkspaceRole')
          .mockResolvedValueOnce(true);

        jest
          .spyOn(roleService, 'isRoleIdWorkspaceStandardRole')
          .mockResolvedValueOnce(true);

        jest
          .spyOn(workspaceUserService, 'userHasAccessToAllWorkspaces')
          .mockResolvedValueOnce(false);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserWorkspaceDto,
          ),
        ).rejects.toThrow(
          new BadRequestException(
            `User doesn't have access to some or all of these workspaces: ${createMultipleUserWorkspaceDto.workspaces.join(
              ', ',
            )}`,
          ),
        );
      });

      it('should throw user does not have permission to invite someone with higher role', async () => {
        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(true);
        jest
          .spyOn(roleService, 'isRoleValidWorkspaceRole')
          .mockResolvedValueOnce(true);
        jest
          .spyOn(workspaceUserService, 'userCanUpdateWithRoleId')
          .mockResolvedValueOnce(false);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserSingleWorkspaceDto,
          ),
        ).rejects.toThrow(
          new BadRequestException(
            `User cannot invite with role ${createMultipleUserSingleWorkspaceDto.roleId} in this workspace`,
          ),
        );
      });

      it('should throw invalid workspace error', async () => {
        jest
          .spyOn(workspaceUserService, 'userCanUpdateWithRoleId')
          .mockResolvedValueOnce(true);
        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(false);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserSingleWorkspaceDto,
          ),
        ).rejects.toThrow(
          new BadRequestException(
            `One or more workspaces in ${createMultipleUserSingleWorkspaceDto.workspaces[0]} doesn't belong to the organization ${TEST_ORG_ID}`,
          ),
        );
      });

      it('should throw invalid role error', async () => {
        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(true);
        jest
          .spyOn(roleService, 'isRoleValidWorkspaceRole')
          .mockResolvedValueOnce(false);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserSingleWorkspaceDto,
          ),
        ).rejects.toThrow(
          new BadRequestException(
            `Role ${createMultipleUserWorkspaceDto.roleId} is not a valid workspace role`,
          ),
        );
      });

      it('should throw no valid user selected', async () => {
        jest
          .spyOn(
            organizationUserQueryService,
            'areWorkspacesBelongsToOrganization',
          )
          .mockResolvedValueOnce(true);
        jest
          .spyOn(roleService, 'isRoleValidWorkspaceRole')
          .mockResolvedValueOnce(true);
        jest
          .spyOn(workspaceUserService, 'userCanUpdateWithRoleId')
          .mockResolvedValueOnce(true);
        jest
          .spyOn(organizationUserQueryService, 'findInactivePeopleByEmail')
          .mockResolvedValueOnce([
            createMultipleUserWorkspaceDto.users[0],
            createMultipleUserWorkspaceDto.users[1],
          ]);

        await expect(
          controller.bulkInviteUsersToWorkspaces(
            TEST_ORG_ID,
            createMultipleUserSingleWorkspaceDto,
          ),
        ).rejects.toThrow(
          new BadRequestException({
            inactiveEmails: createMultipleUserWorkspaceDto.users,
            pendingInviteEmails: [],
          }),
        );
      });
    });
  });

  describe('getPendingInvites', () => {
    const result: PaginatedResultArray<ReadOrganizationInvitesDto> = {
      items: [
        {
          id: '4eeeb152-2576-4ca7-bef3-22101cbf3d47',
          userEmail: '<EMAIL>',
          role: {
            id: 7,
            name: 'Admin',
            type: 'business_entity',
            identifier: 'ADMIN',
            description:
              'Can manage this workspace, its members, and access apps.',
          },
          status: 'PENDING',
          lastUpdated: new Date(Date.now()),
          invitedBy: {
            id: 19949,
            username: '<EMAIL>',
            firstName: 'Test',
            lastName: 'PM',
            displayName: 'Test PM',
            email: '<EMAIL>',
            jobTitle: null,
            photo:
              'https://vidmob-storage-dev.s3.amazonaws.com/SJ4MEBCURZ/avatar/avatar1.jpeg',
          },
        },
        {
          id: '7626a20e-18cd-4658-9114-58dd538bd87f',
          userEmail: '<EMAIL>',
          role: {
            id: 7,
            name: 'Admin',
            type: 'business_entity',
            identifier: 'ADMIN',
            description:
              'Can manage this workspace, its members, and access apps.',
          },
          status: 'PENDING',
          lastUpdated: new Date(Date.now()),
          invitedBy: {
            id: 19949,
            username: '<EMAIL>',
            firstName: 'Test',
            lastName: 'PM',
            displayName: 'Test PM',
            email: '<EMAIL>',
            jobTitle: null,
            photo:
              'https://vidmob-storage-dev.s3.amazonaws.com/SJ4MEBCURZ/avatar/avatar1.jpeg',
          },
        },
        {
          id: '23994401-a5b4-47ed-acb5-3625840504a0',
          userEmail: '<EMAIL>',
          role: {
            id: 7,
            name: 'Admin',
            type: 'business_entity',
            identifier: 'ADMIN',
            description:
              'Can manage this workspace, its members, and access apps.',
          },
          status: 'PENDING',
          lastUpdated: new Date(Date.now()),
          invitedBy: {
            id: 19949,
            username: '<EMAIL>',
            firstName: 'Test',
            lastName: 'PM',
            displayName: 'Test PM',
            email: '<EMAIL>',
            jobTitle: null,
            photo:
              'https://vidmob-storage-dev.s3.amazonaws.com/SJ4MEBCURZ/avatar/avatar1.jpeg',
          },
        },
        {
          id: 'b4a80c44-8116-49ac-8086-d345e3aeba11',
          userEmail: '<EMAIL>',
          role: {
            id: 7,
            name: 'Admin',
            type: 'business_entity',
            identifier: 'ADMIN',
            description:
              'Can manage this workspace, its members, and access apps.',
          },
          status: 'PENDING',
          lastUpdated: new Date(Date.now()),
          invitedBy: {
            id: 19949,
            username: '<EMAIL>',
            firstName: 'Test',
            lastName: 'PM',
            displayName: 'Test PM',
            email: '<EMAIL>',
            jobTitle: null,
            photo:
              'https://vidmob-storage-dev.s3.amazonaws.com/SJ4MEBCURZ/avatar/avatar1.jpeg',
          },
        },
      ],
      totalCount: 4,
    };

    beforeEach(() => {
      jest
        .spyOn(organizationInviteService, 'getPendingInvites')
        .mockResolvedValue(result);
    });

    afterEach(() => jest.restoreAllMocks());

    it('should return pending invites', async () => {
      const response = await controller.getPendingInvites(TEST_ORG_ID, {}, {});

      expect(response).toBe(result);
    });
  });

  describe('getOrganizationInviteById', () => {
    it('should return the organization invite', async () => {
      jest
        .spyOn(organizationInviteService, 'getInviteByIdAndOrganizationId')
        .mockResolvedValue(organizationInvite);

      const response = {
        id: organizationInvite.id,
        userEmail: organizationInvite.email,
        lastUpdated: organizationInvite.lastUpdated,
        role: organizationInvite.role,
        status: organizationInvite.status,
        invitedBy: {
          displayName: organizationInvite.invitedBy.displayName,
          email: organizationInvite.invitedBy.email,
          firstName: organizationInvite.invitedBy.firstName,
          id: organizationInvite.invitedBy.id,
          jobTitle: organizationInvite.invitedBy.displayName,
          lastName: organizationInvite.invitedBy.lastName,
          username: organizationInvite.invitedBy.username,
          photo: organizationInvite.invitedBy.photo,
        },
      };

      await expect(
        controller.getOrganizationInviteById(TEST_ORG_ID, 'xxx'),
      ).resolves.toEqual(response);
    });

    it('should throw not found exception', async () => {
      jest
        .spyOn(organizationInviteService, 'getInviteByIdAndOrganizationId')
        .mockResolvedValue(null);

      await expect(
        controller.getOrganizationInviteById(TEST_ORG_ID, 'xxx'),
      ).rejects.toThrow(new NotFoundException('Invite could not be found'));
    });
  });

  describe('getOrganizationInviteByInviteCodeAndValidationCode', () => {
    afterEach(() => jest.restoreAllMocks());

    const result: ReadPublicOrganizationInvite = {
      id: 'yyyy',
      email: '<EMAIL>',
      organizationId: 'xxxxx',
      status: ORGANIZATION_INVITE_STATUS.PENDING,
      role: new Role(),
    };

    it('should return the organization invite', async () => {
      jest
        .spyOn(organizationInviteService, 'getInviteByInviteCode')
        .mockResolvedValue(result);

      await expect(
        controller.getOrganizationInviteByInviteCodeAndValidationCode(
          TEST_ORG_ID,
          'xxxxx',
          'www',
        ),
      ).resolves.toStrictEqual(result);
    });

    it('should throw an error if validation code is empty or null', async () => {
      controller
        .getOrganizationInviteByInviteCodeAndValidationCode(
          TEST_ORG_ID,
          'xxxxx',
          null,
        )
        .catch((error) => expect(error).toBeInstanceOf(BadRequestException));
    });

    it('should throw an error if organization invite is not found', async () => {
      jest
        .spyOn(organizationInviteService, 'getInviteByInviteCode')
        .mockRejectedValue(
          new NotFoundException('Invitation expired or not found'),
        );

      controller
        .getOrganizationInviteByInviteCodeAndValidationCode(
          TEST_ORG_ID,
          'xxxxx',
          null,
        )
        .catch((error) => expect(error).toBeInstanceOf(NotFoundException));
    });
  });

  describe('cancelOrganizationInvite', () => {
    afterEach(() => jest.restoreAllMocks());

    it('should cancel an organization invite', async () => {
      jest.spyOn(organizationInviteService, 'cancel').mockResolvedValue({
        message: 'Organization <NAME_EMAIL> canceled',
      });

      await expect(
        controller.cancelOrganizationInvite(TEST_ORG_ID, 'xxx'),
      ).resolves.toStrictEqual({
        message: 'Organization <NAME_EMAIL> canceled',
      });
    });

    it('should thrown an error if organization invite is not found', async () => {
      jest
        .spyOn(organizationInviteService, 'cancel')
        .mockResolvedValue(
          new NotFoundException('Organization invite could not be found'),
        );
      controller
        .cancelOrganizationInvite(TEST_ORG_ID, 'xxx')
        .catch((error) => expect(error).toBeInstanceOf(NotFoundException));
    });
  });

  describe('resendOrganizationInvite', () => {
    it('should resend organization invites', async () => {
      jest
        .spyOn(organizationInviteService, 'resend')
        .mockResolvedValue(organizationInvite);

      jest
        .spyOn(organizationUserQueryService, 'findOrganization')
        .mockResolvedValue(organization);

      jest
        .spyOn(organizationUserQueryService, 'findUser')
        .mockResolvedValue(inviter);

      await expect(
        controller.resendOrganizationInvite(TEST_ORG_ID, 'xxxx'),
      ).resolves.toEqual({
        message: 'Organization invite email resent',
      });
    });
  });

  describe('acceptOrganizationInvite', () => {
    const result = {
      message: 'Organization invitation accepted successfully',
    };

    beforeEach(() => {
      jest
        .spyOn(organizationUserQueryService, 'findUser')
        .mockResolvedValue(new User());
    });

    afterEach(() => jest.restoreAllMocks());

    it('should accept invitation', async () => {
      jest
        .spyOn(organizationInviteService, 'getInviteCodeByIdAndEmail')
        .mockResolvedValue(organizationInvite);
      jest
        .spyOn(organizationUserQueryService, 'findOrganization')
        .mockResolvedValue(organization);
      jest
        .spyOn(organizationInviteService, 'acceptOrganizationInvite')
        .mockResolvedValue(result);

      await expect(
        controller.acceptOrganizationInvite(
          TEST_ORG_ID,
          organizationInvite.id,
          { inviteeId: 1234 },
        ),
      ).resolves.toEqual(result);
    });

    it('should not accept invite if organization invite could not be found for user email and organization invite id', async () => {
      jest
        .spyOn(organizationInviteService, 'getInviteCodeByIdAndEmail')
        .mockResolvedValue(null);

      controller
        .acceptOrganizationInvite('zzzz', organizationInvite.id, {
          inviteeId: 1234,
        })
        .catch((error) => expect(error).toBeInstanceOf(NotFoundException));
    });

    it('should not accept invite if organization id and invite organization id mismatch', async () => {
      jest
        .spyOn(organizationInviteService, 'getInviteCodeByIdAndEmail')
        .mockResolvedValue(organizationInvite);
      jest
        .spyOn(organizationUserQueryService, 'findOrganization')
        .mockResolvedValue({ ...organization, id: 'zzzz' });

      controller
        .acceptOrganizationInvite('zzzz', organizationInvite.id, {
          inviteeId: 1234,
        })
        .catch((error) => expect(error).toBeInstanceOf(BadRequestException));
    });
  });
});
