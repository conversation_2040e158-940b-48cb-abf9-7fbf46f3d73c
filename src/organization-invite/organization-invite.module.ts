import { Module } from '@nestjs/common';
import { OrganizationInviteController } from './organization-invite.controller';
import { OrganizationInviteService } from './organization-invite.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrganizationInvite } from './entities/organization-invite.entity';
import { WorkspaceOrganizationInvite } from './entities/workspace-organization-invite.entity';
import { OrganizationInviteProfile } from './profile/organization-invite.profile';
import { OrganizationUserModule } from 'src/organizations/organization-user/organization-user.module';
import { WorkspaceUserModule } from 'src/workspace-user/workspace-user.module';
import { RoleModule } from 'src/role/role.module';
import { EmailTemplateModule } from 'src/email-template/email-template.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([OrganizationInvite, WorkspaceOrganizationInvite]),
    OrganizationUserModule,
    WorkspaceUserModule,
    RoleModule,
    EmailTemplateModule,
  ],
  controllers: [OrganizationInviteController],
  providers: [OrganizationInviteService, OrganizationInviteProfile],
})
export class OrganizationInviteModule {}
