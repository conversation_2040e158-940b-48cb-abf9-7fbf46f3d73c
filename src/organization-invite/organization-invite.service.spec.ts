import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationInviteService } from './organization-invite.service';
import { WorkspaceUserService } from 'src/workspace-user/workspace-user.service';
import { OrganizationInvite } from './entities/organization-invite.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { QueryRunner, Repository, UpdateResult } from 'typeorm';
import { WorkspaceOrganizationInvite } from './entities/workspace-organization-invite.entity';
import { getMapperToken } from '@automapper/nestjs';
import { Mapper, createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { OrganizationUserQueryService } from 'src/organizations/organization-user/organization-user-query.service';
import { CreateMultipleUserWorkspaceDto } from './dto/create-multiple-user-workspace.dto';
import { User } from 'src/entities/user.entity';
import * as crypto from 'crypto';
import { CreateOrganizationInviteWithIdDto } from './dto/create-organization-invite-with-id.dto';
import { OrganizationInviteProfile } from './profile/organization-invite.profile';
import { Role } from 'src/entities/role.entity';
import { ORGANIZATION_INVITE_STATUS } from 'src/common/constants/constants';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { UpdateOrganizationInviteDto } from './dto/update-organization-invite.dto';
import { RoleService } from 'src/role/role.service';
import { Organization } from 'src/organizations/entities/organization.entity';

const createQueryBuilder: any = {
  insert: () => createQueryBuilder,
  into: () => createQueryBuilder,
  values: () => createQueryBuilder,
  orIgnore: () => createQueryBuilder,
  execute: () => createQueryBuilder,
  innerJoinAndSelect: () => createQueryBuilder,
  where: () => createQueryBuilder,
  andWhere: () => createQueryBuilder,
  skip: () => createQueryBuilder,
  take: () => createQueryBuilder,
  orderBy: () => createQueryBuilder,
  getManyAndCount: () => createQueryBuilder,
};

const mockedQueryRunner: Partial<QueryRunner> = {
  connect: jest.fn(),
  startTransaction: jest.fn(),
  commitTransaction: jest.fn(),
  rollbackTransaction: jest.fn(),
  release: jest.fn(),
};

const TEST_ORG_ID = '1754c0a9-c620-4d34-8243-9c8b49cbcaa6';

const createMultipleUserWorkspaceDto: CreateMultipleUserWorkspaceDto = {
  users: ['<EMAIL>', '<EMAIL>'],
  workspaces: [123, 456],
  roleId: 7,
  inviterId: 1,
};

const organizationInvite: OrganizationInvite = {
  id: 'xxxx',
  organizationId: TEST_ORG_ID,
  email: '<EMAIL>',
  roleId: 5,
  inviteCode: 'xx',
  validationCode: 'yy',
  status: 'PENDING',
  inviterId: 1234,
  dateCreated: new Date(Date.now() - 60 * 5 * 1000),
  lastUpdated: new Date(Date.now() - 60 * 5 * 1000),
  invitedBy: new User(),
  role: new Role(),
};

describe('OrganizationInviteService', () => {
  let service: OrganizationInviteService;
  let workspaceUserService: WorkspaceUserService;
  let organizationUserQueryService: OrganizationUserQueryService;
  let roleService: RoleService;
  let organizationInviteRepository: Repository<OrganizationInvite>;
  let workspaceOrganizationInviteRepository: Repository<WorkspaceOrganizationInvite>;
  let mapper: Mapper;

  beforeEach(async () => {
    jest.mock('crypto');

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationInviteService,
        OrganizationInviteProfile,
        {
          provide: getRepositoryToken(OrganizationInvite),
          useValue: {
            queryRunner: mockedQueryRunner,
            createQueryBuilder: jest.fn(),
            update: jest.fn(),
            findOneBy: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(WorkspaceOrganizationInvite),
          useValue: {
            createQueryBuilder: jest.fn(),
            findBy: jest.fn(),
          },
        },
        {
          provide: WorkspaceUserService,
          useValue: {
            addUsersToWorkspaces: jest.fn(),
            addUserToWorkspaces: jest.fn(),
          },
        },
        {
          provide: OrganizationUserQueryService,
          useValue: {
            findOrganizationUserByEmails: jest.fn(),
            createOrganizationUserRoleTransactional: jest.fn(),
            createUserOrganizationMapTransactional: jest.fn(),
          },
        },
        {
          provide: RoleService,
          useValue: {
            findOrganizationRoleByIdentifier: jest.fn(),
          },
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    service = module.get<OrganizationInviteService>(OrganizationInviteService);
    workspaceUserService =
      module.get<WorkspaceUserService>(WorkspaceUserService);
    organizationUserQueryService = module.get<OrganizationUserQueryService>(
      OrganizationUserQueryService,
    );
    roleService = module.get<RoleService>(RoleService);
    organizationInviteRepository = module.get<Repository<OrganizationInvite>>(
      getRepositoryToken(OrganizationInvite),
    );
    workspaceOrganizationInviteRepository = module.get<
      Repository<WorkspaceOrganizationInvite>
    >(getRepositoryToken(WorkspaceOrganizationInvite));

    const organizationInviteProfile: OrganizationInviteProfile =
      module.get<OrganizationInviteProfile>(OrganizationInviteProfile);
    mapper = module.get<Mapper>(getMapperToken());
    organizationInviteProfile.profile(mapper);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    let workspaceUserServiceSpy: jest.SpyInstance;
    let executeSpy: jest.SpyInstance;
    let commitTransactionSpy: jest.SpyInstance;
    let rollbackTransactionSpy: jest.SpyInstance;
    let releaseSpy: jest.SpyInstance;

    beforeEach(() => {
      jest.restoreAllMocks();

      jest
        .spyOn(organizationInviteRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);
      jest
        .spyOn(workspaceOrganizationInviteRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      const uuid1 = 'xxxx' as crypto.UUID;
      const uuid2 = 'yyyyy' as crypto.UUID;

      jest.spyOn(crypto, 'randomUUID').mockImplementationOnce(() => uuid1);

      jest.spyOn(crypto, 'randomUUID').mockImplementationOnce(() => uuid2);

      executeSpy = jest
        .spyOn(createQueryBuilder, 'execute')
        .mockImplementationOnce(() => {
          return {
            identifiers: [{ id: uuid1 }, { id: uuid2 }],
          };
        });

      commitTransactionSpy = jest.spyOn(mockedQueryRunner, 'commitTransaction');
      rollbackTransactionSpy = jest.spyOn(
        mockedQueryRunner,
        'rollbackTransaction',
      );
      releaseSpy = jest.spyOn(mockedQueryRunner, 'release');
      workspaceUserServiceSpy = jest.spyOn(
        workspaceUserService,
        'addUsersToWorkspaces',
      );
    });

    it('should invite two users email to organization and adds no one directly to workspaces', async () => {
      jest
        .spyOn(organizationUserQueryService, 'findOrganizationUserByEmails')
        .mockResolvedValueOnce([]);

      const response = await service.create(
        TEST_ORG_ID,
        createMultipleUserWorkspaceDto,
      );

      expect(response.length).toBe(2);
      expect(workspaceUserServiceSpy).toBeCalledWith(
        [],
        createMultipleUserWorkspaceDto.workspaces,
        createMultipleUserWorkspaceDto.roleId,
        mockedQueryRunner,
      );
      expect(executeSpy).toBeCalledTimes(2);
      expect(rollbackTransactionSpy).not.toBeCalled();
      expect(commitTransactionSpy).toBeCalled();
      expect(releaseSpy).toBeCalled();
    });

    it('should invite one email to organization and adds one user to workspaces', async () => {
      jest
        .spyOn(organizationUserQueryService, 'findOrganizationUserByEmails')
        .mockResolvedValueOnce([
          {
            email: createMultipleUserWorkspaceDto.users[0].toUpperCase(),
          },
        ] as User[]);

      const response = await service.create(
        TEST_ORG_ID,
        createMultipleUserWorkspaceDto,
      );

      expect(response.length).toBe(1);
      expect(workspaceUserServiceSpy).toBeCalledWith(
        [
          {
            email: createMultipleUserWorkspaceDto.users[0].toUpperCase(),
          },
        ],
        createMultipleUserWorkspaceDto.workspaces,
        createMultipleUserWorkspaceDto.roleId,
        mockedQueryRunner,
      );
      expect(executeSpy).toBeCalledTimes(2);
      expect(rollbackTransactionSpy).not.toBeCalled();
      expect(commitTransactionSpy).toBeCalled();
      expect(releaseSpy).toBeCalled();
    });
    it('should invite no user through email and adds two users to workspaces', async () => {
      jest
        .spyOn(organizationUserQueryService, 'findOrganizationUserByEmails')
        .mockResolvedValueOnce([
          {
            email: createMultipleUserWorkspaceDto.users[0],
          },
          {
            email: createMultipleUserWorkspaceDto.users[1],
          },
        ] as User[]);

      const response = await service.create(
        TEST_ORG_ID,
        createMultipleUserWorkspaceDto,
      );

      expect(response.length).toBe(0);
      expect(workspaceUserServiceSpy).toBeCalledWith(
        [
          {
            email: createMultipleUserWorkspaceDto.users[0],
          },
          {
            email: createMultipleUserWorkspaceDto.users[1],
          },
        ],
        createMultipleUserWorkspaceDto.workspaces,
        createMultipleUserWorkspaceDto.roleId,
        mockedQueryRunner,
      );
      expect(executeSpy).not.toBeCalled();
      expect(rollbackTransactionSpy).not.toBeCalled();
      expect(commitTransactionSpy).toBeCalled();
      expect(releaseSpy).toBeCalled();
    });
  });

  describe('inviteEmailUsers', () => {
    let executeSpy: jest.SpyInstance;

    beforeEach(() => {
      jest.restoreAllMocks();

      jest
        .spyOn(organizationInviteRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);
      jest
        .spyOn(workspaceOrganizationInviteRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      executeSpy = jest
        .spyOn(createQueryBuilder, 'execute')
        .mockImplementationOnce(() => {
          return {
            identifiers: [{ id: 'xxxx' }, { id: 'yyyyy' }],
          };
        });
    });

    it('should invite users to organization and workspaces', async () => {
      await service.inviteEmailUsers(
        TEST_ORG_ID,
        createMultipleUserWorkspaceDto.users,
        createMultipleUserWorkspaceDto,
        {} as QueryRunner,
      );
      expect(executeSpy).toBeCalledTimes(2);
    });

    it('should not invite if users array is empty', async () => {
      await service.inviteEmailUsers(
        TEST_ORG_ID,
        [],
        createMultipleUserWorkspaceDto,
        {} as QueryRunner,
      );
      expect(executeSpy).not.toBeCalled();
    });
  });

  describe('bulkInsertUsersToOrganizationInvites', () => {
    const organizationInvite2: CreateOrganizationInviteWithIdDto = {
      id: 'yyyyy',
      organizationId: 'xxxx',
      email: '<EMAIL>',
      roleId: 5,
      inviteCode: 'xx',
      validationCode: 'yy',
      status: 'PENDING',
      inviterId: 1234,
      dateCreated: new Date(Date.now()),
      lastUpdated: new Date(Date.now()),
    };

    beforeEach(() => {
      jest.restoreAllMocks();

      jest
        .spyOn(organizationInviteRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);
    });

    it('should insert two organization invites and return both of them', async () => {
      jest.spyOn(createQueryBuilder, 'execute').mockImplementationOnce(() => {
        return {
          identifiers: [{ id: 'xxxx' }, { id: 'yyyyy' }],
        };
      });

      const organizationInvitesToBeAdded = [
        organizationInvite,
        organizationInvite2,
      ];
      const response = await service.bulkInsertUsersToOrganizationInvites(
        {} as QueryRunner,
        organizationInvitesToBeAdded,
      );
      expect(response).toStrictEqual(organizationInvitesToBeAdded);
    });

    it('should insert only one of two organization invite and return one of them', async () => {
      jest.spyOn(createQueryBuilder, 'execute').mockImplementationOnce(() => {
        return {
          identifiers: [{ id: 'xxxx' }],
        };
      });

      const organizationInvitesToBeAdded = [
        organizationInvite,
        organizationInvite2,
      ];
      const response = await service.bulkInsertUsersToOrganizationInvites(
        {} as QueryRunner,
        organizationInvitesToBeAdded,
      );
      expect(response).toStrictEqual([organizationInvite]);
    });
  });

  describe('bulkInserWorkspaceOrganizationInvites', () => {
    beforeEach(() => {
      jest.restoreAllMocks();

      jest
        .spyOn(workspaceOrganizationInviteRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);
    });

    it('should bulk insert workspace organization invites', async () => {
      const executeSpy = jest.spyOn(createQueryBuilder, 'execute');
      const workspaceOrganizationInvites = [new WorkspaceOrganizationInvite()];
      await service.bulkInserWorkspaceOrganizationInvites(
        {} as QueryRunner,
        workspaceOrganizationInvites,
      );
      expect(executeSpy).toBeCalled();
    });
  });

  describe('getPendingInvites', () => {
    const pendingInvite = {
      id: '4eeeb152-2576-4ca7-bef3-22101cbf3d47',
      email: '<EMAIL>',
      role: {
        id: 7,
        name: 'Admin',
        type: 'business_entity',
        identifier: 'ADMIN',
        description: 'Can manage this workspace, its members, and access apps.',
      },
      status: 'PENDING',
      lastUpdated: new Date(Date.now()),
      invitedBy: {
        id: 19949,
        username: '<EMAIL>',
        firstName: 'Test',
        lastName: 'PM',
        displayName: 'Test PM',
        email: '<EMAIL>',
        jobTitle: null,
        photo:
          'https://vidmob-storage-dev.s3.amazonaws.com/SJ4MEBCURZ/avatar/avatar1.jpeg',
      },
    };

    beforeEach(() => {
      jest
        .spyOn(organizationInviteRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      jest
        .spyOn(createQueryBuilder, 'getManyAndCount')
        .mockResolvedValueOnce([[pendingInvite], 1]);
    });
    afterEach(() => jest.restoreAllMocks());

    it('should return a list of pending invites', async () => {
      const response = await service.getPendingInvites(
        TEST_ORG_ID,
        { perPage: 1, offset: 0 },
        { search: 'leila.patel' },
      );
      const { email, ...rest } = pendingInvite;
      const expectedItem = {
        ...rest,
        userEmail: email,
      };
      expect(response.totalCount).toBe(1);
      expect(response.items).toEqual([expectedItem]);
    });
  });

  describe('getInviteByInviteCode', () => {
    afterEach(() => jest.restoreAllMocks());

    it('should return the invite', async () => {
      jest
        .spyOn(organizationInviteRepository, 'findOne')
        .mockResolvedValueOnce(organizationInvite);

      await expect(
        service.getInviteByInviteCode(
          TEST_ORG_ID,
          organizationInvite.inviteCode,
          organizationInvite.validationCode,
        ),
      ).resolves.toEqual({
        id: organizationInvite.id,
        email: organizationInvite.email,
        organizationId: organizationInvite.organizationId,
        status: organizationInvite.status,
        role: organizationInvite.role,
      });
    });

    it('should throw error if organization invite does not exists', async () => {
      jest
        .spyOn(organizationInviteRepository, 'findOne')
        .mockResolvedValueOnce(null);

      await expect(
        service.getInviteByInviteCode(
          TEST_ORG_ID,
          organizationInvite.inviteCode,
          organizationInvite.validationCode,
        ),
      ).rejects.toEqual(new NotFoundException('Invitation not found'));
    });
  });

  describe('cancel', () => {
    afterEach(() => jest.restoreAllMocks());

    it('should cancel an organization invite', async () => {
      jest
        .spyOn(organizationInviteRepository, 'findOneBy')
        .mockResolvedValueOnce(organizationInvite);
      jest
        .spyOn(organizationInviteRepository, 'update')
        .mockResolvedValueOnce({} as UpdateResult);

      await expect(
        service.cancel(TEST_ORG_ID, organizationInvite.id),
      ).resolves.toStrictEqual({
        message: 'Organization <NAME_EMAIL> canceled',
      });
    });

    it('should not cancel a organization invite not found', async () => {
      jest
        .spyOn(organizationInviteRepository, 'findOneBy')
        .mockResolvedValueOnce(null);

      await expect(
        service.cancel(TEST_ORG_ID, organizationInvite.id),
      ).rejects.toEqual(
        new NotFoundException('Organization invite could not be found'),
      );
    });

    it('should not cancel an accepted organization invite', async () => {
      const acceptedOrganizationInvite = {
        ...organizationInvite,
        status: ORGANIZATION_INVITE_STATUS.ACCEPTED,
      };

      jest
        .spyOn(organizationInviteRepository, 'findOneBy')
        .mockResolvedValueOnce(acceptedOrganizationInvite);

      await expect(
        service.cancel(TEST_ORG_ID, acceptedOrganizationInvite.id),
      ).rejects.toEqual(
        new BadRequestException(
          'Organization invite with status ACCEPTED cannot be revoked',
        ),
      );
    });

    it('should not cancel an revoked organization invite', async () => {
      const revokedOrganizationInvite = {
        ...organizationInvite,
        status: ORGANIZATION_INVITE_STATUS.REVOKED,
      };

      jest
        .spyOn(organizationInviteRepository, 'findOneBy')
        .mockResolvedValueOnce(revokedOrganizationInvite);

      await expect(
        service.cancel(TEST_ORG_ID, revokedOrganizationInvite.id),
      ).rejects.toEqual(
        new BadRequestException(
          'Organization invite with status REVOKED cannot be revoked',
        ),
      );
    });
  });

  describe('resend', () => {
    afterEach(() => jest.restoreAllMocks());

    it('should resent an organization invite', async () => {
      jest
        .spyOn(organizationInviteRepository, 'findOneBy')
        .mockResolvedValueOnce(organizationInvite);
      jest
        .spyOn(organizationInviteRepository, 'update')
        .mockResolvedValueOnce({} as UpdateResult);

      const { inviteCode, validationCode, ...updatedOrganizationInvite } =
        await service.resend(TEST_ORG_ID, organizationInvite.id);

      const {
        inviteCode: oldInviteCode,
        validationCode: oldValidationCode,
        ...oldOrganizationInvite
      } = {
        ...organizationInvite,
      };

      expect(updatedOrganizationInvite).toStrictEqual(oldOrganizationInvite);
      expect(inviteCode).not.toBe(oldInviteCode);
      expect(validationCode).not.toBe(oldValidationCode);
    });

    it('should not resent a organization invite because of rate limit', async () => {
      const rateLimitOrganizationInvite: OrganizationInvite = {
        ...organizationInvite,
        dateCreated: new Date(Date.now() - 60 * 4 * 1000),
        lastUpdated: new Date(Date.now() - 60 * 4 * 1000),
      };

      jest
        .spyOn(organizationInviteRepository, 'findOneBy')
        .mockResolvedValueOnce(rateLimitOrganizationInvite);

      await expect(
        service.resend(TEST_ORG_ID, organizationInvite.id),
      ).rejects.toEqual(
        new BadRequestException(
          'Invite resend rate limit exceeded, wait to resent the invite again',
        ),
      );
    });

    it('should not resent a organization invite not found', async () => {
      jest
        .spyOn(organizationInviteRepository, 'findOneBy')
        .mockResolvedValueOnce(null);

      await expect(
        service.resend(TEST_ORG_ID, organizationInvite.id),
      ).rejects.toEqual(
        new NotFoundException('Organization invite could not be found'),
      );
    });

    it('should not cancel an accepted organization invite', async () => {
      const acceptedOrganizationInvite = {
        ...organizationInvite,
        status: ORGANIZATION_INVITE_STATUS.ACCEPTED,
      };

      jest
        .spyOn(organizationInviteRepository, 'findOneBy')
        .mockResolvedValueOnce(acceptedOrganizationInvite);

      await expect(
        service.resend(TEST_ORG_ID, acceptedOrganizationInvite.id),
      ).rejects.toEqual(
        new BadRequestException(
          'Organization invite with status ACCEPTED cannot be resent',
        ),
      );
    });
  });

  describe('acceptOrganizationInvite', () => {
    const organization = new Organization();
    const invitee = new User();

    let commitTransactionSpy: jest.SpyInstance;
    let rollbackTransactionSpy: jest.SpyInstance;
    let releaseSpy: jest.SpyInstance;

    beforeEach(() => {
      jest.resetAllMocks();

      jest
        .spyOn(roleService, 'findOrganizationRoleByIdentifier')
        .mockResolvedValueOnce(new Role());
      jest
        .spyOn(workspaceOrganizationInviteRepository, 'findBy')
        .mockResolvedValueOnce([new WorkspaceOrganizationInvite()]);

      jest
        .spyOn(
          organizationUserQueryService,
          'createUserOrganizationMapTransactional',
        )
        .mockResolvedValueOnce();

      jest
        .spyOn(organizationInviteRepository, 'update')
        .mockResolvedValueOnce({} as UpdateResult);

      commitTransactionSpy = jest.spyOn(mockedQueryRunner, 'commitTransaction');
      rollbackTransactionSpy = jest.spyOn(
        mockedQueryRunner,
        'rollbackTransaction',
      );
      releaseSpy = jest.spyOn(mockedQueryRunner, 'release');
    });

    it('should accept invitation', async () => {
      jest
        .spyOn(
          organizationUserQueryService,
          'createOrganizationUserRoleTransactional',
        )
        .mockResolvedValueOnce();

      await expect(
        service.acceptOrganizationInvite(
          organization,
          invitee,
          organizationInvite,
        ),
      ).resolves.toEqual({
        message: 'Organization invitation accepted successfully',
      });

      expect(releaseSpy).toBeCalledTimes(1);
      expect(commitTransactionSpy).toBeCalledTimes(1);
      expect(rollbackTransactionSpy).not.toBeCalled();
    });

    it('should not accept invite if organization invite is revoked', async () => {
      const revokedOrganizationInvite = {
        ...organizationInvite,
        status: ORGANIZATION_INVITE_STATUS.REVOKED,
      };

      await expect(
        service.acceptOrganizationInvite(
          organization,
          invitee,
          revokedOrganizationInvite,
        ),
      ).rejects.toThrow(
        new BadRequestException(
          'Organization invite with status REVOKED cannot be accepted',
        ),
      );

      expect(releaseSpy).not.toBeCalled();
      expect(commitTransactionSpy).not.toBeCalled();
      expect(rollbackTransactionSpy).not.toBeCalled();
    });

    it('should rollback when an error occurs on transaction', async () => {
      jest
        .spyOn(
          organizationUserQueryService,
          'createOrganizationUserRoleTransactional',
        )
        .mockRejectedValueOnce(new Error('mocked transaction error'));

      await expect(
        service.acceptOrganizationInvite(
          organization,
          invitee,
          organizationInvite,
        ),
      ).rejects.toEqual(new Error('mocked transaction error'));

      expect(releaseSpy).toBeCalledTimes(1);
      expect(rollbackTransactionSpy).toBeCalledTimes(1);
      expect(commitTransactionSpy).not.toBeCalled();
    });
  });
});
