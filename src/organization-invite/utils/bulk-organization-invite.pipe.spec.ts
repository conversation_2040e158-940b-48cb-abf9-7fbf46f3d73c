import { ArgumentMetadata, BadRequestException } from '@nestjs/common';
import { CreateMultipleUserWorkspaceDto } from '../dto/create-multiple-user-workspace.dto';
import { BulkOrganizationInvitePipe } from './bulk-organization-invite.pipe';

describe('BulkOrganizationInvitePipe', () => {
  let pipe: BulkOrganizationInvitePipe;

  beforeEach(() => {
    pipe = new BulkOrganizationInvitePipe();
  });

  it('should throw a bad request error when passing a invalid email', () => {
    const body = {
      users: ['not a valid email'],
      workspaces: [123],
    };

    expect(() =>
      pipe.transform(
        body as CreateMultipleUserWorkspaceDto,
        {} as ArgumentMetadata,
      ),
    ).toThrow(BadRequestException);
  });

  it('should not throw a bad request error when passing a valid email', () => {
    const body = {
      users: ['<EMAIL>'],
      workspaces: [123],
    };

    expect(() =>
      pipe.transform(
        body as CreateMultipleUserWorkspaceDto,
        {} as ArgumentMetadata,
      ),
    ).not.toThrow(BadRequestException);
  });

  it('should not throw a bad request error when passing a not trimmed email', () => {
    const body = {
      users: [' <EMAIL>'],
      workspaces: [123],
    };

    expect(
      pipe.transform(
        body as CreateMultipleUserWorkspaceDto,
        {} as ArgumentMetadata,
      ),
    ).toStrictEqual({
      ...body,
      users: ['<EMAIL>'],
    });
  });

  it('should not throw a bad request error when passing duplicated emails and workspaces', () => {
    const body = {
      users: [
        '<EMAIL>',
        ' <EMAIL>',
        ' <EMAIL>',
        '     <EMAIL>',
      ],
      workspaces: [123, 123, 123],
    };

    expect(
      pipe.transform(
        body as CreateMultipleUserWorkspaceDto,
        {} as ArgumentMetadata,
      ),
    ).toStrictEqual({
      ...body,
      users: ['<EMAIL>'],
      workspaces: [123],
    });
  });
});
