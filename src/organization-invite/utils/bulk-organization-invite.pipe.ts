import {
  ArgumentMetadata,
  BadRequestException,
  Injectable,
  PipeTransform,
} from '@nestjs/common';
import { CreateMultipleUserWorkspaceDto } from '../dto/create-multiple-user-workspace.dto';
import { isEmailValid } from 'src/common/utils/helper';

@Injectable()
export class BulkOrganizationInvitePipe implements PipeTransform {
  transform(
    body: CreateMultipleUserWorkspaceDto,
    _metadata: ArgumentMetadata,
  ): CreateMultipleUserWorkspaceDto {
    const { users, workspaces } = body;

    const usersTrimmed = users.map((userEmail: string) => {
      const userEmailTrimmed = userEmail.trim();

      if (!isEmailValid(userEmailTrimmed)) {
        throw new BadRequestException(
          `Invalid user email provided: ${userEmail}`,
        );
      }

      return userEmailTrimmed;
    });

    const uniqueUsers = [...new Set(usersTrimmed)];

    const uniqueWorkspaces = [...new Set(workspaces)];

    return {
      ...body,
      workspaces: uniqueWorkspaces,
      users: uniqueUsers,
    };
  }
}
