import { AutoMap } from '@automapper/classes';
import { Role } from 'src/entities/role.entity';
import { User } from 'src/entities/user.entity';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('organization_invite')
export class OrganizationInvite {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column({ name: 'organization_id', type: 'varchar', length: 36 })
  organizationId: string;

  @AutoMap()
  @Column({ name: 'email', type: 'varchar', length: 255 })
  email: string;

  @AutoMap()
  @Column({ name: 'role_id', type: 'bigint' })
  roleId: number;

  @AutoMap()
  @Column({ name: 'invite_code', type: 'varchar', length: 100, unique: true })
  inviteCode: string;

  @AutoMap()
  @Column({ name: 'validation_code', type: 'varchar', length: 32 })
  validationCode: string;

  @AutoMap()
  @Column({ name: 'status', type: 'varchar', length: 32 })
  status: string;

  @AutoMap()
  @Column({ name: 'inviter_id', type: 'bigint' })
  inviterId: number;

  @AutoMap()
  @Column({ name: 'date_created' })
  dateCreated: Date;

  @AutoMap()
  @Column({ name: 'last_updated' })
  lastUpdated: Date;

  @ManyToOne(() => User, (user) => user.id)
  @JoinColumn({ name: 'inviter_id' })
  invitedBy: User;

  @ManyToOne(() => Role, (role) => role.id)
  @JoinColumn({ name: 'role_id' })
  role: Role;
}
