import { AutoMap } from '@automapper/classes';
import { Column, Enti<PERSON>, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm';
import { OrganizationInvite } from './organization-invite.entity';

@Entity('workspace_organization_invite')
export class WorkspaceOrganizationInvite {
  @AutoMap()
  @PrimaryColumn({ name: 'workspace_id', type: 'bigint' })
  workspaceId: number;

  @AutoMap()
  @PrimaryColumn({ name: 'organization_invite_id', type: 'char', length: 36 })
  @ManyToOne(
    () => OrganizationInvite,
    (organizationInvite) => organizationInvite.id,
  )
  @JoinColumn({ name: 'organization_invite_id' })
  organizationInviteId: string;

  @AutoMap()
  @Column({ name: 'date_created' })
  dateCreated: Date;
}
