import {
  Mapper,
  createMap,
  forMember,
  mapFrom,
  mapWith,
} from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreateOrganizationInviteDto } from '../dto/create-organization-invite.dto';
import { OrganizationInvite } from '../entities/organization-invite.entity';
import { CreateMultipleUserWorkspaceDto } from '../dto/create-multiple-user-workspace.dto';
import { ReadOrganizationInvitesDto } from '../dto/read-organization-invites.dto';
import { User } from 'src/entities/user.entity';
import { ReadUserInviteDto } from '../dto/read-user-invite.dto';
import { SuccessfulMessageResponseDto } from '../dto/successful-message-response.dto';
import { ReadPublicOrganizationInvite } from '../dto/read-public-organization-invite.dto';

@Injectable()
export class OrganizationInviteProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  get profile() {
    return (mapper) => {
      createMap(mapper, CreateOrganizationInviteDto, OrganizationInvite);

      createMap(
        mapper,
        CreateMultipleUserWorkspaceDto,
        SuccessfulMessageResponseDto,
        forMember(
          (destination) => destination.message,
          mapFrom(
            (source) =>
              `Successfully assigned user(s) to ${source.workspaces.length} workspace(s)`,
          ),
        ),
      );

      createMap(mapper, User, ReadUserInviteDto);

      createMap(
        mapper,
        OrganizationInvite,
        ReadOrganizationInvitesDto,
        forMember(
          (destination) => destination.invitedBy,
          mapWith(ReadUserInviteDto, User, (source) => source.invitedBy),
        ),
        forMember(
          (destination) => destination.role,
          mapFrom((source) => source.role),
        ),
        forMember(
          (destination) => destination.userEmail,
          mapFrom((source) => source.email),
        ),
      );

      createMap(
        mapper,
        OrganizationInvite,
        ReadPublicOrganizationInvite,
        forMember(
          (destination) => destination.role,
          mapFrom((source) => source.role),
        ),
      );
    };
  }
}
