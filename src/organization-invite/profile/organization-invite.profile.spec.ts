import { classes } from '@automapper/classes';
import { Mapper } from '@automapper/core';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationInviteProfile } from './organization-invite.profile';
import { CreateOrganizationInviteDto } from '../dto/create-organization-invite.dto';
import { OrganizationInvite } from '../entities/organization-invite.entity';
import { CreateMultipleUserWorkspaceDto } from '../dto/create-multiple-user-workspace.dto';
import { ReadOrganizationInvitesDto } from '../dto/read-organization-invites.dto';
import { SuccessfulMessageResponseDto } from '../dto/successful-message-response.dto';

describe('OrganizationInviteProfile', () => {
  let mapper: Mapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [OrganizationInviteProfile],
    }).compile();

    const organizationInviteProfile: OrganizationInviteProfile =
      module.get<OrganizationInviteProfile>(OrganizationInviteProfile);
    mapper = module.get<Mapper>(getMapperToken());
    organizationInviteProfile.profile(mapper);
  });

  it('should map CreateOrganizationInviteDto to OrganizationInvite', () => {
    const createOrganizationInviteDto: CreateOrganizationInviteDto = {
      organizationId: 'xxx-xxx-xxx',
      email: '<EMAIL>',
      roleId: 1,
      inviteCode: 'xxxxxxxxxx',
      validationCode: 'xxxxx',
      status: 'PENDING',
      inviterId: 2,
      dateCreated: new Date(),
      lastUpdated: new Date(),
    };

    const organizationInviteEntity = mapper.map(
      createOrganizationInviteDto,
      CreateOrganizationInviteDto,
      OrganizationInvite,
    );

    expect(organizationInviteEntity).toBeDefined();
    expect(organizationInviteEntity.organizationId).toBe('xxx-xxx-xxx');
    expect(organizationInviteEntity.email).toBe('<EMAIL>');
    expect(organizationInviteEntity.roleId).toBe(1);
    expect(organizationInviteEntity.inviteCode).toBe('xxxxxxxxxx');
    expect(organizationInviteEntity.validationCode).toBe('xxxxx');
    expect(organizationInviteEntity.status).toBe('PENDING');
    expect(organizationInviteEntity.inviterId).toBe(2);
    expect(organizationInviteEntity.dateCreated).toBeInstanceOf(Date);
    expect(organizationInviteEntity.lastUpdated).toBeInstanceOf(Date);
  });

  it('should map CreateMultipleUserWorkspaceDto to SuccessfulMessageResponseDto', () => {
    const createMultipleUserWorkspaceDto: CreateMultipleUserWorkspaceDto = {
      users: ['<EMAIL>'],
      workspaces: [1],
      roleId: 2,
      inviterId: 3,
    };

    const createMultipleUserWorkspaceResultDto = mapper.map(
      createMultipleUserWorkspaceDto,
      CreateMultipleUserWorkspaceDto,
      SuccessfulMessageResponseDto,
    );

    expect(createMultipleUserWorkspaceResultDto).toBeDefined();
    expect(createMultipleUserWorkspaceResultDto.message).toBe(
      'Successfully assigned user(s) to 1 workspace(s)',
    );
  });

  it('should map OrganizationInvite to ReadOrganizationInvitesDto', () => {
    const organizationInvite = {
      id: '4eeeb152-2576-4ca7-bef3-22101cbf3d47',
      email: '<EMAIL>',
      role: {
        id: 7,
        name: 'Admin',
        type: 'business_entity',
        identifier: 'ADMIN',
        description: 'Can manage this workspace, its members, and access apps.',
      },
      status: 'PENDING',
      lastUpdated: new Date(Date.now()),
      invitedBy: {
        id: 19949,
        username: '<EMAIL>',
        firstName: 'Test',
        lastName: 'PM',
        displayName: 'Test PM',
        email: '<EMAIL>',
        jobTitle: null,
        photo:
          'https://vidmob-storage-dev.s3.amazonaws.com/SJ4MEBCURZ/avatar/avatar1.jpeg',
        enabled: false,
        accountExpired: false,
        accountLocked: false,
      },
      organizationId: '',
      roleId: 0,
      inviteCode: '',
      validationCode: '',
      inviterId: 0,
      dateCreated: new Date(Date.now()),
    };

    const readOrganizationInvitesDto = mapper.map(
      organizationInvite,
      OrganizationInvite,
      ReadOrganizationInvitesDto,
    );

    expect(readOrganizationInvitesDto.id).toBe(
      '4eeeb152-2576-4ca7-bef3-22101cbf3d47',
    );
    expect(readOrganizationInvitesDto.userEmail).toBe('<EMAIL>');
    expect(readOrganizationInvitesDto.role).toStrictEqual({
      id: 7,
      name: 'Admin',
      type: 'business_entity',
      identifier: 'ADMIN',
      description: 'Can manage this workspace, its members, and access apps.',
    });
    expect(readOrganizationInvitesDto.status).toBe('PENDING');
    expect(readOrganizationInvitesDto.lastUpdated).toBeInstanceOf(Date);
    expect(readOrganizationInvitesDto.invitedBy).toEqual({
      id: 19949,
      username: '<EMAIL>',
      firstName: 'Test',
      lastName: 'PM',
      displayName: 'Test PM',
      email: '<EMAIL>',
      jobTitle: null,
      photo:
        'https://vidmob-storage-dev.s3.amazonaws.com/SJ4MEBCURZ/avatar/avatar1.jpeg',
    });
  });
});
