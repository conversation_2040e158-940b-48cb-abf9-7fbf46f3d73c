import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Delete,
  Param,
  Post,
  Query,
  Patch,
  NotFoundException,
  Logger,
  HttpStatus,
} from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import {
  GetPagination,
  PaginationOptions,
  VmApiCreatedResponse,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { CreateMultipleUserWorkspaceDto } from './dto/create-multiple-user-workspace.dto';
import { OrganizationInviteService } from './organization-invite.service';
import { BulkOrganizationInvitePipe } from './utils/bulk-organization-invite.pipe';
import { RoleService } from 'src/role/role.service';
import { OrganizationUserQueryService } from 'src/organizations/organization-user/organization-user-query.service';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { OrganizationInviteSearchParamsDto } from './dto/organization-invite-search-params.dto';
import { ReadOrganizationInvitesDto } from './dto/read-organization-invites.dto';
import { SuccessfulMessageResponseDto } from './dto/successful-message-response.dto';
import { UpdateOrganizationInviteDto } from './dto/update-organization-invite.dto';
import { CreateOrganizationInviteWithIdDto } from './dto/create-organization-invite-with-id.dto';
import { ReadPublicOrganizationInvite } from './dto/read-public-organization-invite.dto';
import { AcceptOrganizationInviteDto } from './dto/accept-organization-invite.dto';
import { BulkInviteResponseDto } from './dto/bulk-invite-response.dto';
import { WorkspaceUserService } from 'src/workspace-user/workspace-user.service';
import { OrganizationInvite } from './entities/organization-invite.entity';
import { User } from 'src/entities/user.entity';

@ApiTags('organization-invite')
@Controller('organization/:organizationId')
export class OrganizationInviteController {
  private logger = new Logger(OrganizationInviteController.name);

  constructor(
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly organizationUserQueryService: OrganizationUserQueryService,
    private readonly organizationInviteService: OrganizationInviteService,
    private readonly roleService: RoleService,
    private readonly emailTemplateService: EmailTemplateService,
    private readonly workspaceUserService: WorkspaceUserService,
  ) {}

  /**
   * It bulk invites multiple users to one or more workspaces.
   * If there is some users that is being invited that are not part of the organization,
   * these users will receive an email invitation.
   * @param organizationId - The organization id.
   */
  @VmApiCreatedResponse({
    type: BulkInviteResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to invite users',
  })
  @Post('/invite')
  async bulkInviteUsersToWorkspaces(
    @Param('organizationId') organizationId: string,
    @Body(new BulkOrganizationInvitePipe())
    createMultipleUserWorkspaceDto: CreateMultipleUserWorkspaceDto,
  ) {
    await this.validateRoleAndOrganizationWorkspaces(
      organizationId,
      createMultipleUserWorkspaceDto.roleId,
      createMultipleUserWorkspaceDto.workspaces,
    );

    await this.validateOrgStandardBulkInvite(
      organizationId,
      createMultipleUserWorkspaceDto,
    );

    const { inactiveUsersBeingInvited, pendingEmailsBeingInvited } =
      await this.getPendingInviteEmailsAndInactiveUserEmails(
        organizationId,
        createMultipleUserWorkspaceDto.users,
      );

    createMultipleUserWorkspaceDto.users =
      this.validateUserEmailsBeingInvitedOrReturnValidEmails(
        inactiveUsersBeingInvited,
        pendingEmailsBeingInvited,
        createMultipleUserWorkspaceDto.users,
      );

    await this.createOrganizationInvitesAndDispatch(
      organizationId,
      createMultipleUserWorkspaceDto,
    );

    return {
      message: `Successfully assigned user(s) to ${createMultipleUserWorkspaceDto.workspaces.length} workspace(s)`,
      inactiveEmails: inactiveUsersBeingInvited,
      pendingInviteEmails: pendingEmailsBeingInvited,
    };
  }

  /**
   * * It returns a paginated response with the organization pending invites
   * @param organizationId - The organization id.
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadOrganizationInvitesDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to invite users',
  })
  @Get('/invite')
  async getPendingInvites(
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() searchParams: OrganizationInviteSearchParamsDto,
  ) {
    const response = await this.organizationInviteService.getPendingInvites(
      organizationId,
      paginationOptions,
      searchParams,
    );

    return response;
  }

  /**
   * It will retrieve the organization invite by its id.
   * This will be used by the invitee in a public endpoint.
   *
   * Note: this endpoint is temporarily until we move the public GET by invite code to Get('/invite/code/:inviteCode').
   * @param organizationId - The organization id.
   * @param organizationInviteId - The id of the organization invite.
   */
  @VmApiOkResponse({
    type: ReadOrganizationInvitesDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to invite users',
  })
  @ApiParam({
    name: 'organizationInviteId',
    description: 'The id of the organization invite',
  })
  @Get('/invite/id/:organizationInviteId')
  async getOrganizationInviteById(
    @Param('organizationId') organizationId: string,
    @Param('organizationInviteId') organizationInviteId: string,
  ) {
    const organizationInvite =
      await this.organizationInviteService.getInviteByIdAndOrganizationId(
        organizationId,
        organizationInviteId,
      );

    if (organizationInvite === null) {
      throw new NotFoundException('Invite could not be found');
    }

    return this.classMapper.map(
      organizationInvite,
      OrganizationInvite,
      ReadOrganizationInvitesDto,
    );
  }

  /**
   * It will retrieve the organization invite by invite code and match if the validation code matches.
   * This will be used by the invitee in a public endpoint.
   *
   * @param organizationId - The organization id.
   * @param inviteCode - The invitation code.
   */
  @VmApiOkResponse({
    type: ReadPublicOrganizationInvite,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to invite users',
  })
  @ApiParam({
    name: 'inviteCode',
    description: 'The invitation code',
  })
  @Get('/invite/code/:inviteCode')
  async getOrganizationInviteByInviteCodeAndValidationCode(
    @Param('organizationId') organizationId: string,
    @Param('inviteCode') inviteCode: string,
    @Query('validationCode') validationCode: string,
  ) {
    if (validationCode?.length == 0) {
      throw new BadRequestException(
        'The query param validationCode should not be empty',
      );
    }

    return await this.organizationInviteService.getInviteByInviteCode(
      organizationId,
      inviteCode,
      validationCode,
    );
  }

  /**
   * It will retrieve the organization invite by invite code and match if the validation code matches.
   * This will be used by the invitee in a public endpoint.
   *
   * @deprecated please use GET('/invite/code/:inviteCode') - getOrganizationInviteByInviteCodeAndValidationCode
   * @param organizationId - The organization id.
   * @param inviteCode - The invitation code.
   */
  @VmApiOkResponse({
    type: ReadPublicOrganizationInvite,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to invite users',
  })
  @ApiParam({
    name: 'inviteCode',
    description: 'The invitation code',
  })
  @Get('/invite/:inviteCode')
  async getOrganizationInviteByInviteCode(
    @Param('organizationId') organizationId: string,
    @Param('inviteCode') inviteCode: string,
    @Query('validationCode') validationCode: string,
  ) {
    if (validationCode?.length == 0) {
      throw new BadRequestException(
        'The query param validationCode should not be empty',
      );
    }

    return await this.organizationInviteService.getInviteByInviteCode(
      organizationId,
      inviteCode,
      validationCode,
    );
  }

  /**
   * It will set the organization invite as revoked
   * @param organizationId - The organization id.
   * @param organizationInviteId - The organization invite id.
   */
  @VmApiOkResponse({
    type: SuccessfulMessageResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to invite users',
  })
  @ApiParam({
    name: 'organizationInviteId',
    description: 'The id of the organization invite',
  })
  @Delete('/invite/:organizationInviteId')
  async cancelOrganizationInvite(
    @Param('organizationId') organizationId: string,
    @Param('organizationInviteId') organizationInviteId: string,
  ) {
    return await this.organizationInviteService.cancel(
      organizationId,
      organizationInviteId,
    );
  }

  /**
   * It will resent the organization invite
   * @param organizationId - The organization id.
   * @param organizationInviteId - The organization invite id.
   */
  @VmApiOkResponse({
    type: SuccessfulMessageResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to invite users',
  })
  @ApiParam({
    name: 'organizationInviteId',
    description: 'The id of the organization invite',
  })
  @Patch('/invite/:organizationInviteId')
  async resendOrganizationInvite(
    @Param('organizationId') organizationId: string,
    @Param('organizationInviteId') organizationInviteId: string,
  ) {
    let organizationInvite: OrganizationInvite;

    try {
      organizationInvite = await this.organizationInviteService.resend(
        organizationId,
        organizationInviteId,
      );

      await this.dispatchOrganizationInvites(
        organizationId,
        organizationInvite.inviterId,
        [organizationInvite],
      );
    } catch (error) {
      // doesn't throw errors about rate limit
      if (error.status != HttpStatus.TOO_MANY_REQUESTS) {
        throw error;
      }

      this.logger.error(error.message);
    }

    return {
      message: `Organization invite email resent`,
    };
  }

  /**
   * It will accept the invitation and add the invitee to the invited organization and workpaces
   * @param organizationId - The organization id.
   * @param organizationInviteId - The organization invite id.
   * @param acceptOrganizationInviteDto - the body containing the invitee user id or invitee email
   */
  @VmApiOkResponse({
    type: SuccessfulMessageResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to invite users',
  })
  @ApiParam({
    name: 'organizationInviteId',
    description: 'The id of the organization invite',
  })
  @Patch('/invite/:organizationInviteId/accept')
  async acceptOrganizationInvite(
    @Param('organizationId') organizationId: string,
    @Param('organizationInviteId') organizationInviteId: string,
    @Body()
    acceptOrganizationInviteDto: AcceptOrganizationInviteDto,
  ) {
    let invitee: User;

    if (acceptOrganizationInviteDto.inviteeId) {
      invitee = await this.organizationUserQueryService.findUser(
        acceptOrganizationInviteDto.inviteeId,
      );
    } else if (acceptOrganizationInviteDto.inviteeEmail) {
      invitee = await this.organizationUserQueryService.findUserByEmail(
        acceptOrganizationInviteDto.inviteeEmail,
      );
    }

    const organizationInvite =
      await this.organizationInviteService.getInviteCodeByIdAndEmail(
        organizationInviteId,
        invitee.email,
      );

    if (!organizationInvite) {
      throw new NotFoundException('Organization invite not found');
    }

    const organization =
      await this.organizationUserQueryService.findOrganization(organizationId);

    if (organization.id !== organizationInvite.organizationId) {
      throw new BadRequestException(
        'Organization id request path and the organization id on invite mismatch',
      );
    }

    return await this.organizationInviteService.acceptOrganizationInvite(
      organization,
      invitee,
      organizationInvite,
    );
  }

  /**
   * Validates if the invitation is being made by ORG ADMIN or ORG STANDARD.
   * If it is an ORG STANDARD, we need to validate:
   *  - If the invitation is for more than one workspace, then we need to check:
   *    1. if the invite role is for an WORKSPACE STANDARD role.
   *    2. if the inviter is in all the workspaces they're inviting.
   *  - If the invitation is for one workspace, we need to check:
   *    1. User can invite someone to that workspace with that role
   * @param organizationId
   * @param createMultipleUserWorkspaceDto
   */
  private async validateOrgStandardBulkInvite(
    organizationId: string,
    createMultipleUserWorkspaceDto: CreateMultipleUserWorkspaceDto,
  ) {
    const { workspaces, inviterId, roleId } = createMultipleUserWorkspaceDto;

    const isInviterOrgAdmin =
      await this.organizationUserQueryService.isUserOrganizationAdmin(
        organizationId,
        inviterId,
      );

    if (isInviterOrgAdmin) return;

    if (workspaces.length > 1) {
      return await this.validateBulkInviteToMultipleWorkspaces(
        workspaces,
        inviterId,
        roleId,
      );
    }

    return await this.validateBulkInviteToSingleWorkspace(
      workspaces[0],
      inviterId,
      roleId,
    );
  }

  /**
   * This function checks if the bulk invite to multiple workspaces is valid.
   * This will mainly check if the ORG STANDARD can do this action, it will check:
   *  - if the invite role is for an WORKSPACE STANDARD role.
   *  - if the inviter is in all the workspaces they're inviting.
   * @param workspaces
   * @param inviterId
   * @param roleId
   */
  private async validateBulkInviteToMultipleWorkspaces(
    workspaces: number[],
    inviterId: number,
    roleId: number,
  ) {
    const isWorkspaceStandardRole =
      await this.roleService.isRoleIdWorkspaceStandardRole(roleId);

    if (!isWorkspaceStandardRole)
      throw new BadRequestException(
        `User cannot invite users with role ${roleId} to multiple workspaces`,
      );

    const userHasAccessToAllWorkspaces =
      await this.workspaceUserService.userHasAccessToAllWorkspaces(
        inviterId,
        workspaces,
      );

    if (!userHasAccessToAllWorkspaces)
      throw new BadRequestException(
        `User doesn't have access to some or all of these workspaces: ${workspaces.join(
          ', ',
        )}`,
      );
  }

  /**
   * This function will check if the bulk invite to single workspace is valid.
   * This will mainly check if the ORG STANDARD can do this action, it will check:
   *  - If User can invite someone to that workspace with that role.
   * @param workspaceId
   * @param inviterId
   * @param roleId
   */
  private async validateBulkInviteToSingleWorkspace(
    workspaceId: number,
    inviterId: number,
    roleId: number,
  ) {
    const canUserInviteWithRoleId =
      await this.workspaceUserService.userCanUpdateWithRoleId(
        inviterId,
        workspaceId,
        roleId,
      );

    if (!canUserInviteWithRoleId) {
      throw new BadRequestException(
        `User cannot invite with role ${roleId} in this workspace`,
      );
    }
  }

  /**
   * It retrieves the pending organization invite and inactive user emails
   * @param organizationId
   * @param userEmails
   */
  private async getPendingInviteEmailsAndInactiveUserEmails(
    organizationId: string,
    userEmails: string[],
  ) {
    const inactiveUsersBeingInvited =
      await this.organizationUserQueryService.findInactivePeopleByEmail(
        userEmails,
      );

    const pendingEmailsBeingInvited =
      await this.organizationInviteService.getPendingInvitesEmailsByEmails(
        organizationId,
        userEmails,
      );

    return {
      inactiveUsersBeingInvited,
      pendingEmailsBeingInvited,
    };
  }

  /**
   * It validates if there is any valid email checking inactiveUsersBeingInvited and pendingEmailsBeingInvited arrays, if there is any
   * return the valid ones, otherwise throw a bad request exception
   * @param inactiveUsersBeingInvited
   * @param pendingEmailsBeingInvited
   * @param usersBeingInvited
   * @returns string[] with valid emails
   */
  private validateUserEmailsBeingInvitedOrReturnValidEmails(
    inactiveUsersBeingInvited: string[],
    pendingEmailsBeingInvited: string[],
    usersBeingInvited: string[],
  ): string[] {
    const invalidEmails = inactiveUsersBeingInvited.concat(
      pendingEmailsBeingInvited,
    );

    // This will filter only valid emails to be invited.
    const validUsersToInvite = usersBeingInvited.filter(
      (emailBeingInvited) =>
        !invalidEmails.some(
          (invalidEmail) => invalidEmail == emailBeingInvited,
        ),
    );

    if (validUsersToInvite.length === 0) {
      throw new BadRequestException({
        message: {
          inactiveEmails: inactiveUsersBeingInvited,
          pendingInviteEmails: pendingEmailsBeingInvited,
        },
      });
    }

    return validUsersToInvite;
  }

  /**
   * It will validate if roleId is a valid workspace role and if workspaces belongs to the organizationId
   * @param organizationId
   * @param roleId
   * @param workspaces
   */
  private async validateRoleAndOrganizationWorkspaces(
    organizationId: string,
    roleId: number,
    workspaces: number[],
  ) {
    // Garantee that all the workspaces sent belongs to the organizatioId that was also sent on the request;
    const areWorkspacesValid =
      await this.organizationUserQueryService.areWorkspacesBelongsToOrganization(
        organizationId,
        workspaces,
      );

    if (!areWorkspacesValid) {
      throw new BadRequestException(
        `One or more workspaces in ${workspaces} doesn't belong to the organization ${organizationId}`,
      );
    }
    // Garantee that the role is a valid workspace role;
    const isRoleValid = await this.roleService.isRoleValidWorkspaceRole(roleId);
    if (!isRoleValid) {
      throw new BadRequestException(
        `Role ${roleId} is not a valid workspace role`,
      );
    }
  }

  private async createOrganizationInvitesAndDispatch(
    organizationId: string,
    createMultipleUserWorkspaceDto: CreateMultipleUserWorkspaceDto,
  ) {
    const organizationInvites = await this.organizationInviteService.create(
      organizationId,
      createMultipleUserWorkspaceDto,
    );

    if (organizationInvites.length > 0) {
      await this.dispatchOrganizationInvites(
        organizationId,
        createMultipleUserWorkspaceDto.inviterId,
        organizationInvites,
      );
    }
  }

  // Wrap common function calls to use when dispatching invites
  private async dispatchOrganizationInvites(
    organizationId: string,
    inviterId: number,
    organizationInvites: CreateOrganizationInviteWithIdDto[],
  ) {
    const organization =
      await this.organizationUserQueryService.findOrganization(organizationId);

    const inviter = await this.organizationUserQueryService.findUser(inviterId);

    await this.emailTemplateService.dispatchOrganizationInvites(
      organizationInvites,
      inviter,
      organization.name,
    );
  }
}
