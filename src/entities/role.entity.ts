import { AutoMap } from '@automapper/classes';
import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity('role')
export class Role {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ type: 'varchar', length: 100, nullable: false })
  name: string;

  @AutoMap()
  @Column({ type: 'varchar', length: 100, nullable: false })
  type: string;

  @AutoMap()
  @Column({ type: 'varchar', length: 50, nullable: true })
  identifier: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  description: string;
}
