import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Join<PERSON><PERSON>,
  ManyToMany,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { WorkspaceUser } from '../workspaces/entities/workspace-user.entity';
import { WorkspaceManager } from '../workspaces/entities/workspace-manager.entity';
import { Workspace } from '../workspaces/entities/workspace.entity';

@Entity('person')
export class User {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column()
  username: string;

  @AutoMap()
  @Column({ name: 'first_name' })
  firstName: string;

  @AutoMap()
  @Column({ name: 'last_name' })
  lastName: string;

  @AutoMap()
  @Column()
  photo: string;

  @AutoMap()
  @Column({ name: 'display_name', type: 'varchar', nullable: true })
  displayName: string;

  @AutoMap()
  @Column({ name: 'email', type: 'varchar', nullable: false })
  email: string;

  @AutoMap()
  @Column({ name: 'job_title', type: 'varchar', nullable: true })
  jobTitle: string | null;

  @AutoMap()
  @Column({ name: 'enabled', type: 'boolean', nullable: false })
  enabled: boolean;

  @AutoMap()
  @Column({ name: 'account_expired', type: 'boolean', nullable: false })
  accountExpired: boolean;

  @AutoMap()
  @Column({ name: 'account_locked', type: 'boolean', nullable: false })
  accountLocked: boolean;

  @OneToMany(() => WorkspaceUser, (workspaceUser) => workspaceUser.user)
  workspaceUsers: WorkspaceUser[];

  @OneToMany(
    () => WorkspaceManager,
    (workspaceManager) => workspaceManager.manager,
  )
  workspaceManagers: WorkspaceManager[];

  @ManyToMany(() => Workspace, (workspace) => workspace.users)
  @JoinTable({
    name: 'partner_person',
    joinColumns: [{ name: 'person_id' }],
    inverseJoinColumns: [{ name: 'partner_id' }],
  })
  workspace: Workspace[];
}
