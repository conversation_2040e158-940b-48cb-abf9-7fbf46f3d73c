import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceService } from './workspaces.service';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import { Organization } from '../organizations/entities/organization.entity';
import { Workspace } from './entities/workspace.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { WorkspaceProfile } from './mapper/workspace.profile';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper, Mapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { OrganizationsService } from 'src/organizations/organizations.service';
import { OrganizationProfile } from 'src/organizations/mapper/organization.profile';
import { ReadWorkspaceDto } from './dto/read-workspace.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { SearchParamsDto } from './dto/search-params.dto';
import { SplitAndTrimPipe } from '../organizations/utils/split-and-trim-pipe';
import { AdAccountsService } from '../ad-accounts/ad-accounts.service';
import { AdAccountsProfile } from '../ad-accounts/mapper/ad-accounts.profile';
import { PlatformAdAccount } from '../ad-accounts/entities/ad-account.entity';
import { PlatformAdAccountToWorkspace } from '../ad-accounts/entities/ad-account-workspace-map.entity';
import { OrganizationPlatformAdAccountMap } from '../ad-accounts/entities/organization-platform-ad-account-map.entity';
import { Market } from '../markets/entities/market.entity';
import { Brand } from '../brands/entities/brand.entity';
import { WorkspaceMarket } from './entities/workspace-market.entity';
import { WorkspaceBrand } from './entities/workspace-brand.entity';
import { WorkspaceIndustry } from './entities/partner-industry.entity';
import { AccountType } from './entities/account-type.entity';
import { FeatureWorkspace } from './entities/feature-workspace.entity';
import { FeatureAccountType } from './entities/feature-account-type.entity';
import { MarketService } from '../markets/market.service';
import { BrandService } from '../brands/brand.service';
import { BadRequestException, ForbiddenException } from '@nestjs/common';
import { OrganizationAdAccountsService } from '../ad-accounts/organization-ad-accounts.service';
import { WorkspaceManager } from './entities/workspace-manager.entity';
import { CreatePlatformAdAccountWorkspaceMapDto } from '../ad-accounts/dto/create-platform-ad-account-workspace-map.dto';
import { WorkspaceUser } from './entities/workspace-user.entity';
import { DEFAULT_SESSION_TIMEOUT_MIN } from '../organizations/utils/constants';
import { ProductAccountType } from './entities/product-account-type.entity';
import { OrganizationFeatureWhitelist } from '../organizations/entities/organization-feature-whitelist.entity';
import { FeatureWhitelist } from './entities/feature-whitelist.entity';
import { AdAccountImportStatusService } from 'src/ad-accounts/ad-account-import-status.service';

const createQueryBuilder: any = {
  where: () => createQueryBuilder,
  leftJoinAndSelect: () => createQueryBuilder,
  getOne: () => createQueryBuilder,
};

describe('WorkspaceService', () => {
  let service: WorkspaceService;
  let adAccountService: AdAccountsService;
  let organizationAdAccountsService: OrganizationAdAccountsService;
  let organizationService: OrganizationsService;
  let adAccountStatusService: AdAccountImportStatusService;
  let organizationRepository: Repository<Organization>;
  let workspaceRepo: Repository<Workspace>;
  let adAccountRepo: Repository<PlatformAdAccount>;
  let organizationPlatformAdAccountMapRepository: Repository<OrganizationPlatformAdAccountMap>;
  let adAccountToWorkspaceRepo: Repository<PlatformAdAccountToWorkspace>;
  let orgRepo: Repository<Organization>;
  let marketRepository: Repository<Market>;
  let brandRepository: Repository<Brand>;
  let workspaceMarketRepository: Repository<WorkspaceMarket>;
  let workspaceBrandRepository: Repository<WorkspaceBrand>;
  let workspaceUserRepository: Repository<WorkspaceUser>;
  let workspaceIndustryRepository: Repository<WorkspaceIndustry>;
  let workspaceManagerRepository: Repository<WorkspaceManager>;
  let accountTypeRepository: Repository<AccountType>;
  let featureWorkspaceRepository: Repository<FeatureWorkspace>;
  let featureAccountTypeRepository: Repository<FeatureAccountType>;
  let featureWhitelistRepository: Repository<FeatureWhitelist>;
  let productAccountTypeRepository: Repository<ProductAccountType>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceService,
        WorkspaceProfile,
        MarketService,
        BrandService,
        OrganizationsService,
        OrganizationProfile,
        AdAccountsProfile,
        SplitAndTrimPipe,
        {
          provide: AdAccountsService,
          useFactory: () => ({
            getAdAccountsIdsFromPlatformAccountIds: jest.fn(),
            findAdAccountIdFromPlatformIds: jest.fn(),
            createWorkspacePlatformAdAccountConnections: jest.fn(),
            getPlatformAdAccountIdsConnectedToAWorkspace: jest
              .fn()
              .mockResolvedValueOnce([]),
            deleteWorkspaceToAdAccount: jest.fn(),
            getPlatformAdAccountWorkspaceMappings: jest
              .fn()
              .mockResolvedValue(new Map<string, string[]>()),
          }),
        },
        {
          provide: OrganizationAdAccountsService,
          useFactory: () => ({
            mapAdAccountToOrganization: jest.fn(),
            findOrganizationsForAdAccount: jest.fn(),
            getAdAccountIdsFromAnOrganization: jest.fn(),
            countNumberOfOrganizationAdAccounts: jest.fn(),
          }),
        },
        {
          provide: AdAccountImportStatusService,
          useFactory: () => ({
            createImportStatus: jest.fn(),
          }),
        },
        {
          provide: getRepositoryToken(Organization),
          useClass: Repository,
          useValue: {
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Workspace),
          useValue: {
            find: jest.fn(),
            findBy: jest.fn(),
            findOneBy: jest.fn(),
            findOne: jest.fn(),
            findAndCount: jest.fn(),
            query: jest.fn(),
            countBy: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            manager: {
              transaction: jest
                .fn()
                .mockImplementation(async (cb) => cb({} as EntityManager)),
            },
          },
        },
        {
          provide: getRepositoryToken(PlatformAdAccount),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountToWorkspace),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(OrganizationPlatformAdAccountMap),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(WorkspaceUser),
          useValue: workspaceUserRepository,
        },
        {
          provide: getRepositoryToken(WorkspaceMarket),
          useValue: workspaceBrandRepository,
        },
        {
          provide: getRepositoryToken(WorkspaceBrand),
          useValue: workspaceBrandRepository,
        },
        {
          provide: getRepositoryToken(WorkspaceIndustry),
          useValue: workspaceIndustryRepository,
        },
        {
          provide: getRepositoryToken(WorkspaceManager),
          useValue: workspaceManagerRepository,
        },
        {
          provide: getRepositoryToken(AccountType),
          useValue: accountTypeRepository,
        },
        {
          provide: getRepositoryToken(FeatureWorkspace),
          useValue: featureWorkspaceRepository,
        },
        {
          provide: getRepositoryToken(FeatureAccountType),
          useValue: featureAccountTypeRepository,
        },
        {
          provide: getRepositoryToken(FeatureWhitelist),
          useValue: featureWhitelistRepository,
        },
        {
          provide: getRepositoryToken(ProductAccountType),
          useValue: productAccountTypeRepository,
        },
        { provide: getRepositoryToken(Brand), useValue: brandRepository },

        { provide: getRepositoryToken(Market), useValue: marketRepository },
        {
          provide: getRepositoryToken(OrganizationFeatureWhitelist),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    module.get<Mapper>(getMapperToken());

    service = module.get<WorkspaceService>(WorkspaceService);
    organizationRepository = module.get<Repository<Organization>>(
      getRepositoryToken(Organization),
    );
    accountTypeRepository = module.get<Repository<AccountType>>(
      getRepositoryToken(AccountType),
    );

    workspaceRepo = module.get<Repository<Workspace>>(
      getRepositoryToken(Workspace),
    );
    adAccountService = module.get<AdAccountsService>(AdAccountsService);
    organizationAdAccountsService = module.get<OrganizationAdAccountsService>(
      OrganizationAdAccountsService,
    );
    adAccountRepo = module.get<Repository<PlatformAdAccount>>(
      getRepositoryToken(PlatformAdAccount),
    );
    adAccountToWorkspaceRepo = module.get<
      Repository<PlatformAdAccountToWorkspace>
    >(getRepositoryToken(PlatformAdAccountToWorkspace));
    organizationPlatformAdAccountMapRepository = module.get<
      Repository<OrganizationPlatformAdAccountMap>
    >(getRepositoryToken(OrganizationPlatformAdAccountMap));
    organizationService =
      module.get<OrganizationsService>(OrganizationsService);
    adAccountStatusService = module.get<AdAccountImportStatusService>(
      AdAccountImportStatusService,
    );
  });

  beforeEach(() => {
    jest
      .spyOn(organizationRepository, 'createQueryBuilder')
      .mockImplementationOnce(() => createQueryBuilder);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should throw an exception if a workspace does not exist', async () => {
    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(undefined);

    await expect(service.findOne(1)).rejects.toThrow();
  });

  it('should update a workspace', async () => {
    const organization = new Organization();
    organization.id = 'uuid';

    const primaryWorkspace = new Workspace();
    primaryWorkspace.id = 1;
    primaryWorkspace.name = 'workspace1';
    primaryWorkspace.isPrimary = true;
    primaryWorkspace.organization = organization;
    primaryWorkspace.organizationId = 'uuid';

    const workspace = new Workspace();
    workspace.id = 2;
    workspace.name = 'workspace2';
    workspace.isPrimary = false;
    workspace.organization = organization;
    workspace.organizationId = 'uuid';

    jest
      .spyOn(createQueryBuilder, 'getOne')
      .mockResolvedValueOnce(organization);

    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(workspace);
    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(workspace);
    jest
      .spyOn(workspaceRepo, 'findOneBy')
      .mockResolvedValueOnce(primaryWorkspace);
    jest.spyOn(service, 'validateUniqueName').mockImplementation(null);
    jest.spyOn(workspaceRepo, 'save').mockResolvedValueOnce(workspace);
    jest.spyOn(workspaceRepo, 'update').mockResolvedValueOnce(null);

    expect(workspace.name).toEqual('workspace2');
    expect(workspace.isPrimary).toEqual(false);
    expect(workspace.organizationId).toEqual('uuid');

    const updatedWorkspace = await service.update(1, {
      name: 'workspace3',
      isPrimary: true,
      organizationId: 'uuid',
    });

    expect(updatedWorkspace).toBeDefined();
    expect(updatedWorkspace).toBeInstanceOf(ReadWorkspaceDto);
    expect(updatedWorkspace.name).toEqual('workspace3');
    expect(updatedWorkspace.isPrimary).toEqual(true);
    expect(updatedWorkspace.organizationId).toEqual('uuid');
  });

  it('should throw an exception if a workspace is primary and try to change to false when trying to update', async () => {
    const organization = new Organization();
    organization.id = 'uuid';

    const primaryWorkspace = new Workspace();
    primaryWorkspace.id = 1;
    primaryWorkspace.name = 'workspace1';
    primaryWorkspace.isPrimary = true;
    primaryWorkspace.organization = organization;
    primaryWorkspace.organizationId = 'uuid';

    const workspace = new Workspace();
    workspace.id = 2;
    workspace.name = 'workspace2';
    workspace.isPrimary = false;
    workspace.organization = organization;
    workspace.organizationId = 'uuid';

    jest
      .spyOn(createQueryBuilder, 'getOne')
      .mockResolvedValueOnce(organization);

    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(workspace);
    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(workspace);

    jest
      .spyOn(workspaceRepo, 'findOneBy')
      .mockResolvedValueOnce(primaryWorkspace);
    jest.spyOn(workspaceRepo, 'save').mockResolvedValueOnce(workspace);
    jest.spyOn(service, 'validateUniqueName').mockImplementation(null);
    jest
      .spyOn(service, 'validateUpdatePrimary')
      .mockImplementation(async (currentIsPrimary, isPrimaryToBeUpdated) => {
        if (isPrimaryToBeUpdated === false && currentIsPrimary === true) {
          throw new BadRequestException(
            `Workspace is primary and cannot be updated to false`,
          );
        }
      });

    jest.spyOn(workspaceRepo, 'update').mockResolvedValueOnce(null);

    expect(workspace.name).toEqual('workspace2');
    expect(workspace.isPrimary).toEqual(false);
    expect(workspace.organizationId).toEqual('uuid');

    try {
      await service.update(1, {
        name: 'workspace3',
        isPrimary: false,
        organizationId: 'uuid',
      });
    } catch (error) {
      expect(error).toBeInstanceOf(BadRequestException);
      expect(error.message).toEqual(
        'Workspace is primary and cannot be updated to false',
      );
    }
  });

  it('should update a not primary workspace to true when isPrimary is false on workspace when try to update a workspace', async () => {
    const organization = new Organization();
    organization.id = 'uuid';

    const primaryWorkspace = new Workspace();
    primaryWorkspace.id = 1;
    primaryWorkspace.name = 'workspace1';
    primaryWorkspace.isPrimary = true;
    primaryWorkspace.organization = organization;
    primaryWorkspace.organizationId = 'uuid';

    const workspace = new Workspace();
    workspace.id = 2;
    workspace.name = 'workspace2';
    workspace.isPrimary = false;
    workspace.organization = organization;
    workspace.organizationId = 'uuid';

    jest
      .spyOn(createQueryBuilder, 'getOne')
      .mockResolvedValueOnce(organization);

    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(workspace);
    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(workspace);
    jest
      .spyOn(workspaceRepo, 'findOneBy')
      .mockResolvedValueOnce(primaryWorkspace);
    jest.spyOn(service, 'validateUniqueName').mockImplementation(null);
    jest.spyOn(workspaceRepo, 'save').mockResolvedValueOnce(workspace);
    jest.spyOn(workspaceRepo, 'update').mockResolvedValueOnce(null);

    expect(workspace.name).toEqual('workspace2');
    expect(workspace.isPrimary).toEqual(false);
    expect(workspace.organizationId).toEqual('uuid');

    const updatedWorkspace = await service.update(2, {
      name: 'workspace3',
      isPrimary: true,
      organizationId: 'uuid',
    });

    expect(updatedWorkspace).toBeDefined();
    expect(updatedWorkspace).toBeInstanceOf(ReadWorkspaceDto);
    expect(updatedWorkspace.name).toEqual('workspace3');
    expect(updatedWorkspace.isPrimary).toEqual(true);
    expect(updatedWorkspace.organizationId).toEqual('uuid');

    // primary workspace should be updated to false
    expect(primaryWorkspace.isPrimary).toEqual(false);
  });

  it('should throw an exception if a workspace with the same name already exist when try to update', async () => {
    const organization = new Organization();
    organization.id = 'uuid';

    const workspace1 = new Workspace();
    workspace1.id = 1;
    workspace1.name = 'workspace1';
    workspace1.organizationId = organization.id;

    const workspace2 = new Workspace();
    workspace2.id = 2;
    workspace2.name = 'workspace2';
    workspace2.organizationId = organization.id;

    jest
      .spyOn(organizationRepository, 'findOneBy')
      .mockResolvedValueOnce(organization);

    jest.spyOn(workspaceRepo, 'query').mockResolvedValueOnce(workspace1);

    await expect(
      service.update(2, {
        name: 'workspace1',
        isPrimary: true,
        organizationId: organization.id,
      }),
    ).rejects.toThrow();
  });

  it('should throw an exception if a workspace with the same name already exist when try to create', async () => {
    const organization = new Organization();
    organization.id = 'uuid';

    const workspace = new Workspace();
    workspace.id = 1;
    workspace.name = 'workspace1';
    workspace.organizationId = organization.id;

    jest
      .spyOn(organizationRepository, 'findOneBy')
      .mockResolvedValueOnce(organization);

    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(workspace);

    await expect(
      service.create({
        name: 'workspace1',
        isPrimary: true,
        organizationId: organization.id,
        logoUrl: 'https://example.com/logo.png',
      }),
    ).rejects.toThrow();
  });

  it('should find all workspaces', async () => {
    const organization = new Organization();
    organization.id = 'uuid';

    const workspace1 = new Workspace();
    workspace1.id = 1;

    const workspace2 = new Workspace();
    workspace2.id = 2;

    const workspace3 = new Workspace();
    workspace3.id = 3;

    jest
      .spyOn(workspaceRepo, 'findAndCount')
      .mockResolvedValueOnce([[workspace1, workspace2, workspace3], 3]);

    const paginatedResultArray = await service.findAll({
      offset: 0,
      perPage: 10,
    });

    const readWorkspaceDtos = paginatedResultArray?.items;

    expect(paginatedResultArray).toBeDefined();
    expect(paginatedResultArray).toBeInstanceOf(PaginatedResultArray);
    expect(readWorkspaceDtos.length).toEqual(3);
    expect(readWorkspaceDtos[0]).toBeInstanceOf(ReadWorkspaceDto);
    expect(readWorkspaceDtos[0].id).toEqual(1);
    expect(readWorkspaceDtos[1]).toBeInstanceOf(ReadWorkspaceDto);
    expect(readWorkspaceDtos[1].id).toEqual(2);
    expect(readWorkspaceDtos[2]).toBeInstanceOf(ReadWorkspaceDto);
    expect(readWorkspaceDtos[2].id).toEqual(3);
  });

  /*
   * DL: Commenting this out until I can resolve the following error:
   * TypeError: Cannot read properties of undefined (reading 'createQueryBuilder')
   *
  it('should find all workspaces for a given user', async () => {
    const workspace1 = new Workspace();
    workspace1.id = 1;

    const workspace2 = new Workspace();
    workspace2.id = 2;

    const workspace3 = new Workspace();
    workspace3.id = 3;

    const spy = jest
      .spyOn(workspaceRepo, 'findAndCount')
      .mockResolvedValueOnce([[workspace1, workspace2, workspace3], 3]);

    const paginatedResultArray = await service.getWorkspacesByUserId(
      {
        offset: 0,
        perPage: 10,
      },
      1,
    );

    const readWorkspaceDtos = paginatedResultArray?.items;

    expect(paginatedResultArray).toBeDefined();
    expect(paginatedResultArray).toBeInstanceOf(PaginatedResultArray);
    expect(readWorkspaceDtos.length).toEqual(3);
    expect(readWorkspaceDtos[0]).toBeInstanceOf(ReadWorkspaceDto);
    expect(readWorkspaceDtos[0].id).toEqual(1);
    expect(readWorkspaceDtos[1]).toBeInstanceOf(ReadWorkspaceDto);
    expect(readWorkspaceDtos[1].id).toEqual(2);
    expect(readWorkspaceDtos[2]).toBeInstanceOf(ReadWorkspaceDto);
    expect(readWorkspaceDtos[2].id).toEqual(3);

    expect(spy).toBeCalledWith({
      where: { users: { id: 1 } },
      take: 10,
      skip: 0,
      order: { name: 'ASC', id: 'ASC' },
    });
  });
*/

  it('should find related workspaces', async () => {
    const testWorkspace = { id: 1, name: 'workspace1' };
    workspaceRepo.findOneBy = jest.fn().mockResolvedValueOnce(testWorkspace);
    workspaceRepo.find = jest.fn().mockResolvedValueOnce([testWorkspace]);
    const result = await service.getRelatedWorkspaces(1);
    expect(result).toEqual([
      { partnerId: testWorkspace.id, name: testWorkspace.name },
    ]);
  });

  it('should find related ABI workspaces', async () => {
    const testWorkspace = {
      id: 1,
      name: 'ABI - Peru - Corona Tropical - Equity',
    };
    const relatedWorkspaces = [
      testWorkspace,
      { id: 2, name: 'ABI - United Kingdom - Becks- Equity' },
      { id: 3, name: 'ABI - US - Hoop Tea - Equity' },
    ];

    workspaceRepo.findOneBy = jest.fn().mockResolvedValueOnce(testWorkspace);
    workspaceRepo.find = jest.fn().mockResolvedValueOnce(relatedWorkspaces);
    const result = await service.getRelatedWorkspaces(1);
    expect(result).toEqual(
      relatedWorkspaces.map((workspace) => ({
        partnerId: workspace.id,
        name: workspace.name,
      })),
    );
  });

  it('should get workspaces by organizationId and search', async () => {
    const sampleWorkspaces: any[] = [
      { id: 1, name: 'Workspace 1' },
      { id: 2, name: 'Workspace 2' },
      { id: 3, name: 'Workspace 3' },
    ];
    const paginationOptions = { offset: 0, perPage: 10 };
    const organizationId = 'organizationId';
    const searchParams = new SearchParamsDto('search keyword', 'bra,usa');
    const createQueryBuilder = {
      loadRelationCountAndMap: () => createQueryBuilder,
      innerJoinAndSelect: () => createQueryBuilder,
      leftJoinAndSelect: () => createQueryBuilder,
      where: () => createQueryBuilder,
      andWhere: () => createQueryBuilder,
      skip: () => createQueryBuilder,
      take: () => createQueryBuilder,
      orderBy: () => createQueryBuilder,
      addOrderBy: () => createQueryBuilder,
      getManyAndCount: () => [sampleWorkspaces, sampleWorkspaces.length],
    } as unknown as jest.Mocked<SelectQueryBuilder<Workspace>>;

    jest.spyOn(workspaceRepo, 'find').mockResolvedValueOnce(sampleWorkspaces);
    workspaceRepo.createQueryBuilder = jest.fn(() => {
      return createQueryBuilder;
    });

    const result = await service.getWorkspacesByOrganizationIdAndSearch(
      organizationId,
      paginationOptions,
      searchParams,
    );

    expect(result).toBeInstanceOf(PaginatedResultArray);
    expect(result.items).toEqual(
      sampleWorkspaces.map((workspace) => ({
        id: workspace.id,
        name: workspace.name,
        users: [],
      })),
    );
  });

  it('Throw exception for duplicate workspace names within the organization during creation', async () => {
    const organization = new Organization();
    organization.id = 'uuid';

    const workspace = new Workspace();
    workspace.id = 1;
    workspace.name = 'workspace1';
    workspace.organizationId = organization.id;

    jest
      .spyOn(organizationRepository, 'findOneBy')
      .mockResolvedValueOnce(organization);

    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(workspace);

    await expect(
      service.create({
        name: 'workspace1',
        isPrimary: true,
        organizationId: organization.id,
        logoUrl: 'https://example.com/logo.png',
      }),
    ).rejects.toThrow();
  });

  it('should return all workspaces for an organization ad account', async () => {
    setUpMock();
    const result =
      await service.grabAllWorkspacesFromAnAdAccountForAnOrganization(
        'test_id',
        'test_id',
        {},
      );
    expect(result).toBeInstanceOf(PaginatedResultArray);
  });

  const setUpMock = () => {
    workspaceRepo.createQueryBuilder = jest.fn(() => {
      return {
        where: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        offset: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([new Workspace()]),
        getRawMany: jest.fn().mockResolvedValue([
          {
            name: 'test',
            status: 'ACTIVE',
            dateCreated: new Date(),
          },
        ]),
        getCount: jest.fn().mockResolvedValue(1),
        // Add more mock methods if necessary
      } as unknown as jest.Mocked<SelectQueryBuilder<Workspace>>;
    });
    jest
      .spyOn(organizationService, 'doesOrganizationExist')
      .mockResolvedValue(true);
    jest
      .spyOn(adAccountService, 'getAdAccountsIdsFromPlatformAccountIds')
      .mockResolvedValue([1]);
    jest
      .spyOn(organizationAdAccountsService, 'getAdAccountIdsFromAnOrganization')
      .mockResolvedValue([1]);
    jest
      .spyOn(organizationAdAccountsService, 'findOrganizationsForAdAccount')
      .mockResolvedValue([
        {
          id: 'test_id',
          name: 'test',
          dateCreated: new Date(),
          lastUpdated: new Date(),
          workspaces: [new Workspace()],
          status: 'ACTIVE',
          sessionTimeoutMin: DEFAULT_SESSION_TIMEOUT_MIN,
        },
      ]);
  };
  it('should verify that a list of workspaces exist in an organization', async () => {
    const workspace = new Workspace();
    workspace.id = 1;
    jest.spyOn(workspaceRepo, 'find').mockResolvedValueOnce([workspace]);
    const nonEmptyResult = await service.verifyWorkspacesBelongToAnOrganization(
      'test_id',
      [1],
    );
    expect(nonEmptyResult).toEqual([1]);
  });
  it('should throw an error if the request is invalid', async () => {
    jest.spyOn(workspaceRepo, 'find').mockResolvedValueOnce([]);
    await service
      .verifyWorkspacesBelongToAnOrganization('test_id', [1])
      .catch((e) => expect(e).toBeInstanceOf(BadRequestException));
  });

  it('should return non duplicated workspaces', async () => {
    const workspace = new Workspace();
    workspace.id = 1;
    jest.spyOn(workspaceRepo, 'find').mockResolvedValueOnce([workspace]);
    const duplicatedResults =
      await service.verifyWorkspacesBelongToAnOrganization(
        'test_id',
        [1, 1, 1, 1],
      );
    expect(duplicatedResults).toEqual([1]);
  });
  describe('mapAdAccountsToAWorkspace', () => {
    const mockWorkspaceId = 1;
    const mockWorkspace = new Workspace();
    mockWorkspace.id = mockWorkspaceId;
    const mockAdAccountIds = [1, 2, 3];
    it('should create all workspace ad account maps if ad accounts are not currently mapped', async () => {
      const mockDatabase = [];
      jest
        .spyOn(adAccountService, 'createWorkspacePlatformAdAccountConnections')
        .mockImplementation(
          async (
            em: EntityManager,
            dtos: CreatePlatformAdAccountWorkspaceMapDto[],
          ) => {
            dtos.forEach((dto) => {
              mockDatabase.push({
                platformAdAccountId: dto,
                partnerId: mockWorkspaceId,
              });
            });
            return mockDatabase;
          },
        );
      const result = await service.connectPlatformAdAccountsToAWorkspace(
        mockWorkspace,
        mockAdAccountIds,
      );
      expect(result).toEqual(
        `Platform Ad Accounts successfully connected to workspace 1. Number of mappings created: 3`,
      );
      expect(mockDatabase.length).toEqual(3);
    });

    it('should create some workspace ad account maps if some ad accounts are currently mapped', async () => {
      const mockDatabase = [
        { platformAdAccountId: 1, partnerId: 1 },
        { platformAdAccountId: 2, partnerId: 1 },
      ];
      jest
        .spyOn(adAccountService, 'getPlatformAdAccountIdsConnectedToAWorkspace')
        .mockResolvedValueOnce([1, 2]);
      jest
        .spyOn(adAccountService, 'createWorkspacePlatformAdAccountConnections')
        .mockImplementation(async (_) => {
          const newMap = { platformAdAccountId: 3, partnerId: 1 };
          mockDatabase.push(newMap);
          return [newMap];
        });
      const result = await service.connectPlatformAdAccountsToAWorkspace(
        mockWorkspace,
        mockAdAccountIds,
      );
      expect(result).toEqual(
        `Platform Ad Accounts successfully connected to workspace 1. Number of mappings created: 1`,
      );
      expect(mockDatabase.length).toEqual(3);
    });

    it('should throw an exception if all ad accounts are currently mapped', async () => {
      adAccountService.getPlatformAdAccountIdsConnectedToAWorkspace = jest
        .fn()
        .mockResolvedValueOnce(mockAdAccountIds);
      service
        .connectPlatformAdAccountsToAWorkspace(mockWorkspace, mockAdAccountIds)
        .catch((e) => expect(e).toBeInstanceOf(BadRequestException));
    });
  });

  describe('deletePlatformAdAccountFromWorkspace', () => {
    const mockWorkspaceId = 3;
    const mockWorkspace = new Workspace();
    mockWorkspace.id = mockWorkspaceId;
    const mockAdAccountIds = [3, 4];

    it('should delete workspace ad accounts', async () => {
      let mockDatabase = [
        { platformAdAccountId: 3, partnerId: 3 },
        { platformAdAccountId: 4, partnerId: 3 },
      ];
      jest
        .spyOn(workspaceRepo, 'findOneBy')
        .mockResolvedValueOnce(new Workspace());
      adAccountService.deleteWorkspacePlatformAdAccountConnections = jest
        .fn()
        .mockImplementation(
          async (entityManager, workspaceId, adAccountIds) => {
            const deletedMaps = [
              { platformAdAccountId: 3, partnerId: 3 },
              { platformAdAccountId: 4, partnerId: 3 },
            ];
            mockDatabase = mockDatabase.filter(
              (map) => !adAccountIds.includes(map.platformAdAccountId),
            );
            return deletedMaps;
          },
        );
      const result = await service.disconnectPlatformAdAccountsToAWorkspace(
        mockWorkspace,
        mockAdAccountIds,
      );
      expect(result).toEqual(
        `Platform Ad Accounts successfully disconnected from workspace 3. Number of mappings deleted: 2`,
      );
      expect(mockDatabase.length).toEqual(0);
    });

    it('should delete workspace ad accounts when there is not mapping (204)', async () => {
      jest
        .spyOn(workspaceRepo, 'findOneBy')
        .mockResolvedValueOnce(new Workspace());
      adAccountService.deleteWorkspacePlatformAdAccountConnections = jest
        .fn()
        .mockResolvedValueOnce([]);
      const result = await service.disconnectPlatformAdAccountsToAWorkspace(
        mockWorkspace,
        mockAdAccountIds,
      );
      expect(result).toEqual(
        `Platform Ad Accounts successfully disconnected from workspace 3. Number of mappings deleted: 0`,
      );
    });
  });

  describe('areAdAccountsAbleToMapToWorkspace', () => {
    const mockWorkspaceId = 1;
    const mockWorkspace = new Workspace();
    mockWorkspace.id = mockWorkspaceId;
    const mockAdAccountIds = [1, 2, 3];
    it('should pass verification if ad account is apart of workspace org', async () => {
      const mockWorkspace = new Workspace();
      mockWorkspace.organizationId = 'test_id';
      jest
        .spyOn(workspaceRepo, 'findOneBy')
        .mockResolvedValueOnce(mockWorkspace);
      organizationAdAccountsService.countNumberOfOrganizationPlatformAdAccountConnections =
        jest.fn().mockResolvedValueOnce(3);
      await expect(
        service.arePlatformAdAccountsAbleToMapToAnOrganizationWorkspace(
          mockWorkspace,
          mockAdAccountIds,
        ),
      ).resolves.not.toThrow();
    });
    it('should throw exception if ad accounts are not linked to org', async () => {
      const mockWorkspace = new Workspace();
      mockWorkspace.organizationId = 'test_id';
      jest
        .spyOn(workspaceRepo, 'findOneBy')
        .mockResolvedValueOnce(mockWorkspace);
      organizationAdAccountsService.countNumberOfOrganizationPlatformAdAccountConnections =
        jest.fn().mockResolvedValueOnce(0);
      await service
        .arePlatformAdAccountsAbleToMapToAnOrganizationWorkspace(
          mockWorkspace,
          mockAdAccountIds,
        )
        .catch((e) => expect(e).toBeInstanceOf(ForbiddenException));
    });
  });

  describe('createAccountName', () => {
    // Mock generateFourRandomDigits
    beforeEach(() => {
      jest.spyOn(service, 'generateFourRandomDigits').mockReturnValue(1234);
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should create an account name with the correct format', () => {
      const name = 'User';
      const organizationId = '123';
      const expectedSuffix = ` - ${organizationId}-1234`;
      const accountName = service.createAccountName(name, organizationId);
      expect(accountName.endsWith(expectedSuffix)).toBeTruthy();
    });

    it('should truncate the name if it exceeds the maximum length', () => {
      const longName = 'a'.repeat(200); // Longer than the max allowed length
      const organizationId = '123';
      const accountName = service.createAccountName(longName, organizationId);
      expect(accountName.length).toBe(190);
    });
  });

  describe('getAllLightweight', () => {
    afterEach(() => jest.restoreAllMocks());

    const workspaces = [
      {
        id: 1,
        name: 'Test 1',
        isPrimary: true,
        organizationId: 'xxxx',
      },
      {
        id: 2,
        name: 'Test 2',
        isPrimary: false,
        organizationId: 'xxxx',
      },
      {
        id: 3,
        name: 'Test 3',
        isPrimary: false,
        organizationId: 'xxxx',
      },
    ];

    const lightWeightWorkspaces = [
      {
        id: 1,
        name: 'Test 1',
      },
      {
        id: 2,
        name: 'Test 2',
      },
      {
        id: 3,
        name: 'Test 3',
      },
    ];

    it('should return all workspaces with only id and name', async () => {
      jest
        .spyOn(workspaceRepo, 'findBy')
        .mockResolvedValue(workspaces as Workspace[]);

      await expect(service.getAllLightweight('xxxx')).resolves.toEqual(
        lightWeightWorkspaces,
      );
    });
  });

  describe('validateUserWorkspacesWithinOrganization', () => {
    it('should return true for org admin and all workspaces in organization', async () => {
      jest
        .spyOn(organizationService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce('ORG_ADMIN');
      jest.spyOn(workspaceRepo, 'countBy').mockResolvedValueOnce(3);
      const response = await service.validateUserWorkspacesWithinOrganization(
        'mock-org-id',
        1111,
        [1, 2, 3],
      );
      expect(response).toStrictEqual({
        success: true,
        message: 'User has access to workspaces within organization',
      });
    });

    it('should return true for org standard user and user linked to workspaces', async () => {
      jest
        .spyOn(organizationService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce('ORG_STANDARD');
      workspaceRepo.createQueryBuilder = jest.fn(() => {
        return {
          createQueryBuilder: jest.fn().mockReturnThis(),
          leftJoin: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getCount: jest.fn().mockResolvedValueOnce(3),
        } as unknown as jest.Mocked<SelectQueryBuilder<Workspace>>;
      });

      const response = await service.validateUserWorkspacesWithinOrganization(
        'mock-org-id',
        1111,
        [1, 2, 3],
      );
      expect(response).toStrictEqual({
        success: true,
        message: 'User has access to workspaces within organization',
      });
    });

    it('should return false for org standard user missing access to workspaces', async () => {
      jest
        .spyOn(organizationService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce('ORG_STANDARD');
      workspaceRepo.createQueryBuilder = jest.fn(() => {
        return {
          createQueryBuilder: jest.fn().mockReturnThis(),
          leftJoin: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getCount: jest.fn().mockResolvedValueOnce(0),
        } as unknown as jest.Mocked<SelectQueryBuilder<Workspace>>;
      });

      const response = await service.validateUserWorkspacesWithinOrganization(
        'mock-org-id',
        1111,
        [1, 2, 3],
      );
      expect(response).toStrictEqual({
        success: false,
        message:
          'User does not have access to all provided workspaces within organization mock-org-id',
      });
    });

    it('should return false for org standard user when not all workspaces in organization', async () => {
      jest
        .spyOn(organizationService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce('ORG_ADMIN');
      jest.spyOn(workspaceRepo, 'countBy').mockResolvedValueOnce(1);
      const response = await service.validateUserWorkspacesWithinOrganization(
        'mock-org-id',
        1111,
        [1, 2, 3],
      );
      expect(response).toStrictEqual({
        success: false,
        message:
          'User does not have access to all provided workspaces within organization mock-org-id',
      });
    });
  });
});
