import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { PlatformAdAccount } from '../../../ad-accounts/entities/ad-account.entity';
import { Workspace } from '../../entities/workspace.entity';

@Entity('workspace_platform_ad_account_map')
export class WorkspaceAdAccountMap {
  @AutoMap()
  @PrimaryColumn({
    name: 'partner_id',
  })
  workspaceId: number;

  @AutoMap()
  @PrimaryColumn({
    name: 'platform_ad_account_id',
  })
  platformAdAccountId: number;

  @ManyToOne(
    () => PlatformAdAccount,
    (platformAdAccount) => platformAdAccount.id,
  )
  @JoinColumn({ name: 'platform_ad_account_id' })
  platformAdAccount: PlatformAdAccount;

  @ManyToOne(() => Workspace, (workspace) => workspace.id)
  @JoinColumn({ name: 'partner_id' })
  workspace: Workspace;
}
