import { CreateWorkspacesAdAccountMapDto } from './dtos/create.workspaces-ad-account-map.dto';
import { WorkspaceAdAccountMap } from './entities/workspace-ad-account-map.entities';
import { ReadWorkspaceAdAccountMapDto } from './dtos/read.workspace-ad-account-map.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { ReadPlatformAdAccountDto } from '../../ad-accounts/dto/read-platform-ad-account.dto';
import { ReadWorkspacesAdAccountDto } from './dtos/read.workspace-ad-account.dto';
import { GetWorkspacesPlatformAdAccountsDto } from './dtos/get-workspaces-platform-ad-accounts.dto';
import { PlatformAdAccount } from '../../ad-accounts/entities/ad-account.entity';
import { User } from '../../entities/user.entity';
import { Workspace } from '../entities/workspace.entity';
import { PlatformAdAccountImportStatus } from 'src/ad-accounts/entities/platform-ad-account-import-status.entity';
import {
  ImportState,
  ImportStatus,
  ImportStatusForBFF,
} from 'src/common/constants/constants';

const currentDate = new Date('2024-03-04');

export const mockCreateWorkspaceAdAccountMapDto: CreateWorkspacesAdAccountMapDto =
  {
    workspaceId: 1,
    platformAdAccountId: 2,
  };

export const mockEnabledWorkspaceAdAccountMap: WorkspaceAdAccountMap = {
  workspaceId: 1,
  platformAdAccountId: 2,
  workspace: new Workspace(),
  platformAdAccount: new PlatformAdAccount(),
};

export const mockEnabledWorkspaceAdAccountMap2: WorkspaceAdAccountMap = {
  workspaceId: 1,
  platformAdAccountId: 4,
  workspace: new Workspace(),
  platformAdAccount: new PlatformAdAccount(),
};

export const mockDisabledWorkspaceAdAccountMap: WorkspaceAdAccountMap = {
  workspaceId: 1,
  platformAdAccountId: 3,
  workspace: new Workspace(),
  platformAdAccount: new PlatformAdAccount(),
};

export const mockEnabledReadWorkspaceAdAccountMapDto: ReadWorkspaceAdAccountMapDto =
  {
    workspaceId: 1,
    platformAdAccountId: 2,
  };

export const mockDisabledReadWorkspaceAdAccountMapDto: ReadWorkspaceAdAccountMapDto =
  {
    workspaceId: 1,
    platformAdAccountId: 3,
  };

export const mockPaginationOptions: PaginationOptions = {
  perPage: 10,
  offset: 0,
};

export const mockReadAdAccountDto: ReadPlatformAdAccountDto = {
  id: 1,
  platform: 'FACEBOOK',
  platformAccountName: 'Test Account',
  platformAccountId: '*********',
  canAccess: 1,
  dateCreated: currentDate,
  lastUpdated: currentDate,
  processingCompleted: 1,
  processingCompletedDate: currentDate,
  lastSuccessfulProcessingDate: currentDate,
};

export const mockReadAdAccountDto2: ReadPlatformAdAccountDto = {
  id: 4,
  platform: 'FACEBOOK',
  platformAccountName: 'Test Account 2',
  platformAccountId: '*********',
  canAccess: 1,
  dateCreated: currentDate,
  lastUpdated: currentDate,
  processingCompleted: 0,
  processingCompletedDate: null,
  lastSuccessfulProcessingDate: null,
};

export const readWorkspacesAdAccountDto: ReadWorkspacesAdAccountDto = {
  platform: 'FACEBOOK',
  platformAccountName: 'Test Account',
  platformAccountId: '*********',
  dateCreated: currentDate,
  processingCompleted: true,
  processingCompletedDate: currentDate,
  lastSuccessfulProcessingDate: currentDate,
  connected: true,
};

export const mockWorkspaces = [123, 456, 789];

export const mockGetWorkspacesPlatformAdAccountsRequest: GetWorkspacesPlatformAdAccountsDto =
  {
    platform: 'FACEBOOK',
    workspaces: mockWorkspaces,
  };

export const mockPlatformAdAccountImportStatus: PlatformAdAccountImportStatus =
  {
    id: 'xxx',
    platformAdAccount: null,
    organization: null,
    active: false,
    status: ImportStatus.PROCESSING,
    reason: null,
    importId: '',
    rebuildRequestId: 0,
    person: new User(),
    dateCreated: currentDate,
  };

export const mockPlatformAdAccount: PlatformAdAccount = {
  id: 1,
  userId: 1,
  platform: 'FACEBOOK',
  platformUserId: '*********',
  platformOrganizationId: '',
  platformAccountId: '*********',
  currencyCode: 'USD',
  platformAccountName: 'Test Account',
  canAccess: 1,
  processingCompleted: 1,
  processingCompletedDate: currentDate,
  importV3Enabled: 1,
  dateCreated: currentDate,
  lastUpdated: currentDate,
  lastImportStartDate: currentDate,
  lastImportCompleteDate: currentDate,
  lastImportSuccessDate: currentDate,
  lastImportStatus: ImportState.SUCCESS,
  accountImportStatus: mockPlatformAdAccountImportStatus,
  importActive: 1,
  PlatformAdAccountToWorkspace: [],
  organizationPlatformAdAccountMap: [],
  organizationAdAccountUserPermissions: [],
  user: new User(),
  platformAdAccountMarketMap: [],
  platformAdAccountBrandMap: [],
  workspaceAdAccountMap: [],
  platformAdAccountSequentialFailures: [],
  importStatuses: [],
};

export const mockWorkspaceEnabledPlatformAdAccountMap: WorkspaceAdAccountMap = {
  workspaceId: 1,
  platformAdAccountId: 2,
  workspace: new Workspace(),
  platformAdAccount: mockPlatformAdAccount,
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
export const mockReadPlatformAdAccountDto: ReadPlatformAdAccountDto = {
  id: 1,
  platform: 'FACEBOOK',
  platformAccountId: '*********',
  platformAccountName: 'Test Account',
  canAccess: 1,
  processingCompleted: 1,
  processingCompletedDate: currentDate,
  importStatus: ImportStatusForBFF.PROCESSING,
  dateCreated: currentDate,
  lastUpdated: currentDate,
  lastSuccessfulProcessingDate: currentDate,
  lastImportStatus: ImportState.SUCCESS,
};

export const mockUniqueIds = [1, 2, 3, 4];
