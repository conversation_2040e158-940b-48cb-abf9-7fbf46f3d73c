import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceAdAccountService } from './workspace-ad-account.service';
import { AdAccountsService } from '../../ad-accounts/ad-accounts.service';
import { createMapper } from '@automapper/core';
import { getMapperToken } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { Repository } from 'typeorm';
import { WorkspaceAdAccountMap } from './entities/workspace-ad-account-map.entities';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  mockCreateWorkspaceAdAccountMapDto,
  mockPaginationOptions,
  mockReadAdAccountDto,
  mockEnabledReadWorkspaceAdAccountMapDto,
  mockEnabledWorkspaceAdAccountMap,
  readWorkspacesAdAccountDto,
  mockDisabledWorkspaceAdAccountMap,
  mockEnabledWorkspaceAdAccountMap2,
  mockGetWorkspacesPlatformAdAccountsRequest,
  mockWorkspaces,
  mockWorkspaceEnabledPlatformAdAccountMap,
  mockReadAdAccountDto2,
  mockReadPlatformAdAccountDto,
  mockUniqueIds,
} from './workspace-ad-account.mock';
import { WorkspaceAdAccountProfile } from './mapper/workspace-ad-account.profile';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadWorkspacesAdAccountDto } from './dtos/read.workspace-ad-account.dto';
import { AdAccountsProfile } from '../../ad-accounts/mapper/ad-accounts.profile';

describe('WorkspacesAdAccountsService', () => {
  let service: WorkspaceAdAccountService;
  let repository: Repository<WorkspaceAdAccountMap>;
  let adAccountService: AdAccountsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceAdAccountService,
        WorkspaceAdAccountProfile,
        AdAccountsProfile,
        {
          provide: AdAccountsService,
          useFactory: async () => ({
            findWorkspaceConnectedAdAccounts: await jest
              .fn()
              .mockResolvedValue(
                new PaginatedResultArray([mockReadAdAccountDto], 1),
              ),
          }),
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
        {
          provide: getRepositoryToken(WorkspaceAdAccountMap),
          useClass: Repository,
        },
      ],
    }).compile();
    service = module.get<WorkspaceAdAccountService>(WorkspaceAdAccountService);
    adAccountService = module.get<AdAccountsService>(AdAccountsService);
    repository = module.get<Repository<WorkspaceAdAccountMap>>(
      getRepositoryToken(WorkspaceAdAccountMap),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should throw an error if the mapping already exists', async () => {
      jest.spyOn(repository, 'exist').mockResolvedValue(true);
      await expect(
        service.create(mockCreateWorkspaceAdAccountMapDto),
      ).rejects.toThrowError('Mapping already exists');
    });
    it('should create a new mapping', async () => {
      jest.spyOn(repository, 'exist').mockResolvedValue(false);
      jest
        .spyOn(repository, 'save')
        .mockResolvedValue(mockEnabledWorkspaceAdAccountMap);
      const mockReadDto = await service.create(
        mockCreateWorkspaceAdAccountMapDto,
      );
      expect(mockReadDto).toEqual(mockEnabledReadWorkspaceAdAccountMapDto);
    });
  });

  describe('find', () => {
    it('should return null if no mapping exists', async () => {
      jest.spyOn(repository, 'findOneBy').mockResolvedValue(null);
      const result = await service.find(1, 1);
      expect(result).toEqual(null);
    });
    it('should return a mapping if one exists', async () => {
      jest
        .spyOn(repository, 'findOneBy')
        .mockResolvedValue(mockEnabledWorkspaceAdAccountMap);
      const result = await service.find(1, 1);
      expect(result).toEqual(mockEnabledReadWorkspaceAdAccountMapDto);
    });
  });

  describe('update', () => {
    it('should throw an error if the mapping does not exist', async () => {
      jest.spyOn(repository, 'exist').mockResolvedValue(false);
      await expect(
        service.update(mockCreateWorkspaceAdAccountMapDto),
      ).rejects.toThrowError('Mapping does not exist');
    });
    it('should update the mapping', async () => {
      jest.spyOn(repository, 'exist').mockResolvedValue(true);
      jest
        .spyOn(repository, 'save')
        .mockResolvedValue(mockEnabledWorkspaceAdAccountMap);
      const result = await service.update(mockCreateWorkspaceAdAccountMapDto);
      expect(result).toEqual(mockEnabledReadWorkspaceAdAccountMapDto);
    });
  });

  describe('findAllAdAccountsForWorkspace', () => {
    it('should return an empty array if no mappings exist', async () => {
      jest
        .spyOn(adAccountService, 'findWorkspaceConnectedAdAccounts')
        .mockResolvedValue(new PaginatedResultArray([], 0));
      const result = (await service.findAllAdAccountsForWorkspace(
        1,
        mockPaginationOptions,
        {},
      )) as PaginatedResultArray<ReadWorkspacesAdAccountDto>;
      expect(result.items).toEqual([]);
      expect(result.totalCount).toEqual(0);
    });

    it('should return an array of enabled mappings', async () => {
      jest
        .spyOn(repository, 'find')
        .mockResolvedValue([
          mockEnabledWorkspaceAdAccountMap,
          mockDisabledWorkspaceAdAccountMap,
        ]);
      const result = (await service.findAllAdAccountsForWorkspace(
        1,
        mockPaginationOptions,
        {},
      )) as PaginatedResultArray<ReadWorkspacesAdAccountDto>;
      expect(result.items).toEqual([readWorkspacesAdAccountDto]);
      expect(result.totalCount).toEqual(1);
    });

    it('should return an array of mappings if they exist', async () => {
      jest
        .spyOn(repository, 'find')
        .mockResolvedValue([mockEnabledWorkspaceAdAccountMap]);
      const result = (await service.findAllAdAccountsForWorkspace(
        1,
        mockPaginationOptions,
        {},
      )) as PaginatedResultArray<ReadWorkspacesAdAccountDto>;
      expect(result.items).toEqual([readWorkspacesAdAccountDto]);
      expect(result.totalCount).toEqual(1);
    });

    it('should return a mapping even if there are no rebuild requests for an ad account', async () => {
      jest
        .spyOn(repository, 'find')
        .mockResolvedValue([
          mockEnabledWorkspaceAdAccountMap,
          mockEnabledWorkspaceAdAccountMap2,
        ]);
      jest
        .spyOn(adAccountService, 'findWorkspaceConnectedAdAccounts')
        .mockResolvedValue(
          new PaginatedResultArray(
            [mockReadAdAccountDto, mockReadAdAccountDto2],
            2,
          ),
        );
      const result = (await service.findAllAdAccountsForWorkspace(
        1,
        mockPaginationOptions,
        {},
      )) as PaginatedResultArray<ReadWorkspacesAdAccountDto>;
      expect(result.items.length).toEqual(2);
      expect(result.items[1].processingCompleted).toBeFalsy();
      expect(result.items[1].processingCompletedDate).toBeNull();
      expect(result.items[1].lastSuccessfulProcessingDate).toBeNull();
      expect(result.items[0].processingCompleted).toBeTruthy();
      expect(result.items[0].processingCompletedDate).not.toBeNull();
      expect(result.items[0].lastSuccessfulProcessingDate).not.toBeNull();
    });
  });

  describe('findAdAccountsForWorkspacesAndPlatform', () => {
    it('should return a paginated array of enabled platform ad accounts', async () => {
      // Mock the query builder and its methods
      const mockQueryBuilder = {
        innerJoinAndSelect: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest
          .fn()
          .mockResolvedValue([mockWorkspaceEnabledPlatformAdAccountMap]),
        getRawMany: jest.fn().mockResolvedValue(mockUniqueIds),
        select: jest.fn().mockReturnThis(),
      };

      jest
        .spyOn(repository, 'createQueryBuilder')
        .mockReturnValue(mockQueryBuilder as any);

      const result = await service.findAdAccountsForWorkspacesAndPlatform(
        mockGetWorkspacesPlatformAdAccountsRequest,
        { offset: 20, perPage: 10 },
      );

      expect(mockQueryBuilder.innerJoinAndSelect).toHaveBeenCalledWith(
        'workspaceAdAccountMap.platformAdAccount',
        'platformAdAccount',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'workspaceAdAccountMap.workspaceId IN (:...workspaces)',
        { workspaces: mockWorkspaces },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'platformAdAccount.platform = :platform',
        { platform: 'FACEBOOK' },
      );

      expect(result.items).toEqual([mockReadPlatformAdAccountDto]);
      expect(result.totalCount).toEqual(mockUniqueIds.length);
    });
  });
});
