import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceAdAccountController } from './workspace-ad-account.controller';
import { WorkspaceAdAccountService } from './workspace-ad-account.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { WorkspaceAdAccountMap } from './entities/workspace-ad-account-map.entities';
import { Repository } from 'typeorm';
import { WorkspaceAdAccountProfile } from './mapper/workspace-ad-account.profile';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { AdAccountsService } from '../../ad-accounts/ad-accounts.service';
import { PlatformAdAccount } from '../../ad-accounts/entities/ad-account.entity';
import { PlatformAdAccountToWorkspace } from '../../ad-accounts/entities/ad-account-workspace-map.entity';
import { OrganizationPlatformAdAccountMap } from '../../ad-accounts/entities/organization-platform-ad-account-map.entity';
import { WorkspaceService } from '../workspaces.service';

describe('AdAccountsController', () => {
  let controller: WorkspaceAdAccountController;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspaceAdAccountController],
      providers: [
        WorkspaceAdAccountService,
        WorkspaceAdAccountProfile,
        AdAccountsService,
        {
          provide: getRepositoryToken(WorkspaceAdAccountMap),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccount),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountToWorkspace),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(OrganizationPlatformAdAccountMap),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    })
      .overrideProvider(WorkspaceService)
      .useValue({})
      .overrideProvider(AdAccountsService)
      .useValue({})
      .compile();

    controller = module.get<WorkspaceAdAccountController>(
      WorkspaceAdAccountController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
