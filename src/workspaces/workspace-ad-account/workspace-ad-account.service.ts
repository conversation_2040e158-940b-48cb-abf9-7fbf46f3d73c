import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WorkspaceAdAccountMap } from './entities/workspace-ad-account-map.entities';
import { Repository } from 'typeorm';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { CreateWorkspacesAdAccountMapDto } from './dtos/create.workspaces-ad-account-map.dto';
import { AdAccountsService } from '../../ad-accounts/ad-accounts.service';
import { ReadPlatformAdAccountDto } from '../../ad-accounts/dto/read-platform-ad-account.dto';
import { ReadWorkspacesAdAccountDto } from './dtos/read.workspace-ad-account.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadWorkspaceAdAccountMapDto } from './dtos/read.workspace-ad-account-map.dto';
import { UpdateWorkspaceAdAccountMapDto } from './dtos/update.workspace-ad-account-map.dto';
import { GetWorkspacesPlatformAdAccountsDto } from './dtos/get-workspaces-platform-ad-accounts.dto';
import { PlatformAdAccount } from '../../ad-accounts/entities/ad-account.entity';
import { TRUE_VALUE_MYSQL } from './workspace-ad-account.contants';
import { FilterAdAccountRequestDto } from './dtos/filter-ad-account-request.dto';
import {
  AdAccountMapType,
  AdAccountMapTypeProperty,
} from './constants/workspace-ad-accounts-filter.constants';
import { AccountSearchParamsDto } from '../../ad-accounts/dto/ad-account-search-params.dto';

@Injectable()
export class WorkspaceAdAccountService {
  constructor(
    @InjectRepository(WorkspaceAdAccountMap)
    private readonly workspaceAdAccountRepository: Repository<WorkspaceAdAccountMap>,
    @InjectMapper()
    private readonly mapper: Mapper,
    private readonly adAccountService: AdAccountsService,
  ) {}

  /**
   * Create a new workspace ad account map. If the mapping already exists, an error will be thrown.
   * @param dto
   * @returns {Promise<ReadWorkspacesAdAccountDto>}
   */
  async create(dto: CreateWorkspacesAdAccountMapDto) {
    const doesMappingExist = await this.mappingExists(
      dto.workspaceId,
      dto.platformAdAccountId,
    );
    if (doesMappingExist) {
      throw new Error('Mapping already exists');
    }
    const entity = await this.workspaceAdAccountRepository.save(
      this.mapper.map(
        dto,
        CreateWorkspacesAdAccountMapDto,
        WorkspaceAdAccountMap,
      ),
    );
    return this.mapper.map(
      entity,
      WorkspaceAdAccountMap,
      ReadWorkspaceAdAccountMapDto,
    );
  }

  /**
   * Find a workspace ad account map by workspace id and platform ad account id.
   * If no mapping exists, null will be returned.
   * @param workspaceId
   * @param platformAdAccountId
   * @returns {Promise<ReadWorkspacesAdAccountDto>}
   */
  async find(workspaceId: number, platformAdAccountId: number) {
    const entity = await this.workspaceAdAccountRepository.findOneBy({
      workspaceId,
      platformAdAccountId,
    });
    if (!entity) {
      return null;
    }
    return this.mapper.map(
      entity,
      WorkspaceAdAccountMap,
      ReadWorkspaceAdAccountMapDto,
    );
  }

  /**
   * Find all ad accounts for a workspace. If PaginationOptions are provided, the result will be paginated.
   * Else if PaginationOptions are not provided, the result will limit to 10 entities in the Array.
   * @param workspaceId
   * @param pagination
   * @param searchParams search/sort parameters for filtering ad accounts
   * @returns {Promise<PaginatedResultArray<ReadWorkspacesAdAccountDto>>}
   */
  async findAllAdAccountsForWorkspace(
    workspaceId: number,
    pagination: PaginationOptions,
    searchParams: AccountSearchParamsDto,
  ) {
    const adAccountsResult =
      await this.adAccountService.findWorkspaceConnectedAdAccounts(
        workspaceId,
        pagination,
        searchParams,
      );
    const connectedAdAccounts = adAccountsResult.items;
    const connectedAdAccountListSize = adAccountsResult.totalCount;
    const readDto = this.mapper.mapArray(
      connectedAdAccounts,
      ReadPlatformAdAccountDto,
      ReadWorkspacesAdAccountDto,
    );
    return new PaginatedResultArray(readDto, connectedAdAccountListSize);
  }

  /**
   * Find all unique connected ad accounts for workspaces and platform. If PaginationOptions are provided, the result will be paginated.
   * Else if PaginationOptions are not provided, the result will limit to 10 entities in the Array.
   * @param getWorkspacesPlatformAdAccountsRequest
   * @param pagination
   * @returns {Promise<PaginatedResultArray<ReadPlatformAdAccountDto>>}
   */
  async findAdAccountsForWorkspacesAndPlatform(
    getWorkspacesPlatformAdAccountsRequest: GetWorkspacesPlatformAdAccountsDto,
    pagination: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadPlatformAdAccountDto>> {
    const workspaceAdAccountMaps = await this.getWorkspaceAdAccountMaps(
      getWorkspacesPlatformAdAccountsRequest,
      pagination,
    );

    const uniqueIds =
      await this.getUniqueIdsForFindAdAccountsForWorkspaceAndPlatform(
        getWorkspacesPlatformAdAccountsRequest,
      );
    const totalCount = uniqueIds.length;

    const platformAdAccounts = workspaceAdAccountMaps.map(
      (map) => map.platformAdAccount,
    );

    const platformAdAccountDtos: ReadPlatformAdAccountDto[] =
      this.mapper.mapArray(
        platformAdAccounts,
        PlatformAdAccount,
        ReadPlatformAdAccountDto,
      );

    return new PaginatedResultArray(platformAdAccountDtos, totalCount);
  }

  async getWorkspaceAdAccountMaps(
    getWorkspacesPlatformAdAccountsRequest: GetWorkspacesPlatformAdAccountsDto,
    pagination: PaginationOptions,
  ) {
    const { workspaces, platform } = getWorkspacesPlatformAdAccountsRequest;
    // If this ever needs to be changed, the logic below to return total count will need to be updated
    // the same way
    const queryBuilder = this.workspaceAdAccountRepository
      .createQueryBuilder('workspaceAdAccountMap')
      .innerJoinAndSelect(
        'workspaceAdAccountMap.platformAdAccount',
        'platformAdAccount',
      )
      .where('workspaceAdAccountMap.workspaceId IN (:...workspaces)', {
        workspaces,
      })
      .andWhere('platformAdAccount.canAccess = :canAccess', {
        canAccess: TRUE_VALUE_MYSQL,
      })
      .groupBy('workspaceAdAccountMap.platformAdAccountId') // Ensures only unique ad accounts
      .orderBy('workspaceAdAccountMap.platformAdAccountId')
      .skip(pagination.offset)
      .take(pagination.perPage);

    if (platform) {
      queryBuilder.andWhere('platformAdAccount.platform = :platform', {
        platform,
      });
    }

    return queryBuilder.getMany();
  }

  getUniqueIdsForFindAdAccountsForWorkspaceAndPlatform(
    getWorkspacesPlatformAdAccountsRequest: GetWorkspacesPlatformAdAccountsDto,
  ) {
    const { workspaces, platform } = getWorkspacesPlatformAdAccountsRequest;
    const queryBuilder = this.workspaceAdAccountRepository
      .createQueryBuilder('workspaceAdAccountMap')
      .innerJoin('workspaceAdAccountMap.platformAdAccount', 'platformAdAccount')
      .where('workspaceAdAccountMap.workspaceId IN (:...workspaces)', {
        workspaces,
      })
      .andWhere('platformAdAccount.canAccess = :canAccess', {
        canAccess: TRUE_VALUE_MYSQL,
      })
      .groupBy('workspaceAdAccountMap.platformAdAccountId')
      .select('workspaceAdAccountMap.platformAdAccountId');

    if (platform) {
      queryBuilder.andWhere('platformAdAccount.platform = :platform', {
        platform,
      });
    }

    return queryBuilder.getRawMany();
  }

  async filterPlatformAdAccountsByBrandsOrMarkets(
    filterAdAccountRequestDto: FilterAdAccountRequestDto,
    paginationOptions: PaginationOptions,
  ) {
    const filterType = filterAdAccountRequestDto.brandIds
      ? AdAccountMapType.BRAND
      : AdAccountMapType.MARKET;

    const filteredWorkspaceAdAccountMaps =
      await this.filterPlatformAdAccountsByWorkspaceIdsAndType(
        filterAdAccountRequestDto,
        filterType,
        paginationOptions,
      );

    const count =
      await this.getCountForFilteredPlatformAdAccountsByWorkspaceIdsAndType(
        filterAdAccountRequestDto,
        filterType,
      );

    const platformAdAccounts = filteredWorkspaceAdAccountMaps.map(
      (map) => map.platformAdAccount,
    );

    const platformAdAccountDtos: ReadPlatformAdAccountDto[] =
      this.mapper.mapArray(
        platformAdAccounts,
        PlatformAdAccount,
        ReadPlatformAdAccountDto,
      );

    return new PaginatedResultArray(platformAdAccountDtos, count);
  }

  async filterPlatformAdAccountsByWorkspaceIdsAndType(
    filterAdAccountRequestDto: FilterAdAccountRequestDto,
    mapType: AdAccountMapType,
    paginationOptions: PaginationOptions,
  ) {
    const { brandIds, isoCodes, adAccountIds, workspaceIds } =
      filterAdAccountRequestDto;
    const filterValues = brandIds || isoCodes;
    const mapProperty =
      mapType === AdAccountMapType.BRAND
        ? AdAccountMapTypeProperty.BRAND
        : AdAccountMapTypeProperty.MARKET;

    return this.workspaceAdAccountRepository
      .createQueryBuilder('workspaceAdAccountMap')
      .innerJoinAndSelect(
        'workspaceAdAccountMap.platformAdAccount',
        'platformAdAccount',
      )
      .innerJoinAndSelect(
        `platformAdAccount.${mapType}`,
        'platformAdAccountMap',
      )
      .innerJoinAndSelect(`platformAdAccountMap.${mapProperty}`, 'mapType')
      .where('workspaceAdAccountMap.workspaceId IN (:...workspaceIds)', {
        workspaceIds,
      })
      .andWhere(`platformAdAccountMap.${mapProperty}  IN (:...filterValues)`, {
        filterValues,
      })
      .andWhere('platformAdAccount.platformAccountId IN (:...adAccountIds)', {
        adAccountIds,
      })
      .andWhere('platformAdAccount.canAccess = :canAccess', {
        canAccess: TRUE_VALUE_MYSQL,
      })
      .groupBy('workspaceAdAccountMap.platformAdAccountId')
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .getMany();
  }

  async getCountForFilteredPlatformAdAccountsByWorkspaceIdsAndType(
    filterAdAccountRequestDto: FilterAdAccountRequestDto,
    mapType: AdAccountMapType,
  ) {
    const { brandIds, isoCodes, adAccountIds, workspaceIds } =
      filterAdAccountRequestDto;
    const filterValues = brandIds || isoCodes;
    const mapProperty =
      mapType === AdAccountMapType.BRAND
        ? AdAccountMapTypeProperty.BRAND
        : AdAccountMapTypeProperty.MARKET;

    const workspaceAdAccountMaps = await this.workspaceAdAccountRepository
      .createQueryBuilder('workspaceAdAccountMap')
      .innerJoin('workspaceAdAccountMap.platformAdAccount', 'platformAdAccount')
      .innerJoin(`platformAdAccount.${mapType}`, 'platformAdAccountMap')
      .where('workspaceAdAccountMap.workspaceId IN (:...workspaceIds)', {
        workspaceIds,
      })
      .andWhere(`platformAdAccountMap.${mapProperty} IN (:...filterValues)`, {
        filterValues,
      })
      .andWhere('platformAdAccount.platformAccountId IN (:...adAccountIds)', {
        adAccountIds,
      })
      .andWhere('platformAdAccount.canAccess = :canAccess', {
        canAccess: TRUE_VALUE_MYSQL,
      })
      .groupBy('workspaceAdAccountMap.platformAdAccountId')
      .select('workspaceAdAccountMap.platformAdAccountId')
      .getMany();

    return workspaceAdAccountMaps.length;
  }

  /**
   * Update a workspace ad account map. If the mapping does not exist, an error will be thrown.
   * @param dto
   * @returns {Promise<ReadWorkspacesAdAccountDto>}
   */
  async update(dto: UpdateWorkspaceAdAccountMapDto) {
    const mapExists = await this.mappingExists(
      dto.workspaceId,
      dto.platformAdAccountId,
    );
    if (!mapExists) {
      throw new Error('Mapping does not exist');
    }
    const entity = this.mapper.map(
      dto,
      UpdateWorkspaceAdAccountMapDto,
      WorkspaceAdAccountMap,
    );
    return this.mapper.map(
      await this.workspaceAdAccountRepository.save(entity),
      WorkspaceAdAccountMap,
      ReadWorkspaceAdAccountMapDto,
    );
  }

  /**
   * Check if a mapping exists for a workspace and platform ad account.
   * @param workspaceId
   * @param platformAdAccountId
   * @returns {Promise<boolean>}
   */
  async mappingExists(workspaceId: number, platformAdAccountId: number) {
    return await this.workspaceAdAccountRepository.exist({
      where: {
        workspaceId,
        platformAdAccountId,
      },
    });
  }
}
