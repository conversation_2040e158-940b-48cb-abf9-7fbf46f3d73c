import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
} from '@vidmob/vidmob-nestjs-common';
import { ReadWorkspacesAdAccountDto } from './dtos/read.workspace-ad-account.dto';
import { WorkspaceAdAccountService } from './workspace-ad-account.service';
import { GetWorkspacesPlatformAdAccountsDto } from './dtos/get-workspaces-platform-ad-accounts.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadPlatformAdAccountDto } from '../../ad-accounts/dto/read-platform-ad-account.dto';
import { FilterAdAccountRequestDto } from './dtos/filter-ad-account-request.dto';
import { AccountSearchParamsDto } from '../../ad-accounts/dto/ad-account-search-params.dto';

@ApiTags('workspace-ad-account')
@Controller('workspace')
export class WorkspaceAdAccountController {
  constructor(
    private readonly workspaceAdAccountService: WorkspaceAdAccountService,
  ) {}
  @VmApiOkPaginatedArrayResponse({
    type: ReadWorkspacesAdAccountDto,
    description: 'List of Workspace Ad Accounts',
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'The id of the workspace to start from.',
  })
  @Get(':workspaceId/ad-account')
  listAllConnectedAdAccountsFromWorkspace(
    @Param('workspaceId') workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() searchParams: AccountSearchParamsDto,
  ) {
    return this.workspaceAdAccountService.findAllAdAccountsForWorkspace(
      workspaceId,
      paginationOptions,
      searchParams,
    );
  }

  @VmApiOkPaginatedArrayResponse({
    type: ReadPlatformAdAccountDto,
    description: 'List of Workspace Ad Accounts for a Platform',
  })
  @Post('platform/ad-account')
  getConnectedAdAccountsForWorkspacesAndPlatform(
    @Body()
    getWorkspacesPlatformAdAccountsRequest: GetWorkspacesPlatformAdAccountsDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadPlatformAdAccountDto>> {
    return this.workspaceAdAccountService.findAdAccountsForWorkspacesAndPlatform(
      getWorkspacesPlatformAdAccountsRequest,
      paginationOptions,
    );
  }

  @VmApiOkPaginatedArrayResponse({
    type: ReadPlatformAdAccountDto,
    description: 'List of Workspace Ad Accounts for a Platform',
  })
  @Post('filtered/ad-account')
  async getFilteredAdAccountsByBrandsOrMarkets(
    @Body()
    filterAdAccountRequestDto: FilterAdAccountRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const { brandIds, isoCodes } = filterAdAccountRequestDto;
    if (brandIds && isoCodes) {
      throw new Error('Cannot filter by both brands and markets');
    }

    if (!brandIds && !isoCodes) {
      throw new Error('Must include either brandIds or isoCodes');
    }

    return await this.workspaceAdAccountService.filterPlatformAdAccountsByBrandsOrMarkets(
      filterAdAccountRequestDto,
      paginationOptions,
    );
  }
}
