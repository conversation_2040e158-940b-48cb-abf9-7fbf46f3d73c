import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkspaceAdAccountMap } from './entities/workspace-ad-account-map.entities';
import { WorkspaceAdAccountController } from './workspace-ad-account.controller';
import { WorkspaceAdAccountProfile } from './mapper/workspace-ad-account.profile';
import { PlatformAdAccount } from '../../ad-accounts/entities/ad-account.entity';
import { WorkspaceAdAccountService } from './workspace-ad-account.service';
import { PlatformAdAccountToWorkspace } from '../../ad-accounts/entities/ad-account-workspace-map.entity';
import { OrganizationPlatformAdAccountMap } from '../../ad-accounts/entities/organization-platform-ad-account-map.entity';
import { AdAccountsModule } from '../../ad-accounts/ad-accounts.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WorkspaceAdAccountMap,
      PlatformAdAccountToWorkspace,
      OrganizationPlatformAdAccountMap,
      PlatformAdAccount,
    ]),
    AdAccountsModule,
  ],
  controllers: [WorkspaceAdAccountController],
  providers: [WorkspaceAdAccountProfile, WorkspaceAdAccountService],
})
export class WorkspaceAdAccountModule {}
