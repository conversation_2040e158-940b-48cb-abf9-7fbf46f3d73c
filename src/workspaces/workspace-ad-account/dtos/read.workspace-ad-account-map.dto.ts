import { AutoMap } from '@automapper/classes';
import { IsDate, IsNumber, IsString } from 'class-validator';

export class ReadWorkspaceAdAccountMapDto {
  /**
   * The Workspace (Partner) Id from a user
   * @example 1
   */
  @AutoMap()
  @IsNumber()
  workspaceId: number;

  /**
   * The Platform Ad Account Id from a user.
   * This is not the Platform Account Id; the ID is id from platform_ad_accounts table
   * @example 1
   */
  @AutoMap()
  @IsNumber()
  platformAdAccountId: number;
}