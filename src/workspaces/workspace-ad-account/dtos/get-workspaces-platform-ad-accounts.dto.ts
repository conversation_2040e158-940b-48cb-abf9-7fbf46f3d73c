import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class GetWorkspacesPlatformAdAccountsDto {
  /**
   * Array of workspaces to filter by. Optional.
   * If not included, return all workspaces user has access to.
   * @example [8923, 553, 3232]
   */
  @AutoMap()
  @IsArray()
  @IsNotEmpty()
  workspaces: number[];

  /**
   * Platform name to filter by.
   * @example "FACEBOOK"
   */
  @AutoMap()
  @IsOptional()
  @IsString()
  platform?: string;
}
