import { AutoMap } from '@automapper/classes';
import { IsInt } from 'class-validator';

export class CreateWorkspacesAdAccountMapDto {
  /**
   * Workspace (Partner) Id from a user
   * @example 1
   */
  @AutoMap()
  @IsInt()
  workspaceId: number;

  /**
   * Platform Ad Account Id from a user.
   * This is the Ad Account Id from the DB, not the Platform Account Id
   * @example 1
   */
  @AutoMap()
  @IsInt()
  platformAdAccountId: number;
}
