import { AutoMap } from '@automapper/classes';
import { IsBoolean, IsDate, IsString } from 'class-validator';

export class ReadWorkspacesAdAccountDto {
  /**
   * The Platform that the ad account is connected to
   * @example "FACEBOOK"
   */
  @AutoMap()
  @IsString()
  platform: string;

  /**
   * The name of the ad account
   * @example "VidMob"
   */
  @AutoMap()
  @IsString()
  platformAccountName: string;

  /**
   * The id of the Platform Account (not the Platform Ad Account ID in the DB)
   * @example "*********"
   */
  @AutoMap()
  @IsString()
  platformAccountId: string;

  /**
   * The date that this ad account was created
   * @example "2021-01-01T00:00:00.000Z"
   */
  @AutoMap()
  @IsDate()
  dateCreated: Date;

  /**
   * A flag that tells the user whether imports processing was completed
   * @example true
   */
  @AutoMap()
  @IsBoolean()
  processingCompleted: boolean;

  /**
   * The date that the imports processing was completed
   * @example "2021-01-01T00:00:00.000Z"
   */
  @AutoMap()
  @IsDate()
  processingCompletedDate: Date;

  /**
   * The date that the imports processing was last successful
   */
  @AutoMap()
  @IsDate()
  lastSuccessfulProcessingDate: Date;

  /**
   * A flag that tells the user whether the ad account is connected
   * @example true
   */
  @AutoMap()
  @IsBoolean()
  connected: boolean;

  /**
   * The status of the last import
   * @example IMPORT_ACTIVE
   */
  @AutoMap()
  @IsString()
  importStatus?: string;
}
