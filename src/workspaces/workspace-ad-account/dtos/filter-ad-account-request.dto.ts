import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsString } from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class FilterAdAccountRequestDto {
  /**
   * Array of workspaces to filter by.
   * @example [8923, 553, 3232]
   */
  @AutoMap()
  @IsArray()
  @IsNotEmpty()
  workspaceIds: number[];

  /**
   * Array of ad account ids to filter.
   */
  @AutoMap()
  @IsArray()
  @IsNotEmpty()
  adAccountIds: string[];

  /**
   * Array of BRAND ids to filter by.
   */
  @AutoMap()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  brandIds?: string[];

  /**
   * Array of MARKET iso codes to filter by.
   */
  @AutoMap()
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  isoCodes?: string[];
}
