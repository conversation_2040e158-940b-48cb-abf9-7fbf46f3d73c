import { WorkspaceAdAccountProfile } from './workspace-ad-account.profile';
import { Mapper } from '@automapper/core';
import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { CreateWorkspacesAdAccountMapDto } from '../dtos/create.workspaces-ad-account-map.dto';
import { WorkspaceAdAccountMap } from '../entities/workspace-ad-account-map.entities';
import { ReadWorkspacesAdAccountDto } from '../dtos/read.workspace-ad-account.dto';

import { ReadPlatformAdAccountDto } from '../../../ad-accounts/dto/read-platform-ad-account.dto';
import { ReadWorkspaceAdAccountMapDto } from '../dtos/read.workspace-ad-account-map.dto';
import {
  mockCreateWorkspaceAdAccountMapDto,
  mockReadAdAccountDto,
} from '../workspace-ad-account.mock';
import { UpdateWorkspaceAdAccountMapDto } from '../dtos/update.workspace-ad-account-map.dto';

describe('WorkspacesAdAccountProfile', () => {
  let mapper: Mapper;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [WorkspaceAdAccountProfile],
    }).compile();
    const workspacesAdAccountProfile: WorkspaceAdAccountProfile =
      module.get<WorkspaceAdAccountProfile>(WorkspaceAdAccountProfile);
    mapper = module.get<Mapper>(getMapperToken());
    workspacesAdAccountProfile.profile(mapper);
  });
  it('should map create map dto to an entity', () => {
    const entity = mapper.map(
      mockCreateWorkspaceAdAccountMapDto,
      CreateWorkspacesAdAccountMapDto,
      WorkspaceAdAccountMap,
    );
    expect(entity).toBeDefined();
    expect(entity.workspaceId).toBe(1);
    expect(entity.platformAdAccountId).toBe(2);
  });

  it('should map an ad account dto to a read workspace ad account dto', () => {
    const readDto = mapper.map(
      mockReadAdAccountDto,
      ReadPlatformAdAccountDto,
      ReadWorkspacesAdAccountDto,
    );
    expect(readDto).toBeDefined();
    expect(readDto.platform).toBe('FACEBOOK');
    expect(readDto.platformAccountName).toBe('Test Account');
    expect(readDto.platformAccountId).toBe('*********');
    expect(readDto.connected).toBe(true);
    expect(readDto.processingCompleted).toBe(true);
  });

  it('should map a map entity to a read dto', () => {
    const mockMap = {
      workspaceId: 1,
      platformAdAccountId: 2,
      workspace: null,
      platformAdAccount: null,
    };
    const readDto = mapper.map(
      mockMap,
      WorkspaceAdAccountMap,
      ReadWorkspaceAdAccountMapDto,
    );
    expect(readDto).toBeDefined();
    expect(readDto.workspaceId).toBe(1);
    expect(readDto.platformAdAccountId).toBe(2);
  });

  it('should map update dto to an entity', () => {
    const mockUpdateDto = {
      workspaceId: 1,
      platformAdAccountId: 2,
    };
    const entity = mapper.map(
      mockUpdateDto,
      UpdateWorkspaceAdAccountMapDto,
      WorkspaceAdAccountMap,
    );
    expect(entity).toBeDefined();
    expect(entity.workspaceId).toBe(1);
    expect(entity.platformAdAccountId).toBe(2);
  });
});
