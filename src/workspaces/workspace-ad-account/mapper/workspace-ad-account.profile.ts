import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { WorkspaceAdAccountMap } from '../entities/workspace-ad-account-map.entities';
import { CreateWorkspacesAdAccountMapDto } from '../dtos/create.workspaces-ad-account-map.dto';
import { ReadWorkspacesAdAccountDto } from '../dtos/read.workspace-ad-account.dto';
import { Mapping } from '@automapper/core/lib/types';
import { ReadPlatformAdAccountDto } from '../../../ad-accounts/dto/read-platform-ad-account.dto';
import { ReadWorkspaceAdAccountMapDto } from '../dtos/read.workspace-ad-account-map.dto';
import { UpdateWorkspaceAdAccountMapDto } from '../dtos/update.workspace-ad-account-map.dto';

/**
 * Builder that maps CreateWorkspacesAdAccountDto to WorkspaceAdAccountMap
 * @param mapper
 * @returns {Mapping<CreateWorkspacesAdAccountMapDto, WorkspaceAdAccountMap>}
 */
const buildCreateWorkspaceAdAccountToEntity = (
  mapper,
): Mapping<CreateWorkspacesAdAccountMapDto, WorkspaceAdAccountMap> => {
  return createMap<CreateWorkspacesAdAccountMapDto, WorkspaceAdAccountMap>(
    mapper,
    CreateWorkspacesAdAccountMapDto,
    WorkspaceAdAccountMap,
  );
};

/**
 * Builder that maps WorkspaceAdAccountMap to ReadWorkspaceAdAccountMapDto
 * @param mapper
 * @returns {Mapping<WorkspaceAdAccountMap, ReadWorkspaceAdAccountMapDto>}
 */
const buildWorkspaceAdAccountMapToReadDto = (
  mapper,
): Mapping<WorkspaceAdAccountMap, ReadWorkspaceAdAccountMapDto> => {
  return createMap<WorkspaceAdAccountMap, ReadWorkspaceAdAccountMapDto>(
    mapper,
    WorkspaceAdAccountMap,
    ReadWorkspaceAdAccountMapDto,
  );
};

/**
 * Builder that maps ReadPlatformAdAccountDto to ReadWorkspacesAdAccountDto
 * @param mapper
 * @returns {Mapping<ReadPlatformAdAccountDto, ReadWorkspacesAdAccountDto>}
 */
const buildAdAccountToReadDto = (
  mapper: Mapper,
): Mapping<ReadPlatformAdAccountDto, ReadWorkspacesAdAccountDto> => {
  return createMap<ReadPlatformAdAccountDto, ReadWorkspacesAdAccountDto>(
    mapper,
    ReadPlatformAdAccountDto,
    ReadWorkspacesAdAccountDto,
    forMember(
      (dest) => dest.connected,
      mapFrom((src) => src.canAccess == 1),
    ),
    forMember(
      (dest) => dest.processingCompleted,
      mapFrom((src) => src.processingCompleted == 1),
    ),
    forMember(
      (dest) => dest.lastSuccessfulProcessingDate,
      mapFrom((src) => src.lastSuccessfulProcessingDate),
    ),
    forMember(
      (dest) => dest.processingCompletedDate,
      mapFrom((src) => src.lastSuccessfulProcessingDate),
    ),
  );
};

/**
 * Builder that maps UpdateWorkspaceAdAccountMapDto to WorkspaceAdAccountMap
 * @param mapper
 * @returns {Mapping<UpdateWorkspaceAdAccountMapDto, WorkspaceAdAccountMap>}
 */
const buildUpdateWorkspaceAdAccountMapToEntity = (
  mapper,
): Mapping<UpdateWorkspaceAdAccountMapDto, WorkspaceAdAccountMap> => {
  return createMap<UpdateWorkspaceAdAccountMapDto, WorkspaceAdAccountMap>(
    mapper,
    UpdateWorkspaceAdAccountMapDto,
    WorkspaceAdAccountMap,
  );
};

@Injectable()
export class WorkspaceAdAccountProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper) => {
      buildCreateWorkspaceAdAccountToEntity(mapper);
      buildAdAccountToReadDto(mapper);
      buildWorkspaceAdAccountMapToReadDto(mapper);
      buildUpdateWorkspaceAdAccountMapToEntity(mapper);
    };
  }
}
