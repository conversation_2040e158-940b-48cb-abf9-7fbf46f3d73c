import { forwardRef, Module } from '@nestjs/common';
import { WorkspaceService } from './workspaces.service';
import { WorkspaceController } from './workspaces.controller';
import { Workspace } from './entities/workspace.entity';
import { Organization } from '../organizations/entities/organization.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkspaceProfile } from './mapper/workspace.profile';
import { Market } from '../markets/entities/market.entity';
import { User } from '../entities/user.entity';
import { WorkspaceUser } from './entities/workspace-user.entity';
import { SplitAndTrimPipe } from '../organizations/utils/split-and-trim-pipe';
import { PlatformAdAccountToWorkspace } from '../ad-accounts/entities/ad-account-workspace-map.entity';
import { PlatformAdAccount } from '../ad-accounts/entities/ad-account.entity';
import { OrganizationPlatformAdAccountMap } from '../ad-accounts/entities/organization-platform-ad-account-map.entity';
import { Brand } from '../brands/entities/brand.entity';
import { WorkspaceMarket } from './entities/workspace-market.entity';
import { WorkspaceBrand } from './entities/workspace-brand.entity';
import { AccountType } from './entities/account-type.entity';
import { FeatureAccountType } from './entities/feature-account-type.entity';
import { FeatureWorkspace } from './entities/feature-workspace.entity';
import { WorkspaceIndustry } from './entities/partner-industry.entity';
import { WorkspaceAdAccountController } from './workspace-ad-account/workspace-ad-account.controller';
import { WorkspaceAdAccountService } from './workspace-ad-account/workspace-ad-account.service';
import { WorkspaceAdAccountMap } from './workspace-ad-account/entities/workspace-ad-account-map.entities';
import { OrganizationAdAccountUserPermissions } from '../ad-accounts/entities/organization-ad-account-user-permissions.entity';
import { WorkspaceManager } from './entities/workspace-manager.entity';
import { WorkspaceBrandProfile } from './brand/mappers/workspace-brand.profile';
import { WorkspaceBrandService } from './brand/services/workspace-brand.service';
import { WorkspaceBrandController } from './brand/controllers/workspace-brand.controller';
import { WorkspaceBrandMap } from './brand/entities/workspace-brand-map.entity';
import { BrandProfile } from '../organizations/brand/mappers/brand.profile';
import { PlatformAdAccountBrandMap } from '../ad-accounts/entities/platform-ad-account-brand-map.entity';
import { PlatformAdAccountMarketMap } from '../ad-accounts/entities/platform-ad-account-market-map.entity';
import { FeatureWhitelist } from './entities/feature-whitelist.entity';
import { ProductAccountType } from './entities/product-account-type.entity';
import { OrganizationFeatureWhitelist } from '../organizations/entities/organization-feature-whitelist.entity';
import { PlatformAdAccountIndustryMap } from '../ad-accounts/entities/platform-ad-account-industry-map.entity';
import { PlatformAdAccountSequentialFailure } from '../ad-accounts/entities/platform-ad-account-sequential-failure.entity';
import { PlatformAdAccountImportStatus } from '../ad-accounts/entities/platform-ad-account-import-status.entity';
import { AdAccountsModule } from '../ad-accounts/ad-accounts.module';
import { OrganizationsModule } from '../organizations/organizations.module';
import { BrandModule } from '../brands/brand.module';
import { MarketModule } from '../markets/market.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workspace,
      User,
      Organization,
      Market,
      Brand,
      AccountType,
      WorkspaceAdAccountMap,
      OrganizationFeatureWhitelist,
      WorkspaceIndustry,
      FeatureAccountType,
      FeatureWorkspace,
      FeatureWhitelist,
      WorkspaceUser,
      WorkspaceMarket,
      WorkspaceBrand,
      WorkspaceManager,
      PlatformAdAccountToWorkspace,
      OrganizationPlatformAdAccountMap,
      OrganizationAdAccountUserPermissions,
      PlatformAdAccountImportStatus,
      PlatformAdAccount,
      WorkspaceBrandMap,
      PlatformAdAccountBrandMap,
      PlatformAdAccountMarketMap,
      PlatformAdAccountIndustryMap,
      ProductAccountType,
      PlatformAdAccountSequentialFailure,
    ]),
    BrandModule,
    MarketModule,
    forwardRef(() => AdAccountsModule),
    forwardRef(() => OrganizationsModule),
  ],

  controllers: [
    WorkspaceController,
    WorkspaceAdAccountController,
    WorkspaceBrandController,
  ],

  providers: [
    WorkspaceService,
    WorkspaceAdAccountService,
    WorkspaceProfile,
    SplitAndTrimPipe,
    WorkspaceBrandService,
    WorkspaceBrandProfile,
    BrandProfile,
  ],
  exports: [WorkspaceService],
})
export class WorkspacesModule {}
