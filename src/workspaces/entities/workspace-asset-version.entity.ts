import {
  <PERSON>umn,
  CreateDate<PERSON>olumn,
  <PERSON><PERSON>ty,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { WorkspaceAsset } from './workspace-asset.entity';

@Entity('partner_asset_version')
export class WorkspaceAssetVersion {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ name: 'version' })
  version: number;

  @AutoMap()
  @Column({ name: 'media_id' })
  mediaId: number;

  @AutoMap()
  @Column({ name: 'asset_version' })
  assetVersion: number;

  @AutoMap()
  @Column({ name: 'changes' })
  changes: string;

  @AutoMap()
  @Column({ name: 'creator_id' })
  creatorId: number;

  @AutoMap()
  @Column({ name: 'partner_asset_id' })
  workspaceAssetId: number;

  @AutoMap()
  @ManyToOne(() => WorkspaceAsset)
  @JoinColumn({ name: 'partner_asset_id' })
  workspaceAsset: WorkspaceAsset;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;
}
