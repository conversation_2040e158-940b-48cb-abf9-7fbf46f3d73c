import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { FeatureAccountType } from './feature-account-type.entity';
import { FeatureWorkspace } from './feature-workspace.entity';

@Entity('feature_whitelist')
export class FeatureWhitelist {
  @AutoMap()
  @PrimaryColumn({ name: 'id' })
  id: number;

  @AutoMap()
  @Column({ name: 'version' })
  version: number;

  @AutoMap()
  @Column({ name: 'name' })
  name: string;

  @AutoMap()
  @Column({ name: 'identifier' })
  identifier: string;

  @AutoMap()
  @Column({ name: 'feature_type' })
  featureType: string;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;

  @OneToMany(
    () => FeatureAccountType,
    (featureAccountType) => featureAccountType.featureWhiteList,
  )
  @JoinColumn({ name: 'id' })
  featureAccountType: FeatureAccountType;

  @OneToMany(
    () => FeatureWorkspace,
    (featureWorkspace) => featureWorkspace.featureWhiteList,
  )
  @JoinColumn({ name: 'id' })
  featureWorkspace: FeatureWorkspace;
}
