import {
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { AccountType } from './account-type.entity';
import { FeatureWhitelist } from './feature-whitelist.entity';
import { Exclude } from 'class-transformer';

@Entity('feature_whitelist_account_type')
export class FeatureAccountType {
  @AutoMap()
  @PrimaryColumn({ name: 'feature_whitelist_id' })
  featureWhiteListId: number;

  @AutoMap()
  @PrimaryColumn({ name: 'account_type_id' })
  accountTypeId: number;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;

  @AutoMap()
  @Exclude()
  @JoinColumn({ name: 'feature_whitelist_id' })
  @ManyToOne(
    () => FeatureWhitelist,
    (featureWhiteList) => featureWhiteList.featureAccountType,
  )
  featureWhiteList: FeatureWhitelist;

  @AutoMap()
  @Exclude()
  @JoinColumn({ name: 'account_type_id' })
  @ManyToOne(() => AccountType, (accountType) => accountType.featureAccountType)
  accountType: AccountType;
}
