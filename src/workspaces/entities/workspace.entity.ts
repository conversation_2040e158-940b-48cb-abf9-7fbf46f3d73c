import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON><PERSON><PERSON><PERSON><PERSON>n,
  <PERSON>inTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { AutoMap } from '@automapper/classes';
import { Market } from '../../markets/entities/market.entity';
import { User } from '../../entities/user.entity';
import { WorkspaceUser } from './workspace-user.entity';
import { Brand } from '../../brands/entities/brand.entity';
import { PlatformAdAccountToWorkspace } from '../../ad-accounts/entities/ad-account-workspace-map.entity';
import { Organization } from '../../organizations/entities/organization.entity';
import { WorkspaceMarket } from './workspace-market.entity';
import { WorkspaceBrand } from './workspace-brand.entity';
import { AccountType } from './account-type.entity';
import { WorkspaceManager } from './workspace-manager.entity';
import { FeatureWorkspace } from './feature-workspace.entity';

// the workspace is stored in the partner table
@Entity('partner')
export class Workspace {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ length: 255 })
  name: string;

  @AutoMap()
  @Column({ name: 'logo_url', length: 255 })
  logoUrl: string;

  @AutoMap()
  @ManyToOne(() => AccountType, (accountType) => accountType.workspaces)
  @JoinColumn({ name: 'account_type_id' })
  accountType: AccountType;

  @AutoMap()
  @Exclude()
  @ManyToOne(() => Organization, (organization) => organization.workspaces)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @AutoMap()
  @Column({ name: 'organization_id' })
  organizationId: string;

  @AutoMap()
  @Column({ name: 'account_type_id' })
  accountTypeId: number;

  @AutoMap()
  @Column({ name: 'is_primary' })
  isPrimary: boolean;

  @AutoMap()
  @Column({ name: 'version' })
  version: number;

  @AutoMap()
  @Column({ name: 'include_draft_download_url' })
  includeDraftDownloadUrl: boolean;

  @AutoMap()
  @Column({ name: 'project_final_assets_required' })
  projectFinalAssetsRequired: boolean;

  @AutoMap()
  @Column({ name: 'draft_review', nullable: true })
  draftReview: string;

  @AutoMap()
  @Column({ name: 'parent_partner_id', nullable: true })
  parentPartnerId: number;

  @AutoMap()
  @Column({ name: 'maker_id', nullable: true })
  makerId: number;

  @AutoMap()
  @Column({ name: 'pricing_ratio_tier_id', nullable: true })
  pricingRatioTierId: number;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;

  @AutoMap()
  @Column({ name: 'personal' })
  personal: boolean;

  @AutoMap()
  @Column({ name: 'find_my_team_enabled' })
  findMyTeamEnabled: boolean;

  @OneToMany(() => WorkspaceUser, (workspaceUser) => workspaceUser.workspace)
  workspaceUsers: WorkspaceUser[];

  @OneToMany(
    () => WorkspaceManager,
    (workspaceManager) => workspaceManager.workspace,
  )
  workspaceManagers: WorkspaceManager[];

  @ManyToMany(() => Market, (market) => market.workspaces)
  @JoinTable({
    name: 'workspace_country_map',
    joinColumns: [{ name: 'partner_id' }],
    inverseJoinColumns: [{ name: 'iso_code' }],
  })
  markets: Market[];

  @OneToMany(
    () => WorkspaceMarket,
    (workspaceMarket) => workspaceMarket.workspace,
  )
  workspaceMarkets: WorkspaceMarket[];

  @AutoMap()
  @ManyToMany(() => Brand, (brand) => brand.workspaces)
  @JoinTable({
    name: 'workspace_brand_map',
    joinColumns: [{ name: 'partner_id' }],
    inverseJoinColumns: [{ name: 'brand_id' }],
  })
  brands: Brand[];

  @OneToMany(() => WorkspaceBrand, (workspaceBrand) => workspaceBrand.workspace)
  workspaceBrands: WorkspaceBrand[];

  @ManyToMany(() => User, (user) => user.workspaceUsers)
  @JoinTable({
    name: 'partner_person',
    joinColumns: [{ name: 'partner_id' }],
    inverseJoinColumns: [{ name: 'person_id' }],
  })
  users: WorkspaceUser[];

  @OneToMany(
    () => PlatformAdAccountToWorkspace,
    (platformAdAccountToWorkspace) => platformAdAccountToWorkspace.workspace,
  )
  @JoinColumn({ name: 'id' })
  platformAdAccountToWorkspaces: PlatformAdAccountToWorkspace[];

  @AutoMap()
  platformAdAccountToWorkspacesCount?: number;

  @AutoMap()
  @Exclude()
  @OneToMany(
    () => FeatureWorkspace,
    (featureWorkspace) => featureWorkspace.workspace,
  )
  @JoinColumn({ name: 'id' })
  featureWorkspaces: FeatureWorkspace[];
}
