import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { User } from '../../entities/user.entity';
import { Workspace } from './workspace.entity';
import { AutoMap } from '@automapper/classes';
import { Role } from 'src/entities/role.entity';

@Entity('partner_person')
export class WorkspaceUser {
  @AutoMap()
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  /**
   * The unique partner id.
   */

  @AutoMap()
  @Column({ name: 'partner_id' })
  workspaceId: number;
  @ManyToOne(() => Workspace, (workspace) => workspace.workspaceUsers)
  @JoinColumn({ name: 'partner_id' })
  workspace: Workspace;

  /**
   * The id of the person with a role on this partner.
   */

  @AutoMap()
  @Column({ name: 'person_id' })
  userId: number;

  @AutoMap()
  @Column({ name: 'role_id' })
  roleId: number;
  @ManyToOne(() => Role, (role) => role.id)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @ManyToOne(() => User, (user) => user.workspaceUsers)
  @JoinColumn({ name: 'person_id' })
  user: User;

  @AutoMap()
  @Column('tinyint', { default: 1, nullable: false })
  active: boolean;

  @AutoMap()
  @Column({ name: 'date_created' })
  dateCreated: Date;

  @AutoMap()
  @Column({ name: 'last_updated' })
  lastUpdated: Date;
}
