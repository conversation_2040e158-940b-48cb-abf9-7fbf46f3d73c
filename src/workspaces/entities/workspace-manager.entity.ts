import {
  <PERSON><PERSON>n,
  CreateDateColumn,
  Entity,
  <PERSON>in<PERSON>olumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Workspace } from './workspace.entity';
import { User } from '../../entities/user.entity';

@Entity('partner_manager')
export class WorkspaceManager {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ name: 'version' })
  version: number;

  @AutoMap()
  @Column({ name: 'partner_id' })
  workspaceId: number;

  @ManyToOne(() => Workspace, (workspace) => workspace.workspaceManagers)
  @JoinColumn({ name: 'partner_id' })
  workspace: Workspace;

  @AutoMap()
  @Column({ name: 'manager_id' })
  managerId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'manager_id' })
  manager: User;

  @AutoMap()
  @Column({ type: 'varchar', length: 40 })
  role: string;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;
}
