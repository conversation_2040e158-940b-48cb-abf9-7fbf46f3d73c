import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Workspace } from './workspace.entity';
import { Market } from '../../markets/entities/market.entity';
import { AutoMap } from '@automapper/classes';

@Entity('workspace_country_map')
export class WorkspaceMarket {
  @PrimaryGeneratedColumn('uuid')
  id: string; // Unique identifier for the WorkspaceMarket entry

  @AutoMap()
  @Column({ name: 'partner_id', type: 'bigint' })
  workspaceId: number; // The ID of the associated Workspace

  @AutoMap()
  @Column({ name: 'iso_code', length: 3 })
  isoCode: string; // The ISO code representing the country

  /**
   * The associated Workspace entity.
   */
  @ManyToOne(() => Workspace, (workspace) => workspace.workspaceMarkets)
  @JoinColumn({ name: 'partner_id' })
  workspace: Workspace;

  /**
   * The associated Market entity based on iso_code.
   */
  @ManyToOne(() => Market, (market) => market.workspaceMarkets)
  @JoinColumn({ name: 'iso_code' })
  market: Market;
}
