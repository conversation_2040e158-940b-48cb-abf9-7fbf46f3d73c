import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON>ty,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Workspace } from './workspace.entity';
import { FeatureAccountType } from './feature-account-type.entity';

@Entity('account_type')
export class AccountType {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ length: 190 })
  name: string;

  @AutoMap()
  @Column({ name: 'version' })
  version: number;

  @AutoMap()
  @Column({ length: 50 })
  scope: string;

  @AutoMap()
  @Column({ name: 'allows_custom_auto_bids' })
  allowsCustomAutoBids: boolean;

  @AutoMap()
  @Column({ name: 'display_bid_price' })
  displayBidPrice: boolean;

  @AutoMap()
  @Column()
  markup: number;

  @AutoMap()
  @Column({ name: 'package_pricing' })
  packagePricing: number;

  @AutoMap()
  @Column({ name: 'partner_id' })
  partnerId: number;

  @AutoMap()
  @Column({ name: 'project_management_method', length: 50 })
  projectManagementMethod: string;

  @AutoMap()
  @Column({ name: 'updated_pricing_model' })
  updatedPricingModel: number;

  @AutoMap()
  @Column({ name: 'pricing_ratio_rate' })
  pricingRatioRate: number;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;

  /**
   * List of workspaces connected to the account type
   */
  @AutoMap()
  @OneToMany(() => Workspace, (workspace) => workspace.accountType)
  workspaces: Workspace[];

  /**
   * List of workspaces connected to the account type
   */
  @AutoMap()
  @OneToMany(
    () => FeatureAccountType,
    (featureAccountType) => featureAccountType.accountType,
  )
  featureAccountType: FeatureAccountType[];
}
