import {
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryC<PERSON>umn,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Workspace } from './workspace.entity';
import { Exclude } from 'class-transformer';
import { FeatureWhitelist } from './feature-whitelist.entity';

@Entity('feature_whitelist_partner')
export class FeatureWorkspace {
  @AutoMap()
  @PrimaryColumn({ name: 'feature_whitelist_id' })
  featureWhiteListId: number;

  @AutoMap()
  @PrimaryColumn({ name: 'partner_id' })
  partnerId: number;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;

  @AutoMap()
  @Exclude()
  @JoinColumn({ name: 'feature_whitelist_id' })
  @ManyToOne(
    () => FeatureWhitelist,
    (featureWhiteList) => featureWhiteList.featureWorkspace,
  )
  featureWhiteList: FeatureWhitelist;

  @AutoMap()
  @Exclude()
  @JoinColumn({ name: 'partner_id' })
  @ManyToOne(() => Workspace, (workspace) => workspace.featureWorkspaces)
  workspace: Workspace;
}
