import { Column, <PERSON>tity, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';

@Entity('product_account_type')
export class ProductAccountType {
  @AutoMap()
  @PrimaryColumn({ name: 'product_id', type: 'bigint' })
  productId: number;

  @AutoMap()
  @Column({ name: 'account_type_id' })
  accountTypeId: number;

  @AutoMap()
  @Column({ name: 'sequence' })
  sequence: number;
}
