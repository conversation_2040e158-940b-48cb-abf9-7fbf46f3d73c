import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';

@Entity('partner_industry')
export class WorkspaceIndustry {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ name: 'version' })
  version: number;

  @AutoMap()
  @Column({ name: 'partner_id' })
  workspaceId: number;

  @AutoMap()
  @Column({ name: 'industry_id' })
  industryId: number;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;
}
