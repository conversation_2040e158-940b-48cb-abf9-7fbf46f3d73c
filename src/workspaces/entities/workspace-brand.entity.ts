import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { Workspace } from './workspace.entity';
import { Brand } from '../../brands/entities/brand.entity';

@Entity('workspace_brand_map')
export class WorkspaceBrand {
  /**
   * The unique partner id.
   */
  @ManyToOne(() => Workspace, (workspace) => workspace.workspaceBrands)
  @JoinColumn({ name: 'partner_id' })
  @PrimaryColumn({ name: 'partner_id', type: 'bigint' })
  workspace: Workspace;

  /**
   * iso_code
   */
  @ManyToOne(() => Brand, (brand) => brand.workspaceBrands)
  @JoinColumn({ name: 'brand_id' })
  @PrimaryColumn({ name: 'brand_id', type: 'uuid' })
  brand: Brand;
}
