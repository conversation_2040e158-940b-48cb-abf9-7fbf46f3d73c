import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Workspace } from './workspace.entity';
import { Exclude } from 'class-transformer';

@Entity('partner_asset')
export class WorkspaceAsset {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ name: 'version' })
  version: number;

  @AutoMap()
  @Column({ name: 'name' })
  name: string;

  @AutoMap()
  @Column({ name: 'description' })
  description: string;

  @AutoMap()
  @Column({ name: 'partner_id' })
  workspaceId: number;

  @AutoMap()
  @ManyToOne(() => Workspace)
  @JoinColumn({ name: 'partner_id' })
  workspace: Workspace;

  @AutoMap()
  @Column({ name: 'folder_id' })
  folderId: number;

  @AutoMap()
  @Column({ name: 'is_default' })
  isDefault: boolean;

  @AutoMap()
  @Column({ name: 'deleted' })
  deleted: boolean;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;
}
