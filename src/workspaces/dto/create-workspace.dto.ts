import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>String,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class CreateWorkspaceDto {
  /**
   * The name of the workspace
   * @example "My Workspace"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  name: string;

  /**
   * The Logo Url of the workspace
   * @example "https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png"
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  logoUrl: string;

  /**
   * The organization id of the workspace, need
   * @example 123e4567-e89b-12d3-a456-************
   */
  @AutoMap()
  @IsNotEmpty()
  @IsUUID()
  organizationId: string;

  /**
   * Is this workspace the primary workspace for the organization?
   * @example true
   */
  @AutoMap()
  @IsNotEmpty()
  isPrimary: boolean;

  /**
   * The list of brand ids to be associated with the workspace
   */
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  brands?: string[];

  /**
   * The list of market ids to be associated with the workspace
   */
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  markets?: string[];
}
