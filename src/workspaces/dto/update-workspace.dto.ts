import { PartialType } from '@nestjs/mapped-types';
import { CreateWorkspaceDto } from './create-workspace.dto';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { AutoMap } from '@automapper/classes';
import { ApiExtraModels } from '@nestjs/swagger';

@ApiExtraModels(CreateWorkspaceDto)
export class UpdateWorkspaceDto extends PartialType(CreateWorkspaceDto) {
  /**
   * The name of the workspace
   * @example "My Workspace"
   */
  @AutoMap()
  @IsOptional()
  @IsString()
  name?: string;

  /**
   * The organization id of the workspace,
   * this is NOT updatable, and is needed for validation
   * @example 123e4567-e89b-12d3-a456-************
   */
  @AutoMap()
  @IsNotEmpty()
  @IsUUID()
  organizationId: string;

  /**
   * Is this workspace the primary workspace for the organization?
   * @example true
   */
  @AutoMap()
  @IsOptional()
  @IsBoolean()
  isPrimary?: boolean;

  /**
   * The list of brand ids to be associated with the workspace
   */
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  brands?: string[];

  /**
   * The list of market ids to be associated with the workspace
   */
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  markets?: string[];
}
