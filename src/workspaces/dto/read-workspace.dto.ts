import { AutoMap } from '@automapper/classes';

export class ReadWorkspaceDto {
  /**
   * System assigned id of the Workspace
   */
  @AutoMap()
  id: number;

  /**
   * The name of the workspace
   * @example "My Workspace"
   */
  @AutoMap()
  name: string;

  /**
   * The logoUrl of the workspace
   * @example "https://example.com/logo.png"
   */
  @AutoMap()
  logoUrl: string;

  /**
   * Is this workspace the primary workspace for the organization?
   * @example true
   */
  @AutoMap()
  isPrimary: boolean;

  /**
   * The organization id of the workspace
   * @example 123e4567-e89b-12d3-a456-426614174000
   */
  @AutoMap()
  organizationId: string;
}
