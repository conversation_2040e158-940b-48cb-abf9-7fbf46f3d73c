import { AutoMap } from '@automapper/classes';

export class RealAllUserWorkspacesRawDto {
  @AutoMap()
  workspace_id: string;

  @AutoMap()
  workspace_name: string;

  @AutoMap()
  workspace_logo_url: string;

  @AutoMap()
  workspace_organization_id: string;

  @AutoMap()
  workspace_personal: number;

  @AutoMap()
  workspace_find_my_team_enabled: number;

  @AutoMap()
  accountType_scope: string;

  @AutoMap()
  accountType_id: string;

  @AutoMap()
  accountType_name: string;

  @AutoMap()
  organization_name: string;
}
