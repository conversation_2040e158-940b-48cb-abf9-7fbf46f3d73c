import {
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON>tring,
  IsBoolean,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class AllUserWorkspaceDto {
  /**
   * The id of the workspace
   * @example "12345"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  id: number;

  /**
   * The industry of the workspace
   * @example "Alcohol"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  industry: string;

  /**
   * Is workspace an enterprise workspace
   * @example true
   */
  @AutoMap()
  @IsNotEmpty()
  @IsBoolean()
  isEnterprise: boolean;

  /**
   * Workspace account name
   * @example "ABI"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  publicAccountTypeName: string;

  /**
   * Workspace account type identifier
   * @example "ENTERPRISE"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  accountTypeIdentifier: string;

  /**
   * Is workspace a personal workspace
   * @example false
   */
  @AutoMap()
  @IsNotEmpty()
  @IsBoolean()
  isPersonal: boolean;

  /**
   * Workspace name
   * @example "ABI - Global"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  name: string;

  /**
   * Url to workspace logo
   * @example "www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  logoUrl: string;

  /**
   * Is workspace find my team enabled
   * @example false
   */
  @AutoMap()
  @IsOptional()
  @IsBoolean()
  isFindMyTeamEnabled?: boolean;

  /**
   * Workspace organization id
   * @example "123e4567-e89b-12d3-a456-************"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsUUID('4')
  organizationId: string;

  @AutoMap()
  @IsNotEmpty()
  @IsString()
  organizationName: string;
}
