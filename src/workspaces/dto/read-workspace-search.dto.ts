import { AutoMap } from '@automapper/classes';
import { WorkspaceUser } from '../entities/workspace-user.entity';
import { Market } from '../../markets/entities/market.entity';
import { Brand } from '../../brands/entities/brand.entity';

export class ReadWorkspaceSearchDto {
  /**
   * System assigned id of the Workspace
   */
  @AutoMap()
  id: number;

  /**
   * The name of the workspace
   * @example "My Workspace"
   */
  @AutoMap()
  name: string;

  @AutoMap()
  logoUrl: string;

  /**
   * Is this workspace the primary workspace for the organization?
   * @example true
   */
  @AutoMap()
  isPrimary: boolean;

  /**
   * Total number of users in the workspace
   */
  @AutoMap()
  totalUsers?: number;

  /**
   * The organization id of the workspace
   * @example 123e4567-e89b-12d3-a456-************
   */
  @AutoMap()
  organizationId: string;

  /**
   * The organization name of the workspace
   * @example "My Vidmob Organization"
   */
  @AutoMap()
  organizationName?: string;

  /**
   * Markets associated with the workspace
   */
  @AutoMap()
  markets?: Market[];

  /**
   * Brands associated with the workspace
   */
  @AutoMap()
  brands?: Brand[];

  /**
   * Users in the workspace
   */
  @AutoMap()
  users?: WorkspaceUser[];

  /**
   * Ad Accounts Count
   */
  @AutoMap()
  linkedAdAccountsCount?: number;
}
