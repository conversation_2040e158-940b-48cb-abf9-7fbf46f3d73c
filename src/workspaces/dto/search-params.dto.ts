import { IsOptional, IsString } from 'class-validator';

export class SearchParamsDto {
  /**
   * Search string to filter workspaces by name
   * @example "My Workspace"
   */
  @IsOptional()
  @IsString()
  search?: string;

  /**
   * Market to filter workspaces by ISO code
   * @example "usa,can,bra,fra"
   */
  @IsOptional()
  market?: string;

  /**
   * Brand name to filter workspaces by brand
   * @example "Brand X"
   */
  @IsOptional()
  brand?: string;

  constructor(search?: string, market?: string, brand?: string) {
    this.brand = brand ? brand : '';
    this.market = market ? market : '';
    this.search = search ? search : '';
  }
}
