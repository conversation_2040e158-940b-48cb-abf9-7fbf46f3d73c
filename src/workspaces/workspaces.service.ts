import {
  BadRequestException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { UpdateWorkspaceDto } from './dto/update-workspace.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Entity<PERSON>anager, In, Like, Repository } from 'typeorm';
import { Workspace } from './entities/workspace.entity';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { ReadWorkspaceDto } from './dto/read-workspace.dto';
import { OrganizationsService } from '../organizations/organizations.service';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadRelatedWorkspacesDto } from './dto/read-related-workspaces.dto';
import { ReadWorkspaceSearchDto } from './dto/read-workspace-search.dto';
import { SearchParamsDto } from './dto/search-params.dto';
import { hasFilter } from './utils/build-market-where-clause';
import { SplitAndTrimPipe } from 'src/organizations/utils/split-and-trim-pipe';
import { AdAccountsService } from '../ad-accounts/ad-accounts.service';
import { CreateWorkspaceDto } from './dto/create-workspace.dto';
import { ReadWorkspacesFromAdAccountDto } from '../ad-accounts/dto/read-workspaces-from-ad-account.dto';
import {
  DUPLICATE_NAME_ERROR,
  ImportStatus,
  OrganizationUserRoles,
  ProjectManagementMethod,
  ProjectScope,
  SELF_MANAGED_IDENTIFIER,
  WorkspaceManagerRole,
} from '../common/constants/constants';
import { BrandService } from '../brands/brand.service';
import { MarketService } from '../markets/market.service';
import { WorkspaceMarket } from './entities/workspace-market.entity';
import { Market } from '../markets/entities/market.entity';
import { Brand } from '../brands/entities/brand.entity';
import { WorkspaceBrand } from './entities/workspace-brand.entity';
import { AccountType } from './entities/account-type.entity';
import { FeatureWhitelist } from './entities/feature-whitelist.entity';
import { FeatureAccountType } from './entities/feature-account-type.entity';
import { FeatureWorkspace } from './entities/feature-workspace.entity';
import { WorkspaceIndustry } from './entities/partner-industry.entity';
import { WorkspaceUser } from './entities/workspace-user.entity';
import { CreatePlatformAdAccountWorkspaceMapDto } from '../ad-accounts/dto/create-platform-ad-account-workspace-map.dto';
import { OrganizationAdAccountsService } from '../ad-accounts/organization-ad-accounts.service';
import { WorkspaceManager } from './entities/workspace-manager.entity';
import { ReadPlatformAdAccountWorkspaceMapDto } from '../ad-accounts/dto/read-platform-ad-account-workspace-map.dto';
import { AllUserWorkspaceDto } from './dto/read-all-user-workspace.dto';
import { ReadLightWeightWorkspaceDto } from 'src/organizations/dto/read-light-weight-workspace.dto';
import { RealAllUserWorkspacesRawDto } from './dto/real-all-user-workspaces-raw.dto';
import { ValidateOrganizationEntitiesResponseDto } from '../organizations/dto/validate-organization-entities.dto';
import { ProductAccountType } from './entities/product-account-type.entity';
import { startTransaction } from '../common/utils/helper';
import {
  ACCOUNT_TYPE_PUBLIC_SCOPE,
  FEATURE_TYPE,
  featureBrandGovernanceIdentifier,
  featureBringYourOwnCreatorIdentifier,
} from './../organizations/utils/constants';
import { AdAccountImportStatusService } from '../ad-accounts/ad-account-import-status.service';
import { CreateImportStatusDto } from '../ad-accounts/dto/create-import-status.dto';
import { OrganizationWorkspaceRequestDto } from './dto/organization-workspace-request.dto';

@Injectable()
export class WorkspaceService {
  constructor(
    @InjectRepository(Workspace)
    private workspaceRepository: Repository<Workspace>,
    @InjectRepository(WorkspaceUser)
    private workspaceUserRepository: Repository<WorkspaceUser>,
    @InjectRepository(WorkspaceMarket)
    private workspaceMarketRepository: Repository<WorkspaceMarket>,
    @InjectRepository(WorkspaceBrand)
    private workspaceBrandRepository: Repository<WorkspaceBrand>,
    @InjectRepository(AccountType)
    private accountTypeRepository: Repository<AccountType>,
    @InjectRepository(FeatureWhitelist)
    private featureWhitelistRepository: Repository<FeatureWhitelist>,
    @InjectRepository(FeatureAccountType)
    private featureAccountTypeRepository: Repository<FeatureAccountType>,
    @InjectRepository(FeatureWorkspace)
    private featureWorkspaceRepository: Repository<FeatureWorkspace>,
    @InjectRepository(WorkspaceIndustry)
    private workspaceIndustryRepository: Repository<WorkspaceIndustry>,
    @InjectRepository(WorkspaceManager)
    private workspaceManagerRepository: Repository<WorkspaceManager>,
    @InjectRepository(ProductAccountType)
    private productAccountTypeRepository: Repository<ProductAccountType>,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly splitAndTrimPipe: SplitAndTrimPipe,
    @Inject(forwardRef(() => OrganizationsService))
    private readonly organizationService: OrganizationsService,
    @Inject(forwardRef(() => AdAccountsService))
    private readonly adAccountService: AdAccountsService,
    @Inject(forwardRef(() => AdAccountImportStatusService))
    private readonly adAccountStatusService: AdAccountImportStatusService,
    @Inject(forwardRef(() => OrganizationAdAccountsService))
    private readonly organizationAdAccountService: OrganizationAdAccountsService,
    private readonly brandService: BrandService,
    private readonly marketService: MarketService,
  ) {}

  /**
   * Creates a new workspace, attempts to use primary workspace values as defaults
   * @param createWorkspaceDto
   */
  async create(
    createWorkspaceDto: CreateWorkspaceDto,
  ): Promise<ReadWorkspaceDto> {
    await this.validateUniqueName(
      createWorkspaceDto.name,
      createWorkspaceDto.organizationId,
    );
    const organizationId = createWorkspaceDto.organizationId;

    //find primary workspace
    const primaryWorkspace = await this.findPrimaryWorkspaceEntity(
      organizationId,
    );

    if (!primaryWorkspace) {
      throw new NotFoundException(
        `Missing primary workspace for organization ${organizationId}`,
      );
    }

    const primaryAccountType = await this.findAccountType(primaryWorkspace);

    const includeDraftDownloadUrl =
      primaryWorkspace?.includeDraftDownloadUrl ?? true;
    const makerId = primaryWorkspace?.makerId ?? null;
    const parentPartnerId = primaryWorkspace?.parentPartnerId ?? null;
    const pricingRatioTierId = primaryWorkspace?.pricingRatioTierId ?? null;
    const draftReview = primaryWorkspace?.draftReview ?? 'AUTO_APPROVED';
    const { name, isPrimary, brands, markets, logoUrl } = createWorkspaceDto;
    const workspaceLogoUrl = this.getLogoUrl(primaryWorkspace, logoUrl);

    const brandEntities =
      await this.brandService.findBrandsByIdsAndOrganization(
        brands,
        organizationId,
      );

    const marketEntities = await this.marketService.findMarketsByCodes(markets);

    const createdWorkspace: Workspace = await this.workspaceRepository.save({
      name,
      isPrimary,
      workspaceLogoUrl,
      organizationId,
      includeDraftDownloadUrl,
      makerId,
      parentPartnerId,
      pricingRatioTierId,
      projectFinalAssetsRequired: false,
      draftReview,
      personal: false,
      version: 0,
      dateCreated: new Date(),
      lastUpdated: new Date(),
    });

    const accountType = await this.createAccountType(
      createdWorkspace,
      primaryAccountType,
    );
    await this.createProductTypeMappingToAccountType(accountType);
    await this.saveWorkspaceMarkets(marketEntities, createdWorkspace);
    await this.saveWorkspaceBrands(brandEntities, createdWorkspace);
    await this.copyFeatureWhitelist(
      primaryWorkspace,
      createdWorkspace,
      primaryAccountType,
    );
    await this.copyWorkspaceIndustry(primaryWorkspace, createdWorkspace);
    await this.copyWorkspaceManager(primaryWorkspace, createdWorkspace);
    await this.addFeatureBringYourOwnCreator(createdWorkspace);

    //remove old primary workspace since we created a new primary
    if (isPrimary) {
      await this.removeAsPrimaryWorkspace(primaryWorkspace);
    }
    return this.classMapper.map(createdWorkspace, Workspace, ReadWorkspaceDto);
  }

  private async removeAsPrimaryWorkspace(primaryWorkspace: Workspace) {
    // set old primary of a workspace to false
    if (primaryWorkspace?.isPrimary) {
      primaryWorkspace.isPrimary = false;
      primaryWorkspace.lastUpdated = new Date();
      await this.workspaceRepository.update(
        primaryWorkspace.id,
        primaryWorkspace,
      );
    }
  }

  async validateUniqueName(name: string, organizationId: string) {
    const workspace = await this.findWorkspaceByNameAndOrganizationId(
      name,
      organizationId,
    );
    if (workspace) {
      throw new BadRequestException(DUPLICATE_NAME_ERROR);
    }
  }

  /**
   * Saves the relationships between a workspace and its markets
   * @param marketEntities
   * @param createdWorkspace
   * @private
   */
  private async saveWorkspaceMarkets(
    marketEntities: Market[],
    createdWorkspace: Workspace,
  ) {
    const workspaceMarkets: WorkspaceMarket[] = marketEntities.map((market) => {
      const workspaceMarket: WorkspaceMarket = new WorkspaceMarket();
      workspaceMarket.market = market;
      workspaceMarket.workspace = createdWorkspace;
      return workspaceMarket;
    });
    return this.workspaceMarketRepository.save(workspaceMarkets);
  }

  /**
   * Removes the relationships between a workspace and all of its markets
   * @param workspace
   * @private
   */
  private async clearWorkspaceMarkets(workspace: Workspace) {
    return await this.workspaceMarketRepository.delete({
      workspace: workspace,
    });
  }

  private getLogoUrl(primaryWorkspace: Workspace, logoUrl: string) {
    if (logoUrl) {
      return logoUrl;
    }
    return primaryWorkspace?.logoUrl ?? null;
  }

  /**
   * Deletes the relationships between a workspace and its brands
   * @param workspace
   * @private
   */
  private async clearWorkspaceBrands(workspace: Workspace) {
    return await this.workspaceBrandRepository.delete({ workspace: workspace });
  }

  /**
   * Saves the relationships between a workspace and its brands
   * @param brandEntities
   * @param createdWorkspace
   * @private
   */
  private async saveWorkspaceBrands(
    brandEntities: Brand[],
    createdWorkspace: Workspace,
  ) {
    const workspaceBrands: WorkspaceBrand[] = brandEntities.map((brand) => {
      const workspaceBrand: WorkspaceBrand = new WorkspaceBrand();
      workspaceBrand.brand = brand;
      workspaceBrand.workspace = createdWorkspace;
      return workspaceBrand;
    });
    return this.workspaceBrandRepository.save(workspaceBrands);
  }

  /**
   * Lightweight method to load all workspaces within an organization.
   * The purpose of this endpoint is to provide a fast query to retrieve all workspaces, without much querying.
   *
   */
  async getAllLightweight(organizationId: string) {
    const workspaces = await this.workspaceRepository.findBy({
      organizationId,
    });

    return this.classMapper.mapArray(
      workspaces,
      Workspace,
      ReadLightWeightWorkspaceDto,
    );
  }

  /**
   * Find all workspaces
   *
   * @returns
   */
  async findAll(
    paginationOptions: PaginationOptions,
    workspaceIds?: number[],
  ): Promise<PaginatedResultArray<ReadWorkspaceDto>> {
    const options = {
      skip: paginationOptions.offset,
      take: paginationOptions.perPage,
    };
    if (workspaceIds) {
      options['where'] = { id: In(workspaceIds) };
    }
    const [result, total] = await this.workspaceRepository.findAndCount(
      options,
    );

    const readWorkspaceDtos: ReadWorkspaceDto[] = this.classMapper.mapArray(
      result,
      Workspace,
      ReadWorkspaceDto,
    );

    return new PaginatedResultArray<ReadWorkspaceDto>(readWorkspaceDtos, total);
  }

  /**
   *
   * @param id Find a workspace by id
   * @returns
   */
  async findOne(id: number) {
    const workspace = await this.getWorkspaceEntity(id);

    return this.classMapper.map(workspace, Workspace, ReadWorkspaceDto);
  }

  async findOneByName(name: string) {
    const workspace = await this.workspaceRepository.findOne({
      where: { name: name },
    });

    return this.classMapper.map(workspace, Workspace, ReadWorkspaceDto);
  }

  /**
   * @param name Find a workspace by name
   * @param organizationId Find a workspace by organizationId
   * @returns
   */
  async findWorkspaceByNameAndOrganizationId(
    name: string,
    organizationId: string,
  ) {
    const query = `
      SELECT * FROM partner
      WHERE name COLLATE utf8mb4_bin = ? AND organization_id = ?
    `;

    const parameters = [name.replace(/'/g, "''"), organizationId];

    const [workspace] = await this.workspaceRepository.query(query, parameters);

    return this.classMapper.map(workspace, Workspace, ReadWorkspaceDto);
  }

  /**
   * Finds primary workspace using organizationId
   *
   * @param organizationId id of organization
   */
  async findPrimaryWorkspaceEntity(organizationId: string) {
    const workspace = await this.workspaceRepository.findOneBy({
      isPrimary: true,
      organizationId: organizationId,
    });

    return workspace;
  }

  /**
   * Find an accountType of a given workspace
   *
   * @param workspace the workspace entity related to the accountType
   */
  async findAccountType(workspace: Workspace) {
    if (!workspace) {
      return null;
    }
    const accountType = await this.accountTypeRepository.findOneBy({
      id: workspace.accountTypeId,
    });

    return accountType;
  }

  /**
   * Update a workspace
   *
   * @param id - workspace id
   * @param updateWorkspaceDto request containing the fields to update
   * @returns
   */
  async update(id: number, updateWorkspaceDto: UpdateWorkspaceDto) {
    const workspace = await this.getWorkspaceEntity(id);
    const currentIsPrimary = workspace.isPrimary;

    const { name, organizationId, isPrimary, brands, markets } =
      updateWorkspaceDto;
    if (name && name !== workspace.name) {
      await this.validateUniqueName(name, organizationId);
    }
    await this.validateOrganization(organizationId);
    await this.validateWorkspace(organizationId, id);
    // Can not update isPrimary to false if workspace is primary
    await this.validateUpdatePrimary(currentIsPrimary, isPrimary);

    //make updates to brands
    if (brands) {
      await this.updateWorkspaceBrands(workspace, brands, organizationId);
    }

    //make updates to markets
    if (markets) {
      await this.updateWorkspaceMarkets(workspace, markets);
    }

    if (isPrimary) {
      const primaryWorkspace = await this.findPrimaryWorkspaceEntity(
        organizationId,
      );
      await this.removeAsPrimaryWorkspace(primaryWorkspace);
    }

    //make updates to workspace properties
    workspace.name = name ?? workspace.name;
    workspace.isPrimary = isPrimary ?? workspace.isPrimary;
    workspace.lastUpdated = new Date();
    workspace.version = workspace.version++;
    const savedWorkspace = await this.workspaceRepository.save(workspace);

    return this.classMapper.mapAsync(
      savedWorkspace,
      Workspace,
      ReadWorkspaceDto,
    );
  }

  /**
   * Updates workspace market relationships
   * @param workspace the given workspace
   * @param markets the list of markets to have
   * @private
   */
  private async updateWorkspaceMarkets(
    workspace: Workspace,
    markets: string[],
  ) {
    await this.clearWorkspaceMarkets(workspace);
    const marketEntities = await this.marketService.findMarketsByCodes(markets);
    await this.saveWorkspaceMarkets(marketEntities, workspace);
  }

  private async updateWorkspaceBrands(
    workspace: Workspace,
    brands: string[],
    organizationId: string,
  ) {
    await this.clearWorkspaceBrands(workspace);
    const brandEntities =
      await this.brandService.findBrandsByIdsAndOrganization(
        brands,
        organizationId,
      );
    await this.saveWorkspaceBrands(brandEntities, workspace);
  }

  private async validateOrganization(organizationId: string) {
    if (!organizationId) {
      throw new BadRequestException(`Organization with ID is required`);
    } else {
      const readOrganizationDto = await this.organizationService.findOne(
        organizationId,
      );

      if (!readOrganizationDto) {
        throw new NotFoundException(
          `Organization with ID ${organizationId} not found`,
        );
      }

      return readOrganizationDto;
    }
  }

  async validateUpdatePrimary(
    currentIsPrimary: boolean,
    isPrimaryToBeUpdated: boolean,
  ) {
    if (isPrimaryToBeUpdated == false && currentIsPrimary == true) {
      throw new BadRequestException(
        `Workspace is primary and cannot be updated to false`,
      );
    }
  }

  private async validateWorkspace(organizationId: string, workspaceId: number) {
    if (!workspaceId) {
      throw new BadRequestException(`Workspace with ID is required`);
    } else {
      const workspace = await this.workspaceRepository.findOneBy({
        organizationId: organizationId,
        id: workspaceId,
      });

      if (!workspace) {
        throw new BadRequestException(
          `Workspace with ID ${workspaceId} does not belong to Organization with ID ${organizationId}`,
        );
      }

      return workspace;
    }
  }

  /**
   * Delete a workspace. NYI
   *
   * @param id
   * @returns
   */
  remove(id: number) {
    return `This action removes a #${id} workspace. NYI`;
  }

  /**
   * Find workspaces by organizationId and return them as ReadWorkspaceDto[]
   * Optionally filters workspaces whose name contains the 'search' string
   *
   * @param organizationId
   * @param paginationOptions
   * @param searchParams
   *
   */
  async getWorkspacesByOrganizationIdAndSearch(
    organizationId: string,
    paginationOptions: PaginationOptions,
    searchParams: SearchParamsDto,
  ): Promise<PaginatedResultArray<ReadWorkspaceSearchDto>> {
    const workspaceIds = await this.findWorkspaceIdsByOrganizationId(
      organizationId,
      searchParams,
    );

    const [workspaceBySearch, total] =
      await this.findWorkspacesByWorkspaceIdsAndSearchParams(
        workspaceIds,
        searchParams,
        paginationOptions,
      );

    const result: ReadWorkspaceSearchDto[] = this.classMapper.mapArray(
      workspaceBySearch,
      Workspace,
      ReadWorkspaceSearchDto,
    );

    return new PaginatedResultArray<ReadWorkspaceSearchDto>(result, total);
  }

  private async findWorkspaceIdsByOrganizationId(
    organizationId: string,
    searchParams: SearchParamsDto,
  ): Promise<number[]> {
    const { market, brand } = searchParams;
    let whereClause: Record<string, any> = { organizationId };

    const hasMarketFilter = hasFilter(market);
    const hasBrandFilter = hasFilter(brand);

    if (hasMarketFilter) {
      const formattedMarket = this.splitAndTrimPipe.transform(market, null);

      whereClause = {
        ...whereClause,
        markets: { isoCode: In(formattedMarket) },
      };
    }

    if (hasBrandFilter) {
      whereClause = {
        ...whereClause,
        brands: { name: Like(`%${brand}%`) },
      };
    }

    const workspaces = await this.workspaceRepository.find({
      select: ['id'],
      where: whereClause,
    });

    return workspaces.map((workspace) => workspace.id);
  }

  private async findWorkspacesByWorkspaceIdsAndSearchParams(
    workspaceIds: number[],
    searchParams: SearchParamsDto,
    paginationOptions: PaginationOptions,
  ): Promise<[Workspace[], number]> {
    const { search } = searchParams;
    return this.workspaceRepository
      .createQueryBuilder('workspace')
      .loadRelationCountAndMap(
        'workspace.platformAdAccountToWorkspacesCount',
        'workspace.platformAdAccountToWorkspaces',
        'platformAdAccountToWorkspace',
        (qb) =>
          qb
            .innerJoin(
              'platformAdAccountToWorkspace.platformAdAccount',
              'platformAdAccount',
            )
            .andWhere('platformAdAccount.canAccess = :canAccess', {
              canAccess: 1,
            }),
      )
      .innerJoinAndSelect('workspace.organization', 'organization')
      .leftJoinAndSelect('workspace.markets', 'markets')
      .leftJoinAndSelect('workspace.brands', 'brands')
      .where('workspace.id IN (:...workspaceIds)', { workspaceIds })
      .andWhere('workspace.name LIKE :search', { search: `%${search}%` })
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .orderBy('workspace.name', 'ASC')
      .addOrderBy('workspace.id', 'ASC')
      .getManyAndCount();
  }

  /**
   * List workspaces by organizationId and userId
   * Additionally, filter by workspace name
   * and optionally filter by market
   //  * @param paginationOptions
   //  * @param organizationId
   //  * @param userId
   //  * @param searchParams
   //  * @returns
   //  */
  async getWorkspacesByOrganizationIdAndUserIdAndSearch(
    organizationId: string,
    userId: number,
    getWorkspaceSearchParams: SearchParamsDto,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadWorkspaceSearchDto>> {
    const { search, market, brand } = getWorkspaceSearchParams;
    const hasMarketFilter = hasFilter(market);
    const hasBrandFilter = hasFilter(brand);

    const queryBuilder = this.workspaceRepository
      .createQueryBuilder('workspace')
      .leftJoinAndSelect('workspace.organization', 'organization')
      .leftJoin('workspace.workspaceUsers', 'workspaceUser')
      .leftJoin('workspace.workspaceManagers', 'workspaceManager')
      .leftJoinAndSelect('workspace.markets', 'market')
      .leftJoinAndSelect(
        'workspace.brands',
        'brand',
        'brand.deleted = :deleted',
        {
          deleted: false,
        },
      )
      .loadRelationCountAndMap(
        'workspace.platformAdAccountToWorkspacesCount',
        'workspace.platformAdAccountToWorkspaces',
        'platformAdAccountToWorkspace',
        (qb) =>
          qb
            .innerJoin(
              'platformAdAccountToWorkspace.platformAdAccount',
              'platformAdAccount',
            )
            .andWhere('platformAdAccount.canAccess = :canAccess', {
              canAccess: 1,
            }),
      )

      // Main filter
      .where('workspace.organizationId = :organizationId', { organizationId })
      .andWhere('workspace.name LIKE :search', { search: `%${search}%` })
      .andWhere(
        '((workspaceUser.userId = :userId AND workspaceUser.active = :active) OR (workspaceManager.managerId = :userId AND workspaceManager.role IN (:...roles)))',
        {
          userId,
          active: true,
          roles: [
            WorkspaceManagerRole.PROJECT_MANAGER,
            WorkspaceManagerRole.ACCOUNT_MANAGER,
          ],
        },
      );

    if (hasMarketFilter) {
      const formattedMarket = this.splitAndTrimPipe.transform(market, null);

      queryBuilder
        .leftJoin('workspace.markets', 'filteredMarket')
        .andWhere('filteredMarket.isoCode IN (:...markets)', {
          markets: formattedMarket,
        });
    }

    if (hasBrandFilter) {
      const subQuery = this.workspaceRepository
        .createQueryBuilder('workspaceSub')
        .leftJoin('workspaceSub.brands', 'brandSub')
        .where('brandSub.name Like :brandName', {
          brandName: `%${brand}%`,
        })
        .select('workspaceSub.id');

      queryBuilder
        .andWhere('workspace.id IN (' + subQuery.getQuery() + ')')
        .setParameters(subQuery.getParameters());
    }

    const [result, total] = await queryBuilder
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .orderBy('workspace.name', 'ASC')
      .addOrderBy('workspace.id', 'ASC')
      .getManyAndCount();

    const readWorkspaceDtos: ReadWorkspaceSearchDto[] =
      this.classMapper.mapArray(result, Workspace, ReadWorkspaceSearchDto);

    return new PaginatedResultArray<ReadWorkspaceSearchDto>(
      readWorkspaceDtos,
      total,
    );
  }

  /**
   * Find all workspaces associated with a user
   *
   * @param userId
   * @param paginationOptions
   * @returns
   */
  async getWorkspacesByUserId(
    paginationOptions,
    userId: number,
  ): Promise<PaginatedResultArray<ReadWorkspaceDto>> {
    const [result, total] = await this.workspaceRepository
      .createQueryBuilder('workspace')
      .innerJoin('workspace.workspaceUsers', 'workspaceUser')
      .innerJoin('workspaceUser.user', 'user')
      .where('user.id = :userId', { userId: userId })
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .getManyAndCount();

    const readWorkspaceDtos: ReadWorkspaceDto[] = this.classMapper.mapArray(
      result,
      Workspace,
      ReadWorkspaceDto,
    );

    return new PaginatedResultArray<ReadWorkspaceDto>(readWorkspaceDtos, total);
  }

  /**
   * Find all workspaces (including sub-workspaces) a user is a member of
   * or has a role in  ACCOUNT_MANAGER, PROJECT_MANAGER EXTERNAL_CREATIVE_OPS_MANAGER
   *
   * @param userId
   * @param organizationId
   * @param workspaceFeatures
   * @returns AllUserWorkspaceDto[]
   */
  async getWorkspacesAllOrgsByUserId(
    userId: number,
    organizationId?: string,
    workspaceFeatures?: string[],
  ): Promise<AllUserWorkspaceDto[]> {
    const organizationIdsWithUpdateAllPermissions =
      await this.organizationService.findOrgIdsForUserWithUpdateAllWorkspacePermission(
        userId,
      );
    // Sub-query to select partners where person has an active entry in partner_person
    const partnerPersonSubQuery = this.workspaceUserRepository
      .createQueryBuilder('wu')
      .select('wu.workspaceId')
      .innerJoin('wu.workspace', 'partner')
      .where('wu.userId = :personId', { personId: userId })
      .andWhere('wu.active = 1');

    // Sub-query to select partners where person has a specific role in partner_manager
    const partnerManagerSubQuery = this.workspaceManagerRepository
      .createQueryBuilder('wm')
      .select('wm.workspaceId')
      .innerJoin('wm.workspace', 'partner')
      .where('wm.managerId = :personId', { personId: userId })
      .andWhere('wm.role IN (:...roles)', {
        roles: [
          WorkspaceManagerRole.ACCOUNT_MANAGER,
          WorkspaceManagerRole.PROJECT_MANAGER,
          WorkspaceManagerRole.EXTERNAL_CREATIVE_OPS_MANAGER,
        ],
      });

    const combinedSubqueries = `
      (workspace.id IN (${partnerPersonSubQuery.getQuery()})
      OR
      workspace.id IN (${partnerManagerSubQuery.getQuery()})
      ${
        !organizationIdsWithUpdateAllPermissions.length
          ? ')'
          : ' OR workspace.organization_id IN (:...organizationIdsWithUpdateAll))'
      }
    `;

    // Main query
    const query = this.workspaceRepository
      .createQueryBuilder('workspace')
      .select(['workspace', 'accountType', 'organization.name'])
      .innerJoin('workspace.accountType', 'accountType')
      .innerJoin('workspace.organization', 'organization')
      .where(combinedSubqueries, {
        organizationIdsWithUpdateAll: organizationIdsWithUpdateAllPermissions,
        ...partnerPersonSubQuery.getParameters(),
        ...partnerManagerSubQuery.getParameters(),
      })
      .groupBy('workspace.id')
      .orderBy('workspace.name', 'ASC');

    if (organizationId) {
      query.andWhere('workspace.organization_id = :organizationId', {
        organizationId,
      });
    }

    query.distinct(true);
    const allUserWorkspaces = await query.getRawMany();
    let filteredWorkspaces = allUserWorkspaces;

    if (workspaceFeatures) {
      filteredWorkspaces = await this.filterWorkspacesByFeatures(
        workspaceFeatures,
        allUserWorkspaces,
      );
    }

    return this.classMapper.mapArray(
      filteredWorkspaces,
      RealAllUserWorkspacesRawDto,
      AllUserWorkspaceDto,
    );
  }

  private async filterWorkspacesByFeatures(
    features: string[],
    workspaces: RealAllUserWorkspacesRawDto[],
  ): Promise<RealAllUserWorkspacesRawDto[]> {
    const initialAccumulator: {
      selfManagedWorkspaces: {
        workspaces: RealAllUserWorkspacesRawDto[];
        workspaceIds: number[];
      };
      otherWorkspaces: {
        workspaces: RealAllUserWorkspacesRawDto[];
        accountTypeIds: number[];
      };
    } = {
      selfManagedWorkspaces: { workspaces: [], workspaceIds: [] },
      otherWorkspaces: { workspaces: [], accountTypeIds: [] },
    };
    const { selfManagedWorkspaces, otherWorkspaces } = workspaces.reduce(
      (acc, workspace) => {
        if (workspace.accountType_name == SELF_MANAGED_IDENTIFIER) {
          acc.selfManagedWorkspaces.workspaceIds.push(
            parseInt(workspace.workspace_id, 10),
          );
          acc.selfManagedWorkspaces.workspaces.push(workspace);
        } else {
          acc.otherWorkspaces.workspaces.push(workspace);
          acc.otherWorkspaces.accountTypeIds.push(
            parseInt(workspace.accountType_id, 10),
          );
        }

        return acc;
      },
      initialAccumulator,
    );

    const [validSelfManagedWorkspaceIds, validAccountTypeIds] =
      await Promise.all([
        this.getSelfManagedWorkspaceIdsByFeatures(
          features,
          selfManagedWorkspaces.workspaceIds,
        ),
        this.getAccountTypeIdsByFeatures(features, [
          ...new Set(otherWorkspaces.accountTypeIds),
        ]),
      ]);
    const selfManagedWorkspacesWithAllFeatures =
      selfManagedWorkspaces.workspaces.filter((workspace) =>
        validSelfManagedWorkspaceIds.includes(workspace.workspace_id),
      );
    const otherWorkspacesWithAllFeatures = otherWorkspaces.workspaces.filter(
      (workspace) => validAccountTypeIds.includes(workspace.accountType_id),
    );

    return [
      ...selfManagedWorkspacesWithAllFeatures,
      ...otherWorkspacesWithAllFeatures,
    ];
  }

  private async getSelfManagedWorkspaceIdsByFeatures(
    featureIdentifiers: string[],
    selfManagedWorkspaceIds: number[],
  ): Promise<string[]> {
    if (!selfManagedWorkspaceIds.length) {
      return [];
    }

    const idsWithAllFeatures = await this.featureWorkspaceRepository
      .createQueryBuilder('featureWorkspace')
      .innerJoin('featureWorkspace.featureWhiteList', 'featureWhiteList')
      .where('featureWorkspace.partnerId IN (:...selfManagedWorkspaceIds)', {
        selfManagedWorkspaceIds,
      })
      .andWhere('featureWhiteList.identifier IN (:...featureIdentifiers)', {
        featureIdentifiers,
      })
      .groupBy('featureWorkspace.partnerId')
      .having('COUNT(DISTINCT featureWhiteList.identifier) = :numFeatures', {
        numFeatures: featureIdentifiers.length,
      })
      .select(['featureWorkspace.partnerId'])
      .getRawMany();

    return idsWithAllFeatures.map((obj) => obj.featureWorkspace_partner_id);
  }

  private async getAccountTypeIdsByFeatures(
    featureIdentifiers: string[],
    accountTypeIds: number[],
  ): Promise<string[]> {
    if (!accountTypeIds.length) {
      return [];
    }

    const accountTypeIdsWithAllFeatures =
      await this.featureAccountTypeRepository
        .createQueryBuilder('featureAccountType')
        .innerJoin('featureAccountType.featureWhiteList', 'featureWhiteList')
        .where('featureAccountType.accountTypeId IN (:...accountTypeIds)', {
          accountTypeIds,
        })
        .andWhere('featureWhiteList.identifier IN (:...featureIdentifiers)', {
          featureIdentifiers,
        })
        .groupBy('featureAccountType.accountTypeId')
        .having('COUNT(DISTINCT featureWhiteList.identifier) = :numFeatures', {
          numFeatures: featureIdentifiers.length,
        })
        .select(['featureAccountType.accountTypeId'])
        .getRawMany();

    return accountTypeIdsWithAllFeatures.map(
      (obj) => obj.featureAccountType_account_type_id,
    );
  }

  /**
   * Check if the Brand Governance feature is enabled for a workspace.
   *
   * @param workspaceId - the ID of the workspace
   * @returns - an object with a boolean indicating if the feature is enabled or not
   */
  async isBrandGovernanceEnabled(workspaceId: number) {
    const workspace = await this.workspaceRepository.findOne({
      where: { id: workspaceId },
      relations: ['accountType'],
    });

    if (!workspace || !workspace.accountType) {
      throw new NotFoundException(
        'Workspace or account type not found for workspace ID ' + workspaceId,
      );
    }

    if (
      workspace.accountType.scope === ACCOUNT_TYPE_PUBLIC_SCOPE &&
      workspace.accountType.name === SELF_MANAGED_IDENTIFIER
    ) {
      const directFeature = await this.featureWorkspaceRepository.exist({
        where: {
          workspace: { id: workspaceId },
          featureWhiteList: { identifier: featureBrandGovernanceIdentifier },
        },
        relations: ['featureWhiteList'],
      });

      return { brandGovernance: !!directFeature };
    } else {
      const featureViaAccountType =
        await this.featureAccountTypeRepository.exist({
          where: {
            accountType: { id: workspace.accountType.id },
            featureWhiteList: { identifier: featureBrandGovernanceIdentifier },
          },
          relations: ['featureWhiteList'],
        });

      return { brandGovernance: !!featureViaAccountType };
    }
  }

  private async isFeatureEnabled(
    workspaceId: number,
    featureIdentifier: string,
  ): Promise<boolean> {
    const ws = await this.workspaceRepository.findOne({
      where: { id: workspaceId },
      relations: ['accountType'],
    });
    if (!ws || !ws.accountType) {
      throw new NotFoundException(
        `Workspace or account type not found for workspace ID ${workspaceId}`,
      );
    }

    const direct = () =>
      this.featureWorkspaceRepository.exist({
        where: {
          workspace: { id: workspaceId },
          featureWhiteList: { identifier: featureIdentifier },
        },
        relations: ['featureWhiteList'],
      });

    const viaAccountType = () =>
      this.featureAccountTypeRepository.exist({
        where: {
          accountType: { id: ws.accountType.id },
          featureWhiteList: { identifier: featureIdentifier },
        },
        relations: ['featureWhiteList'],
      });

    const selfManaged =
      ws.accountType.scope === ACCOUNT_TYPE_PUBLIC_SCOPE &&
      ws.accountType.name === SELF_MANAGED_IDENTIFIER;

    return !!(selfManaged ? await direct() : await viaAccountType());
  }

  /**
   * Returns a map of every known featureIdentifier → whether it's enabled.
   */
  async getAllFeaturesForWorkspace(
    workspaceId: number,
  ): Promise<Record<string, boolean>> {
    const all = await this.featureWhitelistRepository.find({
      select: ['identifier'],
      where: { featureType: FEATURE_TYPE.ACCOUNT_TYPE },
    });

    const pairs = await Promise.all(
      all.map(async ({ identifier }) => {
        const enabled = await this.isFeatureEnabled(workspaceId, identifier);
        return [identifier, enabled] as const;
      }),
    );

    return Object.fromEntries(pairs);
  }

  /**
   * Get a workspace entity by id. Throws NotFoundException if not found.
   *
   * @param id Find a workspace by id
   * @returns workspace entity
   */
  async getWorkspaceEntity(id: number) {
    const workspace = await this.workspaceRepository.findOneBy({ id });

    if (!workspace) {
      throw new NotFoundException(`Workspace with ID ${id} not found`);
    }
    return workspace;
  }

  /**
   * THIS IS A HACK UNTIL ORGANIZATIONS ARE POPULATED WITH WORKSPACES.
   * Get a list of related workspaces ordered by name.
   * Given a workspace id return all the other workspaces that are in the same organization.
   * This will be replaced by a method to find all workspaces in an org once that's available.
   * @param id - the id of the workspace (partner).
   */
  async getRelatedWorkspaces(id: number) {
    const workspace = await this.getWorkspaceEntity(id);

    const KENVUE_WORKSPACE_PREFIX = 'Kenvue - ';
    const KELLOGGS_WORKSPACE_PREFIX = 'Kelloggs ';
    const LOREAL_WORKSPACE_PREFIX = "L'Oréal ";
    const ABI_WORKSPACE_PREFIX = 'ABI - ';

    let relatedWorkspaces: Workspace[];
    if (workspace.name.startsWith(KENVUE_WORKSPACE_PREFIX)) {
      relatedWorkspaces = await this.workspaceRepository.find({
        where: { name: Like(KENVUE_WORKSPACE_PREFIX + '%') },
        order: { name: 'ASC' },
      });
    } else if (workspace.name.startsWith(KELLOGGS_WORKSPACE_PREFIX)) {
      relatedWorkspaces = await this.workspaceRepository.find({
        where: { name: Like(KELLOGGS_WORKSPACE_PREFIX + '%') },
        order: { name: 'ASC' },
      });
    } else if (workspace.name.startsWith(LOREAL_WORKSPACE_PREFIX)) {
      relatedWorkspaces = await this.workspaceRepository.find({
        where: { name: Like(LOREAL_WORKSPACE_PREFIX + '%') },
        order: { name: 'ASC' },
      });
    } else if (workspace.name.startsWith(ABI_WORKSPACE_PREFIX)) {
      relatedWorkspaces = await this.workspaceRepository.find({
        where: { name: Like(ABI_WORKSPACE_PREFIX + '%') },
        order: { name: 'ASC' },
      });
    } else {
      relatedWorkspaces = [workspace];
    }
    return this.classMapper.mapArray(
      relatedWorkspaces,
      Workspace,
      ReadRelatedWorkspacesDto,
    );
  }

  async connectPlatformAdAccountsToAWorkspace(
    workspace: Workspace,
    platformAdAccountIds: number[],
  ): Promise<string> {
    return this.workspaceRepository.manager.transaction((entityManager) => {
      return this.connectPlatformAdAccountsToAWorkspaceInTransaction(
        entityManager,
        workspace,
        platformAdAccountIds,
      );
    });
  }

  async connectPlatformAdAccountsToAWorkspaceInTransaction(
    entityManager: EntityManager,
    workspace: Workspace,
    platformAdAccountIds: number[],
  ): Promise<string> {
    const connectedPlatformAdAccountIds =
      await this.adAccountService.getPlatformAdAccountIdsConnectedToAWorkspace(
        entityManager,
        workspace.id,
        platformAdAccountIds,
      );
    if (platformAdAccountIds.length === connectedPlatformAdAccountIds.length) {
      return `Platform ad accounts successfully connected to workspace ${workspace.id}. Number of mappings created: 0`;
    }
    const accountWorkspaceMappingsPreUpdate =
      await this.adAccountService.getPlatformAdAccountWorkspaceMappings(
        entityManager,
        platformAdAccountIds,
      );
    platformAdAccountIds = platformAdAccountIds.filter(
      (id) => !connectedPlatformAdAccountIds.includes(id),
    );
    const createMapDtos: CreatePlatformAdAccountWorkspaceMapDto[] =
      await this.buildPlatformAdAccountWorkspaceConnectionDtosForAWorkspace(
        workspace.id,
        platformAdAccountIds,
      );
    const affectedConnections: ReadPlatformAdAccountWorkspaceMapDto[] =
      await this.adAccountService.createWorkspacePlatformAdAccountConnections(
        entityManager,
        createMapDtos,
      );

    await this.updateAllAccountImportStatusOnNewConnection(
      entityManager,
      workspace,
      affectedConnections,
      accountWorkspaceMappingsPreUpdate,
    );

    return `Platform Ad Accounts successfully connected to workspace ${workspace.id}. Number of mappings created: ${affectedConnections.length}`;
  }

  isAccountUnmapped(
    platformAdAccountId: number,
    accountWorkspaceMappings: Map<number, number[]>,
  ): boolean {
    const workspaces = accountWorkspaceMappings?.get(platformAdAccountId);
    return !workspaces?.length;
  }

  async updateAllAccountImportStatusOnNewConnection(
    entityManager: EntityManager,
    workspace: Workspace,
    affectedConnections: ReadPlatformAdAccountWorkspaceMapDto[],
    accountWorkspaceMappingsPreUpdate: Map<number, number[]>,
  ) {
    if (!affectedConnections.length) {
      return;
    }

    const accountUpdatePromises = affectedConnections.map((connection) => {
      return this.updateAccountImportStatusOnNewConnection(
        entityManager,
        connection.platformAdAccountId,
        workspace,
        accountWorkspaceMappingsPreUpdate,
      );
    });
    await Promise.all(accountUpdatePromises);
  }

  async updateAccountImportStatusOnNewConnection(
    entityManager: EntityManager,
    platformAdAccountId: number,
    workspace: Workspace,
    accountWorkspaceMappingsPreUpdate: Map<number, number[]>,
  ) {
    if (
      this.isAccountUnmapped(
        platformAdAccountId,
        accountWorkspaceMappingsPreUpdate,
      )
    ) {
      const createStatusDto: CreateImportStatusDto = {
        platformAdAccountId: platformAdAccountId,
        organizationId: workspace.organizationId,
        status: ImportStatus.PROCESSING,
        reason: {
          message: `Platform ad account ${platformAdAccountId} is connected to workspace ${workspace.id}.`,
        },
      };
      await this.adAccountStatusService.createImportStatus(
        entityManager,
        createStatusDto,
      );
    }
  }

  async disconnectPlatformAdAccountsToAWorkspace(
    workspace: Workspace,
    platformAdAccountIds: number[],
  ): Promise<string> {
    return this.workspaceRepository.manager.transaction((entityManager) => {
      return this.disconnectPlatformAdAccountsToAWorkspaceInTransaction(
        entityManager,
        workspace,
        platformAdAccountIds,
      );
    });
  }

  async disconnectPlatformAdAccountsToAWorkspaceInTransaction(
    entityManager: EntityManager,
    workspace: Workspace,
    platformAdAccountIds: number[],
  ): Promise<string> {
    const affectedConnections: ReadPlatformAdAccountWorkspaceMapDto[] =
      await this.adAccountService.deleteWorkspacePlatformAdAccountConnections(
        entityManager,
        workspace.id,
        platformAdAccountIds,
      );
    const accountWorkspaceMappingsPostUpdate =
      await this.adAccountService.getPlatformAdAccountWorkspaceMappings(
        entityManager,
        platformAdAccountIds,
      );
    await this.createAllAccountImportStatusOnDisconnection(
      entityManager,
      workspace,
      affectedConnections,
      accountWorkspaceMappingsPostUpdate,
    );
    return `Platform Ad Accounts successfully disconnected from workspace ${workspace.id}. Number of mappings deleted: ${affectedConnections.length}`;
  }

  async createAllAccountImportStatusOnDisconnection(
    entityManager: EntityManager,
    workspace: Workspace,
    affectedConnections: ReadPlatformAdAccountWorkspaceMapDto[],
    accountWorkspaceMappingsPostUpdate: Map<number, number[]>,
  ) {
    if (!affectedConnections.length) {
      return;
    }

    const accountUpdatePromises = affectedConnections.map((connection) => {
      return this.createAccountImportStatusOnDisconnection(
        entityManager,
        connection.platformAdAccountId,
        workspace,
        accountWorkspaceMappingsPostUpdate,
      );
    });
    await Promise.all(accountUpdatePromises);
  }

  async createAccountImportStatusOnDisconnection(
    entityManager: EntityManager,
    platformAdAccountId: number,
    workspace: Workspace,
    accountWorkspaceMappingsPostUpdate: Map<number, number[]>,
  ) {
    if (
      this.isAccountUnmapped(
        platformAdAccountId,
        accountWorkspaceMappingsPostUpdate,
      )
    ) {
      const createStatusDto: CreateImportStatusDto = {
        platformAdAccountId: platformAdAccountId,
        organizationId: workspace.organizationId,
        status: ImportStatus.NOT_IMPORTING,
        reason: {
          message: `Platform ad account ${platformAdAccountId} disconnected from workspace ${workspace.id}.`,
        },
      };
      await this.adAccountStatusService.createImportStatus(
        entityManager,
        createStatusDto,
      );
    }
  }

  async arePlatformAdAccountsAbleToMapToAnOrganizationWorkspace(
    workspace: Workspace,
    platformAdAccountIds: number[],
  ) {
    const numberOfOrganizationPlatformAdAccountConnections =
      await this.organizationAdAccountService.countNumberOfOrganizationPlatformAdAccountConnections(
        workspace.organizationId,
        platformAdAccountIds,
      );
    if (
      numberOfOrganizationPlatformAdAccountConnections !=
      platformAdAccountIds.length
    ) {
      throw new ForbiddenException(
        `One or more platform ad account do not belongs to organization ${workspace.organizationId}`,
      );
    }
  }

  private async getAllWorkspaceNamesAndDateCreatedFromAdAccount(
    paginationOptions: PaginationOptions,
    organizationId: string,
    adAccountIds: number[],
  ): Promise<[ReadWorkspacesFromAdAccountDto[], number]> {
    const workspaceQuery = this.workspaceRepository
      .createQueryBuilder('workspace')
      .innerJoin(
        'workspace.platformAdAccountToWorkspaces',
        'platformAdAccountToWorkspace',
      )
      .select('workspace.id', 'id')
      .addSelect('workspace.name', 'name')
      .addSelect('workspace.dateCreated', 'createdAt')
      .where(
        'workspace.organizationId = :organizationId and platformAdAccountToWorkspace.platformAdAccountId IN (:...adAccountIds)',
        { organizationId, adAccountIds },
      )
      .offset(paginationOptions.offset)
      .limit(paginationOptions.perPage);
    const workspaces =
      await workspaceQuery.getRawMany<ReadWorkspacesFromAdAccountDto>();
    if (workspaces.length === 0) {
      return [[], 0];
    }
    return [workspaces, await workspaceQuery.getCount()];
  }

  private async isAdAccountConnectedToOrg(
    adAccountId: string,
    orgId: string,
  ): Promise<boolean> {
    const listOfOrgs =
      await this.organizationAdAccountService.findOrganizationsForAdAccount([
        adAccountId,
      ]);
    if (listOfOrgs.length === 0) return false;
    const expectedOrg = listOfOrgs.filter((org) => org.id === orgId);
    return expectedOrg.length === 1;
  }

  async grabAllWorkspacesFromAnAdAccountForAnOrganization(
    organizationId: string,
    platformAccountId: string,
    paginationOptions: PaginationOptions,
  ) {
    const organizationExist =
      await this.organizationService.doesOrganizationExist(organizationId);
    if (!organizationExist)
      throw new NotFoundException(
        `Organization with ID ${organizationId} not found`,
      );
    const isAdAccountConnectToOrg = await this.isAdAccountConnectedToOrg(
      platformAccountId,
      organizationId,
    );
    if (!isAdAccountConnectToOrg)
      throw new NotFoundException(
        `AdAccount with ID ${platformAccountId} not found in organization with ID ${organizationId}`,
      );
    const adAccountIds =
      await this.adAccountService.getAdAccountsIdsFromPlatformAccountIds([
        platformAccountId,
      ]);
    if (adAccountIds?.length !== 1)
      throw new NotFoundException(
        `AdAccount with ID ${platformAccountId} not found`,
      );
    const expectedAdAccountId = adAccountIds[0];
    const [workspaces, count] =
      await this.getAllWorkspaceNamesAndDateCreatedFromAdAccount(
        paginationOptions,
        organizationId,
        [expectedAdAccountId],
      );

    return new PaginatedResultArray<ReadWorkspacesFromAdAccountDto>(
      workspaces,
      count,
    );
  }

  generateFourRandomDigits() {
    return Math.floor(Math.random() * 9000) + 1000;
  }

  createAccountName(name: string, organizationId: string) {
    const organizationIdSuffix = ` - ${organizationId}-${this.generateFourRandomDigits()}`;
    const maxAccountNameLength = 190 - organizationIdSuffix.length;
    return name.substring(0, maxAccountNameLength) + organizationIdSuffix;
  }

  private async createAccountType(
    createdWorkspace: Workspace,
    primaryAccountType: AccountType,
  ) {
    // We don't need to create a new account type for non-enterprise accounts
    if (
      primaryAccountType &&
      primaryAccountType?.scope != ProjectScope.ENTERPRISE.toString()
    ) {
      createdWorkspace.accountType = primaryAccountType;
      await this.workspaceRepository.update(createdWorkspace.id, {
        accountType: primaryAccountType,
        accountTypeId: primaryAccountType.id,
      });
      return primaryAccountType;
    }
    const accountType = new AccountType();
    accountType.name = this.createAccountName(
      createdWorkspace.name,
      createdWorkspace.organizationId,
    );
    accountType.version = 0;
    accountType.scope =
      primaryAccountType?.scope ?? ProjectScope.ENTERPRISE.toString();
    accountType.allowsCustomAutoBids =
      primaryAccountType?.allowsCustomAutoBids ?? false;
    accountType.displayBidPrice = primaryAccountType?.displayBidPrice ?? true;
    accountType.markup = primaryAccountType?.markup ?? 0;
    accountType.packagePricing = primaryAccountType?.packagePricing ?? 0;
    accountType.partnerId = primaryAccountType?.partnerId
      ? createdWorkspace.id
      : null;
    accountType.projectManagementMethod =
      primaryAccountType?.projectManagementMethod ??
      ProjectManagementMethod.MANAGED.toString();
    accountType.updatedPricingModel =
      primaryAccountType?.updatedPricingModel ?? 0;
    accountType.pricingRatioRate = primaryAccountType?.pricingRatioRate ?? null;
    accountType.workspaces = [createdWorkspace];
    accountType.dateCreated = new Date();
    accountType.lastUpdated = new Date();
    const copiedAccountType = await this.accountTypeRepository.save(
      accountType,
    );
    createdWorkspace.accountTypeId = copiedAccountType.id;
    createdWorkspace.accountType = copiedAccountType;
    return copiedAccountType;
  }

  private async createProductTypeMappingToAccountType(
    accountType: AccountType,
  ) {
    // There's only one Account Type for all Self Managed Workspaces
    const existingAccountType = await this.productAccountTypeRepository.findOne(
      { where: { accountTypeId: accountType.id } },
    );
    if (existingAccountType) return;

    const businessStandardProductAccountTypes =
      await this.fetchBusinessStandardProductAccountTypeMappings();

    const newAccountTypeProductMappings: ProductAccountType[] =
      businessStandardProductAccountTypes.map((productAccountType) => ({
        productId: productAccountType.productId,
        accountTypeId: accountType.id,
        sequence: productAccountType.sequence,
      }));

    await this.insertProductAccountTypeMappings(newAccountTypeProductMappings);
  }

  private async fetchBusinessStandardProductAccountTypeMappings(): Promise<
    ProductAccountType[]
  > {
    return this.productAccountTypeRepository
      .createQueryBuilder()
      .select([
        'product_account_type.productId',
        'product_account_type.sequence',
      ])
      .from(ProductAccountType, 'product_account_type')
      .innerJoin(
        'account_type',
        'account_type',
        'product_account_type.account_type_id = account_type.id',
      )
      .innerJoin(
        'default_account_type',
        'default_account_type',
        'account_type.id = default_account_type.account_type_id',
      )
      .where('default_account_type.account_type_identifier = :identifier', {
        identifier: 'business',
      })
      .getMany();
  }

  private async insertProductAccountTypeMappings(
    productAccountTypes: ProductAccountType[],
  ) {
    const queryRunner = await startTransaction(
      this.productAccountTypeRepository,
    );
    await this.productAccountTypeRepository
      .createQueryBuilder('productAccountType', queryRunner)
      .insert()
      .into(ProductAccountType)
      .values(productAccountTypes)
      .execute();

    await queryRunner.commitTransaction();
  }

  private async copyFeatureWhitelist(
    primaryWorkspace: Workspace,
    createdWorkspace: Workspace,
    primaryAccountType: AccountType,
  ) {
    if (!primaryAccountType) {
      // should never happen
      return;
    } else if (primaryAccountType.name == SELF_MANAGED_IDENTIFIER) {
      await this.copyFeaturesWorkspace(primaryWorkspace, createdWorkspace);
    } else {
      await this.copyFeaturesAccountType(createdWorkspace, primaryAccountType);
    }
  }

  private async copyFeaturesAccountType(
    createdWorkspace: Workspace,
    primaryAccountType: AccountType,
  ) {
    if (createdWorkspace.accountTypeId == primaryAccountType.id) {
      return;
    }
    const featureAccountTypes = await this.featureAccountTypeRepository.find({
      where: { accountTypeId: primaryAccountType.id },
    });
    const copiedFeatureAccountTypes = featureAccountTypes.map(
      (featureAccountType) => {
        const copiedFeatureAccountType = new FeatureAccountType();
        copiedFeatureAccountType.accountTypeId = createdWorkspace.accountTypeId;
        copiedFeatureAccountType.featureWhiteListId =
          featureAccountType.featureWhiteListId;
        copiedFeatureAccountType.dateCreated = new Date();
        copiedFeatureAccountType.lastUpdated = new Date();
        return copiedFeatureAccountType;
      },
    );
    await this.featureAccountTypeRepository.save(copiedFeatureAccountTypes);
  }

  private async copyFeaturesWorkspace(
    primaryWorkspace: Workspace,
    createdWorkspace: Workspace,
  ) {
    const featureWorkspace = await this.featureWorkspaceRepository.find({
      where: { partnerId: primaryWorkspace.id },
    });
    const copiedFeatureWorkspaces = featureWorkspace.map(
      (featureAccountType) => {
        const copiedFeatureWorkspace = new FeatureWorkspace();
        copiedFeatureWorkspace.partnerId = createdWorkspace.id;
        copiedFeatureWorkspace.featureWhiteListId =
          featureAccountType.featureWhiteListId;
        copiedFeatureWorkspace.dateCreated = new Date();
        copiedFeatureWorkspace.lastUpdated = new Date();
        return copiedFeatureWorkspace;
      },
    );
    await this.featureWorkspaceRepository.save(copiedFeatureWorkspaces);
  }

  private async buildPlatformAdAccountWorkspaceConnectionDtosForAWorkspace(
    workspaceId: number,
    adAccountIds: number[],
  ): Promise<CreatePlatformAdAccountWorkspaceMapDto[]> {
    return adAccountIds.map((platformAdAccountId) => {
      return { workspaceId, platformAdAccountId };
    });
  }

  private async copyWorkspaceIndustry(
    primaryWorkspace: Workspace,
    createdWorkspace: Workspace,
  ) {
    if (!primaryWorkspace) {
      return; // should never happen
    }

    await this.workspaceIndustryRepository
      .findOne({ where: { workspaceId: primaryWorkspace?.id } })
      .then((industry) => {
        if (industry) {
          const copiedWorkspaceIndustry = new WorkspaceIndustry();
          copiedWorkspaceIndustry.workspaceId = createdWorkspace.id;
          copiedWorkspaceIndustry.industryId = industry.industryId;
          copiedWorkspaceIndustry.dateCreated = new Date();
          return this.workspaceIndustryRepository.save(copiedWorkspaceIndustry);
        }
      });
  }

  // Clones WorkspaceManager entries from primaryWorkspace to createdWorkspace
  // Only clones entries with roles ACCOUNT_MANAGER or PROJECT_MANAGER
  private async copyWorkspaceManager(
    primaryWorkspace: Workspace,
    createdWorkspace: Workspace,
  ) {
    if (!primaryWorkspace) {
      return;
    }
    // Fetch only WorkspaceManager entries related to primaryWorkspace with roles ACCOUNT_MANAGER or PROJECT_MANAGER
    const primaryManagers = await this.workspaceManagerRepository.find({
      where: {
        workspaceId: primaryWorkspace.id,
        role: In([
          WorkspaceManagerRole.ACCOUNT_MANAGER,
          WorkspaceManagerRole.PROJECT_MANAGER,
        ]),
      },
    });

    // Clone these entries and update the workspaceId
    const clonedManagers = primaryManagers.map((manager) => {
      const clonedManager = new WorkspaceManager();
      clonedManager.workspaceId = createdWorkspace.id;
      clonedManager.managerId = manager.managerId;
      clonedManager.role = manager.role;
      clonedManager.dateCreated = new Date();
      clonedManager.lastUpdated = new Date();

      return clonedManager;
    });

    // Save the cloned entries into the database
    await this.workspaceManagerRepository.save(clonedManagers);
  }

  async verifyWorkspacesBelongToAnOrganization(
    orgId: string,
    workspaceIds: number[],
  ): Promise<number[]> {
    const nonDuplicatedWorkspaceIds: number[] = Array.from(
      new Set(workspaceIds),
    );
    const workspaces = await this.workspaceRepository.find({
      where: {
        id: In(nonDuplicatedWorkspaceIds),
        organizationId: orgId,
      },
    });
    const queryWorkspaceIds = workspaces.map((workspace) => workspace.id);
    if (queryWorkspaceIds.length != nonDuplicatedWorkspaceIds.length) {
      throw new BadRequestException(
        'Some of the workspaces do not belong to the organization',
      );
    }
    return queryWorkspaceIds;
  }

  /**
   * Check if a user is authorized in all workspaces provided in the organization
   * @param organizationId organization id
   * @param workspaceIds workspace ids
   * @param userId user id
   */
  async isNonAdminUserAuthorizedInAllWorkspaces(
    organizationId: string,
    workspaceIds: number[],
    userId: number,
  ): Promise<boolean> {
    const workspaceCount = await this.workspaceRepository
      .createQueryBuilder('workspace')
      .leftJoin('workspace.workspaceUsers', 'workspaceUser')
      .leftJoin('workspace.workspaceManagers', 'workspaceManager')
      .where('workspace.organizationId = :organizationId', { organizationId })
      .andWhere('workspace.id  IN (:...workspaceIds)', { workspaceIds })
      .andWhere(
        '((workspaceUser.userId = :userId AND workspaceUser.active = :active) OR (workspaceManager.managerId = :userId AND workspaceManager.role IN (:...roles)))',
        {
          userId,
          active: true,
          roles: [
            WorkspaceManagerRole.PROJECT_MANAGER,
            WorkspaceManagerRole.ACCOUNT_MANAGER,
          ],
        },
      )
      .getCount();

    return workspaceCount === workspaceIds.length;
  }

  async getNonAdminUserAuthorizedWorkspaces(
    organizationId: string,
    userId: number,
  ): Promise<number[]> {
    const workspaces = await this.workspaceRepository
      .createQueryBuilder('workspace')
      .select('workspace.id')
      .leftJoin('workspace.workspaceUsers', 'workspaceUser')
      .leftJoin('workspace.workspaceManagers', 'workspaceManager')
      .where('workspace.organizationId = :organizationId', { organizationId })
      .andWhere(
        '((workspaceUser.userId = :userId AND workspaceUser.active = :active) OR (workspaceManager.managerId = :userId AND workspaceManager.role IN (:...roles)))',
        {
          userId,
          active: true,
          roles: [
            WorkspaceManagerRole.PROJECT_MANAGER,
            WorkspaceManagerRole.ACCOUNT_MANAGER,
          ],
        },
      )
      .distinct(true)
      .getMany();

    return workspaces.map((workspace) => workspace.id);
  }

  async areWorkspacesBelongsToOrganization(
    organizationId: string,
    workspaceIds: number[],
  ) {
    const workspacesCount = await this.workspaceRepository.countBy({
      id: In(workspaceIds),
      organizationId,
    });

    return workspacesCount === workspaceIds.length;
  }

  async validateUserWorkspacesWithinOrganization(
    organizationId: string,
    userId: number,
    workspaceIds: number[],
  ): Promise<ValidateOrganizationEntitiesResponseDto> {
    const uniqueWorkspaceIds = [...new Set(workspaceIds)];
    let isUserAccessValidQuery: Promise<boolean>;

    const userOrganizationRole =
      await this.organizationService.getUserAuthorizedRoleInOrganization(
        organizationId,
        userId,
      );

    if (userOrganizationRole == OrganizationUserRoles.ORG_ADMIN) {
      isUserAccessValidQuery = this.areWorkspacesBelongsToOrganization(
        organizationId,
        uniqueWorkspaceIds,
      );
    } else {
      isUserAccessValidQuery = this.isNonAdminUserAuthorizedInAllWorkspaces(
        organizationId,
        uniqueWorkspaceIds,
        userId,
      );
    }

    const isUserAccessValid = await isUserAccessValidQuery;
    return {
      success: isUserAccessValid,
      message: isUserAccessValid
        ? 'User has access to workspaces within organization'
        : `User does not have access to all provided workspaces within organization ${organizationId}`,
    };
  }

  /**
   * Adds Bring Your Own Creator (BYOC) feature to a workspace
   * @param workspace The workspace to add the feature to
   * @private
   */
  private async addFeatureBringYourOwnCreator(workspace: Workspace) {
    const featureBringYourOwnCreator =
      await this.featureWhitelistRepository.findOne({
        where: { identifier: featureBringYourOwnCreatorIdentifier },
      });

    if (!featureBringYourOwnCreator) {
      throw new NotFoundException('Feature Bring Your Own Creator not found');
    }

    if (
      workspace.accountType.scope === ACCOUNT_TYPE_PUBLIC_SCOPE &&
      workspace.accountType.name === SELF_MANAGED_IDENTIFIER
    ) {
      // For self-managed workspaces, add feature directly to workspace
      const featureWorkspace = new FeatureWorkspace();
      featureWorkspace.featureWhiteListId = featureBringYourOwnCreator.id;
      featureWorkspace.partnerId = workspace.id;
      featureWorkspace.dateCreated = new Date();
      featureWorkspace.lastUpdated = new Date();
      await this.featureWorkspaceRepository.save(featureWorkspace);
    } else {
      // For enterprise workspaces, add feature to account type
      const featureAccountType = new FeatureAccountType();
      featureAccountType.accountTypeId = workspace.accountType.id;
      featureAccountType.featureWhiteListId = featureBringYourOwnCreator.id;
      featureAccountType.dateCreated = new Date();
      featureAccountType.lastUpdated = new Date();
      await this.featureAccountTypeRepository.save(featureAccountType);
    }
  }

  async getAllWorkspacesForOrganizations(
    organizationWorkspaceRequestDto: OrganizationWorkspaceRequestDto,
  ) {
    const workspaces = await this.workspaceRepository.findBy({
      organizationId: In(organizationWorkspaceRequestDto.organizationIds),
    });

    return workspaces.map((workspace) => workspace.id);
  }
}
