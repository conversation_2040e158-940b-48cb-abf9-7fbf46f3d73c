import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, Mapper } from '@automapper/core';
import { WorkspaceBrandMap } from '../entities/workspace-brand-map.entity';
import { CreateWorkspaceBrandMapDto } from '../dto/create-workspace-brand-map.dto';
import { WorkspaceBrandMapDto } from '../dto/workspace-brand-map.dto';
import { ReadWorkspaceBrandMapDto } from '../dto/read-workspace-brand-map.dto';
import { Brand } from '../../../organizations/brand/entities/brand.entity';
import { WorkspaceReadBrandDto } from '../dto/workspace-read-brand.dto';

/**
 * Define the mapping between criteria DTOs and entities.
 */
@Injectable()
export class WorkspaceBrandProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  /**
   * A custom profile to define how the automapper should transform criteria. The primary function is to convert the
   * parameters from a string to an object.
   */
  override get profile() {
    return (mapper: Mapper) => {
      createMap(mapper, WorkspaceBrandMap, CreateWorkspaceBrandMapDto);

      createMap(mapper, WorkspaceBrandMap, WorkspaceBrandMapDto);

      createMap(mapper, CreateWorkspaceBrandMapDto, WorkspaceBrandMap);

      createMap(mapper, WorkspaceBrandMap, ReadWorkspaceBrandMapDto);

      createMap(mapper, Brand, WorkspaceReadBrandDto);
    };
  }
}
