import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
} from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { WorkspaceBrandService } from '../services/workspace-brand.service';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { ReadWorkspaceBrandMapDto } from '../dto/read-workspace-brand-map.dto';
import { WorkspaceBrandMapDto } from '../dto/workspace-brand-map.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { WorkspaceReadBrandDto } from '../dto/workspace-read-brand.dto';
import { LocalWorkspaceBrandMapDto } from '../dto/local-workspace-brand-map.dto';

@ApiTags('WorkspaceBrand')
@Controller('workspace/:workspaceId/brand')
export class WorkspaceBrandController {
  constructor(private readonly workspaceBrandService: WorkspaceBrandService) {}
  /**
   * Assign a brand to a workspace.
   * @param workspaceId - The id of the workspace (Partner).
   * @param brandId - The id of the brand(uuid).
   */
  @VmApiOkResponse({
    type: ReadWorkspaceBrandMapDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @ApiParam({ name: 'brandId', description: 'The id of the brand' })
  @Post()
  async createBrand(
    @Param('workspaceId') workspaceId: number,
    @Body() createWorkspaceBrandDto: LocalWorkspaceBrandMapDto,
  ) {
    return this.workspaceBrandService.createWorkspaceBrand(
      workspaceId,
      createWorkspaceBrandDto,
    );
  }

  /**
   * Remove a Brand from a workspace.
   * @param workspaceId - The id of the workspace (Partner).
   * @param brandId - The id of the brand (uuid).
   */
  @VmApiOkResponse({
    type: ReadWorkspaceBrandMapDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @ApiParam({ name: 'brandId', description: 'The id of the brand' })
  @Delete(':brandId')
  async removeBrandFromWorkspace(
    @Param('workspaceId') workspaceId: number,
    @Param('brandId') brandId: string,
  ) {
    return this.workspaceBrandService.removeBrandFromWorkspace(
      workspaceId,
      brandId,
    );
  }

  /**
   * This endpoint returns brands assigned to a workspace.
   */
  @VmApiOkPaginatedArrayResponse({
    type: WorkspaceReadBrandDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Get()
  async getWorkspaceBrands(
    @Param('workspaceId') workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<WorkspaceReadBrandDto>> {
    return this.workspaceBrandService.getWorkspaceBrands(
      workspaceId,
      paginationOptions,
    );
  }
}
