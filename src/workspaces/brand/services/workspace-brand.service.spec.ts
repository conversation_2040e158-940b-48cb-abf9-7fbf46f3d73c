import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceBrandController } from '../controllers/workspace-brand.controller';
import { WorkspaceBrandService } from './workspace-brand.service';
import { NotFoundException } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { LocalWorkspaceBrandMapDto } from '../dto/local-workspace-brand-map.dto';

describe('WorkspaceBrandController', () => {
  let brandController: WorkspaceBrandController;
  let brandService: WorkspaceBrandService;
  const workspaceRepoStub = {
    findOneBy: jest.fn(),
  };

  const brandRepositoryStub = {
    findOneBy: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspaceBrandController],
      providers: [
        {
          provide: WorkspaceBrandService,
          useValue: {
            createWorkspaceBrand: jest.fn().mockResolvedValue(undefined),
            removeBrandFromWorkspace: jest.fn().mockResolvedValue(undefined),
            getWorkspaceBrands: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: 'WorkspaceRepository',
          useValue: workspaceRepoStub,
        },
        {
          provide: 'BrandRepository',
          useValue: brandRepositoryStub,
        },
      ],
    }).compile();

    brandController = module.get<WorkspaceBrandController>(
      WorkspaceBrandController,
    );
    brandService = module.get<WorkspaceBrandService>(WorkspaceBrandService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(brandController).toBeDefined();
  });

  describe('createBrand', () => {
    it('should call service with valid CreateWorkspaceBrandMapDto', async () => {
      const workspaceId = 123;
      const validDto: LocalWorkspaceBrandMapDto = {
        brandId: '68ec7ff3-41ed-4a58-8d49-2b3c8af63c29',
      };

      await brandController.createBrand(workspaceId, validDto);

      expect(brandService.createWorkspaceBrand).toHaveBeenCalledWith(
        workspaceId,
        validDto,
      );
    });
  });

  describe('removeBrandFromWorkspace', () => {
    it('should call service with valid workspaceId and brandId', async () => {
      const workspaceId = 123;
      const brandId = '68ec7ff3-41ed-4a58-8d49-2b3c8af63c29';

      await brandController.removeBrandFromWorkspace(workspaceId, brandId);

      expect(brandService.removeBrandFromWorkspace).toHaveBeenCalledWith(
        workspaceId,
        brandId,
      );
    });

    it('should throw NotFoundException when mapping not found', async () => {
      const workspaceId = 123;
      const brandId = '68ec7ff3-41ed-4a58-8d49-2b3c8af63c29';

      jest
        .spyOn(brandService, 'removeBrandFromWorkspace')
        .mockRejectedValueOnce(new NotFoundException('Mapping not found'));

      await expect(
        brandController.removeBrandFromWorkspace(workspaceId, brandId),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('getWorkspaceBrands', () => {
    it('should call service with valid workspaceId', async () => {
      const workspaceId = 123;
      const paginationOptions: PaginationOptions = {};
      await brandController.getWorkspaceBrands(workspaceId, paginationOptions);

      expect(brandService.getWorkspaceBrands).toHaveBeenCalledWith(
        workspaceId,
        paginationOptions,
      );
    });
  });
});
