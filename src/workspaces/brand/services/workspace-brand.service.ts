import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkspaceBrandMap } from '../entities/workspace-brand-map.entity';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CreateWorkspaceBrandMapDto } from '../dto/create-workspace-brand-map.dto';
import { WorkspaceBrandMapDto } from '../dto/workspace-brand-map.dto';
import { ReadWorkspaceBrandMapDto } from '../dto/read-workspace-brand-map.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { WorkspaceReadBrandDto } from '../dto/workspace-read-brand.dto';
import { Brand } from '../../../organizations/brand/entities/brand.entity';
import { Workspace } from '../../../workspaces/entities/workspace.entity';
import { LocalWorkspaceBrandMapDto } from '../dto/local-workspace-brand-map.dto';

@Injectable()
export class WorkspaceBrandService {
  static validateBrandOwnership: any;
  constructor(
    @InjectRepository(WorkspaceBrandMap)
    private readonly WorkspaceBrandMapRepo: Repository<WorkspaceBrandMap>,

    @InjectRepository(Workspace)
    private readonly workspaceRepo: Repository<Workspace>,

    @InjectRepository(Brand)
    private readonly brandRepository: Repository<Brand>,

    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  async createWorkspaceBrand(
    workspaceId: number,
    createWorkspaceBrandMap: LocalWorkspaceBrandMapDto,
  ) {
    await this.validateBrandOwnership(
      workspaceId,
      createWorkspaceBrandMap.brandId,
    );

    const createWorkspaceBrandPayload: CreateWorkspaceBrandMapDto = {
      ...createWorkspaceBrandMap,
      workspaceId: workspaceId,
    };

    const WorkspaceBrandMapEntity = this.classMapper.map(
      createWorkspaceBrandPayload,
      CreateWorkspaceBrandMapDto,
      WorkspaceBrandMap,
    );

    return this.classMapper.mapAsync(
      await this.WorkspaceBrandMapRepo.save(WorkspaceBrandMapEntity),
      WorkspaceBrandMap,
      ReadWorkspaceBrandMapDto,
    );
  }

  async removeBrandFromWorkspace(workspaceId: number, brandId: string) {
    const workspaceBrandMap = await this.WorkspaceBrandMapRepo.findOne({
      where: {
        workspaceId: workspaceId,
        brandId: brandId,
      },
    });

    if (!workspaceBrandMap) {
      throw new NotFoundException('Workspace Brand mapping not found');
    }
    const result = await this.WorkspaceBrandMapRepo.remove(workspaceBrandMap);

    return this.classMapper.mapAsync(
      result,
      WorkspaceBrandMap,
      WorkspaceBrandMapDto,
    );
  }

  // Validates if the brand belongs to the workspace
  // and organization
  private async validateBrandOwnership(workspaceId: number, brandId: string) {
    const workspace = await this.workspaceRepo.findOneBy({ id: workspaceId });

    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    if (!workspace.organizationId) {
      throw new NotFoundException('Workspace organizationId not found');
    }

    const brand = await this.brandRepository.findOneBy({
      id: brandId,
      organizationId: workspace.organizationId,
      deleted: false,
    });

    if (!brand) {
      throw new NotFoundException('Brand not found');
    }
  }

  /**
   * find all brands
   *
   * @returns
   */
  async getWorkspaceBrands(
    workspaceId: number,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<WorkspaceReadBrandDto>> {
    const [result, total] = await this.brandRepository
      .createQueryBuilder('brand')
      .innerJoin(
        'brand.workspaces',
        'workspace',
        'workspace.id = :workspaceId',
        { workspaceId },
      )
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .getManyAndCount();

    const ReadWorkspaceBrandMapDtos: WorkspaceReadBrandDto[] =
      this.classMapper.mapArray(result, Brand, WorkspaceReadBrandDto);
    return new PaginatedResultArray(ReadWorkspaceBrandMapDtos, total);
  }
}
