import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import {
  createMap,
  forMember,
  ignore,
  mapFrom,
  Mapper,
} from '@automapper/core';
import { CreateWorkspaceDto } from '../dto/create-workspace.dto';
import { Workspace } from '../entities/workspace.entity';
import { ReadWorkspaceDto } from '../dto/read-workspace.dto';
import { UpdateWorkspaceDto } from '../dto/update-workspace.dto';
import { ReadRelatedWorkspacesDto } from '../dto/read-related-workspaces.dto';
import { ReadWorkspaceSearchDto } from '../dto/read-workspace-search.dto';
import { AllUserWorkspaceDto } from '../dto/read-all-user-workspace.dto';
import { AccountTypeScope } from '../../common/constants/constants';
import { ReadLightWeightWorkspaceDto } from 'src/organizations/dto/read-light-weight-workspace.dto';
import { RealAllUserWorkspacesRawDto } from '../dto/real-all-user-workspaces-raw.dto';
const { ENTERPRISE, PUBLIC } = AccountTypeScope;

/**
 * Profile to create mapping between DTOs and Entities.
 * Updates the mapper with a collection of maps where
 * each map defines the relationship and flow between a DTO and an Entity class.
 */
@Injectable()
export class WorkspaceProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper) => {
      /*
       For creating a workspace, this map links CreateWorkspaceDto to Workspace entity.
       Source : CreateWorkspaceDto ; Destination: Workspace
       */
      createMap(
        mapper,
        CreateWorkspaceDto,
        Workspace,
        forMember((dest) => dest.id, ignore()),
      );

      // Map UpdateWorkspaceDto to Workspace entity
      createMap(mapper, UpdateWorkspaceDto, Workspace);

      // Map Workspace to ReadWorkspaceDto before responding to controller.
      createMap(mapper, Workspace, ReadWorkspaceDto);

      createMap(
        mapper,
        Workspace,
        ReadRelatedWorkspacesDto,
        forMember(
          (dest) => dest.partnerId,
          mapFrom((src) => src.id),
        ),
      );

      createMap(
        mapper,
        Workspace,
        AllUserWorkspaceDto,
        forMember(
          (dest) => dest.isEnterprise,
          mapFrom((src) => src?.accountType?.scope === ENTERPRISE),
        ),
        forMember(
          (dest) => dest.publicAccountTypeName,
          mapFrom((src) => src?.accountType?.name),
        ),
        forMember(
          (dest) => dest.accountTypeIdentifier,
          mapFrom((src) =>
            src?.accountType?.scope === PUBLIC
              ? src?.accountType?.name?.toUpperCase()
              : ENTERPRISE,
          ),
        ),
        forMember(
          (dest) => dest.isPersonal,
          mapFrom((src) => src.personal),
        ),
        forMember(
          (dest) => dest.isFindMyTeamEnabled,
          mapFrom((src) => src.findMyTeamEnabled),
        ),
        forMember(
          (dest) => dest.organizationName,
          mapFrom((src) => src.organization.name),
        ),
      );

      createMap(
        mapper,
        RealAllUserWorkspacesRawDto,
        AllUserWorkspaceDto,
        forMember(
          (dest) => dest.id,
          mapFrom((src) => parseInt(src.workspace_id, 10)),
        ),
        forMember(
          (dest) => dest.logoUrl,
          mapFrom((src) => src.workspace_logo_url),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.workspace_name),
        ),
        forMember(
          (dest) => dest.organizationId,
          mapFrom((src) => src.workspace_organization_id),
        ),
        forMember(
          (dest) => dest.organizationName,
          mapFrom((src) => src.organization_name),
        ),
        forMember(
          (dest) => dest.isEnterprise,
          mapFrom((src) => src?.accountType_scope === ENTERPRISE),
        ),
        forMember(
          (dest) => dest.publicAccountTypeName,
          mapFrom((src) => src.accountType_name),
        ),
        forMember(
          (dest) => dest.accountTypeIdentifier,
          mapFrom((src) =>
            src.accountType_scope === PUBLIC
              ? src.accountType_name?.toUpperCase()
              : ENTERPRISE,
          ),
        ),
        forMember(
          (dest) => dest.isPersonal,
          mapFrom((src) => Boolean(src.workspace_personal)),
        ),
        forMember(
          (dest) => dest.isFindMyTeamEnabled,
          mapFrom((src) => Boolean(src.workspace_find_my_team_enabled)),
        ),
      );

      createMap(
        mapper,
        Workspace,
        ReadWorkspaceSearchDto,
        forMember(
          (dest) => dest.totalUsers,
          mapFrom((src) => (src.users ? src.users.length : undefined)),
        ),
        forMember(
          (dest) => dest.organizationName,
          mapFrom((src) =>
            src.organization ? src.organization.name : undefined,
          ),
        ),
        forMember(
          (dest) => dest.markets,
          mapFrom((src) => src.markets),
        ),

        forMember(
          (dest) => dest.brands,
          mapFrom((source) => source.brands),
        ),
        /**
         * We are including an empty users array in the response for backward compatibility.
         * Including the users in the query caused performance issues for large clients
         */
        forMember(
          (dest) => dest.users,
          mapFrom(() => []),
        ),
        forMember(
          (dest) => dest.linkedAdAccountsCount,
          mapFrom((source) => source.platformAdAccountToWorkspacesCount),
        ),
      );

      createMap(mapper, Workspace, ReadLightWeightWorkspaceDto);
    };
  }
}
