import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { WorkspaceProfile } from './workspace.profile';
import { CreateWorkspaceDto } from '../dto/create-workspace.dto';
import { Workspace } from '../entities/workspace.entity';
import { ReadWorkspaceDto } from '../dto/read-workspace.dto';
import { UpdateWorkspaceDto } from '../dto/update-workspace.dto';
import { classes } from '@automapper/classes';
import { Organization } from '../../organizations/entities/organization.entity';
import { WorkspaceUser } from '../entities/workspace-user.entity';
import { PlatformAdAccountToWorkspace } from '../../ad-accounts/entities/ad-account-workspace-map.entity';
import { WorkspaceManager } from '../entities/workspace-manager.entity';

describe('WorkspaceProfile', () => {
  let mapper: Mapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [WorkspaceProfile],
    }).compile();

    const workspaceProfile: WorkspaceProfile =
      module.get<WorkspaceProfile>(WorkspaceProfile);

    mapper = module.get<Mapper>(getMapperToken());
    workspaceProfile.profile(mapper);
  });

  it('should map CreateWorkspaceDto to Workspace', () => {
    const createWorkspaceDto: CreateWorkspaceDto = {
      name: 'Example Workspace',
      organizationId: '123e4567-e89b-12d3-a456-************',
      isPrimary: true,
      logoUrl: 'https://example.com/logo.png',
    };

    const workspace = mapper.map(
      createWorkspaceDto,
      CreateWorkspaceDto,
      Workspace,
    );
    expect(workspace).toBeDefined();
    expect(workspace.name).toBe('Example Workspace');
    expect(workspace.organizationId).toBe(
      '123e4567-e89b-12d3-a456-************',
    );
    expect(workspace.isPrimary).toBe(true);
  });

  it('should map UpdateWorkspaceDto to Workspace', () => {
    const updateWorkspaceDto: UpdateWorkspaceDto = {
      name: 'Updated Workspace',
      organizationId: '789e0123-e89b-12d3-a456-************',
      isPrimary: false,
    };

    const workspace = mapper.map(
      updateWorkspaceDto,
      UpdateWorkspaceDto,
      Workspace,
    );
    expect(workspace).toBeDefined();
    expect(workspace.name).toBe('Updated Workspace');
    expect(workspace.organizationId).toBe(
      '789e0123-e89b-12d3-a456-************',
    );
    expect(workspace.isPrimary).toBe(false);
  });

  it('should map Workspace to ReadWorkspaceDto', () => {
    const workspace: Workspace = {
      id: 123456,
      name: 'Example Workspace',
      logoUrl: 'https://example.com/logo.png',
      organization: new Organization(),
      organizationId: '123e4567-e89b-12d3-a456-************',
      isPrimary: true,
      version: 1,
      includeDraftDownloadUrl: true,
      projectFinalAssetsRequired: true,
      draftReview: null,
      parentPartnerId: null,
      pricingRatioTierId: null,
      personal: false,
      dateCreated: new Date(),
      lastUpdated: new Date(),
      accountType: null,
      accountTypeId: null,
      makerId: null,
      workspaceBrands: [],
      workspaceMarkets: [],
      markets: [],
      brands: [],
      workspaceUsers: [new WorkspaceUser()],
      users: [new WorkspaceUser()],
      workspaceManagers: [new WorkspaceManager()],
      platformAdAccountToWorkspaces: [new PlatformAdAccountToWorkspace()],
      findMyTeamEnabled: false,
      featureWorkspaces: [],
    };

    const readWorkspaceDto = mapper.map(workspace, Workspace, ReadWorkspaceDto);
    expect(readWorkspaceDto).toBeDefined();
    expect(readWorkspaceDto.id).toBe(123456);
    expect(readWorkspaceDto.logoUrl).toBe('https://example.com/logo.png');
    expect(readWorkspaceDto.name).toBe('Example Workspace');
    expect(readWorkspaceDto.isPrimary).toBe(true);
    expect(readWorkspaceDto.organizationId).toBe(
      '123e4567-e89b-12d3-a456-************',
    );
  });
});
