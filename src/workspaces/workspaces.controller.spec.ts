import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceController } from './workspaces.controller';
import { WorkspaceService } from './workspaces.service';
import { Repository } from 'typeorm';
import { Organization } from '../organizations/entities/organization.entity';
import { Workspace } from './entities/workspace.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { classes } from '@automapper/classes';
import { createMapper, Mapper } from '@automapper/core';
import { getMapperToken } from '@automapper/nestjs';
import { OrganizationsService } from '../organizations/organizations.service';
import { SplitAndTrimPipe } from '../organizations/utils/split-and-trim-pipe';
import { AdAccountsService } from '../ad-accounts/ad-accounts.service';
import { PlatformAdAccount } from '../ad-accounts/entities/ad-account.entity';
import { PlatformAdAccountToWorkspace } from '../ad-accounts/entities/ad-account-workspace-map.entity';
import { OrganizationPlatformAdAccountMap } from '../ad-accounts/entities/organization-platform-ad-account-map.entity';
import { WorkspaceMarket } from './entities/workspace-market.entity';
import { WorkspaceBrand } from './entities/workspace-brand.entity';
import { WorkspaceIndustry } from './entities/partner-industry.entity';
import { AccountType } from './entities/account-type.entity';
import { FeatureWorkspace } from './entities/feature-workspace.entity';
import { FeatureAccountType } from './entities/feature-account-type.entity';
import { MarketService } from '../markets/market.service';
import { BrandService } from '../brands/brand.service';
import { Market } from '../markets/entities/market.entity';
import { Brand } from '../brands/entities/brand.entity';
import { OrganizationAdAccountsService } from '../ad-accounts/organization-ad-accounts.service';
import { OrganizationAdAccountUserPermissions } from '../ad-accounts/entities/organization-ad-account-user-permissions.entity';
import { WorkspaceManager } from './entities/workspace-manager.entity';
import { WorkspaceUser } from './entities/workspace-user.entity';
import { PlatformAdAccountBrandMap } from '../ad-accounts/entities/platform-ad-account-brand-map.entity';
import { PlatformAdAccountMarketMap } from '../ad-accounts/entities/platform-ad-account-market-map.entity';
import { ProductAccountType } from './entities/product-account-type.entity';
import { HttpException, HttpStatus } from '@nestjs/common';
import { OrganizationFeatureWhitelist } from '../organizations/entities/organization-feature-whitelist.entity';
import { PlatformAdAccountIndustryMap } from '../ad-accounts/entities/platform-ad-account-industry-map.entity';
import { PlatformAdAccountSequentialFailure } from '../ad-accounts/entities/platform-ad-account-sequential-failure.entity';
import { AdAccountImportStatusService } from '../ad-accounts/ad-account-import-status.service';
import { SqsService } from '@vidmob/vidmob-nestjs-common';
import { AdAccountSequentialFailureService } from '../ad-accounts/ad-account-sequential-failure.service';

describe('WorkspaceController', () => {
  let controller: WorkspaceController;
  let workspaceService: WorkspaceService;
  const emptyRepo = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspaceController],
      providers: [
        MarketService,
        BrandService,
        OrganizationsService,
        AdAccountsService,
        SqsService,
        AdAccountImportStatusService,
        OrganizationAdAccountsService,
        AdAccountSequentialFailureService,
        SplitAndTrimPipe,
        {
          provide: WorkspaceService,
          useValue: {
            isBrandGovernanceEnabled: jest.fn(),
            getWorkspaceEntity: jest.fn(),
          },
        },
        { provide: getRepositoryToken(Organization), useValue: emptyRepo },
        {
          provide: getRepositoryToken(WorkspaceMarket),
          useValue: emptyRepo,
        },
        {
          provide: getRepositoryToken(WorkspaceBrand),
          useValue: emptyRepo,
        },
        {
          provide: getRepositoryToken(WorkspaceIndustry),
          useValue: emptyRepo,
        },
        {
          provide: getRepositoryToken(WorkspaceManager),
          useValue: emptyRepo,
        },
        {
          provide: getRepositoryToken(WorkspaceUser),
          useValue: emptyRepo,
        },
        {
          provide: getRepositoryToken(AccountType),
          useValue: emptyRepo,
        },
        {
          provide: getRepositoryToken(FeatureWorkspace),
          useValue: emptyRepo,
        },
        {
          provide: getRepositoryToken(FeatureAccountType),
          useValue: emptyRepo,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountIndustryMap),
          useValue: Repository,
        },
        { provide: getRepositoryToken(Brand), useValue: emptyRepo },

        { provide: getRepositoryToken(Market), useValue: emptyRepo },
        { provide: getRepositoryToken(Workspace), useValue: emptyRepo },
        {
          provide: getRepositoryToken(PlatformAdAccount),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountToWorkspace),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(OrganizationPlatformAdAccountMap),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(OrganizationAdAccountUserPermissions),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountBrandMap),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountMarketMap),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountSequentialFailure),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(OrganizationFeatureWhitelist),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(ProductAccountType),
          useValue: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    })
      .overrideProvider(SqsService)
      .useValue({})
      .compile();

    module.get<Mapper>(getMapperToken());
    controller = module.get<WorkspaceController>(WorkspaceController);
    workspaceService = module.get<WorkspaceService>(WorkspaceService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should return false when the feature is not enabled', async () => {
    jest
      .spyOn(workspaceService, 'isBrandGovernanceEnabled')
      .mockResolvedValue({ brandGovernance: false });
    expect(await controller.checkBrandGovernance(1)).toEqual({
      brandGovernance: false,
    });
  });

  it('should return true when the feature is enabled', async () => {
    jest
      .spyOn(workspaceService, 'isBrandGovernanceEnabled')
      .mockResolvedValue({ brandGovernance: true });
    expect(await controller.checkBrandGovernance(2)).toEqual({
      brandGovernance: true,
    });
  });

  it('should throw BadRequestException when the ID is not valid', async () => {
    jest
      .spyOn(workspaceService, 'isBrandGovernanceEnabled')
      .mockRejectedValue(
        new HttpException(
          'Validation failed (numeric string is expected)',
          HttpStatus.BAD_REQUEST,
        ),
      );
    await expect(controller.checkBrandGovernance(NaN)).rejects.toThrow(
      HttpException,
    );
  });

  it('should throw NotFoundException when the workspace does not exist', async () => {
    jest
      .spyOn(workspaceService, 'isBrandGovernanceEnabled')
      .mockRejectedValue(
        new HttpException(
          'Workspace or account type not found for workspace ID ********',
          HttpStatus.NOT_FOUND,
        ),
      );
    await expect(controller.checkBrandGovernance(********)).rejects.toThrow(
      HttpException,
    );
  });
});
