import { In } from 'typeorm';

export function buildMarketOrgAdminWhereClause(
  organizationId: string,
  market: string[],
): Record<string, any> {
  let whereClause: Record<string, any> = { organizationId };

  if (market && market.length > 0) {
    whereClause = {
      ...whereClause,
      markets: { isoCode: In(market) },
    };
  }

  return whereClause;
}

export function hasFilter(value: any): boolean {
  return value && value.length > 0;
}
