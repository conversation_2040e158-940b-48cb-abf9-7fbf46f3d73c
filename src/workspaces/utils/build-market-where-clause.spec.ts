import { In } from 'typeorm';
import { buildMarketOrgAdminWhereClause } from './build-market-where-clause';

describe('buildMarketOrgAdminWhereClause', () => {
  it('should return whereClause with organizationId when market is empty', () => {
    const organizationId = 'org123';
    const market: string[] = [];

    const result = buildMarketOrgAdminWhereClause(organizationId, market);

    expect(result).toEqual({ organizationId });
  });

  it('should return whereClause with organizationId and market when market is not empty', () => {
    const organizationId = 'org123';
    const market = ['us', 'ca'];

    const result = buildMarketOrgAdminWhereClause(organizationId, market);

    expect(result).toEqual({
      organizationId,
      markets: { isoCode: In(market) },
    });
  });

  it('should return whereClause with organizationId when market is undefined', () => {
    const organizationId = 'org123';
    const market = undefined;

    const result = buildMarketOrgAdminWhereClause(organizationId, market);

    expect(result).toEqual({ organizationId });
  });
});
