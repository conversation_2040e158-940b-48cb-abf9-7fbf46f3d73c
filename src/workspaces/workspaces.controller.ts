import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseArrayPipe,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { WorkspaceService } from './workspaces.service';
import { UpdateWorkspaceDto } from './dto/update-workspace.dto';
import {
  ApiExtraModels,
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { WORKSPACE_API_TAG_NAME } from 'src/common/constants/api.constants';
import { ReadWorkspaceDto } from './dto/read-workspace.dto';
import {
  GetPagination,
  PaginationOptions,
  VmApiCreatedResponse,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
  VmApiOkUnPaginatedArrayResponse,
} from '@vidmob/vidmob-nestjs-common';
import { ReadRelatedWorkspacesDto } from './dto/read-related-workspaces.dto';
import { CreateWorkspaceDto } from './dto/create-workspace.dto';
import { AllUserWorkspaceDto } from './dto/read-all-user-workspace.dto';
import { OrganizationWorkspaceRequestDto } from './dto/organization-workspace-request.dto';

@ApiTags(WORKSPACE_API_TAG_NAME)
@ApiExtraModels(ReadWorkspaceDto)
@Controller('workspace')
export class WorkspaceController {
  constructor(private readonly workspaceService: WorkspaceService) {}

  /**
   * Create a new workspace. Uses primary workspace as a template.
   *
   * @returns
   */
  @VmApiCreatedResponse({
    type: ReadWorkspaceDto,
    description:
      'Creates a workspace using the primary workspace of a given org as a template.',
  })
  @Post()
  create(@Body() createWorkspaceDto: CreateWorkspaceDto) {
    return this.workspaceService.create(createWorkspaceDto);
  }

  /**
   * Returns all workspaces
   *
   * @returns Workspace[]
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadWorkspaceDto,
    description: 'Returns all workspaces for user.',
  })
  @ApiQuery({
    name: 'userId',
    type: Number,
    description: 'System assigned id of the VidMob user',
    required: false,
  })
  @Get()
  findAll(
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('userId') userId?: number | undefined,
  ) {
    if (userId) {
      return this.workspaceService.getWorkspacesByUserId(
        paginationOptions,
        userId,
      );
    } else {
      return this.workspaceService.findAll(paginationOptions);
    }
  }

  /**
   * Returns all workspaces (and sub-workspaces) for a user across all orgs
   *
   * @returns AllUserWorkspaceDto[]
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: AllUserWorkspaceDto,
    description: 'Returns all workspaces for user.',
  })
  @ApiQuery({
    name: 'userId',
    type: Number,
    description: 'System assigned id of the VidMob user',
    required: true,
  })
  @ApiQuery({
    name: 'organizationId',
    type: String,
    description: 'System assigned id of the VidMob organization',
    required: false,
  })
  @ApiQuery({
    name: 'feature',
    type: String,
    description:
      'Workspace feature identifiers to filter by. Example "BRAND-GOVERNANCE,CREATIVE-INTELLIGENCE"',
    required: false,
  })
  @Get('all')
  findAllUserWorkspaceForAllOrgs(
    @Query('userId') userId: number,
    @Query('organizationId') organizationId: string,
    @Query(
      'feature',
      new ParseArrayPipe({ items: String, separator: ',', optional: true }),
    )
    workspaceFeatures?: string[],
  ): Promise<AllUserWorkspaceDto[]> {
    return this.workspaceService.getWorkspacesAllOrgsByUserId(
      userId,
      organizationId,
      workspaceFeatures,
    );
  }

  /**
   * Returns a workspace by id
   *
   * @param id
   * @returns Workspace
   */
  @VmApiOkResponse({
    type: ReadWorkspaceDto,
  })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'System assigned id of the Workspace',
  })
  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return await this.workspaceService.findOne(id);
  }

  /**
   * Update a workspace by id
   *
   * @param id
   * @param updateWorkspaceDto
   * @returns
   */
  @VmApiOkResponse({
    type: ReadWorkspaceDto,
  })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'System assigned id of the Workspace',
  })
  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateWorkspaceDto: UpdateWorkspaceDto,
  ) {
    return this.workspaceService.update(id, updateWorkspaceDto);
  }

  /**
   * Delete a workspace by id
   *
   * NOT YET IMPLEMENTED
   *
   * @param id
   * @returns
   */
  @VmApiOkResponse({
    description: 'Not yet implemented.',
  })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'System assigned id of the Workspace',
  })
  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.workspaceService.remove(+id);
  }

  /**
   * Returns all workspaces related to a workspace.
   * @param id - the id of the workspace to start from.
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadRelatedWorkspacesDto,
  })
  @ApiParam({
    name: 'id',
    description: 'The id of the workspace to start from.',
  })
  @Get(':id/related-workspaces')
  getRelatedWorkspaces(@Param('id', ParseIntPipe) id: number) {
    return this.workspaceService.getRelatedWorkspaces(id);
  }

  /**
   * Check if the 'BRAND-GOVERNANCE' feature is enabled for a workspace.
   *
   * @param id - Workspace ID to check the feature for
   * @returns Object with a boolean value indicating if the feature is enabled or not
   */
  @ApiOkResponse({
    description:
      'Returns true if BRAND-GOVERNANCE feature is enabled for the workspace, otherwise false.',
  })
  @ApiParam({
    name: 'id',
    type: Number,
    description: 'System assigned id of the Workspace',
  })
  @Get(':id/brand-governance')
  async checkBrandGovernance(@Param('id', ParseIntPipe) id: number) {
    return await this.workspaceService.isBrandGovernanceEnabled(id);
  }

  @Get(':id/feature')
  @ApiOkResponse({
    description: 'Which features are enabled for this workspace.',
  })
  async getFeatures(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<Record<string, boolean>> {
    return this.workspaceService.getAllFeaturesForWorkspace(id);
  }

  /**
   * Obtains an un-paginated list of all workspaces associated with a list of organizations.
   * @param organizationWorkspaceRequestDto
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: Array,
  })
  @Post('organization')
  getAllWorkspacesForOrganizations(
    @Body() organizationWorkspaceRequestDto: OrganizationWorkspaceRequestDto,
  ) {
    return this.workspaceService.getAllWorkspacesForOrganizations(
      organizationWorkspaceRequestDto,
    );
  }
}
