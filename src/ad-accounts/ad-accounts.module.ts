import { forwardRef, Module } from '@nestjs/common';
import { AdAccountsService } from './ad-accounts.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Organization } from '../organizations/entities/organization.entity';
import { Workspace } from '../workspaces/entities/workspace.entity';
import { PlatformAdAccount } from './entities/ad-account.entity';
import { PlatformAdAccountToWorkspace } from './entities/ad-account-workspace-map.entity';
import { AdAccountsProfile } from './mapper/ad-accounts.profile';
import { OrganizationPlatformAdAccountMap } from './entities/organization-platform-ad-account-map.entity';
import { AdAccountsController } from './ad-accounts.controller';
import { OrganizationAdAccountsService } from './organization-ad-accounts.service';
import { OrganizationAdAccountUserPermissions } from './entities/organization-ad-account-user-permissions.entity';
import { OrganizationAdAccountUserPermissionsProfile } from './mapper/organization-ad-account-user-permissions.profile';
import { PlatformAdAccountBrandMap } from './entities/platform-ad-account-brand-map.entity';
import { BrandModule } from 'src/brands/brand.module';
import { PlatformAdAccountMarketMap } from './entities/platform-ad-account-market-map.entity';
import { MarketModule } from 'src/markets/market.module';
import { WorkspacesModule } from '../workspaces/workspaces.module';
import { OrganizationFeatureWhitelist } from '../organizations/entities/organization-feature-whitelist.entity';
import { PlatformAdAccountIndustryMap } from './entities/platform-ad-account-industry-map.entity';
import { PlatformAdAccountSequentialFailure } from './entities/platform-ad-account-sequential-failure.entity';
import { PlatformAdAccountImportStatus } from './entities/platform-ad-account-import-status.entity';
import { AdAccountImportStatusService } from './ad-account-import-status.service';
import { OrganizationsModule } from 'src/organizations/organizations.module';
import { SqsModule } from '@vidmob/vidmob-nestjs-common';
import { AdAccountSequentialFailureService } from './ad-account-sequential-failure.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Organization,
      Workspace,
      PlatformAdAccount,
      PlatformAdAccountImportStatus,
      PlatformAdAccountToWorkspace,
      OrganizationFeatureWhitelist,
      OrganizationPlatformAdAccountMap,
      OrganizationAdAccountUserPermissions,
      PlatformAdAccountBrandMap,
      PlatformAdAccountMarketMap,
      PlatformAdAccountIndustryMap,
      PlatformAdAccountSequentialFailure,
    ]),
    MarketModule,
    BrandModule,
    SqsModule,
    forwardRef(() => WorkspacesModule),
    forwardRef(() => OrganizationsModule),
  ],
  exports: [
    AdAccountsService,
    OrganizationAdAccountsService,
    AdAccountImportStatusService,
    AdAccountSequentialFailureService,
  ],
  providers: [
    AdAccountsService,
    OrganizationAdAccountsService,
    AdAccountsProfile,
    OrganizationAdAccountUserPermissionsProfile,
    AdAccountImportStatusService,
    AdAccountSequentialFailureService,
  ],
  controllers: [AdAccountsController],
})
export class AdAccountsModule {}
