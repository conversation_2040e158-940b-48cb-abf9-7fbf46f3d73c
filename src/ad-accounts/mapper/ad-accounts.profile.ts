import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { PlatformAdAccount } from '../entities/ad-account.entity';
import { ReadPlatformAdAccountDto } from '../dto/read-platform-ad-account.dto';
import { CreatePlatformAdAccountWorkspaceMapDto } from '../dto/create-platform-ad-account-workspace-map.dto';
import { PlatformAdAccountToWorkspace } from '../entities/ad-account-workspace-map.entity';
import { ReadPlatformAdAccountWorkspaceMapDto } from '../dto/read-platform-ad-account-workspace-map.dto';
import { ReadHealthDashboardAdAccountEntity } from '../dto/read-health-dashboard-ad-account-entity.dto';
import { ReadHealthDashboardAdAccountDto } from '../dto/read-health-dashboard-ad-account.dto';
import {
  formatDateToLocalDateString,
  getImportStatusForBff,
} from 'src/common/utils/helper';
import { PlatformAdAccountBrandMap } from '../entities/platform-ad-account-brand-map.entity';
import { CreateAdAccountBrandMap } from '../dto/create-ad-account-brand-map.dto';
import { ReadPlatformAdAccountWithAdditionalInfoDto } from '../dto/read-platform-ad-account-with-additional-info.dto';
import { CreateAdAccountMarketMap } from '../dto/create-ad-account-market-map.dto';
import { PlatformAdAccountMarketMap } from '../entities/platform-ad-account-market-map.entity';
import { ReadAdAccountMapDto } from '../dto/read-ad-account-map.dto';
import { CreateBulkAdAccountMarketMapDto } from '../dto/create-bulk-ad-account-market-map.dto';
import { CreateBulkAdAccountBrandMapDto } from '../dto/create-bulk-ad-account-brand-map.dto';
import { Market } from 'src/markets/entities/market.entity';
import { ReadBasicMarketDto } from 'src/markets/dto/read-basic-market.dto';
import { CreatePlatformAdAccountIndustryDto } from '../dto/create-platform-ad-account-industry.dto';
import { PlatformAdAccountIndustryMap } from '../entities/platform-ad-account-industry-map.entity';
import { CreatePlatformAdAccountSequentialFailureDto } from '../dto/create-platform-ad-account-sequential-failure.dto';
import { PlatformAdAccountSequentialFailure } from '../entities/platform-ad-account-sequential-failure.entity';

@Injectable()
export class AdAccountsProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper) => {
      createMap(
        mapper,
        PlatformAdAccount,
        ReadPlatformAdAccountDto,
        forMember(
          (dest) => dest.lastSuccessfulProcessingDate,
          mapFrom((src) => src.lastImportSuccessDate),
        ),
        forMember(
          (dest) => dest.importStatus,
          mapFrom((src) => {
            if (src.accountImportStatus) {
              return getImportStatusForBff(src.accountImportStatus.status);
            }

            return null;
          }),
        ),
      );

      createMap(
        mapper,
        PlatformAdAccount,
        ReadPlatformAdAccountWithAdditionalInfoDto,
        forMember(
          (dest) => dest.lastSuccessfulProcessingDate,
          mapFrom((src) => src.lastImportSuccessDate),
        ),
        forMember(
          (dest) => dest.connected,
          mapFrom((src) => src.canAccess === 1),
        ),
        forMember(
          (dest) => dest.isLinkedToSelectedWorkspace,
          mapFrom((src) => {
            if (src.PlatformAdAccountToWorkspace) {
              return src.PlatformAdAccountToWorkspace.length > 0;
            }
          }),
        ),
        forMember(
          (dest) => dest.connectedBy,
          mapFrom((src) =>
            // get the uniques as array
            Array.from(
              new Set(
                src.organizationAdAccountUserPermissions?.map(
                  (orgAdAccountPermission) => orgAdAccountPermission.user.email,
                ),
              ),
            ),
          ),
        ),
        forMember(
          (dest) => dest.importStatus,
          mapFrom((src) => {
            if (src.accountImportStatus) {
              return getImportStatusForBff(src.accountImportStatus.status);
            }

            return null;
          }),
        ),
      );

      createMap(
        mapper,
        CreatePlatformAdAccountWorkspaceMapDto,
        PlatformAdAccountToWorkspace,
        forMember(
          (dest) => dest.partnerId,
          mapFrom((src) => src.workspaceId),
        ),
      );
      createMap(
        mapper,
        PlatformAdAccountToWorkspace,
        ReadPlatformAdAccountWorkspaceMapDto,
        forMember(
          (dest) => dest.partnerId,
          mapFrom((src) => src.workspace?.id || src.partnerId),
        ),
        forMember(
          (dest) => dest.platformAdAccountId,
          mapFrom(
            (src) => src.platformAdAccount?.id || src.platformAdAccountId,
          ),
        ),
      );

      createMap(
        mapper,
        ReadHealthDashboardAdAccountEntity,
        ReadHealthDashboardAdAccountDto,
        forMember(
          (dest) => dest.platformAccountId,
          mapFrom((src) => src.platform_account_id),
        ),
        forMember(
          (dest) => dest.platformAccountName,
          mapFrom((src) => src.platform_account_name),
        ),
        forMember(
          (dest) => dest.channel,
          mapFrom((src) => src.platform),
        ),
        forMember(
          (dest) => dest.currencyCode,
          mapFrom((src) => src.currency_code),
        ),
        forMember(
          (dest) => dest.connectionStatus,
          mapFrom((src) => src.connection_status),
        ),
        forMember(
          (dest) => dest.lastConnectedOn,
          mapFrom((src) => formatDateToLocalDateString(src.last_connected_on)),
        ),
        forMember(
          (dest) => dest.lastImportedOn,
          mapFrom((src) =>
            formatDateToLocalDateString(src.last_import_success_date),
          ),
        ),
        forMember(
          (dest) => dest.connectedBy,
          mapFrom((src) =>
            src.connected_by ? src.connected_by.split(',,') : [],
          ),
        ),
        forMember(
          (dest) => dest.workspaces,
          mapFrom((src) => (src.workspaces ? src.workspaces.split(',,') : [])),
        ),
        forMember(
          (dest) => dest.isConnectedByUser,
          mapFrom((src) => src.is_connected_by_user == 1),
        ),
        forMember(
          (dest) => dest.brands,
          mapFrom((src) => (src.brands ? src.brands.split(',,') : [])),
        ),
        forMember(
          (dest) => dest.markets,
          mapFrom((src) => (src.markets ? src.markets.split(',,') : [])),
        ),
        forMember(
          (dest) => dest.industryGroup,
          mapFrom((src) =>
            !src.industry_group_id
              ? null
              : {
                  id: src.industry_group_id,
                  name: src.industry_group_name,
                },
          ),
        ),
        forMember(
          (dest) => dest.industry,
          mapFrom((src) =>
            !src.industry_id
              ? null
              : {
                  id: src.industry_id,
                  name: src.industry_name,
                },
          ),
        ),
        forMember(
          (dest) => dest.subIndustry,
          mapFrom((src) =>
            !src.sub_industry_id
              ? null
              : {
                  id: src.sub_industry_id,
                  name: src.sub_industry_name,
                },
          ),
        ),
        forMember(
          (dest) => dest.importStatus,
          mapFrom((src) =>
            src.import_status ? getImportStatusForBff(src.import_status) : null,
          ),
        ),
      );

      createMap(mapper, CreateAdAccountBrandMap, PlatformAdAccountBrandMap);

      createMap(mapper, CreateAdAccountMarketMap, PlatformAdAccountMarketMap);

      createMap(
        mapper,
        CreateBulkAdAccountBrandMapDto,
        ReadAdAccountMapDto,
        forMember(
          (dest) => dest.message,
          mapFrom(
            (src) =>
              `Selected brands successfully added/removed from to/from ${src.accounts.length} ad account(s).`,
          ),
        ),
      );

      createMap(
        mapper,
        CreateBulkAdAccountMarketMapDto,
        ReadAdAccountMapDto,
        forMember(
          (dest) => dest.message,
          mapFrom(
            (src) =>
              `Selected markets successfully added/removed from to/from ${src.accounts.length} ad account(s).`,
          ),
        ),
      );

      createMap(
        mapper,
        Market,
        ReadBasicMarketDto,
        forMember(
          (dest) => dest.isoCode,
          mapFrom((src) => src.isoCode),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.name),
        ),
      );

      createMap(
        mapper,
        CreatePlatformAdAccountIndustryDto,
        PlatformAdAccountIndustryMap,
      );
      createMap(
        mapper,
        CreatePlatformAdAccountSequentialFailureDto,
        PlatformAdAccountSequentialFailure,
        forMember(
          (dest) => dest.dateCreated,
          mapFrom(() => new Date()),
        ),
        forMember(
          (dest) => dest.lastUpdated,
          mapFrom(() => new Date()),
        ),
        forMember(
          (dest) => dest.failureReason,
          mapFrom((src) => src.failureReason),
        ),
      );
    };
  }
}
