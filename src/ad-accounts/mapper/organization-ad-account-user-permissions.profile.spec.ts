import { Mapper } from '@automapper/core';
import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { ImportState, Permission } from '../../common/constants/constants';
import { User } from '../../entities/user.entity';
import { Organization } from '../../organizations/entities/organization.entity';
import { OrganizationAdAccountUserPermissionsProfile } from './organization-ad-account-user-permissions.profile';
import { OrganizationAdAccountUserPermissions } from '../entities/organization-ad-account-user-permissions.entity';
import { ReadOrganizationAdAccountUserPermissionsDto } from '../dto/read-organization-ad-account-user-permissions.dto';
import { CreateOrganizationPlatformAdAccountDto } from '../dto/create-organization-platform-ad-account.dto';
import { ReadPlatformAdAccountPermissionsDto } from '../dto/read-platform-ad-account-permissions.dto';

const TEST_USER_ID = 1234;
const TEST_ORGANIZATION_ID = '75973c70-a05c-4c18-96b2-327d93082419';
const TEST_PLATFORM_ACCOUNT_ID = 'test_account_123';
const TEST_ORGANIZATION_AD_ACCOUNT_USER_PERMISSIONS: OrganizationAdAccountUserPermissions =
  {
    organizationId: TEST_ORGANIZATION_ID,
    platformAdAccountId: 1,
    userId: TEST_USER_ID,
    permission: Permission.ALLOW,
    dateCreated: new Date(),
    lastUpdated: new Date(),
    dateLastConnected: new Date(),
    accountAccessibleByUser: true,
    user: new User(),
    platformAdAccount: {
      id: 1,
      userId: 1,
      platform: 'facebook',
      platformUserId: '*********',
      platformOrganizationId: null,
      platformAccountId: TEST_PLATFORM_ACCOUNT_ID,
      currencyCode: 'USD',
      platformAccountName: 'Test Account',
      canAccess: 1,
      processingCompleted: 1,
      processingCompletedDate: new Date(),
      importV3Enabled: 1,
      dateCreated: new Date(),
      lastUpdated: new Date(),
      lastImportStartDate: new Date(),
      lastImportCompleteDate: new Date(),
      lastImportSuccessDate: new Date(),
      lastImportStatus: ImportState.SUCCESS,
      PlatformAdAccountToWorkspace: [],
      organizationPlatformAdAccountMap: [],
      organizationAdAccountUserPermissions: [],
      platformAdAccountMarketMap: [],
      platformAdAccountBrandMap: [],
      workspaceAdAccountMap: [],
      platformAdAccountSequentialFailures: [],
      user: new User(),
      accountImportStatus: null,
      importActive: 1,
      importStatuses: [],
    },
    organization: new Organization(),
  };

describe('OrganizationAdAccountUserPermissionsProfile', () => {
  let mapper: Mapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [OrganizationAdAccountUserPermissionsProfile],
    }).compile();

    const organizationAdAccountUserPermissionsProfile: OrganizationAdAccountUserPermissionsProfile =
      module.get<OrganizationAdAccountUserPermissionsProfile>(
        OrganizationAdAccountUserPermissionsProfile,
      );

    mapper = module.get<Mapper>(getMapperToken());
    organizationAdAccountUserPermissionsProfile.profile(mapper);
  });

  it('should map CreateOrganizationPlatformAdAccountDto to OrganizationAdAccountUserPermissions', () => {
    const createOrganizationPlatformAdAccountDto = {
      userId: TEST_USER_ID,
      platformAccountId: TEST_PLATFORM_ACCOUNT_ID,
      permission: Permission.ALLOW,
    };
    const organizationAdAccountUserPermissions = mapper.map(
      createOrganizationPlatformAdAccountDto,
      CreateOrganizationPlatformAdAccountDto,
      OrganizationAdAccountUserPermissions,
    );
    expect(organizationAdAccountUserPermissions).toBeDefined();
    expect(organizationAdAccountUserPermissions.userId).toBe(TEST_USER_ID);
    expect(
      organizationAdAccountUserPermissions.platformAdAccountId,
    ).not.toBeDefined();
    expect(organizationAdAccountUserPermissions.permission).toBe(
      createOrganizationPlatformAdAccountDto.permission,
    );
  });

  it('should map OrganizationAdAccountUserPermissions to ReadOrganizationAdAccountUserPermissionsDto', () => {
    const readOrganizationAdAccountUserPermissionsDto = mapper.map(
      TEST_ORGANIZATION_AD_ACCOUNT_USER_PERMISSIONS,
      OrganizationAdAccountUserPermissions,
      ReadOrganizationAdAccountUserPermissionsDto,
    );
    expect(readOrganizationAdAccountUserPermissionsDto).toBeDefined();
    expect(readOrganizationAdAccountUserPermissionsDto.organizationId).toBe(
      TEST_ORGANIZATION_ID,
    );
    expect(readOrganizationAdAccountUserPermissionsDto.platformAccountId).toBe(
      TEST_PLATFORM_ACCOUNT_ID,
    );
    expect(readOrganizationAdAccountUserPermissionsDto.userId).toBe(
      TEST_USER_ID,
    );
    expect(readOrganizationAdAccountUserPermissionsDto.permission).toBe(
      Permission.ALLOW,
    );
  });

  it('should map OrganizationAdAccountUserPermissions to ReadOrganizationAdAccountUserPermissionsDto', () => {
    const readPlatformAdAccountPermissionsDto = mapper.map(
      TEST_ORGANIZATION_AD_ACCOUNT_USER_PERMISSIONS,
      OrganizationAdAccountUserPermissions,
      ReadPlatformAdAccountPermissionsDto,
    );
    expect(readPlatformAdAccountPermissionsDto).toBeDefined();
    expect(readPlatformAdAccountPermissionsDto.platformAccountId).toBe(
      TEST_PLATFORM_ACCOUNT_ID,
    );
    expect(readPlatformAdAccountPermissionsDto.permission).toBe(
      TEST_ORGANIZATION_AD_ACCOUNT_USER_PERMISSIONS.permission,
    );
  });
});
