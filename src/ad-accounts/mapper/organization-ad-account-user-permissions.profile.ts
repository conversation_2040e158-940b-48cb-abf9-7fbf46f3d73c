import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { Injectable } from '@nestjs/common';
import { CreateOrganizationPlatformAdAccountDto } from '../dto/create-organization-platform-ad-account.dto';
import { OrganizationAdAccountUserPermissions } from '../entities/organization-ad-account-user-permissions.entity';
import { ReadOrganizationAdAccountUserPermissionsDto } from '../dto/read-organization-ad-account-user-permissions.dto';
import { ReadPlatformAdAccountPermissionsDto } from '../dto/read-platform-ad-account-permissions.dto';

/**
 * Defines the mapping rule between DTOs and entities
 */
@Injectable()
export class OrganizationAdAccountUserPermissionsProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  /**
   * Profile to create mapping between DTOs and Entities.
   * Updates the mapper with a collection of maps where
   * each map defines the relationship and flow between a DTO and an Entity class.
   */
  override get profile() {
    return (mapper) => {
      /**
       * For creating a platform ad account, this map links CreateOrganizationPlatformAdAccountDto to OrganizationAdAccountUserPermissions entity.
       * Source : CreateOrganizationPlatformAdAccountDto ; Destination: OrganizationAdAccountUserPermissions
       */
      createMap(
        mapper,
        CreateOrganizationPlatformAdAccountDto,
        OrganizationAdAccountUserPermissions,
      );

      /**
       * For reading a platform ad account, this map links OrganizationAdAccountUserPermissions to ReadOrganizationAdAccountUserPermissionsDto entity.
       * Source : OrganizationAdAccountUserPermissions ; Destination: ReadOrganizationAdAccountUserPermissionsDto
       */
      createMap(
        mapper,
        OrganizationAdAccountUserPermissions,
        ReadOrganizationAdAccountUserPermissionsDto,
        forMember(
          (destination) => destination.platformAccountId,
          mapFrom((source) => source.platformAdAccount?.platformAccountId),
        ),
      );

      /**
       * For reading a platform ad account, this map links OrganizationAdAccountUserPermissions to ReadPlatformAdAccountPermissionsDto entity.
       * Source : OrganizationAdAccountUserPermissions ; Destination: ReadPlatformAdAccountPermissionsDto
       */
      createMap(
        mapper,
        OrganizationAdAccountUserPermissions,
        ReadPlatformAdAccountPermissionsDto,
        forMember(
          (destination) => destination.platformAccountId,
          mapFrom((source) => source.platformAdAccount?.platformAccountId),
        ),
      );
    };
  }
}
