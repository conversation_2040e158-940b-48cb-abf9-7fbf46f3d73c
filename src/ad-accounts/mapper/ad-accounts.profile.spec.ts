import { Mapper } from '@automapper/core';
import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { AdAccountsProfile } from './ad-accounts.profile';
import { PlatformAdAccount } from '../entities/ad-account.entity';
import { ReadPlatformAdAccountDto } from '../dto/read-platform-ad-account.dto';
import { CreatePlatformAdAccountWorkspaceMapDto } from '../dto/create-platform-ad-account-workspace-map.dto';
import { PlatformAdAccountToWorkspace } from '../entities/ad-account-workspace-map.entity';
import { User } from '../../entities/user.entity';
import { Workspace } from '../../workspaces/entities/workspace.entity';
import { ReadPlatformAdAccountWorkspaceMapDto } from '../dto/read-platform-ad-account-workspace-map.dto';
import { ReadHealthDashboardAdAccountEntity } from '../dto/read-health-dashboard-ad-account-entity.dto';
import { ReadHealthDashboardAdAccountDto } from '../dto/read-health-dashboard-ad-account.dto';
import { CreateAdAccountBrandMap } from '../dto/create-ad-account-brand-map.dto';
import { PlatformAdAccountBrandMap } from '../entities/platform-ad-account-brand-map.entity';
import { ReadPlatformAdAccountWithAdditionalInfoDto } from '../dto/read-platform-ad-account-with-additional-info.dto';
import { CreateAdAccountMarketMap } from '../dto/create-ad-account-market-map.dto';
import { PlatformAdAccountMarketMap } from '../entities/platform-ad-account-market-map.entity';
import { CreateBulkAdAccountBrandMapDto } from '../dto/create-bulk-ad-account-brand-map.dto';
import { ReadAdAccountMapDto } from '../dto/read-ad-account-map.dto';
import { CreateBulkAdAccountMarketMapDto } from '../dto/create-bulk-ad-account-market-map.dto';
import {
  ImportState,
  ImportStatus,
  ImportStatusForBFF,
  Permission,
} from 'src/common/constants/constants';
import { Organization } from 'src/organizations/entities/organization.entity';
import { CreatePlatformAdAccountIndustryDto } from '../dto/create-platform-ad-account-industry.dto';
import { PlatformAdAccountIndustryMap } from '../entities/platform-ad-account-industry-map.entity';

describe('AdAccountProfile', () => {
  let mapper: Mapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [AdAccountsProfile],
    }).compile();
    const adAccountsProfile: AdAccountsProfile =
      module.get<AdAccountsProfile>(AdAccountsProfile);
    mapper = module.get<Mapper>(getMapperToken());
    adAccountsProfile.profile(mapper);
  });
  it('should map PlatformAdAccount to ReadPlatformAdAccountDto', () => {
    // Create a mock PlatformAdAccount with mock data
    const mockPlatformAdAccount: PlatformAdAccount = {
      id: 1,
      userId: 1,
      platform: 'facebook',
      platformUserId: '*********',
      platformOrganizationId: null,
      platformAccountId: '*********',
      currencyCode: 'USD',
      platformAccountName: 'Test Account',
      canAccess: 1,
      processingCompleted: 1,
      processingCompletedDate: new Date(),
      importV3Enabled: 1,
      dateCreated: new Date(),
      lastUpdated: new Date(),
      lastImportStartDate: new Date(),
      lastImportCompleteDate: new Date(),
      lastImportSuccessDate: new Date(),
      lastImportStatus: ImportState.SUCCESS,
      PlatformAdAccountToWorkspace: [
        // Provide any necessary properties for the workspace map
      ],
      organizationPlatformAdAccountMap: [],
      organizationAdAccountUserPermissions: [],
      platformAdAccountBrandMap: [],
      platformAdAccountMarketMap: [],
      workspaceAdAccountMap: [],
      platformAdAccountSequentialFailures: [],
      user: new User(),
      accountImportStatus: null,
      importActive: 1,
      importStatuses: [],
    };
    const mockReadPlatformAdAccountDto = mapper.map(
      mockPlatformAdAccount,
      PlatformAdAccount,
      ReadPlatformAdAccountDto,
    );
    expect(mockReadPlatformAdAccountDto).toBeDefined();
    expect(mockReadPlatformAdAccountDto.id).toBe(1);
    expect(mockReadPlatformAdAccountDto.platform).toBe('facebook');
    expect(mockReadPlatformAdAccountDto.platformAccountName).toBe(
      'Test Account',
    );
    expect(mockReadPlatformAdAccountDto.processingCompleted).toBe(1);
    expect(mockReadPlatformAdAccountDto.dateCreated).toBe(
      mockPlatformAdAccount.dateCreated,
    );
    expect(mockReadPlatformAdAccountDto.lastUpdated).toBe(
      mockPlatformAdAccount.lastUpdated,
    );
  });
  it('should map PlatformAdAccount to ReadPlatformAdAccountWithAdditionalInfoDto', () => {
    const mockPlatformAdAccount: PlatformAdAccount = {
      id: 1,
      userId: 1,
      platform: 'facebook',
      platformUserId: '*********',
      platformOrganizationId: null,
      platformAccountId: '*********',
      currencyCode: 'USD',
      platformAccountName: 'Test Account',
      canAccess: 1,
      processingCompleted: 1,
      processingCompletedDate: new Date(),
      importV3Enabled: 1,
      dateCreated: new Date(),
      lastUpdated: new Date(),
      lastImportStartDate: new Date(),
      lastImportCompleteDate: new Date(),
      lastImportSuccessDate: new Date(),
      lastImportStatus: ImportState.SUCCESS,
      accountImportStatus: null,
      importActive: 1,
      importStatuses: [],
      PlatformAdAccountToWorkspace: [
        {
          platformAdAccountId: 1,
          partnerId: 1,
          platformAdAccount: new PlatformAdAccount(),
          workspace: new Workspace(),
        },
      ],
      organizationPlatformAdAccountMap: [],
      platformAdAccountBrandMap: [],
      platformAdAccountMarketMap: [],
      workspaceAdAccountMap: [],
      platformAdAccountSequentialFailures: [],
      organizationAdAccountUserPermissions: [
        {
          organizationId: '1',
          platformAdAccountId: 1,
          userId: 1,
          permission: Permission.ALLOW,
          dateCreated: new Date(Date.now()),
          lastUpdated: new Date(Date.now()),
          dateLastConnected: new Date(Date.now()),
          accountAccessibleByUser: true,
          platformAdAccount: new PlatformAdAccount(),
          organization: new Organization(),
          user: new User(),
        },
      ],
      user: new User(),
    };
    const dto = mapper.map(
      mockPlatformAdAccount,
      PlatformAdAccount,
      ReadPlatformAdAccountWithAdditionalInfoDto,
    );
    expect(dto).toBeDefined();
    expect(dto.isLinkedToSelectedWorkspace).toBe(true);
    expect(dto.connected).toBe(true);
    expect(dto.lastSuccessfulProcessingDate).toBe(
      mockPlatformAdAccount.lastImportSuccessDate,
    );
  });

  it('should create a new map from CreatePlatformAdAccountWorkspaceMapDto', () => {
    const mockCreatePlatformAdAccountWorkspaceMapDto: CreatePlatformAdAccountWorkspaceMapDto =
      {
        platformAdAccountId: 1,
        workspaceId: 1,
      };
    const mockPlatformAdAccount: PlatformAdAccountToWorkspace = mapper.map(
      mockCreatePlatformAdAccountWorkspaceMapDto,
      CreatePlatformAdAccountWorkspaceMapDto,
      PlatformAdAccountToWorkspace,
    );
    expect(mockPlatformAdAccount).toBeDefined();
    expect(mockPlatformAdAccount.platformAdAccountId).toBe(1);
    expect(mockPlatformAdAccount.partnerId).toBe(1);
  });

  it('should map PlatformAdAccountToWorkspace to ReadPlatformAdAccountWorkspaceMapDto', () => {
    const mockPlatformAdAccountToWorkspace: PlatformAdAccountToWorkspace = {
      platformAdAccountId: 1,
      partnerId: 1,
      platformAdAccount: new PlatformAdAccount(),
      workspace: new Workspace(),
    };
    const mockReadPlatformAdAccountDto = mapper.map(
      mockPlatformAdAccountToWorkspace,
      PlatformAdAccountToWorkspace,
      ReadPlatformAdAccountWorkspaceMapDto,
    );
    expect(mockReadPlatformAdAccountDto).toBeDefined();
    expect(mockReadPlatformAdAccountDto.platformAdAccountId).toBe(1);
    expect(mockReadPlatformAdAccountDto.partnerId).toBe(1);
  });

  it('should map ReadHealthDashboardAdAccountEntity to ReadHealthDashboardAdAccountDto', () => {
    const readHealthDashboardAdAccountEntity: ReadHealthDashboardAdAccountEntity =
      {
        platform_account_id: '*********',
        platform_account_name: 'test account',
        platform: 'facebook',
        currency_code: 'USD',
        connection_status: 'Connected',
        last_connected_on: '2023-08-31 20:38:54',
        last_import_success_date: '2021-12-23 16:27:38',
        connected_by: '<EMAIL>,,<EMAIL>',
        is_connected_by_user: 1,
        workspaces: 'vidmob,,vidmob2',
        brands: 'brand1,,brand2',
        markets: 'market1,,market2',
        industry_group_id: 1,
        industry_group_name: 'test industry group',
        industry_id: 2,
        industry_name: 'test industry',
        sub_industry_id: null,
        sub_industry_name: null,
        import_status: ImportStatus.PROCESSING,
      };
    const mockReadHealthDashboardAdAccount = mapper.map(
      readHealthDashboardAdAccountEntity,
      ReadHealthDashboardAdAccountEntity,
      ReadHealthDashboardAdAccountDto,
    );
    expect(mockReadHealthDashboardAdAccount).toBeDefined();
    expect(mockReadHealthDashboardAdAccount).toEqual({
      platformAccountId: '*********',
      platformAccountName: 'test account',
      channel: 'facebook',
      currencyCode: 'USD',
      connectionStatus: 'Connected',
      lastConnectedOn: 'Aug 31, 2023',
      lastImportedOn: 'Dec 23, 2021',
      connectedBy: ['<EMAIL>', '<EMAIL>'],
      workspaces: ['vidmob', 'vidmob2'],
      brands: ['brand1', 'brand2'],
      markets: ['market1', 'market2'],
      isConnectedByUser: true,
      industryGroup: {
        id: 1,
        name: 'test industry group',
      },
      industry: {
        id: 2,
        name: 'test industry',
      },
      subIndustry: null,
      importStatus: ImportStatusForBFF.PROCESSING,
    });
  });

  it('should map ReadHealthDashboardAdAccountEntity to ReadHealthDashboardAdAccountDto with null values', () => {
    const readHealthDashboardAdAccountEntity: ReadHealthDashboardAdAccountEntity =
      {
        platform_account_id: '*********',
        platform_account_name: 'test account',
        platform: 'facebook',
        currency_code: 'USD',
        connection_status: 'Missing permissions',
        last_connected_on: '2023-08-31 20:38:54',
        last_import_success_date: null,
        connected_by: null,
        is_connected_by_user: 0,
        workspaces: null,
        brands: null,
        markets: null,
        industry_group_id: null,
        industry_group_name: null,
        industry_id: null,
        industry_name: null,
        sub_industry_id: null,
        sub_industry_name: null,
        import_status: ImportStatus.PROCESSING,
      };
    const mockReadHealthDashboardAdAccount = mapper.map(
      readHealthDashboardAdAccountEntity,
      ReadHealthDashboardAdAccountEntity,
      ReadHealthDashboardAdAccountDto,
    );
    expect(mockReadHealthDashboardAdAccount).toBeDefined();
    expect(mockReadHealthDashboardAdAccount).toEqual({
      platformAccountId: '*********',
      platformAccountName: 'test account',
      channel: 'facebook',
      currencyCode: 'USD',
      connectionStatus: 'Missing permissions',
      lastConnectedOn: 'Aug 31, 2023',
      lastImportedOn: null,
      connectedBy: [],
      workspaces: [],
      brands: [],
      markets: [],
      isConnectedByUser: false,
      industryGroup: null,
      industry: null,
      subIndustry: null,
      importStatus: ImportStatusForBFF.PROCESSING,
    });
  });

  it('should map CreateAdAccountBrandMap to PlatformAdAccountBrandMap', () => {
    const createAdAccountBrandMap: CreateAdAccountBrandMap = {
      platformAdAccountId: 123,
      brandId: '5385c1e6-e031-4505-af1b-5b593bbbc395',
      dateCreated: new Date(Date.now()),
    };

    const platformAdAccountBrandMapEntity = mapper.map(
      createAdAccountBrandMap,
      CreateAdAccountBrandMap,
      PlatformAdAccountBrandMap,
    );

    expect(platformAdAccountBrandMapEntity).toBeDefined();
    expect(platformAdAccountBrandMapEntity.brandId).toBe(
      '5385c1e6-e031-4505-af1b-5b593bbbc395',
    );
    expect(platformAdAccountBrandMapEntity.platformAdAccountId).toBe(123);
  });

  it('should map CreateAdAccountMarketMap to PlatformAdAccountMarketMap', () => {
    const createAdAccountMarketMap: CreateAdAccountMarketMap = {
      platformAdAccountId: 123,
      countryIsoCode: 'usa',
      dateCreated: new Date(Date.now()),
    };

    const platformAdAccountMarketMapEntity = mapper.map(
      createAdAccountMarketMap,
      CreateAdAccountMarketMap,
      PlatformAdAccountMarketMap,
    );

    expect(platformAdAccountMarketMapEntity).toBeDefined();
    expect(platformAdAccountMarketMapEntity.countryIsoCode).toBe('usa');
    expect(platformAdAccountMarketMapEntity.platformAdAccountId).toBe(123);
  });

  it('should map CreateBulkAdAccountBrandMapDto to ReadAdAccountMapDto', () => {
    const createBulkAdAccountBrandMap: CreateBulkAdAccountBrandMapDto = {
      accounts: ['123', '456'],
      selected_brands: ['xxxxx', 'yyyy'],
      unselected_brands: ['wwww', 'zzzz'],
    };

    const platformAdAccountMarketMapEntity = mapper.map(
      createBulkAdAccountBrandMap,
      CreateBulkAdAccountBrandMapDto,
      ReadAdAccountMapDto,
    );

    expect(platformAdAccountMarketMapEntity).toBeDefined();
    expect(platformAdAccountMarketMapEntity.message).toBe(
      'Selected brands successfully added/removed from to/from 2 ad account(s).',
    );
  });

  it('should map CreateBulkAdAccountMarketMapDto to ReadAdAccountMapDto', () => {
    const createBulkAdAccountMarketMap: CreateBulkAdAccountMarketMapDto = {
      accounts: ['123', '456'],
      selected_markets: ['usa', 'bra'],
      unselected_markets: ['arg', 'alb'],
    };

    const platformAdAccountMarketMapEntity = mapper.map(
      createBulkAdAccountMarketMap,
      CreateBulkAdAccountMarketMapDto,
      ReadAdAccountMapDto,
    );

    expect(platformAdAccountMarketMapEntity).toBeDefined();
    expect(platformAdAccountMarketMapEntity.message).toBe(
      'Selected markets successfully added/removed from to/from 2 ad account(s).',
    );
  });

  it('should map CreatePlatformAdAccountIndustryDto to PlatformAdAccountIndustryMap', () => {
    const createPlatformAdAccountIndustryDto: CreatePlatformAdAccountIndustryDto =
      {
        platformAdAccountId: 123456,
        industryId: 123,
        assigningPersonId: 1,
        dateCreated: new Date(Date.now()),
        lastUpdated: new Date(Date.now()),
      };

    const platformAdAccountIndustryMap = mapper.map(
      createPlatformAdAccountIndustryDto,
      CreatePlatformAdAccountIndustryDto,
      PlatformAdAccountIndustryMap,
    );

    expect(platformAdAccountIndustryMap).toBeDefined();
    expect(platformAdAccountIndustryMap.platformAdAccountId).toBe(123456);
    expect(platformAdAccountIndustryMap.industryId).toBe(123);
    expect(platformAdAccountIndustryMap.assigningPersonId).toBe(1);
  });
});
