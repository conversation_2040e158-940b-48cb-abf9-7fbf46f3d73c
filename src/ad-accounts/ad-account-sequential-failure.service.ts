import { Injectable, Logger } from '@nestjs/common';
import { PlatformAdAccount } from './entities/ad-account.entity';
import { UpdateAdAccountImportDataDto } from './dto/update-ad-account-import-data.dto';
import { PlatformAdAccountSequentialFailure } from './entities/platform-ad-account-sequential-failure.entity';
import { EntityManager } from 'typeorm';
import { MAX_ACCOUNT_IMPORT_FAILURE_THRESHOLD } from '../common/constants/constants';

@Injectable()
export class AdAccountSequentialFailureService {
  private readonly logger: Logger = new Logger(
    AdAccountSequentialFailureService.name,
  );

  hasNotReachedFailureThreshold(failureCount: number): boolean {
    return failureCount < MAX_ACCOUNT_IMPORT_FAILURE_THRESHOLD;
  }

  /**
   * Save an ad account sequential failure record in database.
   * @param entityManager entity manager to use for the transaction
   * @param platformAdAccount platform ad account to save the sequential failure for
   * @param accountImportDto account import data transfer object containing the failure reason
   */
  async saveAdAccountSequentialFailure(
    entityManager: EntityManager,
    platformAdAccount: PlatformAdAccount,
    accountImportDto: UpdateAdAccountImportDataDto,
  ) {
    this.logger.log(
      `Creating sequential failure for platform ad account ${platformAdAccount.platformAccountId}`,
    );
    const sequentialFailure = new PlatformAdAccountSequentialFailure();
    sequentialFailure.platformAdAccount = platformAdAccount;
    sequentialFailure.importId = accountImportDto.importId;
    sequentialFailure.rebuildRequestId = accountImportDto.rebuildRequestId;
    if (accountImportDto.failureReason)
      sequentialFailure.failureReason = accountImportDto.failureReason;
    sequentialFailure.dateCreated = new Date();
    sequentialFailure.lastUpdated = new Date();
    return entityManager.save(sequentialFailure);
  }

  /**
   * Delete all sequential failures for a platform ad account
   * @param entityManager entity manager to use for the transaction
   * @param adAccountId The ad account id
   */
  async deleteAdAccountSequentialFailuresByPlatformAdAccountId(
    entityManager: EntityManager,
    adAccountId: string,
  ): Promise<void> {
    const failures = await entityManager.find(
      PlatformAdAccountSequentialFailure,
      {
        where: {
          platformAdAccount: {
            platformAccountId: adAccountId,
          },
        },
      },
    );
    await entityManager.remove(failures);
  }
}
