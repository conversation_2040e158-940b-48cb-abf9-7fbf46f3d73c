import { OrganizationPlatformAdAccountMap } from './entities/organization-platform-ad-account-map.entity';
import { CreateOrganizationPlatformAdAccountDto } from './dto/create-organization-platform-ad-account.dto';
import { ImportStatus, Permission } from '../common/constants/constants';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  EntityManager,
  In,
  QueryRunner,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { AdAccountsService } from './ad-accounts.service';
import { ReadOrganizationDto } from '../organizations/dto/read-organization.dto';
import { Organization } from '../organizations/entities/organization.entity';
import { OrganizationAdAccountUserPermissions } from './entities/organization-ad-account-user-permissions.entity';
import { ReadOrganizationAdAccountUserPermissionsDto } from './dto/read-organization-ad-account-user-permissions.dto';
import { ReadPlatformAdAccountPermissionsDto } from './dto/read-platform-ad-account-permissions.dto';
import { PlatformAdAccount } from './entities/ad-account.entity';
import { ReadHealthDashboardAdAccountDto } from './dto/read-health-dashboard-ad-account.dto';
import {
  PaginationOptions,
  SqsConsumerEventHandler,
  SqsMessageHandler,
  SqsService,
} from '@vidmob/vidmob-nestjs-common';
import {
  GET_ORGANIZATION_AD_ACCOUNTS_FOR_DASHBOARD_QUERY,
  GET_ORGANIZATION_AD_ACCOUNTS_FOR_DASHBOARD_TOTAL_COUNT,
  GET_WORKSPACE_AD_ACCOUNTS_FOR_DASHBOARD_QUERY,
  GET_WORKSPACE_AD_ACCOUNTS_FOR_DASHBOARD_TOTAL_COUNT,
} from './util/sql-queries';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadHealthDashboardAdAccountEntity } from './dto/read-health-dashboard-ad-account-entity.dto';
import { AccountSearchParamsDto } from './dto/ad-account-search-params.dto';
import { ReadAdAccountOrganizationsDto } from './dto/read-ad-account-organizations.dto';
import { CreateBulkAdAccountBrandMapDto } from './dto/create-bulk-ad-account-brand-map.dto';
import { PlatformAdAccountBrandMap } from './entities/platform-ad-account-brand-map.entity';
import { CreateAdAccountBrandMap } from './dto/create-ad-account-brand-map.dto';
import { startTransaction } from 'src/common/utils/helper';
import { CreateBulkAdAccountMarketMapDto } from './dto/create-bulk-ad-account-market-map.dto';
import { PlatformAdAccountMarketMap } from './entities/platform-ad-account-market-map.entity';
import { CreateAdAccountMarketMap } from './dto/create-ad-account-market-map.dto';
import { ReadAdAccountMapDto } from './dto/read-ad-account-map.dto';
import { BrandService } from 'src/brands/brand.service';
import { ReadAdAccountBrandsDto } from './dto/read-ad-account-brands.dto';
import { ReadAdAccountMarketsDto } from './dto/read-ad-accounts-markets.dto';
import { MarketService } from 'src/markets/market.service';
import { Market } from '../markets/entities/market.entity';
import { ReadBasicMarketDto } from '../markets/dto/read-basic-market.dto';
import { PlatformAdAccountIndustryMap } from './entities/platform-ad-account-industry-map.entity';
import { CreatePlatformAdAccountIndustryDto } from './dto/create-platform-ad-account-industry.dto';
import { ReadPersonalNonPersonalOrganizations } from './dto/read-personal-non-personal-organizations.dto';
import { ReadPlatformOrganizationUsersMapDto } from './dto/read-platform-organization-users-map.dto';
import { OrganizationFeatureWhitelist } from 'src/organizations/entities/organization-feature-whitelist.entity';
import { FEATURE_INTEGRATION_ACCOUNT_IMPORTS_ENABLED } from 'src/organizations/utils/constants';
import { FeatureWhitelist } from 'src/workspaces/entities/feature-whitelist.entity';
import { AdAccountImportStatusService } from './ad-account-import-status.service';
import { CreateImportStatusDto } from './dto/create-import-status.dto';
import { AdAccountHealthSearchParamsDto } from './dto/ad-account-health-search-params.dto';
import { Message } from '@aws-sdk/client-sqs';
import { AccountsPreImportTasksSqsMessageBody } from './dto/read-accounts-pre-import-tasks-sqs-message-body.dto';

const PER_PAGE_DEFAULT = 10;
const OFFSET_DEFAULT = 0;

const ACCOUNTS_PRE_IMPORT_TASKS_QUEUE_NAME = 'accountsPreImportTasksQueue';

@Injectable()
export class OrganizationAdAccountsService {
  private readonly logger = new Logger(OrganizationAdAccountsService.name);
  constructor(
    @InjectRepository(OrganizationPlatformAdAccountMap)
    private organizationPlatformAdAccountMapRepository: Repository<OrganizationPlatformAdAccountMap>,
    @InjectRepository(OrganizationAdAccountUserPermissions)
    private organizationAdAccountUserPermissionsRepository: Repository<OrganizationAdAccountUserPermissions>,
    @InjectRepository(PlatformAdAccountBrandMap)
    private platformAdAccountBrandMapRepository: Repository<PlatformAdAccountBrandMap>,
    @InjectRepository(PlatformAdAccountMarketMap)
    private platformAdAccountMarketMapRepository: Repository<PlatformAdAccountMarketMap>,
    @InjectRepository(PlatformAdAccountIndustryMap)
    private platformAdAccountIndustryMapRepository: Repository<PlatformAdAccountIndustryMap>,
    private adAccountsService: AdAccountsService,
    private adAccountStatusService: AdAccountImportStatusService,
    private brandService: BrandService,
    private marketService: MarketService,
    private readonly sqsService: SqsService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  /**
   * Returns the number of ad accounts connected to an organization
   * @param organizationId
   * @param platformAdAccountIds
   */
  async countNumberOfOrganizationPlatformAdAccountConnections(
    organizationId: string,
    platformAdAccountIds: number[],
  ) {
    return await this.organizationPlatformAdAccountMapRepository.count({
      where: {
        organizationId,
        platformAdAccount: {
          id: In(platformAdAccountIds),
          canAccess: 1,
        },
      },
    });
  }

  /**
   * Find the organization ad account map from database
   * @param organizationId
   * @param adAccountId
   * @return The organization platform ad account map
   */
  private async findOrganizationAdAccountMap(
    organizationId: string,
    adAccountId: string,
  ): Promise<OrganizationPlatformAdAccountMap> {
    return this.organizationPlatformAdAccountMapRepository.findOne({
      where: {
        organizationId: organizationId,
        platformAdAccount: {
          platformAccountId: adAccountId,
        },
      },
      relations: [
        'platformAdAccount',
        'organization',
        'organization.workspaces',
        'platformAdAccount.PlatformAdAccountToWorkspace',
      ],
    });
  }

  /**
   * Find User Ad Account Permission in Organization
   * @param organizationId organization id
   * @param platformAccountId platform account id
   * @param userId user id
   * @return OrganizationAdAccountUserPermissions entity
   */
  private async findUserAdAccountPermissionInOrganization(
    organizationId: string,
    platformAccountId: string,
    userId: number,
  ): Promise<OrganizationAdAccountUserPermissions> {
    return this.organizationAdAccountUserPermissionsRepository.findOne({
      where: {
        organizationId: organizationId,
        platformAdAccount: {
          platformAccountId: platformAccountId,
        },
        user: {
          id: userId,
        },
      },
    });
  }

  /**
   * Find the organization account users count with allow access
   * @param organizationId
   * @param platformAccountId
   * @return number of users with allow access to the ad account in the organization
   */
  private async findOrgAccountUsersWithAllowAccessCount(
    organizationId: string,
    platformAccountId: string,
  ) {
    return this.organizationAdAccountUserPermissionsRepository.count({
      where: {
        organizationId: organizationId,
        platformAdAccount: {
          platformAccountId: platformAccountId,
        },
        permission: Permission.ALLOW,
        accountAccessibleByUser: true,
      },
    });
  }

  /**
   * Save the organization platform ad account map to database
   * @param organizationId organization id
   * @param createOrganizationPlatformAdAccountDto organization platform ad account dto create object
   */
  private async saveOrganizationPlatformAdAccountDto(
    organizationId: string,
    createOrganizationPlatformAdAccountDto: CreateOrganizationPlatformAdAccountDto,
  ) {
    const platformAdAccount =
      await this.adAccountsService.findPlatformAdAccountByAccountId(
        createOrganizationPlatformAdAccountDto.platformAccountId,
      );
    const createOrganizationPlatformAdAccountMap =
      new OrganizationPlatformAdAccountMap();
    createOrganizationPlatformAdAccountMap.organizationId = organizationId;
    createOrganizationPlatformAdAccountMap.platformAdAccount =
      platformAdAccount;
    await this.organizationPlatformAdAccountMapRepository.save(
      createOrganizationPlatformAdAccountMap,
    );
  }

  /**
   * create user ad account permission in organization DTO
   * @param organizationId organization id
   * @param createOrganizationPlatformAdAccountDto organization platform ad account dto create object
   * @return ReadOrganizationAdAccountUserPermissionsDto organization ad account user permissions dto read object
   */
  private async saveUserAdAccountPermissionInOrganizationDTO(
    organizationId: string,
    createOrganizationPlatformAdAccountDto: CreateOrganizationPlatformAdAccountDto,
  ): Promise<ReadOrganizationAdAccountUserPermissionsDto> {
    const createOrganizationAdAccountUserPermissions = this.classMapper.map(
      createOrganizationPlatformAdAccountDto,
      CreateOrganizationPlatformAdAccountDto,
      OrganizationAdAccountUserPermissions,
    );
    const platformAdAccount =
      await this.adAccountsService.findPlatformAdAccountByAccountId(
        createOrganizationPlatformAdAccountDto.platformAccountId,
      );
    createOrganizationAdAccountUserPermissions.organizationId = organizationId;
    createOrganizationAdAccountUserPermissions.platformAdAccount =
      platformAdAccount;
    if (
      createOrganizationPlatformAdAccountDto.permission === Permission.ALLOW
    ) {
      createOrganizationAdAccountUserPermissions.dateLastConnected = new Date();
    }
    const organizationAdAccountUserPermissions =
      await this.organizationAdAccountUserPermissionsRepository.save(
        createOrganizationAdAccountUserPermissions,
      );
    return this.classMapper.map(
      organizationAdAccountUserPermissions,
      OrganizationAdAccountUserPermissions,
      ReadOrganizationAdAccountUserPermissionsDto,
    );
  }

  /**
   * update user ad account permission in organization DTO
   * @param createOrganizationPlatformAdAccountDto organization platform ad account dto create object
   * @param organizationAdAccountUserPermissions organization ad account user permissions
   * @return ReadOrganizationAdAccountUserPermissionsDto organization ad account user permissions dto read object
   */
  private async updateOrganizationAdAccountUserPermissionsDTO(
    createOrganizationPlatformAdAccountDto: CreateOrganizationPlatformAdAccountDto,
    organizationAdAccountUserPermissions: OrganizationAdAccountUserPermissions,
  ): Promise<ReadOrganizationAdAccountUserPermissionsDto> {
    let updateOrganizationAdAccountUserPermissions =
      organizationAdAccountUserPermissions;
    if (
      organizationAdAccountUserPermissions.permission !==
      createOrganizationPlatformAdAccountDto.permission
    ) {
      updateOrganizationAdAccountUserPermissions.permission =
        createOrganizationPlatformAdAccountDto.permission;
      const updateData: any = {
        permission: createOrganizationPlatformAdAccountDto.permission,
      };
      if (
        createOrganizationPlatformAdAccountDto.permission === Permission.ALLOW
      ) {
        updateData.dateLastConnected = new Date();
      }
      await this.organizationAdAccountUserPermissionsRepository.update(
        {
          organizationId: organizationAdAccountUserPermissions.organizationId,
          platformAdAccountId:
            organizationAdAccountUserPermissions.platformAdAccountId,
          userId: organizationAdAccountUserPermissions.userId,
        },
        updateData,
      );
      updateOrganizationAdAccountUserPermissions =
        await this.findUserAdAccountPermissionInOrganization(
          organizationAdAccountUserPermissions.organizationId,
          createOrganizationPlatformAdAccountDto.platformAccountId,
          createOrganizationPlatformAdAccountDto.userId,
        );
    }
    return this.classMapper.map(
      updateOrganizationAdAccountUserPermissions,
      OrganizationAdAccountUserPermissions,
      ReadOrganizationAdAccountUserPermissionsDto,
    );
  }

  /**
   * save user ad account permission in organization
   * @param organizationId organization id
   * @param organizationPlatformAdAccountDto organization platform ad account dto create object
   * @return ReadOrganizationAdAccountUserPermissionsDto organization ad account user permissions dto read object
   */
  private async saveUserAdAccountPermissionInOrganization(
    organizationId: string,
    organizationPlatformAdAccountDto: CreateOrganizationPlatformAdAccountDto,
  ): Promise<ReadOrganizationAdAccountUserPermissionsDto> {
    const userAdAccountPermissionInOrg: OrganizationAdAccountUserPermissions =
      await this.findUserAdAccountPermissionInOrganization(
        organizationId,
        organizationPlatformAdAccountDto.platformAccountId,
        organizationPlatformAdAccountDto.userId,
      );
    if (userAdAccountPermissionInOrg) {
      return this.updateOrganizationAdAccountUserPermissionsDTO(
        organizationPlatformAdAccountDto,
        userAdAccountPermissionInOrg,
      );
    } else {
      return this.saveUserAdAccountPermissionInOrganizationDTO(
        organizationId,
        organizationPlatformAdAccountDto,
      );
    }
  }

  /**
   * Save or Delete ad account to an organization mapping
   * @param organizationId  The organization id
   * @param createOrganizationPlatformAdAccountDto  The ad account to organization map
   * @param canMapAccount  flag to know whether the ad account should be mapped to organization or not
   * @return message ad account to organization map linked/unlinked
   */
  private async saveOrDeleteAdAccountToOrganizationMap(
    organizationId: string,
    createOrganizationPlatformAdAccountDto: CreateOrganizationPlatformAdAccountDto,
    canMapAccount: boolean,
  ): Promise<string> {
    const organizationPlatformAdAccountMap =
      await this.findOrganizationAdAccountMap(
        organizationId,
        createOrganizationPlatformAdAccountDto.platformAccountId,
      );
    if (organizationPlatformAdAccountMap && !canMapAccount) {
      await this.deletePlatformAdAccountMapFromOrganization(
        organizationPlatformAdAccountMap,
      );
      return 'Ad account unlinked from the organization successfully.';
    } else if (organizationPlatformAdAccountMap) {
      return 'Ad account already linked to the organization.';
    } else if (canMapAccount) {
      await this.saveOrganizationPlatformAdAccountDto(
        organizationId,
        createOrganizationPlatformAdAccountDto,
      );
      return 'Ad account linked to the organization successfully.';
    } else {
      return 'Ad account not associated with the organization. No action will be taken as the requested permission is DENY.';
    }
  }

  /**
   * Map an ad account to an organization
   * @param organizationId  The organization id
   * @param createOrganizationPlatformAdAccountDto  The ad account to organization map
   * @return message ad account to organization map linked/unlinked
   */
  async mapAdAccountToOrganization(
    organizationId: string,
    createOrganizationPlatformAdAccountDto: CreateOrganizationPlatformAdAccountDto,
  ): Promise<string> {
    // Before mapping Ad account to Organization - Save user ad account permission in organization
    await this.saveUserAdAccountPermissionInOrganization(
      organizationId,
      createOrganizationPlatformAdAccountDto,
    );
    // Upon successful save - Map ad account to organization when at least one user has ALLOW access
    const orgAccountUsersWithAllowAccessCount =
      await this.findOrgAccountUsersWithAllowAccessCount(
        organizationId,
        createOrganizationPlatformAdAccountDto.platformAccountId,
      );
    const canMapAccount =
      orgAccountUsersWithAllowAccessCount &&
      orgAccountUsersWithAllowAccessCount > 0;
    return this.saveOrDeleteAdAccountToOrganizationMap(
      organizationId,
      createOrganizationPlatformAdAccountDto,
      canMapAccount,
    );
  }

  /**
   * Find all platform ad accounts for an organization
   * @param organizationId organization id
   * @return platform ad accounts for the given org
   */
  async getPlatformAdAccountFromAnOrganization(
    organizationId: string,
  ): Promise<PlatformAdAccount[]> {
    const organizationAdAccountMaps =
      await this.organizationPlatformAdAccountMapRepository.find({
        where: {
          organizationId,
        },
        relations: {
          platformAdAccount: true,
        },
      });
    return organizationAdAccountMaps.map((map) => map.platformAdAccount);
  }

  /**
   * Find all ad accounts for an organization
   * @param organizationId organization id
   * @return adAccountIds ad account ids in organization
   */
  async getAdAccountIdsFromAnOrganization(
    organizationId: string,
  ): Promise<number[]> {
    const organizationAdAccountMaps =
      await this.organizationPlatformAdAccountMapRepository.findBy({
        organizationId,
      });
    return organizationAdAccountMaps.map((map) => map.platformAdAccountId);
  }

  /**
   * Extract unique organizations from organization ad account map sorted
   * @param organizationAdAccounts The organization ad account map
   * @return The unique organizations
   */
  private static extractSortedUniqueOrganizations(
    organizationAdAccounts: OrganizationPlatformAdAccountMap[],
  ): Organization[] {
    const uniqueOrganizations: Organization[] = Object.values(
      organizationAdAccounts.reduce((accumulator, organizationAdAccount) => {
        accumulator[organizationAdAccount.organization.id] =
          accumulator[organizationAdAccount.organization.id] ||
          organizationAdAccount.organization;
        return accumulator;
      }, {}),
    );
    return uniqueOrganizations.sort((org1, org2) =>
      org1.name.localeCompare(org2.name),
    );
  }

  /**
   * Find organizations for an ad account.
   * @param adAccountIds  The ad account ids
   * @return The organizations for the ad account
   */
  async findOrganizationsForAdAccount(
    adAccountIds: string[],
  ): Promise<ReadOrganizationDto[]> {
    const platformAdAccountIds =
      await this.adAccountsService.getAdAccountsIdsFromPlatformAccountIds(
        adAccountIds,
      );
    if (adAccountIds.length === 0) {
      return [];
    } else if (platformAdAccountIds.length !== adAccountIds.length) {
      throw new NotFoundException(`Ad accounts ${adAccountIds} not found`);
    }
    const orgAdAccounts =
      await this.organizationPlatformAdAccountMapRepository.find({
        where: {
          platformAdAccount: {
            platformAccountId: In(adAccountIds),
            canAccess: 1,
          },
        },
        relations: ['organization'],
      });

    const organizations =
      OrganizationAdAccountsService.extractSortedUniqueOrganizations(
        orgAdAccounts,
      );

    return this.classMapper.mapArray(
      organizations,
      Organization,
      ReadOrganizationDto,
    );
  }

  /**
   * Get user permissions for an ad account in an organization.
   * @param organizationId organization id
   * @param platform platform
   * @param userId user id
   */
  async getOrganizationAdAccountUserPermissions(
    organizationId: string,
    platform: string,
    userId: number,
  ): Promise<OrganizationAdAccountUserPermissions[]> {
    return this.organizationAdAccountUserPermissionsRepository.find({
      where: {
        organizationId: organizationId,
        platformAdAccount: {
          platform: platform,
        },
        user: {
          id: userId,
        },
      },
      relations: ['platformAdAccount', 'platformAdAccount.accountImportStatus'],
    });
  }

  /**
   * Get user accounts with permissions for platform in an organization.
   * @param organizationId organization id
   * @param platform platform
   * @param userId user id
   * @returns ReadPlatformAdAccountPermissionsDtoList user's list of ad accounts with permissions in an organization
   */
  async getUserAccountsWithPermissionsInOrganization(
    organizationId: string,
    platform: string,
    userId: number,
  ): Promise<ReadPlatformAdAccountPermissionsDto[]> {
    const organizationAdAccountUserPermissions =
      await this.getOrganizationAdAccountUserPermissions(
        organizationId,
        platform,
        userId,
      );
    return this.classMapper.mapArray(
      organizationAdAccountUserPermissions,
      OrganizationAdAccountUserPermissions,
      ReadPlatformAdAccountPermissionsDto,
    );
  }

  /**
   * Get user account permissions across organization.
   * @param accountId account id
   * @param userId user id
   * @returns ReadPlatformAdAccountPermissionsDtoList user's list of ad account  permissions in all organizations
   */
  async getUserAccountPermissionsAcrossOrganizations(
    accountId: string,
    userId: number,
  ): Promise<ReadOrganizationAdAccountUserPermissionsDto[]> {
    const organizationAdAccountUserPermissions =
      await this.organizationAdAccountUserPermissionsRepository.find({
        where: {
          platformAdAccount: {
            platformAccountId: accountId,
          },
          user: {
            id: userId,
          },
        },
        relations: ['platformAdAccount'],
      });
    return this.classMapper.mapArray(
      organizationAdAccountUserPermissions,
      OrganizationAdAccountUserPermissions,
      ReadOrganizationAdAccountUserPermissionsDto,
    );
  }

  /**
   * Disconnects a Platform Ad Account from an Organizations
   * @param {OrganizationPlatformAdAccountMap} organizationPlatformAdAccountMap - The organization platform ad account map
   * @returns {Promise<void>}
   */
  async deletePlatformAdAccountMapFromOrganization(
    organizationPlatformAdAccountMap: OrganizationPlatformAdAccountMap,
  ) {
    await this.organizationPlatformAdAccountMapRepository.manager.transaction(
      async (entityManager) => {
        await this.deletePlatformAdAccountMapFromOrganizationInTransaction(
          entityManager,
          organizationPlatformAdAccountMap,
        );
      },
    );
  }

  /**
   * Disconnects a Platform Ad Account from an Organizations
   * @param {EntityManager} entityManager - The entity manager
   * @param {OrganizationPlatformAdAccountMap} organizationPlatformAdAccountMap - The organization platform ad account map
   * @returns {Promise<void>}
   */
  async deletePlatformAdAccountMapFromOrganizationInTransaction(
    entityManager: EntityManager,
    organizationPlatformAdAccountMap: OrganizationPlatformAdAccountMap,
  ) {
    await this.disconnectPlatformAdAccountFromOrganizationWorkspaces(
      entityManager,
      organizationPlatformAdAccountMap,
    );
    await entityManager.remove(organizationPlatformAdAccountMap);
  }

  /**
   * Disconnects a Platform Ad Account from an Organization's Workspaces
   * @param {EntityManager} entityManager - The entity manager
   * @param {OrganizationPlatformAdAccountMap} organizationPlatformAdAccountMap - The platform ad account id
   */
  private async disconnectPlatformAdAccountFromOrganizationWorkspaces(
    entityManager: EntityManager,
    organizationPlatformAdAccountMap: OrganizationPlatformAdAccountMap,
  ) {
    const organizationWorkspaceIds =
      this.getAllOrganizationWorkspaceIdsAssociatedWithPlatformAdAccount(
        organizationPlatformAdAccountMap.organization,
        organizationPlatformAdAccountMap.platformAdAccount,
      );
    if (organizationWorkspaceIds.length <= 0) {
      return;
    }
    const platformAdAccountId =
      organizationPlatformAdAccountMap.platformAdAccountId;
    await this.adAccountsService.deleteMultipleWorkspacesConnectionsFromAPlatformAdAccount(
      organizationWorkspaceIds,
      platformAdAccountId,
    );

    if (organizationPlatformAdAccountMap.platformAdAccount.importActive) {
      const createStatusDto: CreateImportStatusDto = {
        platformAdAccountId: platformAdAccountId,
        organizationId: organizationPlatformAdAccountMap.organization.id,
        status: ImportStatus.NOT_IMPORTING,
        reason: {
          message: `Ad account ${platformAdAccountId} disconnected from organization id - ${organizationPlatformAdAccountMap.organization.id}`,
        },
      };
      await this.adAccountStatusService.createImportStatus(
        entityManager,
        createStatusDto,
      );
    }
  }

  /**
   * Gets all the workspace ids associated with an organization's platform ad account
   * @param organization
   * @param platformAdAccount
   * @private
   * @returns {number[]}
   */
  private getAllOrganizationWorkspaceIdsAssociatedWithPlatformAdAccount(
    organization: Organization,
    platformAdAccount: PlatformAdAccount,
  ): number[] {
    const platformWorkspaceMaps =
      platformAdAccount.PlatformAdAccountToWorkspace;
    const workspacesIdsAssociatedWithOrganization = organization.workspaces.map(
      (workspace) => workspace.id,
    );
    const workspaceIdsAssociatedWithAdAccount = platformWorkspaceMaps.map(
      (platformWorkspaceMap) => platformWorkspaceMap.partnerId,
    );
    return workspaceIdsAssociatedWithAdAccount.filter((workspaceId) =>
      workspacesIdsAssociatedWithOrganization.includes(workspaceId),
    );
  }

  private static getOrganizationAdAccountsListForHealthDashBoardCountParams(
    organizationId: string,
    searchParams: AccountSearchParamsDto,
  ): (string | number)[] {
    if (searchParams.search && searchParams.search.trim().length > 0) {
      return [
        organizationId,
        organizationId,
        organizationId,
        organizationId,
        searchParams.search.trim(),
        `%${searchParams.search.trim()}%`,
        `%${searchParams.search.trim()}%`,
        `%${searchParams.search.trim()}%`,
      ];
    } else {
      return [organizationId, organizationId, organizationId, organizationId];
    }
  }

  async getOrganizationAdAccountsListForHealthDashBoardCount(
    organizationId: string,
    searchParams: AccountSearchParamsDto,
  ): Promise<number> {
    const query = GET_ORGANIZATION_AD_ACCOUNTS_FOR_DASHBOARD_TOTAL_COUNT(
      organizationId,
      searchParams,
    );
    const queryParams =
      OrganizationAdAccountsService.getOrganizationAdAccountsListForHealthDashBoardCountParams(
        organizationId,
        searchParams,
      );
    const total: { count: string }[] =
      await this.organizationAdAccountUserPermissionsRepository.query(
        query,
        queryParams,
      );
    return total.length;
  }

  private static getOrganizationAdAccountsListForHealthDashBoardParams(
    organizationId: string,
    userId: number,
    paginationOptions: PaginationOptions,
    searchParams: AccountSearchParamsDto,
  ): (string | number)[] {
    const perPage = paginationOptions.perPage ?? PER_PAGE_DEFAULT;
    const offset = paginationOptions.offset ?? OFFSET_DEFAULT;
    if (searchParams.search && searchParams.search.trim().length > 0) {
      return [
        userId,
        organizationId,
        organizationId,
        organizationId,
        organizationId,
        searchParams.search.trim(),
        `%${searchParams.search.trim()}%`,
        `%${searchParams.search.trim()}%`,
        `%${searchParams.search.trim()}%`,
        perPage,
        offset,
      ];
    } else {
      return [
        userId,
        organizationId,
        organizationId,
        organizationId,
        organizationId,
        perPage,
        offset,
      ];
    }
  }

  async getOrganizationAdAccountsListForHealthDashBoard(
    organizationId: string,
    userId: number,
    paginationOptions: PaginationOptions,
    searchParams: AdAccountHealthSearchParamsDto,
  ): Promise<PaginatedResultArray<ReadHealthDashboardAdAccountDto>> {
    const query = GET_ORGANIZATION_AD_ACCOUNTS_FOR_DASHBOARD_QUERY(
      organizationId,
      searchParams,
    );
    const queryParams =
      OrganizationAdAccountsService.getOrganizationAdAccountsListForHealthDashBoardParams(
        organizationId,
        userId,
        paginationOptions,
        searchParams,
      );
    const result: ReadHealthDashboardAdAccountEntity[] =
      await this.organizationAdAccountUserPermissionsRepository.query(
        query,
        queryParams,
      );

    const adAccountsData = this.classMapper.mapArray(
      result,
      ReadHealthDashboardAdAccountEntity,
      ReadHealthDashboardAdAccountDto,
    );
    const totalCount =
      await this.getOrganizationAdAccountsListForHealthDashBoardCount(
        organizationId,
        searchParams,
      );
    return new PaginatedResultArray(adAccountsData, totalCount);
  }

  private static getWorkspaceAdAccountsListForHealthDashBoardCountParams(
    organizationId: string,
    workspaceId: number,
    searchParams: AccountSearchParamsDto,
  ): (string | number)[] {
    if (searchParams.search && searchParams.search.trim().length > 0) {
      return [
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        searchParams.search.trim(),
        `%${searchParams.search.trim()}%`,
        `%${searchParams.search.trim()}%`,
        `%${searchParams.search.trim()}%`,
      ];
    } else {
      return [
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
      ];
    }
  }

  async getWorkspaceAdAccountsListForHealthDashBoardCount(
    organizationId: string,
    workspaceId: number,
    searchParams: AccountSearchParamsDto,
  ): Promise<number> {
    const query = GET_WORKSPACE_AD_ACCOUNTS_FOR_DASHBOARD_TOTAL_COUNT(
      organizationId,
      workspaceId,
      searchParams,
    );
    const queryParams =
      OrganizationAdAccountsService.getWorkspaceAdAccountsListForHealthDashBoardCountParams(
        organizationId,
        workspaceId,
        searchParams,
      );
    const total: { count: string }[] =
      await this.organizationAdAccountUserPermissionsRepository.query(
        query,
        queryParams,
      );
    return total.length;
  }

  private static getWorkspaceAdAccountsListForHealthDashBoardParams(
    organizationId: string,
    workspaceId: number,
    userId: number,
    paginationOptions: PaginationOptions,
    searchParams: AccountSearchParamsDto,
  ): (string | number)[] {
    const perPage = paginationOptions.perPage ?? PER_PAGE_DEFAULT;
    const offset = paginationOptions.offset ?? OFFSET_DEFAULT;
    if (searchParams.search && searchParams.search.trim().length > 0) {
      return [
        userId,
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        searchParams.search.trim(),
        `%${searchParams.search.trim()}%`,
        `%${searchParams.search.trim()}%`,
        `%${searchParams.search.trim()}%`,
        perPage,
        offset,
      ];
    } else {
      return [
        userId,
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        organizationId,
        workspaceId,
        perPage,
        offset,
      ];
    }
  }

  async getWorkspaceAdAccountsListForHealthDashBoard(
    organizationId: string,
    workspaceId: number,
    userId: number,
    paginationOptions: PaginationOptions,
    searchParams: AdAccountHealthSearchParamsDto,
  ): Promise<PaginatedResultArray<ReadHealthDashboardAdAccountDto>> {
    const query = GET_WORKSPACE_AD_ACCOUNTS_FOR_DASHBOARD_QUERY(
      organizationId,
      workspaceId,
      searchParams,
    );
    const queryParams =
      OrganizationAdAccountsService.getWorkspaceAdAccountsListForHealthDashBoardParams(
        organizationId,
        workspaceId,
        userId,
        paginationOptions,
        searchParams,
      );
    const result: ReadHealthDashboardAdAccountEntity[] =
      await this.organizationAdAccountUserPermissionsRepository.query(
        query,
        queryParams,
      );
    const adAccountsData = this.classMapper.mapArray(
      result,
      ReadHealthDashboardAdAccountEntity,
      ReadHealthDashboardAdAccountDto,
    );
    const totalCount =
      await this.getWorkspaceAdAccountsListForHealthDashBoardCount(
        organizationId,
        workspaceId,
        searchParams,
      );
    return new PaginatedResultArray(adAccountsData, totalCount);
  }

  /**
   * Get Ad Account associated organizations.
   * @param accountIds account ids
   * @returns ReadPlatformAdAccountPermissionsDtoList user's list of ad account  permissions in all organizations
   */
  async getAdAccountAssociatedOrganizations(
    accountIds: string[],
  ): Promise<ReadAdAccountOrganizationsDto[]> {
    const orgAdAccountMappings =
      await this.organizationPlatformAdAccountMapRepository
        .createQueryBuilder('orgAdAccountMap')
        .innerJoin('orgAdAccountMap.platformAdAccount', 'platformAdAccount')
        .innerJoin('orgAdAccountMap.organization', 'organization')
        .select([
          'platformAdAccount.platformAccountId as platformAccountId',
          'orgAdAccountMap.organizationId as organizationId',
          'organization.isPersonal as isPersonal',
        ])
        .where('platformAdAccount.platformAccountId IN (:...accountIds)', {
          accountIds,
        })
        .getRawMany();
    const uniqueAccountWithOrganizationsMap: Map<
      string,
      ReadPersonalNonPersonalOrganizations
    > = orgAdAccountMappings.reduce((acc, item) => {
      const { platformAccountId, organizationId, isPersonal } = item;

      if (!acc.has(platformAccountId)) {
        acc.set(platformAccountId, {
          personalOrganizationIds: [],
          nonPersonalOrganizationIds: [],
        });
      }
      const orgData = acc.get(platformAccountId);
      if (isPersonal) {
        orgData.personalOrganizationIds.push(organizationId);
      } else {
        orgData.nonPersonalOrganizationIds.push(organizationId);
      }
      return acc;
    }, new Map<string, ReadPersonalNonPersonalOrganizations[]>());
    const result: ReadAdAccountOrganizationsDto[] = [];
    uniqueAccountWithOrganizationsMap.forEach((organizationIds, accountId) => {
      result.push(
        new ReadAdAccountOrganizationsDto(
          accountId,
          organizationIds.nonPersonalOrganizationIds,
          organizationIds.personalOrganizationIds,
        ),
      );
    });
    return result;
  }

  async getOrganizationAdAccountBrands(
    adAccountId: string,
  ): Promise<ReadAdAccountBrandsDto> {
    const brands = await this.brandService.findBrandsByPlatformAccountId(
      adAccountId,
    );

    return {
      brands,
    };
  }

  async getOrganizationAdAccountMarkets(
    adAccountId: string,
  ): Promise<ReadAdAccountMarketsDto> {
    const markets = await this.marketService.findMarketsByPlatformAccountId(
      adAccountId,
    );

    return {
      markets,
    };
  }

  async getAdAccountMarketsByWorkspaceIds(
    workspaceIds: number[],
    paginationOptions: PaginationOptions,
  ) {
    const [markets, totalCount] =
      await this.marketService.findMarketsByWorkspaceIds(
        workspaceIds,
        paginationOptions,
      );

    const marketDtos = this.classMapper.mapArray(
      markets,
      Market,
      ReadBasicMarketDto,
    );
    return new PaginatedResultArray(marketDtos, totalCount);
  }

  async saveBulkMapBetweenAdAccountsAndBrands(
    _organizationId: string,
    _workspaceId: number,
    dto: CreateBulkAdAccountBrandMapDto,
  ): Promise<ReadAdAccountMapDto> {
    const platformAdAccountIds =
      await this.adAccountsService.getAdAccountsIdsFromPlatformAccountIds(
        dto.accounts,
      );

    const queryRunner = await startTransaction(
      this.platformAdAccountBrandMapRepository,
    );

    try {
      await this.bulkInsertMapBetweenAdAccountsAndBrands(
        dto,
        platformAdAccountIds,
        queryRunner,
      );

      await this.bulkDeleteMapBetweenAdAccountsAndBrands(
        dto,
        platformAdAccountIds,
        queryRunner,
      );

      await queryRunner.commitTransaction();

      return this.classMapper.map(
        dto,
        CreateBulkAdAccountBrandMapDto,
        ReadAdAccountMapDto,
      );
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async bulkInsertMapBetweenAdAccountsAndBrands(
    dto: CreateBulkAdAccountBrandMapDto,
    platformAdAccountIds: number[],
    queryRunner: QueryRunner,
  ): Promise<void> {
    if (dto.selected_brands?.length > 0) {
      const platformAdAccountBrandMapToAddEntities =
        this.mapPlatformAdAccountAndBrand(
          platformAdAccountIds,
          dto.selected_brands,
        );

      await this.platformAdAccountBrandMapRepository
        .createQueryBuilder('platformAdAccountBrandMap', queryRunner)
        .insert()
        .into(PlatformAdAccountBrandMap)
        .values(platformAdAccountBrandMapToAddEntities)
        .orIgnore()
        .execute();
    }
  }

  async bulkDeleteMapBetweenAdAccountsAndBrands(
    dto: CreateBulkAdAccountBrandMapDto,
    platformAdAccountIds: number[],
    queryRunner: QueryRunner,
  ): Promise<void> {
    if (dto.unselected_brands?.length > 0) {
      const platformAdAccountBrandMapToRemoveEntities =
        this.mapPlatformAdAccountAndBrand(
          platformAdAccountIds,
          dto.unselected_brands,
        );

      const deleteQuery = this.platformAdAccountBrandMapRepository
        .createQueryBuilder('platformAdAccountBrandMap', queryRunner)
        .delete()
        .from(PlatformAdAccountBrandMap);

      platformAdAccountBrandMapToRemoveEntities.forEach(
        (platformAdAccountBrandMapEntity, index) => {
          if (index === 0) {
            deleteQuery.where({
              platformAdAccountId:
                platformAdAccountBrandMapEntity.platformAdAccountId,
              brandId: platformAdAccountBrandMapEntity.brandId,
            });
          } else {
            deleteQuery.orWhere({
              platformAdAccountId:
                platformAdAccountBrandMapEntity.platformAdAccountId,
              brandId: platformAdAccountBrandMapEntity.brandId,
            });
          }
        },
      );

      await deleteQuery.execute();
    }
  }

  async saveBulkMapBetweenAdAccountsAndMarkets(
    _organizationId: string,
    _workspace: number,
    dto: CreateBulkAdAccountMarketMapDto,
  ): Promise<ReadAdAccountMapDto> {
    const platformAdAccountIds =
      await this.adAccountsService.getAdAccountsIdsFromPlatformAccountIds(
        dto.accounts,
      );

    const queryRunner = await startTransaction(
      this.platformAdAccountMarketMapRepository,
    );

    try {
      await this.bulkInsertMapBetweenAdAccountsAndMarkets(
        dto,
        platformAdAccountIds,
        queryRunner,
      );
      await this.bulkDeleteMapBetweenAdAccountsAndMarkets(
        dto,
        platformAdAccountIds,
        queryRunner,
      );

      await queryRunner.commitTransaction();

      return this.classMapper.map(
        dto,
        CreateBulkAdAccountMarketMapDto,
        ReadAdAccountMapDto,
      );
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }

  async bulkInsertMapBetweenAdAccountsAndMarkets(
    dto: CreateBulkAdAccountMarketMapDto,
    platformAdAccountIds: number[],
    queryRunner: QueryRunner,
  ): Promise<void> {
    if (dto.selected_markets?.length > 0) {
      const platformAdAccountMarketMapToAddEntities =
        this.mapPlatformAdAccountAndMarket(
          platformAdAccountIds,
          dto.selected_markets,
        );

      await this.platformAdAccountMarketMapRepository
        .createQueryBuilder('platformAdAccountMarketMap', queryRunner)
        .insert()
        .into(PlatformAdAccountMarketMap)
        .values(platformAdAccountMarketMapToAddEntities)
        .orIgnore()
        .execute();
    }
  }

  async bulkDeleteMapBetweenAdAccountsAndMarkets(
    dto: CreateBulkAdAccountMarketMapDto,
    platformAdAccountIds: number[],
    queryRunner: QueryRunner,
  ): Promise<void> {
    if (dto.unselected_markets?.length > 0) {
      const platformAdAccountMarketMapToRemoveEntities =
        this.mapPlatformAdAccountAndMarket(
          platformAdAccountIds,
          dto.unselected_markets,
        );

      const deleteQuery = this.platformAdAccountMarketMapRepository
        .createQueryBuilder('platformAdAccountMarketMap', queryRunner)
        .delete()
        .from(PlatformAdAccountMarketMap);

      platformAdAccountMarketMapToRemoveEntities.forEach(
        (platformAdAccountMarketMapEntity, index) => {
          if (index === 0) {
            deleteQuery.where({
              platformAdAccountId:
                platformAdAccountMarketMapEntity.platformAdAccountId,
              countryIsoCode: platformAdAccountMarketMapEntity.countryIsoCode,
            });
          } else {
            deleteQuery.orWhere({
              platformAdAccountId:
                platformAdAccountMarketMapEntity.platformAdAccountId,
              countryIsoCode: platformAdAccountMarketMapEntity.countryIsoCode,
            });
          }
        },
      );

      await deleteQuery.execute();
    }
  }

  mapPlatformAdAccountAndMarket(
    platformAdAccountIds: number[],
    countryIsoCodes: string[],
  ): PlatformAdAccountMarketMap[] {
    return platformAdAccountIds
      .map((platformAdAccountId: number) => {
        return countryIsoCodes.map((countryIsoCode: string) => {
          return this.classMapper.map(
            {
              countryIsoCode,
              platformAdAccountId,
              dateCreated: new Date(Date.now()),
            },
            CreateAdAccountMarketMap,
            PlatformAdAccountMarketMap,
          );
        });
      })
      .flat();
  }

  mapPlatformAdAccountAndBrand(
    platformAdAccountIds: number[],
    brandIds: string[],
  ): PlatformAdAccountBrandMap[] {
    return platformAdAccountIds
      .map((platformAdAccountId: number) => {
        return brandIds.map((brandId: string) => {
          return this.classMapper.map(
            {
              brandId,
              platformAdAccountId,
              dateCreated: new Date(Date.now()),
            },
            CreateAdAccountBrandMap,
            PlatformAdAccountBrandMap,
          );
        });
      })
      .flat();
  }

  /**
   * Filter out the accounts that are not currently assigned to the industry
   * @param accountIndustryMappings - The current mappings between accounts and industries
   * @param accountIds - The account ids to filter
   * @returns The account ids that are not currently assigned to the industry
   */
  filterNewAccountsToBeAddedToPlatformAdAccountIndustry(
    accountIndustryMappings: [string, number][],
    accountIds: string[],
  ): string[] {
    const alreadyAssignedAccountIds = accountIndustryMappings.map(
      (accountIndustry) => accountIndustry[0],
    );
    return accountIds.filter(
      (accountId) => !alreadyAssignedAccountIds.includes(accountId),
    );
  }

  /**
   * Get the mappings between platform ad accounts and industries from database
   * @param platformAccountIds - The platform account ids
   * @param industryId - The industry id
   * @returns The mappings between platform ad accounts and industries
   */
  async getPlatformAdAccountsIndustryMappings(
    platformAccountIds: string[],
    industryId: number,
  ): Promise<PlatformAdAccountIndustryMap[]> {
    return this.platformAdAccountIndustryMapRepository.find({
      where: [
        {
          platformAdAccount: {
            platformAccountId: In(platformAccountIds),
          },
          industryId: industryId,
        },
        {
          platformAdAccount: {
            platformAccountId: In(platformAccountIds),
          },
          industry: {
            parentId: industryId,
          },
        },
        {
          platformAdAccount: {
            platformAccountId: In(platformAccountIds),
          },
          industry: {
            rootId: industryId,
          },
        },
      ],
      relations: ['platformAdAccount'],
    });
  }

  async getAllIndustryMappingForAdAccounts(
    platformAccountIds: string[],
  ): Promise<PlatformAdAccountIndustryMap[]> {
    return this.platformAdAccountIndustryMapRepository.find({
      where: {
        platformAdAccount: {
          platformAccountId: In(platformAccountIds),
        },
      },
      relations: [
        'platformAdAccount',
        'industry',
        'industry.parentIndustry',
        'industry.rootIndustry',
      ],
    });
  }

  async getPlatformAdAccountsIndustryGroups(
    platformAccountIds: string[],
  ): Promise<[string, number][]> {
    const accountIndustryMap = await this.getAllIndustryMappingForAdAccounts(
      platformAccountIds,
    );
    return accountIndustryMap.map((accountIndustry) => {
      const platformAccountId =
        accountIndustry.platformAdAccount.platformAccountId;
      const industryId =
        accountIndustry.industry.parentId === null &&
        accountIndustry.industry.rootId === null
          ? accountIndustry.industry.id
          : accountIndustry.industry.rootId;
      return [platformAccountId, industryId];
    });
  }

  /**
   * Get platform ad accounts industries by industry group
   * @param platformAccountIds - The platform account ids
   * @param industryGroupId - The industry group id
   * @returns The platform ad accounts industries
   */
  async getPlatformAdAccountsIndustriesByIndustryGroup(
    platformAccountIds: string[],
    industryGroupId: number,
  ): Promise<[string, number][]> {
    const accountIndustryMap = await this.getAllIndustryMappingForAdAccounts(
      platformAccountIds,
    );
    return accountIndustryMap
      .filter(
        (accountIndustry) =>
          accountIndustry.industry.parentId === industryGroupId ||
          accountIndustry.industry.rootId === industryGroupId,
      )
      .map((accountIndustry) => {
        if (accountIndustry.industry.parentId === industryGroupId) {
          return [
            accountIndustry.platformAdAccount.platformAccountId,
            accountIndustry.industry.id,
          ];
        } else if (accountIndustry.industry.rootId === industryGroupId) {
          return [
            accountIndustry.platformAdAccount.platformAccountId,
            accountIndustry.industry.parentId,
          ];
        }
      });
  }

  /**
   * Get platform ad accounts sub industries by industry
   * @param platformAccountIds - The platform account ids
   * @param industryId - The industry id
   * @returns The platform ad accounts sub industries
   */
  async getPlatformAdAccountsSubIndustriesByIndustry(
    platformAccountIds: string[],
    industryId: number,
  ): Promise<[string, number][]> {
    const accountIndustryMap = await this.getAllIndustryMappingForAdAccounts(
      platformAccountIds,
    );
    return accountIndustryMap
      .filter(
        (accountIndustry) => accountIndustry.industry.parentId === industryId,
      )
      .map((accountIndustry) => [
        accountIndustry.platformAdAccount.platformAccountId,
        accountIndustry.industry.id,
      ]);
  }

  async assignIndustryGroupToPlatformAdAccounts(
    userId: number,
    adAccountIds: string[],
    entityId: number,
  ): Promise<void> {
    if (adAccountIds.length === 0) {
      return;
    }
    const queryRunner = await startTransaction(
      this.platformAdAccountIndustryMapRepository,
    );
    const platformAdAccountIds =
      await this.adAccountsService.getAdAccountsIdsFromPlatformAccountIds(
        adAccountIds,
      );
    const adAccountIndustryMap = platformAdAccountIds.map(
      (platformAdAccountId) =>
        this.classMapper.map(
          {
            platformAdAccountId: platformAdAccountId,
            industryId: entityId,
            assigningPersonId: userId,
            dateCreated: new Date(Date.now()),
            lastUpdated: new Date(Date.now()),
          },
          CreatePlatformAdAccountIndustryDto,
          PlatformAdAccountIndustryMap,
        ),
    );
    try {
      await queryRunner.manager.save(adAccountIndustryMap);
      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  async updatePlatformAdAccountIndustryData(
    userId: number,
    adAccountIds: string[],
    newEntityId: number,
  ): Promise<void> {
    if (adAccountIds.length === 0) {
      return;
    }
    const adAccountIndustries =
      await this.platformAdAccountIndustryMapRepository.find({
        where: {
          platformAdAccount: {
            platformAccountId: In(adAccountIds),
          },
        },
      });
    const platformAdAccountIds = adAccountIndustries.map(
      (adAccountIndustry) => adAccountIndustry.platformAdAccountId,
    );
    const queryRunner = await startTransaction(
      this.platformAdAccountIndustryMapRepository,
    );
    try {
      await queryRunner.manager.update(
        PlatformAdAccountIndustryMap,
        {
          platformAdAccountId: In(platformAdAccountIds),
        },
        {
          industryId: newEntityId,
          assigningPersonId: userId,
          lastUpdated: new Date(Date.now()),
        },
      );
      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  async removeIndustryEntityFromPlatformAdAccounts(
    userId: number,
    adAccountIds: string[],
    entityIds: number[],
  ): Promise<void> {
    if (adAccountIds.length === 0) {
      return;
    }
    const adAccountIndustryMapsToRemove =
      await this.platformAdAccountIndustryMapRepository.find({
        select: ['id'],
        where: [
          {
            platformAdAccount: {
              platformAccountId: In(adAccountIds),
            },
            industry: {
              id: In(entityIds),
            },
          },
          {
            platformAdAccount: {
              platformAccountId: In(adAccountIds),
            },
            industry: {
              parentId: In(entityIds),
            },
          },
          {
            platformAdAccount: {
              platformAccountId: In(adAccountIds),
            },
            industry: {
              rootId: In(entityIds),
            },
          },
        ],
      });
    const queryRunner = await startTransaction(
      this.platformAdAccountIndustryMapRepository,
    );
    try {
      await queryRunner.manager.delete(PlatformAdAccountIndustryMap, {
        id: In(adAccountIndustryMapsToRemove.map((map) => map.id)),
      });
      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Disconnects a Platform Ad Account from an Organizations
   * @param accountId - The Platform Account ID
   * @param organizationIds - The Organization IDs
   */
  async removeAdAccountFromOrganizations(
    accountId: string,
    organizationIds: string[],
  ) {
    const accountOrgMaps =
      await this.organizationPlatformAdAccountMapRepository.find({
        where: {
          platformAdAccount: {
            platformAccountId: accountId,
          },
        },
        select: ['platformAdAccountId'],
      });
    if (accountOrgMaps.length === 0) {
      return;
    }
    const adAccountId = accountOrgMaps.map(
      (accountOrg) => accountOrg.platformAdAccountId,
    )[0];
    const queryRunner = await startTransaction(
      this.organizationPlatformAdAccountMapRepository,
    );
    try {
      await queryRunner.manager.delete(OrganizationPlatformAdAccountMap, {
        platformAdAccountId: adAccountId,
        organizationId: In(organizationIds),
      });
      await queryRunner.manager.delete(OrganizationAdAccountUserPermissions, {
        platformAdAccountId: adAccountId,
        organizationId: In(organizationIds),
      });
      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get Organization Users Map per platform
   * @param platform - The Platform Name
   * @param importV3EnabledOnly - Import V3 Enabled Only
   * @param importEnabledOrganizationsOnly - Import Enabled Organizations Only
   * @returns The Organization Users Map
   */
  async getOrganizationUsersPerPlatform(
    platform: string,
    importV3EnabledOnly: boolean,
    importEnabledOrganizationsOnly: boolean,
  ): Promise<ReadPlatformOrganizationUsersMapDto[]> {
    const builderQueryBase = this.organizationAdAccountUserPermissionsRepository
      .createQueryBuilder('orgAccountUserPermissions')
      .innerJoin(
        OrganizationPlatformAdAccountMap,
        'orgAccountMap',
        'orgAccountMap.organizationId = orgAccountUserPermissions.organizationId ' +
          'AND orgAccountMap.platformAdAccountId = orgAccountUserPermissions.platformAdAccountId',
      )
      .innerJoin(
        PlatformAdAccount,
        'platformAdAccount',
        'platformAdAccount.id = orgAccountUserPermissions.platformAdAccountId ',
      );
    const builderWithOptionalFeatureWhitelist = importEnabledOrganizationsOnly
      ? this.getQueryBuilderWithImportsEnabledFeature(builderQueryBase)
      : builderQueryBase;

    const orgAccountUserPermissions = await builderWithOptionalFeatureWhitelist
      .select([
        'orgAccountUserPermissions.organizationId',
        'orgAccountUserPermissions.userId',
      ])
      .where(
        `orgAccountUserPermissions.permission = :permission AND platformAdAccount.canAccess IS TRUE AND UPPER(platformAdAccount.platform) = :platform` +
          (importV3EnabledOnly
            ? ` AND platformAdAccount.importV3Enabled IS TRUE`
            : ''),
        {
          permission: Permission.ALLOW,
          platform: platform.toUpperCase(),
        },
      )
      .getMany();
    const orgUsersMap = orgAccountUserPermissions.reduce(
      (orgUsersMap, orgAccountUserPermission) => {
        const { organizationId, userId } = orgAccountUserPermission;
        if (!orgUsersMap[organizationId]) {
          orgUsersMap[organizationId] = new Set<number>();
        }
        orgUsersMap[organizationId].add(userId);
        return orgUsersMap;
      },
      {} as Record<string, Set<number>>,
    );
    return Object.entries(orgUsersMap).map(
      ([orgId, userIds]) =>
        new ReadPlatformOrganizationUsersMapDto(orgId, Array.from(userIds)),
    );
  }

  getQueryBuilderWithImportsEnabledFeature(
    builderQueryBase: SelectQueryBuilder<OrganizationAdAccountUserPermissions>,
  ) {
    return builderQueryBase
      .innerJoin(
        OrganizationFeatureWhitelist,
        'organizationFeatureWhitelist',
        'organizationFeatureWhitelist.organizationId = orgAccountUserPermissions.organizationId',
      )
      .innerJoin(
        FeatureWhitelist,
        'featureWhitelist',
        'featureWhitelist.id = organizationFeatureWhitelist.featureWhitelistId ' +
          'AND featureWhitelist.identifier = :identifier',
        { identifier: FEATURE_INTEGRATION_ACCOUNT_IMPORTS_ENABLED },
      );
  }

  /**
   * Update User Account Access in Organization Ad Account Permissions
   * @param organizationId Organization Id
   * @param userId User Id
   * @param platformAdAccountIds Platform Ad Account Ids
   * @param accountAccessibleByUser True if the user still has access to the account
   */
  async updateUserAccountAccessInOrgAdAccountPermissions(
    organizationId: string,
    userId: number,
    platformAdAccountIds: number[],
    accountAccessibleByUser: boolean,
  ): Promise<number> {
    if (platformAdAccountIds.length === 0) {
      return 0;
    }
    this.logger.log(
      `User's access is updated as ${accountAccessibleByUser} for ${platformAdAccountIds.length} accounts ` +
        `in organization '${organizationId}', User Id - ${userId}.`,
    );
    await this.organizationAdAccountUserPermissionsRepository.update(
      {
        organizationId,
        userId,
        platformAdAccountId: In(platformAdAccountIds),
      },
      {
        accountAccessibleByUser,
        lastUpdated: new Date(),
      },
    );
    return platformAdAccountIds.length;
  }

  /**
   * Update Access Flag to true for Recovered Org User Accounts
   * @param organizationId Organization Id
   * @param userId User Id
   * @param platformAccountIds Platform Account Ids
   * @param databaseUserAccountPermissions Database User Account Permissions
   */
  async updateRecoverAccessFlagToOrgUserAccounts(
    organizationId: string,
    userId: number,
    platformAccountIds: string[],
    databaseUserAccountPermissions: OrganizationAdAccountUserPermissions[],
  ): Promise<number> {
    const dbInactiveAccounts = databaseUserAccountPermissions
      .filter((uap) => !uap.accountAccessibleByUser)
      .map((uap) => uap.platformAdAccount);
    const dbAdAccountIdsUserRecoveredAccessTo = dbInactiveAccounts
      .filter((dbAccount) =>
        platformAccountIds.includes(dbAccount.platformAccountId),
      )
      .map((account) => account.id);
    if (dbAdAccountIdsUserRecoveredAccessTo.length === 0) {
      return 0;
    }
    return this.updateUserAccountAccessInOrgAdAccountPermissions(
      organizationId,
      userId,
      dbAdAccountIdsUserRecoveredAccessTo,
      true,
    );
  }

  /**
   * Update Account Status to Not Importing when no user has access
   * @param organizationId organization Id
   * @param platformAdAccount Platform Ad Account
   */
  async updateAccountStatusWhenNoUserHasAccess(
    organizationId: string,
    platformAdAccount: PlatformAdAccount,
  ): Promise<void> {
    const orgAccountUsersWithAllowAccessCount =
      await this.findOrgAccountUsersWithAllowAccessCount(
        organizationId,
        platformAdAccount.platformAccountId,
      );
    if (orgAccountUsersWithAllowAccessCount > 0) {
      return;
    }
    return this.adAccountsService.updateAccountWithNotImportingStatus(
      organizationId,
      platformAdAccount,
      {
        importSkipReason: `User lost access to the account ${platformAdAccount.platformAccountId} in the platform`,
      },
    );
  }

  /**
   * Update Access Flag to false for Accounts user lost access to
   * @param organizationId Organization Id
   * @param userId User Id
   * @param platformAccountIds Platform Account Ids
   * @param databaseUserAccountPermissions Database User Account Permissions
   */
  async updateLostAccessFlagToOrgUserAccounts(
    organizationId: string,
    userId: number,
    platformAccountIds: string[],
    databaseUserAccountPermissions: OrganizationAdAccountUserPermissions[],
  ): Promise<number> {
    const dbActiveAccounts = databaseUserAccountPermissions
      .filter((uap) => uap.accountAccessibleByUser)
      .map((uap) => uap.platformAdAccount);
    const accountsUserLostAccessTo = dbActiveAccounts.filter(
      (dbAccount) => !platformAccountIds.includes(dbAccount.platformAccountId),
    );
    if (accountsUserLostAccessTo.length === 0) {
      return 0;
    }
    const dbAdAccountIdsUserLostAccessTo = accountsUserLostAccessTo.map(
      (account) => account.id,
    );
    const updatedAccounts =
      await this.updateUserAccountAccessInOrgAdAccountPermissions(
        organizationId,
        userId,
        dbAdAccountIdsUserLostAccessTo,
        false,
      );
    await Promise.all(
      accountsUserLostAccessTo.map(async (account) => {
        await this.updateAccountStatusWhenNoUserHasAccess(
          organizationId,
          account,
        );
      }),
    );
    return updatedAccounts;
  }

  /**
   * Update User Account Access in Org Ad Account Permissions Table when user lost access to the account in the platform
   * @param organizationId organizationId
   * @param platform platform
   * @param userId userId
   * @param adAccountIds adAccountIds
   */
  async updateOrganizationUserAccountsAccess(
    organizationId: string,
    platform: string,
    userId: number,
    adAccountIds: string[],
  ): Promise<void> {
    const organizationAdAccountUserPermissions =
      await this.getOrganizationAdAccountUserPermissions(
        organizationId,
        platform,
        userId,
      );
    const accountsUserLostAccessTo =
      await this.updateLostAccessFlagToOrgUserAccounts(
        organizationId,
        userId,
        adAccountIds,
        organizationAdAccountUserPermissions,
      );
    const accountsUserRecoveredAccessTo =
      await this.updateRecoverAccessFlagToOrgUserAccounts(
        organizationId,
        userId,
        adAccountIds,
        organizationAdAccountUserPermissions,
      );
    this.logger.log(
      `User's access is updated as false for ${accountsUserLostAccessTo} accounts and ` +
        `true for ${accountsUserRecoveredAccessTo} accounts in organization '${organizationId}', ` +
        `User Id - ${userId}, Platform - ${platform}.`,
    );
  }

  /**
   * Send SQS message to process accounts pre import tasks
   * @param request Accounts Pre Import Tasks SQS request
   */
  async sendSyncPlatformAdAccountsMessage(
    request: AccountsPreImportTasksSqsMessageBody,
  ) {
    await this.sqsService.send(ACCOUNTS_PRE_IMPORT_TASKS_QUEUE_NAME, {
      id: `${request.organizationId}-${request.platform}-${request.userId}`,
      body: JSON.stringify(request),
    });
    return {
      message:
        `SQS message sent to sync ad accounts to the database. Organization '${request.organizationId}', ` +
        `Platform - ${request.platform}, User Id - ${request.userId}.`,
    };
  }

  /**
   * Check if the request is valid
   * @param request Accounts Pre Import Tasks SQS request
   */
  isSyncAdAccountsMessagesRequestValid(
    request: AccountsPreImportTasksSqsMessageBody,
  ) {
    return (
      request?.organizationId &&
      request?.platform &&
      request?.userId &&
      request?.platformAccountIds
    );
  }

  @SqsMessageHandler(ACCOUNTS_PRE_IMPORT_TASKS_QUEUE_NAME, false)
  async handleAccountsPreImportTasksProcessingMessage(message: Message) {
    this.logger.debug(
      `Received pre import tasks processing message with body: ${message.Body}`,
    );
    const request = JSON.parse(
      message.Body,
    ) as AccountsPreImportTasksSqsMessageBody;
    if (!this.isSyncAdAccountsMessagesRequestValid(request)) {
      this.logger.error(`Received invalid message: ${message.Body}`);
      return;
    }
    await this.updateOrganizationUserAccountsAccess(
      request.organizationId,
      request.platform,
      request.userId,
      request.platformAccountIds,
    );
  }

  @SqsConsumerEventHandler(
    ACCOUNTS_PRE_IMPORT_TASKS_QUEUE_NAME,
    'processing_error',
  )
  public onProcessingError(error: Error, message: Message) {
    this.logger.error(
      `Processing error on accounts pre import tasks message: ${JSON.stringify(
        message.Body,
      )}, error: ${error.message}`,
    );
  }
}
