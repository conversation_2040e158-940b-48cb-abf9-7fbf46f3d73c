import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsUUID } from 'class-validator';
import { PrimaryColumn } from 'typeorm';

export class ReadAdAccountOrganizationsDto {
  constructor(
    platformAccountId: string,
    organizationIds: string[],
    personalOrganizationIds: string[],
  ) {
    this.platformAccountId = platformAccountId;
    this.organizationIds = organizationIds;
    this.personalOrganizationIds = personalOrganizationIds;
  }

  /**
   * Platform Ad Account Id
   */
  @AutoMap()
  @PrimaryColumn({
    name: 'platform_ad_account_id',
  })
  platformAccountId: string;

  /**
   * Organization Ids
   * @example ['75973c70-a05c-4c18-96b2-327d93082419']
   */
  @AutoMap()
  @IsNotEmpty()
  organizationIds: string[];

  /**
   * Personal Organization Ids
   */
  personalOrganizationIds: string[];
}
