import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsN<PERSON>ber, IsString } from 'class-validator';
import { ImportStatus } from 'src/common/constants/constants';

export class ReadHealthDashboardAdAccountEntity {
  /**
   * Platform Account Name
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  platform_account_name: string;

  /**
   * Platform Account ID
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  platform_account_id: string;

  /**
   * Platform
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  platform: string;

  /**
   * Currency Code
   */
  @AutoMap()
  @IsString()
  currency_code: string;

  /**
   * Connection Status
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  connection_status: string;

  /**
   * Last Connected On
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  last_connected_on: string;

  /**
   * Last Imported On
   */
  @AutoMap()
  @IsString()
  last_import_success_date: string;

  /**
   * Connected by users list
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  connected_by: string;

  /**
   * Workspaces account associated with
   */
  @AutoMap()
  @IsString()
  workspaces: string;

  /**
   * Is connected by user
   */
  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  is_connected_by_user: number;

  /**
   * Brands associated with
   */
  @AutoMap()
  @IsString()
  brands: string;

  /**
   * Markets associated with
   */
  @AutoMap()
  @IsString()
  markets: string;

  /**
   * Industry Group ID
   */
  @AutoMap()
  @IsNumber()
  industry_group_id: number;

  /**
   * Industry Group
   */
  @AutoMap()
  @IsString()
  industry_group_name: string;

  /**
   * Industry ID
   */
  @AutoMap()
  @IsNumber()
  industry_id: number;

  /**
   * Industry Name
   */
  @AutoMap()
  @IsString()
  industry_name: string;

  /**
   * Sub-industry ID
   */
  @AutoMap()
  @IsNumber()
  sub_industry_id: number;

  /**
   * Sub-industry Name
   */
  @AutoMap()
  @IsString()
  sub_industry_name: string;

  /**
   * The status of the last import
   * @example IMPORT_ACTIVE
   */
  @AutoMap()
  @IsString()
  import_status: ImportStatus;
}
