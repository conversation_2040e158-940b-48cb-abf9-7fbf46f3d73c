import { AutoMap } from '@automapper/classes';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateAdAccountImportDataDto {
  @AutoMap()
  @IsString()
  @IsOptional()
  importId?: string;

  @AutoMap()
  @IsNumber()
  @IsOptional()
  rebuildRequestId?: number;

  @AutoMap()
  @IsString()
  @IsOptional()
  importSkipReason?: string;

  @AutoMap()
  @IsNumber()
  @IsOptional()
  userId?: number;

  @AutoMap()
  @IsString()
  @IsOptional()
  importStatus?: string;

  @AutoMap()
  @IsString()
  @IsOptional()
  @Type(() => Date)
  importStartDate?: Date;

  @AutoMap()
  @IsString()
  @IsOptional()
  @Type(() => Date)
  importCompleteDate?: Date;

  @AutoMap()
  @IsString()
  @IsOptional()
  @Type(() => Date)
  importSuccessDate?: Date;

  @AutoMap()
  @IsOptional()
  failureReason?: any;
}
