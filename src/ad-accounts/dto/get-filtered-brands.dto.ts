import { IsArray } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class GetFilteredBrandsDto {
  @ApiProperty({
    type: [Number],
    description: 'Array of Workspace IDs',
    example: [23142, 12451],
  })
  @IsArray()
  workspaceIds: number[];

  @ApiProperty({
    type: [String],
    description: 'Array of Ad Account IDs',
    example: ['ad-account-id-1', 'ad-account-id-2'],
  })
  @IsArray()
  adAccountIds: string[];
}
