import { AutoMap } from '@automapper/classes';
import { <PERSON><PERSON><PERSON><PERSON>, IsN<PERSON>ber, IsString } from 'class-validator';

export class ReadPlatformOrganizationUsersMapDto {
  constructor(organizationId: string, userIds: number[]) {
    this.organizationId = organizationId;
    this.userIds = userIds;
  }
  /**
   * Organization Id
   */
  @AutoMap()
  @IsString()
  organizationId: string;

  /**
   * User Ids
   */
  @AutoMap()
  @IsArray()
  @IsNumber({}, { each: true })
  userIds: number[];
}
