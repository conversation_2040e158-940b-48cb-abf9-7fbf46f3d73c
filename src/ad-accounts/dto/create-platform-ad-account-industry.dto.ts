import { AutoMap } from '@automapper/classes';
import { IsDate, IsNumber } from 'class-validator';

export class CreatePlatformAdAccountIndustryDto {
  /**
   * List of platform ad account ids
   */
  @AutoMap()
  @IsNumber()
  platformAdAccountId: number;

  /**
   * Industry Id
   */
  @AutoMap()
  @IsNumber()
  industryId: number;

  /**
   * User Id
   */
  @AutoMap()
  @IsNumber()
  assigningPersonId: number;

  /**
   * Date created
   */
  @AutoMap()
  @IsDate()
  dateCreated: Date;

  /**
   * Last updated
   */
  @AutoMap()
  @IsDate()
  lastUpdated: Date;
}
