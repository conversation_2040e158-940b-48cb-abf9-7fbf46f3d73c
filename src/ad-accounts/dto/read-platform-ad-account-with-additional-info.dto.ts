import { ReadPlatformAdAccountDto } from './read-platform-ad-account.dto';
import { AutoMap } from '@automapper/classes';
import { IsBoolean, IsDate } from 'class-validator';

export class ReadPlatformAdAccountWithAdditionalInfoDto extends ReadPlatformAdAccountDto {
  @AutoMap()
  @IsBoolean()
  isLinkedToSelectedWorkspace?: boolean;

  @AutoMap()
  @IsBoolean()
  connected: boolean;

  @AutoMap()
  connectedBy?: string[];
}
