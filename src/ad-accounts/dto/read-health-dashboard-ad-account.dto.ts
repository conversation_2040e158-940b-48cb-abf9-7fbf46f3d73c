import { AutoMap } from '@automapper/classes';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';
import { ReadAccountIndustryDto } from './read-account-industry.dto';
import { ImportStatusForBFF } from 'src/common/constants/constants';

export class ReadHealthDashboardAdAccountDto {
  /**
   * Platform Account Name
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  platformAccountName: string;

  /**
   * Platform Account ID
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  platformAccountId: string;

  /**
   * Currency Code
   */
  @AutoMap()
  @IsString()
  currencyCode: string;

  /**
   * Platform
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  channel: string;

  /**
   * Connection Status
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  connectionStatus: string;

  /**
   * Last Connected On
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  lastConnectedOn: string;

  /**
   * Last Imported On
   */
  @AutoMap()
  @IsString()
  lastImportedOn: string;

  /**
   * Connected by users list
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  connectedBy: string[];

  /**
   * Workspaces account associated with
   */
  @AutoMap()
  @IsString()
  workspaces: string[];

  /**
   * Is connected by user
   */
  @AutoMap()
  @IsNotEmpty()
  @IsBoolean()
  isConnectedByUser: boolean;

  /**
   * Brands associated with
   */
  @AutoMap()
  @IsString()
  brands: string[];

  /**
   * markets associated with
   */
  @AutoMap()
  @IsString()
  markets: string[];

  /**
   * Industry Group
   */
  @AutoMap()
  industryGroup: ReadAccountIndustryDto;

  /**
   * Industry
   */
  @AutoMap()
  industry: ReadAccountIndustryDto;

  /**
   * Sub-industry
   */
  @AutoMap()
  subIndustry: ReadAccountIndustryDto;

  /**
   * The status of the last import
   * @example IMPORT_ACTIVE
   */
  @AutoMap()
  @IsBoolean()
  importStatus?: ImportStatusForBFF;
}
