import { AutoMap } from '@automapper/classes';
import { Is<PERSON><PERSON><PERSON>, IsN<PERSON>ber, IsOptional } from 'class-validator';

export class CreateIndustryAdAccountsRequestDto {
  /**
   * List of ad account ids
   * @example ['*****************', 't2_t5vae3ih']
   */
  @AutoMap()
  @IsArray()
  accountIds: string[];

  /**
   * Selected IndustryGroup Id
   */
  @AutoMap()
  @IsOptional()
  @IsNumber()
  selectedIndustryGroupId?: number;

  /**
   * Unselected IndustryGroup Ids
   */
  @AutoMap()
  @IsOptional()
  @IsArray()
  unselectedIndustryGroupIds?: number[];

  /**
   * Selected Industry Id
   */
  @AutoMap()
  @IsOptional()
  @IsNumber()
  selectedIndustryId?: number;

  /**
   * Unselected Industry Ids
   */
  @AutoMap()
  @IsOptional()
  @IsArray()
  unselectedIndustryIds?: number[];

  /**
   * Selected Sub-industry Id
   */
  @AutoMap()
  @IsOptional()
  @IsNumber()
  selectedSubIndustryId?: number;

  /**
   * Unselected Sub-industry Ids
   */
  @AutoMap()
  @IsOptional()
  @IsArray()
  unselectedSubIndustryIds?: number[];
}
