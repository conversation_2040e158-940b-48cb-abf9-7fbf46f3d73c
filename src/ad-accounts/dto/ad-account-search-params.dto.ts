import { IsEnum, IsOptional, IsString } from 'class-validator';
import {
  PlatformAdAccountSortBy,
  SortOrder,
} from '../../common/constants/constants';

export class AccountSearchParamsDto {
  /**
   * Search string to filter workspaces by name
   * @example "My Workspace"
   */
  @IsOptional()
  @IsString()
  search?: string;

  /**
   * Sort by string
   * @example "importStatus"
   */
  @IsOptional()
  @IsEnum(PlatformAdAccountSortBy)
  sortBy?: string;

  /**
   * Sort Order string
   * @example "ASC"
   */
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: string;

  /**
   * Brands string
   * @example "none"
   */
  @IsOptional()
  @IsString()
  brands?: string;

  /**
   * Markets string
   * @example "none"
   */
  @IsOptional()
  @IsString()
  markets?: string;
}
