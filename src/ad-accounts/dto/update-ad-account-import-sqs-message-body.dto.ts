import { AutoMap } from '@automapper/classes';
import { IsString } from 'class-validator';
import { UpdateAdAccountImportDataDto } from './update-ad-account-import-data.dto';
import { Type } from 'class-transformer';

export class UpdateAdAccountImportSqsMessageBodyDto {
  @AutoMap()
  @IsString()
  organizationId: string;

  @AutoMap()
  @IsString()
  platformAccountId: string;

  @AutoMap()
  @Type(() => UpdateAdAccountImportDataDto)
  accountImportData: UpdateAdAccountImportDataDto;
}
