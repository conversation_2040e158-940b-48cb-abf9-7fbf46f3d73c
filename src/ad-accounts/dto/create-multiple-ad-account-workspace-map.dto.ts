import { AutoMap } from '@automapper/classes';
import { <PERSON><PERSON><PERSON><PERSON>, IsInt } from 'class-validator';

export class CreateMultipleAdAccountWorkspaceMapDto {
 
  /**
   * List of workspaces ids to link to the ad account
   * @example [1, 2, 3]
   */
  @AutoMap()
  @IsArray()
  workspacesIds: number[];

  /**
   * List of platform account id to link to the workspaces
   * @example [1, 2, 3]
   */
  @AutoMap()
  @IsArray()
  platformAccountIds: string[];
}