import { AutoMap } from '@automapper/classes';
import { IsArray, IsOptional } from 'class-validator';

export class CreateBulkAdAccountMarketMapDto {
  /**
   * List of country iso code to map
   * @example ['usa', 'alb']
   */
  @AutoMap()
  @IsArray()
  @IsOptional()
  selected_markets: string[];

  /**
   * List of country iso code to unmap
   * @example ['usa', 'alb']
   */
  @AutoMap()
  @IsArray()
  @IsOptional()
  unselected_markets: string[];

  /**
   * List of ad account ids
   * @example ['*****************', 't2_t5vae3ih']
   */
  @AutoMap()
  @IsArray()
  accounts: string[];
}
