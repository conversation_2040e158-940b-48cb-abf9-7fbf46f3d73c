import { IsArray, <PERSON><PERSON><PERSON>, IsOptional, IsString } from 'class-validator';
import {
  AdAccountConnectionStatus,
  ImportStatus,
  ImportStatusForBFF,
  PlatformAdAccountHealthSortBy,
} from '../../common/constants/constants';
import { AccountSearchParamsDto } from './ad-account-search-params.dto';
import { Transform } from 'class-transformer';
import { getImportStatusFromBff } from 'src/common/utils/helper';
import { ApiProperty } from '@nestjs/swagger';

export class AdAccountHealthSearchParamsDto extends AccountSearchParamsDto {
  /**
   * Sort by string
   * @example "account_id"
   */
  @IsOptional()
  @IsEnum(PlatformAdAccountHealthSortBy)
  sortBy?: string;

  /**
   * Workspaces string
   * @example "none"
   */
  @IsOptional()
  @IsString()
  workspaces?: string;

  /**
   * Filter by array of import status
   * @example "Not Importing,Suspended"
   */
  @IsOptional()
  @IsArray()
  @ApiProperty({
    type: String,
    description: 'Array of Import status comma separated',
    example: 'Suspended,Enabled',
  })
  @IsEnum(ImportStatus, {
    each: true,
    message: ({ property }) => {
      const importStatusValues = Object.values(ImportStatusForBFF).join(', ');

      return `each value in ${property} must be one of the following values: ${importStatusValues}.`;
    },
  })
  @Transform(
    ({ value }) =>
      value
        .split(',')
        .map((importStatus: string) => getImportStatusFromBff(importStatus)),
    { toClassOnly: true },
  )
  importStatus?: ImportStatus[];

  /**
   * Filter by array of connection status
   * @example "Missing permissions,Suspended"
   */
  @IsOptional()
  @IsArray()
  @ApiProperty({
    type: String,
    description: 'Array of Connection status comma separated',
    example: 'Missing permissions,Disconnected',
  })
  @IsEnum(AdAccountConnectionStatus, { each: true })
  @Transform(({ value }) => value.split(','), { toClassOnly: true })
  connectionStatus?: AdAccountConnectionStatus[];
}
