import { AutoMap } from '@automapper/classes';
import { Is<PERSON><PERSON><PERSON>, IsEnum, IsString, Length } from 'class-validator';
import { Platform } from '../../common/constants/constants';

export class PlatformAccount {
  @AutoMap()
  @IsString()
  @Length(1, 100)
  platformAccountId: string;

  @AutoMap()
  @IsEnum(Platform)
  platform: Platform;
}

export enum WorkspaceAdAccountConnectionStatus {
  /**
   * This status connects ad accounts to a workspace
   */
  CONNECTED = 'CONNECTED',

  /**
   * This status disconnects ad accounts to a workspace
   */
  DISCONNECTED = 'DISCONNECTED',
}

export class CreateAdAccountWorkspaceMapDto {
  /**
   * List of platform account id to link to the workspaces
   * @example [
   *  {
   *     platformAccountId: '*********',
   *     platform: 'FACEBOOK'
   *  }
   * ]
   */
  @AutoMap()
  @IsArray()
  platformAccountList: PlatformAccount[];

  /**
   * Status of the link
   * If the status is 'CONNECTED', the mapping will be created and inserted into VM database.
   * If the status is 'DISCONNECTED', the mapping will be deleted from VM database.
   * @example 'CONNECTED'
   */
  @AutoMap()
  @IsEnum(WorkspaceAdAccountConnectionStatus)
  status: WorkspaceAdAccountConnectionStatus;
}
