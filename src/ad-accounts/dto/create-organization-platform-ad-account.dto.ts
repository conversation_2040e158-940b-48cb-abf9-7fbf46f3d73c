import { <PERSON><PERSON>num, IsNotEmpty, IsN<PERSON>ber, IsString } from 'class-validator';
import { AutoMap } from '@automapper/classes';
import { Permission } from '../../common/constants/constants';

export class CreateOrganizationPlatformAdAccountDto {
  /**
   * User Id
   * @example 1234
   */
  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  userId: number;

  /**
   * Platform Account Id
   * @example 't2_t5vae3ih'
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  platformAccountId: string;

  /**
   * Permission of the Ad Account
   * @example 'ALLOW'
   */
  @AutoMap()
  @IsNotEmpty()
  @IsEnum(Permission)
  permission: string;
}
