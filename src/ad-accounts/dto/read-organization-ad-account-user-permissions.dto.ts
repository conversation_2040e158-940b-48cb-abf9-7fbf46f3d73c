import { AutoMap } from '@automapper/classes';
import {
  <PERSON>Enum,
  IsNotEmpty,
  IsNumber,
  IsString,
  IsUUID,
} from 'class-validator';
import { Status } from '../../common/constants/constants';

export class ReadOrganizationAdAccountUserPermissionsDto {
  /**
   * Organization Id
   * @example '75973c70-a05c-4c18-96b2-327d93082419'
   */
  @AutoMap()
  @IsNotEmpty()
  @IsUUID()
  organizationId: string;

  /**
   * Platform Account Id
   * @example 't2_t5vae3ih'
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  platformAccountId: string;

  /**
   * User Id
   * @example 123
   */
  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  userId: number;

  /**
   * Platform Ad Account Organization mapping status
   * @example ALLOW
   */
  @AutoMap()
  @IsNotEmpty()
  @IsEnum(Status)
  permission: string;
}
