import { AutoMap } from '@automapper/classes';
import { Exclude } from 'class-transformer';
import { IsDate, IsInt, IsNumber, IsString } from 'class-validator';
import { ImportStatusForBFF } from 'src/common/constants/constants';

export class ReadPlatformAdAccountDto {
  /**
   * Platform Ad Account Id
   * @example 1234
   */
  @AutoMap()
  @Exclude()
  @IsInt()
  id: number;

  /**
   * Platform for the Ad Account
   * @example 'tiktok'
   */
  @AutoMap()
  @IsString()
  platform: string;

  /**
   * Platform Ad Account Name
   * @example 'VidMob'
   */
  @AutoMap()
  @IsString()
  platformAccountName: string;

  /**
   * Platform Ad Account Id
   * @example 't2_t5vae3ih'
   */
  @AutoMap()
  @IsString()
  platformAccountId: string;

  /**
   * Date when the Platform Ad Account was created
   * @example '2021-01-01T00:00:00.000Z'
   */
  @AutoMap()
  @IsDate()
  dateCreated: Date;

  /**
   * Tinyint (1 for true, 0 for false) when the Platform Ad Account was processed
   */
  @AutoMap()
  @IsNumber()
  processingCompleted: number;

  /**
   * Date when the Platform Ad Account was processed
   * @example '2021-01-01T00:00:00.000Z'
   */
  @AutoMap()
  @IsDate()
  processingCompletedDate: Date | null;

  @AutoMap()
  @Exclude()
  @IsDate()
  lastUpdated: Date;

  @AutoMap()
  @Exclude()
  @IsInt()
  canAccess: number;

  /**
   * Date when import last completed for the account
   */
  @AutoMap()
  @IsDate()
  @Exclude()
  lastImportCompletedDate?: Date;

  /**
   * import status from very recent import
   */
  @AutoMap()
  @IsString()
  @Exclude()
  lastImportStatus?: string;

  @AutoMap()
  @IsDate()
  lastSuccessfulProcessingDate: Date;

  /**
   * Calculated account overall import status from platform_ad_account_import_status table
   */
  @AutoMap()
  @IsString()
  importStatus?: ImportStatusForBFF;
}
