import { AutoMap } from '@automapper/classes';
import { IsBoolean, IsOptional } from 'class-validator';
import { UpdateAdAccountImportDataDto } from './update-ad-account-import-data.dto';
import { Type } from 'class-transformer';

export class UpdatePlatformAdAccountRequestDto {
  @AutoMap()
  @IsBoolean()
  @IsOptional()
  importV3Enabled?: boolean;

  @AutoMap()
  @IsBoolean()
  @IsOptional()
  processingCompleted?: boolean;

  @AutoMap()
  @IsOptional()
  @Type(() => UpdateAdAccountImportDataDto)
  accountImportData?: UpdateAdAccountImportDataDto;
}
