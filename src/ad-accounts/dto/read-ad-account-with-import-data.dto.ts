import { AutoMap } from '@automapper/classes';
import { IsBoolean, IsNumber, IsString } from 'class-validator';
import { ImportStatus } from '../../common/constants/constants';

export class ReadAdAccountWithImportData {
  /**
   * Platform Ad Account Id
   */
  @AutoMap()
  @IsNumber()
  id: number;

  /**
   * User Id
   */
  @AutoMap()
  @IsNumber()
  userId: number;

  /**
   * Platform
   */
  @AutoMap()
  @IsString()
  platform: string;

  /**
   * Platform User Id
   */
  @AutoMap()
  @IsString()
  platformUserId: string;

  /**
   * Platform Account Id
   */
  @AutoMap()
  @IsString()
  platformAccountId: string;

  /**
   * Platform Account Name
   */
  @AutoMap()
  @IsString()
  platformAccountName: string;

  /**
   * Processing completed
   */
  @AutoMap()
  @IsNumber()
  processingCompleted: number;

  /**
   * Import failure count
   */
  @AutoMap()
  @IsNumber()
  failuresCount: number;

  /**
   * Linked workspaces in org
   */
  @AutoMap()
  @IsNumber()
  linkedWorkspacesInOrg: number;

  /**
   * Account import status
   */
  @AutoMap()
  @IsString()
  accountImportStatus: string;

  /**
   * Spend Enabled Flag
   */
  @AutoMap()
  @IsBoolean()
  spendEnabled: boolean;
}
