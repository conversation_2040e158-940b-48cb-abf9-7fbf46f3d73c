import { Test, TestingModule } from '@nestjs/testing';
import { AdAccountSequentialFailureService } from './ad-account-sequential-failure.service';
import { EntityManager } from 'typeorm';
import { PlatformAdAccount } from './entities/ad-account.entity';
import { UpdateAdAccountImportDataDto } from './dto/update-ad-account-import-data.dto';
import { PlatformAdAccountSequentialFailure } from './entities/platform-ad-account-sequential-failure.entity';
import { MAX_ACCOUNT_IMPORT_FAILURE_THRESHOLD } from '../common/constants/constants';

describe('AdAccountSequentialFailureService', () => {
  let service: AdAccountSequentialFailureService;
  let entityManager: jest.Mocked<EntityManager>;

  const TEST_ACCOUNT_ID = '*********';
  const TEST_IMPORT_ID = 'ee20fb02-f8b2-45d6-ae01-6dae4db84bd3';

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdAccountSequentialFailureService,
        {
          provide: EntityManager,
          useValue: {
            save: jest.fn(),
            find: jest.fn(),
            remove: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get(AdAccountSequentialFailureService);
    entityManager = module.get(EntityManager);
  });

  describe('hasNotReachedFailureThreshold', () => {
    it('should return true if failure count is below or equal to threshold', () => {
      expect(service.hasNotReachedFailureThreshold(1)).toBe(true);
      expect(
        service.hasNotReachedFailureThreshold(
          MAX_ACCOUNT_IMPORT_FAILURE_THRESHOLD - 1,
        ),
      ).toBe(true);
    });

    it('should return false if failure count exceeds the threshold', () => {
      expect(
        service.hasNotReachedFailureThreshold(
          MAX_ACCOUNT_IMPORT_FAILURE_THRESHOLD,
        ),
      ).toBe(false);
      expect(
        service.hasNotReachedFailureThreshold(
          MAX_ACCOUNT_IMPORT_FAILURE_THRESHOLD + 1,
        ),
      ).toBe(false);
    });
  });

  describe('saveAdAccountSequentialFailure', () => {
    const FAILURES_REASON = `user does not have access to the ad account`;
    it('should save sequential failure with proper values', async () => {
      const adAccount = {
        platformAccountId: TEST_ACCOUNT_ID,
      } as PlatformAdAccount;
      const dto: UpdateAdAccountImportDataDto = {
        importId: TEST_IMPORT_ID,
        failureReason: FAILURES_REASON,
      };

      const expected = new PlatformAdAccountSequentialFailure();
      entityManager.save.mockResolvedValue(expected);

      const result = await service.saveAdAccountSequentialFailure(
        entityManager,
        adAccount,
        dto,
      );

      expect(entityManager.save).toHaveBeenCalledWith(
        expect.objectContaining({
          platformAdAccount: adAccount,
          importId: TEST_IMPORT_ID,
          failureReason: FAILURES_REASON,
          dateCreated: expect.any(Date),
          lastUpdated: expect.any(Date),
        }),
      );

      expect(result).toBe(expected);
    });

    it('should skip failureReason if not provided', async () => {
      const adAccount = {
        platformAccountId: TEST_ACCOUNT_ID,
      } as PlatformAdAccount;
      const dto: UpdateAdAccountImportDataDto = {
        importId: TEST_IMPORT_ID,
      };

      await service.saveAdAccountSequentialFailure(
        entityManager,
        adAccount,
        dto,
      );

      expect(entityManager.save).toHaveBeenCalledWith(
        expect.not.objectContaining({
          failureReason: undefined,
        }),
      );
    });
  });

  describe('deleteAdAccountSequentialFailuresByPlatformAdAccountId', () => {
    it('should find and remove failures for a given ad account ID', async () => {
      const failures: PlatformAdAccountSequentialFailure[] = [
        {
          id: 1,
          importId: TEST_IMPORT_ID,
          platformAdAccountId: 1,
        } as PlatformAdAccountSequentialFailure,
      ];
      entityManager.find.mockResolvedValue(failures);

      await service.deleteAdAccountSequentialFailuresByPlatformAdAccountId(
        entityManager,
        TEST_ACCOUNT_ID,
      );

      expect(entityManager.find).toHaveBeenCalledWith(
        PlatformAdAccountSequentialFailure,
        {
          where: { platformAdAccount: { platformAccountId: TEST_ACCOUNT_ID } },
        },
      );

      expect(entityManager.remove).toHaveBeenCalledWith(failures);
    });
  });
});
