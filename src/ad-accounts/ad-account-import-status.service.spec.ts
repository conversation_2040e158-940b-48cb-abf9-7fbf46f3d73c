import { Test, TestingModule } from '@nestjs/testing';
import { AdAccountImportStatusService } from './ad-account-import-status.service';
import { SqsService } from '@vidmob/vidmob-nestjs-common';
import { PlatformAdAccountImportStatus } from './entities/platform-ad-account-import-status.entity';
import { PlatformAdAccount } from './entities/ad-account.entity';
import {
  DEFAULT_HISTORIC_IMPORT_DAYS,
  DEFAULT_HISTORIC_IMPORT_DAYS_OFFSET,
  IAV3_ENABLED_PLATFORMS,
  ImportRebuildType,
} from '../common/constants/constants';

describe('AdAccountImportStatusService', () => {
  let service: AdAccountImportStatusService;
  let sqsService: SqsService;

  const TEST_ORG_ID = '50603f4d-c2d0-43fc-8ff8-77a735839cd4';
  const TEST_PLATFORM = 'MOCK';
  const TEST_ACCOUNT_ID = '**********';
  const TEST_USER_ID = 1;
  const TEST_IMPORT_STATUS_ID = 'a9995187-9bcf-4d96-aabb-89689f492ca0';

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdAccountImportStatusService,
        {
          provide: SqsService,
          useValue: {
            send: jest.fn(),
          },
        },
      ],
    }).compile();
    service = module.get<AdAccountImportStatusService>(
      AdAccountImportStatusService,
    );
    sqsService = module.get<SqsService>(SqsService);
  });

  const getMockPlatformAdAccount = (): PlatformAdAccount => {
    const platformAdAccount = new PlatformAdAccount();
    platformAdAccount.platform = TEST_PLATFORM;
    platformAdAccount.platformAccountId = TEST_ACCOUNT_ID;
    platformAdAccount.userId = TEST_USER_ID;
    platformAdAccount.lastImportSuccessDate = new Date();
    return platformAdAccount;
  };
  const mockImportStatus = {
    id: TEST_IMPORT_STATUS_ID,
    platformAdAccount: getMockPlatformAdAccount(),
    organization: {
      id: TEST_ORG_ID,
    },
  } as PlatformAdAccountImportStatus;

  describe('sendImportStartMessageToImportManagementService', () => {
    it('should send message to import management queue without days for new account', async () => {
      const newAccountImportStatus = {
        ...mockImportStatus,
        platformAdAccount: {
          ...mockImportStatus.platformAdAccount,
          lastImportSuccessDate: null,
        },
      };
      await service.sendImportStartMessageToImportManagementService(
        newAccountImportStatus,
      );
      expect(sqsService.send).toHaveBeenCalledWith('importStartQueueIAv3', {
        id: TEST_IMPORT_STATUS_ID,
        body: JSON.stringify({
          platform: TEST_PLATFORM,
          organizationId: TEST_ORG_ID,
          accountId: TEST_ACCOUNT_ID,
          userId: TEST_USER_ID,
        }),
      });
    });

    it('should call getHistoricImportDays when lastSuccessfulImportDate is present', async () => {
      jest.spyOn(service, 'getHistoricImportDays');
      await service.sendImportStartMessageToImportManagementService(
        mockImportStatus,
      );
      expect(service.getHistoricImportDays).toHaveBeenCalledWith(
        mockImportStatus.platformAdAccount.lastImportSuccessDate,
      );
      expect(sqsService.send).toHaveBeenCalledWith('importStartQueueIAv3', {
        id: TEST_IMPORT_STATUS_ID,
        body: JSON.stringify({
          platform: TEST_PLATFORM,
          organizationId: TEST_ORG_ID,
          accountId: TEST_ACCOUNT_ID,
          userId: TEST_USER_ID,
          days: service.getHistoricImportDays(
            mockImportStatus.platformAdAccount.lastImportSuccessDate,
          ),
        }),
      });
    });
  });

  describe('sendImportStartMessageToAnalyticsServiceImportQueue', () => {
    it('should send message to analytics service queue', async () => {
      await service.sendImportStartMessageToAnalyticsServiceImportQueue(
        mockImportStatus,
      );
      expect(sqsService.send).toHaveBeenCalledWith('importStartQueueLegacy', {
        id: TEST_IMPORT_STATUS_ID,
        body: JSON.stringify({
          network: TEST_PLATFORM.toLowerCase(),
          organizationId: TEST_ORG_ID,
          adAccountId: TEST_ACCOUNT_ID,
          userId: TEST_USER_ID,
          rebuildType: ImportRebuildType.HISTORIC,
          daysFrom: service.getHistoricImportDays(
            mockImportStatus.platformAdAccount.lastImportSuccessDate,
          ),
        }),
      });
    });
  });

  describe('getHistoricImportDays', () => {
    it('should return default days if lastImportSuccessDate is null', () => {
      const days = service.getHistoricImportDays(null);
      expect(days).toBe(DEFAULT_HISTORIC_IMPORT_DAYS);
    });

    it(`should calculate the correct number of days when it's less than historic import days`, () => {
      const LAST_SUCCESSFUL_IMPORT_SINCE_DAYS = 10;
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - LAST_SUCCESSFUL_IMPORT_SINCE_DAYS);

      const days = service.getHistoricImportDays(pastDate);
      expect(days).toEqual(
        LAST_SUCCESSFUL_IMPORT_SINCE_DAYS + DEFAULT_HISTORIC_IMPORT_DAYS_OFFSET,
      );
    });

    it(`should calculate the correct number of days when it's more than historic import days`, () => {
      const LAST_SUCCESSFUL_IMPORT_SINCE_DAYS = 400;
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - LAST_SUCCESSFUL_IMPORT_SINCE_DAYS);

      const days = service.getHistoricImportDays(pastDate);
      expect(days).toEqual(DEFAULT_HISTORIC_IMPORT_DAYS);
    });
  });

  describe('sendAccountImportMessage', () => {
    it('should call sendImportStartMessageToAnalyticsServiceImportQueue for Non IAv3 platforms', async () => {
      jest.spyOn(
        service,
        'sendImportStartMessageToAnalyticsServiceImportQueue',
      );
      await service.sendAccountImportMessage(mockImportStatus);
      expect(
        service.sendImportStartMessageToAnalyticsServiceImportQueue,
      ).toHaveBeenCalledWith(mockImportStatus);
    });

    it('should call sendImportStartMessageToImportManagementService for IAV3 platforms', async () => {
      const iAv3PlatformImportStatus = {
        ...mockImportStatus,
        platformAdAccount: {
          ...mockImportStatus.platformAdAccount,
          platform: IAV3_ENABLED_PLATFORMS[0],
        },
      };
      jest.spyOn(service, 'sendImportStartMessageToImportManagementService');
      await service.sendAccountImportMessage(iAv3PlatformImportStatus);
      expect(
        service.sendImportStartMessageToImportManagementService,
      ).toHaveBeenCalledWith(iAv3PlatformImportStatus);
    });
  });
});
