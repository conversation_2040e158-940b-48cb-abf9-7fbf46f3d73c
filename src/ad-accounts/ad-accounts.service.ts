import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  EntityManager,
  FindManyOptions,
  FindOptionsOrder,
  In,
  Like,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { PlatformAdAccountToWorkspace } from './entities/ad-account-workspace-map.entity';
import { PlatformAdAccount } from './entities/ad-account.entity';
import { ReadPlatformAdAccountDto } from './dto/read-platform-ad-account.dto';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { CreatePlatformAdAccountWorkspaceMapDto } from './dto/create-platform-ad-account-workspace-map.dto';
import { ReadPlatformAdAccountWorkspaceMapDto } from './dto/read-platform-ad-account-workspace-map.dto';
import { ReadPlatformAdAccountWithAdditionalInfoDto } from './dto/read-platform-ad-account-with-additional-info.dto';
import {
  PaginationOptions,
  SqsConsumerEventHandler,
  SqsMessageHandler,
  SqsService,
} from '@vidmob/vidmob-nestjs-common';
import { ReadWorkspacePlatformAdAccountDto } from './dto/read-workspace-platform-ad-account.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ValidateOrganizationEntitiesResponseDto } from '../organizations/dto/validate-organization-entities.dto';
import { OrganizationsService } from '../organizations/organizations.service';
import { WorkspaceService } from '../workspaces/workspaces.service';
import {
  ImportState,
  ImportStatus,
  OrganizationUserRoles,
  PlatformAdAccountSortBy,
  SortBy,
  SortOrder,
} from '../common/constants/constants';
import { AccountSearchParamsDto } from './dto/ad-account-search-params.dto';
import { ReadAdAccountWithImportData } from './dto/read-ad-account-with-import-data.dto';
import { PlatformAdAccountSequentialFailure } from './entities/platform-ad-account-sequential-failure.entity';
import { CreatePlatformAdAccountSequentialFailureDto } from './dto/create-platform-ad-account-sequential-failure.dto';
import { UpdatePlatformAdAccountRequestDto } from './dto/update-platform-ad-account-request.dto';
import { UpdateAdAccountImportDataDto } from './dto/update-ad-account-import-data.dto';
import { AdAccountSequentialFailureService } from './ad-account-sequential-failure.service';
import { AdAccountImportStatusService } from './ad-account-import-status.service';
import { Message } from '@aws-sdk/client-sqs';
import { UpdateAdAccountImportSqsMessageBodyDto } from './dto/update-ad-account-import-sqs-message-body.dto';

const POST_ACCOUNT_IMPORT_COMPLETION_QUEUE_NAME =
  'accountPostImportCompleteQueue';

@Injectable()
export class AdAccountsService {
  private readonly logger = new Logger(AdAccountsService.name);
  constructor(
    @InjectRepository(PlatformAdAccountToWorkspace)
    private platformAdAccountToWorkspaceRepository: Repository<PlatformAdAccountToWorkspace>,
    @InjectRepository(PlatformAdAccount)
    private platformAdAccountRepository: Repository<PlatformAdAccount>,
    @InjectRepository(PlatformAdAccountSequentialFailure)
    private platformAdAccountSequentialFailureRepository: Repository<PlatformAdAccountSequentialFailure>,
    @Inject(forwardRef(() => OrganizationsService))
    private organizationsService: OrganizationsService,
    @Inject(forwardRef(() => WorkspaceService))
    private workspaceService: WorkspaceService,
    @Inject(forwardRef(() => AdAccountSequentialFailureService))
    private adAccountSequentialFailureService: AdAccountSequentialFailureService,
    @Inject(forwardRef(() => AdAccountImportStatusService))
    private adAccountImportStatusService: AdAccountImportStatusService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly sqsService: SqsService,
  ) {}

  async getPlatformAdAccountIdsConnectedToAWorkspace(
    entityManager: EntityManager,
    workspaceId: number,
    adAccountsIds: number[],
  ): Promise<number[]> {
    const platformAdAccountWorkspaceMap = await entityManager.findBy(
      PlatformAdAccountToWorkspace,
      {
        partnerId: workspaceId,
        platformAdAccountId: In(adAccountsIds),
      },
    );
    return platformAdAccountWorkspaceMap.map((map) => map.platformAdAccountId);
  }

  async getPlatformAdAccountWorkspaceMappings(
    entityManager: EntityManager,
    adAccountIds: number[],
  ): Promise<Map<number, number[]>> {
    const platformAdAccountWorkspaceMap = await entityManager.findBy(
      PlatformAdAccountToWorkspace,
      {
        platformAdAccountId: In(adAccountIds),
      },
    );
    const workspaceAdAccountMap = new Map<number, number[]>();
    platformAdAccountWorkspaceMap.forEach((map) => {
      if (!workspaceAdAccountMap.has(map.platformAdAccountId)) {
        workspaceAdAccountMap.set(map.platformAdAccountId, []);
      }
      workspaceAdAccountMap.get(map.platformAdAccountId).push(map.partnerId);
    });
    return workspaceAdAccountMap;
  }

  async findPlatformAdAccountById(
    platformAdAccountId: number,
  ): Promise<ReadPlatformAdAccountDto> {
    const adAccount = await this.platformAdAccountRepository.findOneBy({
      id: platformAdAccountId,
    });
    return this.classMapper.map(
      adAccount,
      PlatformAdAccount,
      ReadPlatformAdAccountDto,
    );
  }

  async getPlatformAdAccountIdFromPlatformAndPlatformId(
    platformAccountId: string,
    platform: string,
  ): Promise<number> {
    const platformAdAccount = await this.platformAdAccountRepository.findOneBy({
      platformAccountId,
      platform,
      canAccess: 1,
    });
    if (!platformAdAccount) {
      throw new NotFoundException(
        `Ad account with platform id ${platformAccountId} not found`,
      );
    }
    return platformAdAccount.id;
  }

  async findPlatformAdAccountByAccountId(
    adAccountId: string,
  ): Promise<PlatformAdAccount> {
    const account = await this.platformAdAccountRepository.findOneBy({
      platformAccountId: adAccountId,
    });
    if (!account) {
      throw new NotFoundException(
        `Ad account with id ${adAccountId} not found.`,
      );
    }
    return account;
  }

  private validateAndGetSortByValueForAdAccountSearch(sortBy?: string): string {
    const VALID_SORT_BY_COLUMNS_FOR_SEARCH = [
      SortBy.PLATFORM_ACCOUNT_NAME_COLUMN,
      SortBy.PLATFORM,
      SortBy.DATE_CREATED_COLUMN,
    ];
    if (sortBy && VALID_SORT_BY_COLUMNS_FOR_SEARCH.includes(<SortBy>sortBy)) {
      return sortBy;
    } else {
      return SortBy.PLATFORM_ACCOUNT_NAME_COLUMN;
    }
  }

  async findWorkspaceConnectedAdAccounts(
    workspaceId: number,
    pagination: PaginationOptions,
    searchParams: AccountSearchParamsDto,
  ) {
    const sortOrder =
      searchParams.sortOrder === SortOrder.DESC
        ? SortOrder.DESC
        : SortOrder.ASC;
    const order = this.getSortByColumn(searchParams.sortBy, sortOrder);
    const queryParams: FindManyOptions<PlatformAdAccount> = {
      where: {
        workspaceAdAccountMap: {
          workspaceId: workspaceId,
        },
        canAccess: 1,
      },
      relations: {
        accountImportStatus: true,
      },
      order,
      skip: pagination.offset,
      take: pagination.perPage,
    };
    if (searchParams.search) {
      queryParams.where = {
        ...queryParams.where,
        platformAccountName: Like(`%${searchParams.search}%`),
      };
    }
    const [adAccounts, total] =
      await this.platformAdAccountRepository.findAndCount(queryParams);
    const mappedAdAccounts = this.classMapper.mapArray(
      adAccounts,
      PlatformAdAccount,
      ReadPlatformAdAccountDto,
    );
    return new PaginatedResultArray(mappedAdAccounts, total);
  }

  async findAdAccounts(
    platformAdAccountIds: number[],
  ): Promise<ReadPlatformAdAccountDto[]> {
    const adAccounts = await this.platformAdAccountRepository.findBy({
      id: In(platformAdAccountIds),
    });
    return this.classMapper.mapArray(
      adAccounts,
      PlatformAdAccount,
      ReadPlatformAdAccountDto,
    );
  }

  async getAdAccountsIdsFromPlatformAccountIds(platformAccountIds: string[]) {
    return this.platformAdAccountRepository
      .findBy({ platformAccountId: In(platformAccountIds) })
      .then((adAccounts) => adAccounts.map((adAccount) => adAccount.id));
  }

  async createWorkspacePlatformAdAccountConnections(
    entityManager: EntityManager,
    createDtos: CreatePlatformAdAccountWorkspaceMapDto[],
  ): Promise<ReadPlatformAdAccountWorkspaceMapDto[]> {
    const platformAdAccountIds: number[] = createDtos.map(
      (adAccount) => adAccount.platformAdAccountId,
    );
    const existingPlatformAdAccounts: PlatformAdAccount[] =
      await entityManager.findBy(PlatformAdAccount, {
        id: In(platformAdAccountIds),
      });
    const existingPlatformAdAccountIds: number[] =
      existingPlatformAdAccounts.map((adAccount) => adAccount.id);
    const nonExistingPlatformAdAccountsIds: number[] =
      platformAdAccountIds.filter(
        (id) => !existingPlatformAdAccountIds.includes(id),
      );
    if (nonExistingPlatformAdAccountsIds.length > 0) {
      throw new NotFoundException(
        `Ad Accounts Ids ( ${nonExistingPlatformAdAccountsIds} ) are not found`,
      );
    }
    const newWorkspacePlatformAdAccountConnections: PlatformAdAccountToWorkspace[] =
      this.classMapper.mapArray(
        createDtos,
        CreatePlatformAdAccountWorkspaceMapDto,
        PlatformAdAccountToWorkspace,
      );
    const savedWorkspacePlatformAdAccountConnections: PlatformAdAccountToWorkspace[] =
      await entityManager.save(newWorkspacePlatformAdAccountConnections);
    return this.classMapper.mapArray(
      savedWorkspacePlatformAdAccountConnections,
      PlatformAdAccountToWorkspace,
      ReadPlatformAdAccountWorkspaceMapDto,
    );
  }

  async deleteWorkspacePlatformAdAccountConnections(
    entityManager: EntityManager,
    workspaceId: number,
    platformAdAccountIds: number[],
  ): Promise<ReadPlatformAdAccountWorkspaceMapDto[]> {
    const platformAdAccountWorkspaceConnections = await entityManager.findBy(
      PlatformAdAccountToWorkspace,
      {
        partnerId: workspaceId,
        platformAdAccountId: In(platformAdAccountIds),
      },
    );
    const res = this.classMapper.mapArray(
      platformAdAccountWorkspaceConnections,
      PlatformAdAccountToWorkspace,
      ReadPlatformAdAccountWorkspaceMapDto,
    );
    await entityManager.remove(platformAdAccountWorkspaceConnections);
    return res;
  }

  async deleteMultipleWorkspacesConnectionsFromAPlatformAdAccount(
    workspaceIds: number[],
    platformAdAccountId: number,
  ): Promise<ReadPlatformAdAccountWorkspaceMapDto[]> {
    const platformAdAccountWorkspaceConnections =
      await this.platformAdAccountToWorkspaceRepository.findBy({
        partnerId: In(workspaceIds),
        platformAdAccountId: platformAdAccountId,
      });
    const deletedPlatformAdAccountWorkspaceConnections =
      await this.platformAdAccountToWorkspaceRepository.remove(
        platformAdAccountWorkspaceConnections,
      );
    return this.classMapper.mapArray(
      deletedPlatformAdAccountWorkspaceConnections,
      PlatformAdAccountToWorkspace,
      ReadPlatformAdAccountWorkspaceMapDto,
    );
  }

  private buildWorkspaceAdAccountQuery(
    readDto: ReadWorkspacePlatformAdAccountDto,
    paginationOptions?: PaginationOptions,
  ) {
    const adAccountQueryBuilder =
      this.platformAdAccountRepository.createQueryBuilder('platformAdAccount');
    adAccountQueryBuilder
      .innerJoin(
        'platformAdAccount.PlatformAdAccountToWorkspace',
        'platformAdAccountToWorkspace',
      )
      .where('platformAdAccountToWorkspace.partnerId = :partnerId', {
        partnerId: readDto.workspaceId,
      });
    if (readDto.canAccess)
      adAccountQueryBuilder.andWhere(
        'platformAdAccount.canAccess = :canAccess',
        { canAccess: readDto.canAccess ? 1 : 0 },
      );
    if (paginationOptions) {
      adAccountQueryBuilder
        .take(paginationOptions.perPage)
        .skip(paginationOptions.offset);
    }
    return adAccountQueryBuilder;
  }

  async findAdAccountsByWorkspaceId(
    readDto: ReadWorkspacePlatformAdAccountDto,
    paginationOptions?: PaginationOptions,
  ): Promise<
    PaginatedResultArray<ReadPlatformAdAccountDto> | ReadPlatformAdAccountDto[]
  > {
    const adAccountQuery = this.buildWorkspaceAdAccountQuery(
      readDto,
      paginationOptions,
    );
    if (paginationOptions) {
      const [adAccounts, count] = await adAccountQuery.getManyAndCount();
      return new PaginatedResultArray(
        this.classMapper.mapArray(
          adAccounts,
          PlatformAdAccount,
          ReadPlatformAdAccountDto,
        ),
        count,
      );
    }
    return adAccountQuery
      .getMany()
      .then((adAccounts) =>
        this.classMapper.mapArray(
          adAccounts,
          PlatformAdAccount,
          ReadPlatformAdAccountDto,
        ),
      );
  }

  /**
   * Find connected ad accounts for an organization.
   * @param organizationId  The organization id
   * @param paginationOptions The pagination options
   * @param workspaceId The optional workspace id query parameter
   */
  async getConnectedAdAccountsForAnOrganization(
    organizationId: string,
    workspaceId: number,
    paginationOptions: PaginationOptions,
    searchParams: AccountSearchParamsDto,
  ): Promise<PaginatedResultArray<ReadPlatformAdAccountWithAdditionalInfoDto>> {
    let queryBuilder = this.platformAdAccountRepository
      .createQueryBuilder('platformAdAccount')
      .innerJoin(
        'platformAdAccount.organizationPlatformAdAccountMap',
        'organizationPlatformAdAccountMap',
        'organizationPlatformAdAccountMap.organizationId = :organizationId',
        { organizationId },
      )
      .leftJoinAndSelect(
        'platformAdAccount.organizationAdAccountUserPermissions',
        'organizationAdAccountUserPermissions',
        'organizationAdAccountUserPermissions.platformAdAccountId = platformAdAccount.id and organizationAdAccountUserPermissions.organizationId = :organizationId',
        { organizationId },
      )
      .leftJoinAndSelect(
        'platformAdAccount.accountImportStatus',
        'platformAdAccountImportStatus',
      )
      .leftJoinAndSelect(
        'organizationAdAccountUserPermissions.user',
        'user',
        'user.id = organizationAdAccountUserPermissions.userId and organizationAdAccountUserPermissions.organizationId = :organizationId',
        { organizationId },
      );

    if (workspaceId) {
      queryBuilder = queryBuilder.leftJoinAndSelect(
        'platformAdAccount.PlatformAdAccountToWorkspace',
        'platformAdAccountToWorkspace',
        'platformAdAccountToWorkspace.partnerId = :workspaceId',
        { workspaceId },
      );
    }

    queryBuilder = queryBuilder.where({
      canAccess: 1,
    });

    if (searchParams.search && searchParams.search.trim().length > 0) {
      queryBuilder.andWhere(
        `(platformAdAccount.platform_account_id = :search OR platformAdAccount.platform_account_name like :likeSearch OR user.username like :likeSearch)`,
        { search: searchParams.search, likeSearch: `%${searchParams.search}%` },
      );
    }

    this.applyOrderToAdAccounts(queryBuilder, searchParams);

    const [adAccounts, total] = await queryBuilder
      .skip(paginationOptions.offset)
      .take(paginationOptions.perPage)
      .getManyAndCount();

    const adAccountsDTOs = this.classMapper.mapArray(
      adAccounts,
      PlatformAdAccount,
      ReadPlatformAdAccountWithAdditionalInfoDto,
    );

    return new PaginatedResultArray<ReadPlatformAdAccountWithAdditionalInfoDto>(
      adAccountsDTOs,
      total,
    );
  }

  public applyOrderToAdAccounts(
    queryBuilder: SelectQueryBuilder<PlatformAdAccount>,
    searchParams: AccountSearchParamsDto,
  ): void {
    if (!searchParams.sortBy) {
      // apply default sort by columns
      queryBuilder.orderBy('platformAdAccount.platform', 'ASC');
      queryBuilder.addOrderBy('platformAdAccount.platformAccountName', 'ASC');

      return;
    }
    const sortOrder =
      searchParams.sortOrder === SortOrder.DESC
        ? SortOrder.DESC
        : SortOrder.ASC;

    const sortBy = this.getQueryBuilderSortByColumn(searchParams.sortBy);

    queryBuilder.orderBy(sortBy, sortOrder);
  }

  private getQueryBuilderSortByColumn(sortBy: string): string {
    switch (sortBy) {
      case PlatformAdAccountSortBy.IMPORT_STATUS:
        return `platformAdAccountImportStatus.status`;
      case PlatformAdAccountSortBy.LAST_SUCCESSFUL_PROCESSING_DATE:
        return `platformAdAccount.lastImportCompleteDate`;
      case PlatformAdAccountSortBy.CONNECTED:
        return `platformAdAccount.canAccess`;
      default:
        return `platformAdAccount.${sortBy}`;
    }
  }

  private getSortByColumn(
    sortBy: string,
    sortOrder: SortOrder,
  ): FindOptionsOrder<PlatformAdAccount> {
    switch (sortBy) {
      case PlatformAdAccountSortBy.IMPORT_STATUS:
        return { accountImportStatus: { status: sortOrder } };
      case PlatformAdAccountSortBy.LAST_SUCCESSFUL_PROCESSING_DATE:
        return { lastImportCompleteDate: sortOrder };
      case PlatformAdAccountSortBy.CONNECTED:
        return { canAccess: sortOrder };
      case null:
      case undefined:
        // default
        return { platformAccountName: sortOrder };
      default:
        return { [sortBy]: sortOrder };
    }
  }

  private async areAllAccountsInOrgWorkspaces(
    organizationId: string,
    adAccountIds: string[],
    workspaceIds: number[],
  ): Promise<boolean> {
    const accountsInWorkspaces = await this.platformAdAccountRepository.count({
      where: {
        platformAccountId: In(adAccountIds),
        organizationPlatformAdAccountMap: {
          organizationId: organizationId,
        },
        PlatformAdAccountToWorkspace: {
          partnerId: In(workspaceIds),
        },
        canAccess: 1,
      },
    });
    return accountsInWorkspaces === adAccountIds.length;
  }

  /**
   * Validate that all ad accounts are within the organization and workspaces.
   *
   * Must satisfy these conditions:
   * - Each account must be in the organization and in one of the workspaces
   * - If the user is an org admin, then they have access to all accounts
   * - If the user is a non-admin, and access to all the workspaces, then they have access to all accounts
   *
   * @param organizationId The organization id
   * @param userId The user id
   * @param adAccountIds The ad account ids
   * @param workspaceIds The workspace ids
   */
  async validateUserAccountsWithinOrganizationWorkspaces(
    organizationId: string,
    userId: number,
    adAccountIds: string[],
    workspaceIds: number[],
  ): Promise<ValidateOrganizationEntitiesResponseDto> {
    const uniqueAdAccountIds = [...new Set(adAccountIds)];
    const uniqueWorkspaceIds = [...new Set(workspaceIds)];
    const [areAllAccountsInWorkspaces, userAuthorizedRoleInOrganization] =
      await Promise.all([
        this.areAllAccountsInOrgWorkspaces(
          organizationId,
          uniqueAdAccountIds,
          uniqueWorkspaceIds,
        ),
        this.organizationsService.getUserAuthorizedRoleInOrganization(
          organizationId,
          userId,
        ),
      ]);

    if (!areAllAccountsInWorkspaces) {
      return {
        success: false,
        message:
          'One or more accounts in the request are not in the organization workspaces.',
      };
    }

    if (userAuthorizedRoleInOrganization === OrganizationUserRoles.ORG_ADMIN) {
      return {
        success: true,
        message: `User ${userId} has access to the accounts in the organization ${organizationId}.`,
      };
    }

    const userAccessValid =
      await this.workspaceService.isNonAdminUserAuthorizedInAllWorkspaces(
        organizationId,
        uniqueWorkspaceIds,
        userId,
      );

    if (userAccessValid) {
      return {
        success: true,
        message: `User ${userId} has access to the accounts in the ${uniqueWorkspaceIds.length} workspaces.`,
      };
    }

    return {
      success: false,
      message: `User ${userId} does not have access to the accounts in one or more of the ${uniqueWorkspaceIds.length} workspaces.`,
    };
  }

  /**
   * Validate user access to ad accounts within an organization and user-accessible workspaces.
   *
   * Must satisfy these conditions:
   * - Each account must be in the organization and mapped to a workspace
   *      (an account mus be mapped to a workspace in order to be accessible)
   * - If the user is an org admin, then they have access to all accounts
   * - If the user is a standard user, then for each account they must have access to at least one workspace of that account
   *
   * @param organizationId The organization id
   * @param userId The user id
   * @param adAccountIds The ad account ids
   */
  async validateUserAccountsWithinOrganization(
    organizationId: string,
    userId: number,
    adAccountIds: string[],
  ): Promise<ValidateOrganizationEntitiesResponseDto> {
    const uniqueAdAccountIds = [...new Set(adAccountIds)];

    // Get all workspace-account maps, so we can
    //  (a) check if all accounts are in the organization
    //  (b) if they are not admin in the org, check if the user has access to any of the account's workspaces
    const workspacePlatformAdAccounts =
      await this.platformAdAccountToWorkspaceRepository.find({
        relations: ['platformAdAccount'],
        where: {
          platformAdAccount: {
            canAccess: 1,
            organizationPlatformAdAccountMap: {
              organizationId: organizationId,
            },
            platformAccountId: In(uniqueAdAccountIds),
          },
        },
      });

    const accountsInWorkspaces = workspacePlatformAdAccounts.map(
      (workspace) => workspace.platformAdAccount.platformAccountId,
    );
    const uniqueWorkspaceAccountIds = [...new Set(accountsInWorkspaces)];

    const areAllAccountsInOrgAndWorkspace =
      uniqueWorkspaceAccountIds.length === uniqueAdAccountIds.length;

    if (!areAllAccountsInOrgAndWorkspace) {
      return {
        success: false,
        message:
          'One or more accounts in the request are not in the organization and mapped to a workspace.',
      };
    }

    const userAuthorizedRoleInOrganization =
      await this.organizationsService.getUserAuthorizedRoleInOrganization(
        organizationId,
        userId,
      );

    if (userAuthorizedRoleInOrganization === OrganizationUserRoles.ORG_ADMIN) {
      return {
        success: true,
        message: `User ${userId} has access to all accounts in the organization ${organizationId}.`,
      };
    }

    // get all workspaces for the user
    const userWorkspaces =
      await this.workspaceService.getNonAdminUserAuthorizedWorkspaces(
        organizationId,
        userId,
      );

    // user has access if every account has at least one workspace they can access
    const userAccessValid = this.validateWorkspacesUserAccessible(
      userWorkspaces,
      workspacePlatformAdAccounts,
    );

    if (userAccessValid) {
      return {
        success: true,
        message: `User ${userId} has access to the accounts.`,
      };
    }

    return {
      success: false,
      message: `User ${userId} does not have access to a workspace of the accounts.`,
    };
  }

  /**
   * For each ad account, validate if the user has access to at least one of the account's workspaces
   * @param userWorkspaces User accessible workspace IDs
   * @param workspacePlatformAdAccounts Workspace-account mappings
   */
  validateWorkspacesUserAccessible(
    userWorkspaces: number[],
    workspacePlatformAdAccounts: PlatformAdAccountToWorkspace[],
  ): boolean {
    // collect all workspaces for each account, so we can compare the account's workspace to the user's workspaces
    const accountWorkspaceMap: Record<string, Set<number>> = {};
    workspacePlatformAdAccounts.forEach((workspace) => {
      const accountId = workspace.platformAdAccount.platformAccountId;
      if (!accountWorkspaceMap[accountId]) {
        accountWorkspaceMap[accountId] = new Set<number>();
      }
      accountWorkspaceMap[accountId].add(workspace.partnerId);
    });

    // over each account-workspaces, check if the user has access to any of the account's workspaces
    const userHasAccessToWorkspaces = Object.values(accountWorkspaceMap).map(
      (workspaceIds) => {
        // check if the user has access to any of the account's workspaces, i.e. these lists intersect
        return userWorkspaces.some((workspace) => workspaceIds.has(workspace));
      },
    );

    // user has access if every account has at least one workspace they can access
    return userHasAccessToWorkspaces.every((hasAccess) => hasAccess);
  }

  getLinkedWorkspaceCountInOrganization(
    organizationId: string,
    account: PlatformAdAccount,
  ): number {
    if (!organizationId) {
      console.warn(
        'Organization id not provided. Returning all linked workspaces. Please provide organization id to get linked workspaces in the organization.',
      );
      return account.workspaceAdAccountMap.length;
    }
    return account.workspaceAdAccountMap.filter(
      (workspaceAccountMap) =>
        workspaceAccountMap.workspace.organizationId === organizationId,
    ).length;
  }

  async getSpendEnabledForOrganization(
    organizationId: string,
  ): Promise<boolean> {
    const organization = await this.organizationsService.findOne(
      organizationId,
    );

    return organization.spendEnabled;
  }

  /**
   * Get Ad account details with import information
   * @param organizationId The organization id
   * @param platform The platform name
   * @param adAccountId The ad account id
   */
  async getPlatformAdAccountWithImportData(
    organizationId: string,
    platform: string,
    adAccountId: string,
  ): Promise<ReadAdAccountWithImportData> {
    const account = await this.platformAdAccountRepository.findOne({
      where: {
        platformAccountId: adAccountId,
        platform: platform.toUpperCase(),
      },
      relations: [
        'platformAdAccountSequentialFailures',
        'workspaceAdAccountMap.workspace',
        'accountImportStatus',
      ],
    });

    if (!account) {
      throw new NotFoundException(
        `Ad account with id ${adAccountId} not found for platform ${platform}.`,
      );
    } else {
      const failuresCount = account.platformAdAccountSequentialFailures.length;
      const linkedWorkspaceCount = this.getLinkedWorkspaceCountInOrganization(
        organizationId,
        account,
      );
      const spendEnabled = await this.getSpendEnabledForOrganization(
        organizationId,
      );

      return {
        id: account.id,
        userId: account.userId,
        platform: account.platform,
        platformUserId: account.platformUserId,
        platformAccountId: account.platformAccountId,
        platformAccountName: account.platformAccountName,
        processingCompleted: account.processingCompleted,
        failuresCount: failuresCount,
        linkedWorkspacesInOrg: linkedWorkspaceCount,
        accountImportStatus: account.accountImportStatus?.status,
        spendEnabled,
      };
    }
  }

  /**
   * Save platform ad account sequential failure record
   * @deprecated should no longer be used by IMS
   * @param createPlatformAdAccountSequentialFailureDto The sequential failure DTO
   */
  async savePlatformAdAccountSequentialFailure(
    adAccountId: string,
    createPlatformAdAccountSequentialFailureDto: CreatePlatformAdAccountSequentialFailureDto,
  ): Promise<void> {
    const platformAdAccount = await this.findPlatformAdAccountByAccountId(
      adAccountId,
    );
    const platformAdAccountSequentialFailure = this.classMapper.map(
      createPlatformAdAccountSequentialFailureDto,
      CreatePlatformAdAccountSequentialFailureDto,
      PlatformAdAccountSequentialFailure,
    );
    platformAdAccountSequentialFailure.platformAdAccount = platformAdAccount;
    await this.platformAdAccountSequentialFailureRepository.save(
      platformAdAccountSequentialFailure,
    );
  }

  /**
   * Delete all sequential failures for a platform ad account
   * @param adAccountId The ad account id
   */
  async deleteSequentialFailuresByPlatformAdAccountId(
    adAccountId: string,
  ): Promise<void> {
    await this.platformAdAccountSequentialFailureRepository.manager.transaction(
      (entityManager) => {
        return this.adAccountSequentialFailureService.deleteAdAccountSequentialFailuresByPlatformAdAccountId(
          entityManager,
          adAccountId,
        );
      },
    );
  }

  private isEmptyAccountImportData(
    accountImportDto: UpdateAdAccountImportDataDto | undefined,
  ): boolean {
    return (
      accountImportDto !== undefined &&
      accountImportDto.userId === undefined &&
      accountImportDto.importSkipReason === undefined &&
      accountImportDto.importStatus === undefined &&
      accountImportDto.importStartDate === undefined &&
      accountImportDto.importCompleteDate === undefined &&
      accountImportDto.importSuccessDate === undefined
    );
  }

  /**
   * Validate the request parameters for updating a platform ad account
   * @param organizationId The organization id
   * @param platform The platform name
   * @param adAccountId The ad account id
   * @param dto The update DTO
   * @return True if the request parameters are valid, false otherwise
   */
  validateAccountUpdateRequestParameters(
    organizationId: string,
    platform: string,
    adAccountId: string,
    dto: UpdatePlatformAdAccountRequestDto,
  ): void {
    if (!organizationId || !platform || !adAccountId) {
      throw new BadRequestException(
        'Organization id, platform name and ad account id are required to update the ad account data.',
      );
    }
    if (
      dto.processingCompleted === undefined &&
      dto.importV3Enabled === undefined &&
      dto.accountImportData === undefined
    ) {
      throw new BadRequestException(
        'Request must contain at least one of the following fields: ' +
          'processingCompleted, importV3Enabled, accountImportData',
      );
    }
    if (
      dto.accountImportData &&
      !dto.accountImportData?.importSkipReason &&
      !dto.accountImportData?.importId &&
      !dto.accountImportData?.rebuildRequestId
    ) {
      throw new BadRequestException(
        'accountImportData must include either importId, rebuild request id or importSkipReason, when present.',
      );
    }
    if (
      dto.accountImportData?.importId &&
      dto.accountImportData?.rebuildRequestId
    ) {
      throw new BadRequestException(
        'accountImportData cannot include both importId or rebuild request id.',
      );
    }
    if (this.isEmptyAccountImportData(dto.accountImportData)) {
      throw new BadRequestException(
        `At least one field (userId, importStatus, importStartDate, importCompleteDate, importSuccessDate, ` +
          `importSkipReason) must be provided when accountImportData is included.`,
      );
    }
  }

  /**
   * Check if importV3Enabled flag is updated
   * @param updateDto The update DTO request
   * @param dbAccount The platform ad account from the database
   * @returns True if the importV3Enabled flag is updated, false otherwise
   */
  private isImportV3EnabledUpdated(
    updateDto: UpdatePlatformAdAccountRequestDto,
    dbAccount: PlatformAdAccount,
  ): boolean {
    if (updateDto.importV3Enabled === undefined) {
      return false;
    }
    const requestImportV3Enabled = updateDto.importV3Enabled || false;
    const dbImportV3Enabled = dbAccount.importV3Enabled === 1;
    return requestImportV3Enabled !== dbImportV3Enabled;
  }

  /**
   * Check if processingCompleted flag is updated
   * @param updateDto The update DTO request
   * @param dbAccount The platform ad account from the database
   * @returns True if the processingCompleted flag is updated or processingCompletedDate is null, False otherwise
   */
  private isProcessingCompletedUpdated(
    updateDto: UpdatePlatformAdAccountRequestDto,
    dbAccount: PlatformAdAccount,
  ): boolean {
    if (updateDto.processingCompleted === undefined) {
      return false;
    }
    const requestProcessingCompleted = updateDto.processingCompleted ? 1 : 0;
    const dbProcessingCompleted = dbAccount.processingCompleted;
    return (
      requestProcessingCompleted !== dbProcessingCompleted ||
      (updateDto.processingCompleted &&
        dbAccount.processingCompletedDate === null)
    );
  }

  private compareDates(date1: Date | null, date2: Date | null): boolean {
    if (date1 === null && date2 === null) {
      return true;
    }
    if (date1 === null || date2 === null) {
      return false;
    }
    return date1.getTime() === date2.getTime();
  }

  /**
   * Check if account import information is updated
   * @param updateDto The update DTO request
   * @param dbAccount The platform ad account from the database
   * @returns True if any of the account import information is updated, false otherwise
   */
  private isAccountImportInformationUpdated(
    updateDto: UpdatePlatformAdAccountRequestDto,
    dbAccount: PlatformAdAccount,
  ): boolean {
    if (updateDto.accountImportData === undefined) {
      return false;
    }
    const accountImportData = updateDto.accountImportData;
    const importUserId = accountImportData.userId || dbAccount.userId;
    const importStatus =
      accountImportData.importStatus || dbAccount.lastImportStatus;
    const importStartDate =
      accountImportData.importStartDate || dbAccount.lastImportStartDate;
    const importCompleteDate =
      accountImportData.importCompleteDate || dbAccount.lastImportCompleteDate;
    const importSuccessDate =
      accountImportData.importSuccessDate || dbAccount.lastImportSuccessDate;
    return (
      importUserId !== dbAccount.userId ||
      importStatus !== dbAccount.lastImportStatus ||
      !this.compareDates(importStartDate, dbAccount.lastImportStartDate) ||
      !this.compareDates(
        importCompleteDate,
        dbAccount.lastImportCompleteDate,
      ) ||
      !this.compareDates(importSuccessDate, dbAccount.lastImportSuccessDate)
    );
  }

  /**
   * Check if any of the platform ad account properties are updated
   * @param updateDto The update DTO request
   * @param platformAdAccount The platform ad account from the database
   * @returns True if any of the properties are updated, false otherwise
   */
  isPlatformAdAccountUpdated(
    updateDto: UpdatePlatformAdAccountRequestDto,
    platformAdAccount: PlatformAdAccount,
  ): boolean {
    return (
      this.isImportV3EnabledUpdated(updateDto, platformAdAccount) ||
      this.isProcessingCompletedUpdated(updateDto, platformAdAccount) ||
      this.isAccountImportInformationUpdated(updateDto, platformAdAccount)
    );
  }

  /**
   * Update the platform ad account with the request parameters
   * @param platformAdAccount The platform ad account from the database
   * @param updateDto The update DTO request
   * @return The updated platform ad account
   */
  updatePlatformAdAccountWithRequestParams(
    platformAdAccount: PlatformAdAccount,
    updateDto: UpdatePlatformAdAccountRequestDto,
  ): void {
    if (this.isImportV3EnabledUpdated(updateDto, platformAdAccount)) {
      platformAdAccount.importV3Enabled = updateDto.importV3Enabled ? 1 : 0;
    }
    if (this.isProcessingCompletedUpdated(updateDto, platformAdAccount)) {
      platformAdAccount.processingCompleted = updateDto.processingCompleted
        ? 1
        : 0;
      if (
        !platformAdAccount.processingCompletedDate &&
        updateDto.processingCompleted
      ) {
        platformAdAccount.processingCompletedDate = new Date();
      }
    }
    if (this.isAccountImportInformationUpdated(updateDto, platformAdAccount)) {
      const data = updateDto.accountImportData;
      platformAdAccount.userId = data?.userId ?? platformAdAccount.userId;
      platformAdAccount.lastImportStatus =
        data?.importStatus ?? platformAdAccount.lastImportStatus;
      platformAdAccount.lastImportStartDate =
        data?.importStartDate ?? platformAdAccount.lastImportStartDate;
      platformAdAccount.lastImportCompleteDate =
        data?.importCompleteDate ?? platformAdAccount.lastImportCompleteDate;
      platformAdAccount.lastImportSuccessDate =
        data?.importSuccessDate ?? platformAdAccount.lastImportSuccessDate;
    }
  }

  /**
   * Handle the update of the account import failure status
   * @param organizationId The organization id
   * @param platformAdAccount The platform ad account
   * @param accountImportDto The update ad account import data DTO
   * @param currentFailuresCount The account import failure count including the current one
   */
  async handleUpdateAccountImportFailureStatus(
    organizationId: string,
    platformAdAccount: PlatformAdAccount,
    accountImportDto: UpdateAdAccountImportDataDto,
    currentFailuresCount: number,
  ) {
    if (
      this.adAccountSequentialFailureService.hasNotReachedFailureThreshold(
        currentFailuresCount,
      )
    ) {
      await this.platformAdAccountSequentialFailureRepository.manager.transaction(
        (entityManager) => {
          return this.adAccountSequentialFailureService.saveAdAccountSequentialFailure(
            entityManager,
            platformAdAccount,
            accountImportDto,
          );
        },
      );
    }
    let importStatus = ImportStatus.FAILED;
    if (
      !this.adAccountSequentialFailureService.hasNotReachedFailureThreshold(
        currentFailuresCount + 1,
      )
    ) {
      importStatus = ImportStatus.SUSPENDED;
    }
    await this.platformAdAccountRepository.manager.transaction(
      (entityManager) => {
        return this.adAccountImportStatusService.insertAccountImportStatusRecord(
          entityManager,
          organizationId,
          accountImportDto,
          platformAdAccount,
          importStatus,
        );
      },
    );
  }

  /**
   * Handle the update of the account import success status
   * @param organizationId The organization id
   * @param platformAdAccount The platform ad account
   * @param accountImportDto The update ad account import data DTO
   * @param previousFailuresCount The account import failure count
   */
  async handleUpdateAccountImportSuccessStatus(
    organizationId: string,
    platformAdAccount: PlatformAdAccount,
    accountImportDto: UpdateAdAccountImportDataDto,
    previousFailuresCount: number,
  ) {
    if (previousFailuresCount > 0) {
      await this.deleteSequentialFailuresByPlatformAdAccountId(
        platformAdAccount.platformAccountId,
      );
    }
    await this.platformAdAccountRepository.manager.transaction(
      (entityManager) => {
        return this.adAccountImportStatusService.insertAccountImportStatusRecord(
          entityManager,
          organizationId,
          accountImportDto,
          platformAdAccount,
          ImportStatus.ENABLED,
        );
      },
    );
  }

  /**
   * Update the account import status of the platform ad account
   * @param organizationId The organization id
   * @param platformAdAccount The platform ad account
   * @param accountImportDto The update ad account import data DTO
   */
  async updateAccountImportStatus(
    organizationId: string,
    platformAdAccount: PlatformAdAccount,
    accountImportDto: UpdateAdAccountImportDataDto,
  ) {
    const failuresCount =
      await this.platformAdAccountSequentialFailureRepository.countBy({
        platformAdAccount: {
          platformAccountId: platformAdAccount.platformAccountId,
        },
      });
    if (accountImportDto.importStatus === ImportState.FAILED) {
      await this.handleUpdateAccountImportFailureStatus(
        organizationId,
        platformAdAccount,
        accountImportDto,
        failuresCount,
      );
    } else if (accountImportDto.importStatus === ImportState.SUCCESS) {
      await this.handleUpdateAccountImportSuccessStatus(
        organizationId,
        platformAdAccount,
        accountImportDto,
        failuresCount,
      );
    }
  }

  /**
   * Check if the account import status should be updated
   * @param accountImportData The account import data
   */
  canUpdateAccountImportStatus(
    accountImportData: UpdateAdAccountImportDataDto,
  ): boolean {
    return (
      accountImportData?.importStatus === ImportState.FAILED ||
      accountImportData?.importStatus === ImportState.SUCCESS
    );
  }

  /**
   * Update the platform ad account with not importing status
   * @param organizationId The organization id
   * @param platformAdAccount The platform ad account
   * @param accountImportDto The update ad account import data DTO
   */
  async updateAccountWithNotImportingStatus(
    organizationId: string,
    platformAdAccount: PlatformAdAccount,
    accountImportDto: UpdateAdAccountImportDataDto,
  ): Promise<void> {
    return this.platformAdAccountRepository.manager.transaction(
      (entityManager) => {
        return this.adAccountImportStatusService.insertAccountImportStatusRecord(
          entityManager,
          organizationId,
          accountImportDto,
          platformAdAccount,
          ImportStatus.NOT_IMPORTING,
        );
      },
    );
  }

  /**
   * Check if the request is made with import skip reason
   * @param accountImportData The account import data DTO
   */
  isAccountNotImporting(
    accountImportData: UpdateAdAccountImportDataDto,
  ): boolean {
    return accountImportData?.importSkipReason !== undefined;
  }

  /**
   * Check if the platform ad account is valid
   * @param organizationId organization id
   * @param platform platform name
   * @param adAccountId  ad account id
   */
  async validateAndReturnPlatformAdAccountInOrg(
    organizationId: string,
    platform: string,
    adAccountId: string,
  ) {
    const platformAdAccount = await this.platformAdAccountRepository.findOne({
      where: {
        platformAccountId: adAccountId,
        organizationPlatformAdAccountMap: {
          organizationId: organizationId,
        },
      },
    });
    if (!platformAdAccount) {
      throw new NotFoundException(
        `Platform ad account with id ${adAccountId} not found.`,
      );
    }
    if (platformAdAccount.platform.toUpperCase() !== platform.toUpperCase()) {
      throw new BadRequestException(
        `Incorrect platform value provided for ad account ${adAccountId}. ` +
          `Expected ${platformAdAccount.platform} but received ${platform}.`,
      );
    }
    return platformAdAccount;
  }

  /**
   * Update ad account with the given parameters
   * @param organizationId The organization id
   * @param platform The platform name
   * @param adAccountId The ad account id
   * @param updateDto The update DTO request
   * @returns The updated platform ad account
   */
  async updatePlatformAdAccount(
    organizationId: string,
    platform: string,
    adAccountId: string,
    updateDto: UpdatePlatformAdAccountRequestDto,
  ) {
    const platformAdAccount =
      await this.validateAndReturnPlatformAdAccountInOrg(
        organizationId,
        platform,
        adAccountId,
      );
    if (this.isPlatformAdAccountUpdated(updateDto, platformAdAccount)) {
      this.updatePlatformAdAccountWithRequestParams(
        platformAdAccount,
        updateDto,
      );
      await this.platformAdAccountRepository.save(platformAdAccount);
    }
    await this.sendMessageToPostAccountImportCompletionQueue(
      organizationId,
      adAccountId,
      updateDto.accountImportData,
    );
    return this.classMapper.map(
      platformAdAccount,
      PlatformAdAccount,
      ReadPlatformAdAccountDto,
    );
  }

  /**
   * Send a message to the post account import completion queue
   * @param organizationId organization id
   * @param platformAccountId platform account id
   * @param accountImportData The update ad account import data DTO
   */
  async sendMessageToPostAccountImportCompletionQueue(
    organizationId: string,
    platformAccountId: string,
    accountImportData: UpdateAdAccountImportDataDto,
  ): Promise<void> {
    if (accountImportData) {
      await this.sqsService.send(POST_ACCOUNT_IMPORT_COMPLETION_QUEUE_NAME, {
        id: `${accountImportData.importId}_${platformAccountId}`,
        body: JSON.stringify({
          organizationId,
          platformAccountId,
          accountImportData,
        } as UpdateAdAccountImportSqsMessageBodyDto),
      });
    }
  }

  /**
   * Update the platform ad account with the given request parameters
   * @param organizationId organization id
   * @param platformAccountId platform account id
   * @param accountImportDataDTO The update ad account import data DTO
   */
  async updateAccountDetailsPostImportCompletion(
    organizationId: string,
    platformAccountId: string,
    accountImportDataDTO: UpdateAdAccountImportDataDto,
  ) {
    const platformAdAccount = await this.platformAdAccountRepository
      .createQueryBuilder('paa')
      .leftJoinAndSelect('paa.accountImportStatus', 'status')
      .where('paa.platformAccountId = :platformAccountId', {
        platformAccountId,
      })
      .getOne();
    if (this.canUpdateAccountImportStatus(accountImportDataDTO)) {
      await this.updateAccountImportStatus(
        organizationId,
        platformAdAccount,
        accountImportDataDTO,
      );
    } else if (this.isAccountNotImporting(accountImportDataDTO)) {
      await this.updateAccountWithNotImportingStatus(
        organizationId,
        platformAdAccount,
        accountImportDataDTO,
      );
    } else {
      this.logger.debug(
        `No action needed for account import status update. DTO: ${JSON.stringify(
          accountImportDataDTO,
        )}`,
      );
    }
  }

  @SqsMessageHandler(POST_ACCOUNT_IMPORT_COMPLETION_QUEUE_NAME, false)
  async handlePostImportCompletionUpdateMessage(message: Message) {
    this.logger.debug(
      `Received ad account post import update message with body: ${message.Body}`,
    );
    const body = JSON.parse(
      message.Body,
    ) as UpdateAdAccountImportSqsMessageBodyDto;
    if (
      body.organizationId === undefined ||
      body.platformAccountId === undefined
    ) {
      this.logger.error(
        `Received invalid message without organizationId and account id. Body: ${message.Body}`,
      );
      return;
    }
    await this.updateAccountDetailsPostImportCompletion(
      body.organizationId,
      body.platformAccountId,
      body.accountImportData,
    );
  }

  @SqsConsumerEventHandler(
    POST_ACCOUNT_IMPORT_COMPLETION_QUEUE_NAME,
    'processing_error',
  )
  public onProcessingError(error: Error, message: Message) {
    this.logger.error(
      `Processing error on account post import completion message: ${JSON.stringify(
        message.Body,
      )}, error: ${error.message}`,
    );
  }
}
