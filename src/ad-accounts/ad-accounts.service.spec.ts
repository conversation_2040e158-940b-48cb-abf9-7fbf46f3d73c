import { Test, TestingModule } from '@nestjs/testing';
import { AdAccountsService } from './ad-accounts.service';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import { PlatformAdAccountToWorkspace } from './entities/ad-account-workspace-map.entity';
import { PlatformAdAccount } from './entities/ad-account.entity';
import { getEntityManagerToken, getRepositoryToken } from '@nestjs/typeorm';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { AdAccountsProfile } from './mapper/ad-accounts.profile';
import { OrganizationPlatformAdAccountMap } from './entities/organization-platform-ad-account-map.entity';
import { ReadPlatformAdAccountDto } from './dto/read-platform-ad-account.dto';
import { OrganizationProfile } from '../organizations/mapper/organization.profile';
import { Workspace } from '../workspaces/entities/workspace.entity';
import { BadRequestException, Logger, NotFoundException } from '@nestjs/common';
import { WorkspaceService } from '../workspaces/workspaces.service';
import { OrganizationsService } from '../organizations/organizations.service';
import { OrganizationUserRoles } from '../common/constants/constants';
import { PlatformAdAccountSequentialFailure } from './entities/platform-ad-account-sequential-failure.entity';
import { CreatePlatformAdAccountSequentialFailureDto } from './dto/create-platform-ad-account-sequential-failure.dto';
import { WorkspaceAdAccountMap } from '../workspaces/workspace-ad-account/entities/workspace-ad-account-map.entities';
import { UpdatePlatformAdAccountRequestDto } from './dto/update-platform-ad-account-request.dto';
import { UpdateAdAccountImportDataDto } from './dto/update-ad-account-import-data.dto';
import { AdAccountSequentialFailureService } from './ad-account-sequential-failure.service';
import { AdAccountImportStatusService } from './ad-account-import-status.service';
import { SqsService } from '@vidmob/vidmob-nestjs-common';
import { ReadOrganizationDto } from 'src/organizations/dto/read-organization.dto';

const TEST_ORG_ID = 'TEST_ORG_ID';
const TEST_ACCOUNT_ID = 'TEST_ACCOUNT_ID';
const TEST_USER_ID = 123;
const TEST_PLATFORM = 'MOCK';
const TEST_IMPORT_ID = 'TEST_IMPORT_ID';
const mkAccount = (
  id: number,
  platformAccountId: string,
  platform = 'facebook',
  canAccess = 1,
) => {
  const account = new PlatformAdAccount();
  account.id = id;
  account.platform = platform;
  account.platformAccountId = platformAccountId;
  account.canAccess = canAccess;
  return account;
};

describe('AdAccountsService', () => {
  let service: AdAccountsService;
  let orgService: OrganizationsService;
  let workspaceService: WorkspaceService;
  let platformAdAccountToWorkspaceRepository: Repository<
    PlatformAdAccountToWorkspace | PlatformAdAccountToWorkspace[]
  >;
  let platformAdAccountRepository: Repository<PlatformAdAccount>;
  let platformAdAccountSequentialFailureRepository: Repository<PlatformAdAccountSequentialFailure>;
  let entityManager: EntityManager;

  const account1 = mkAccount(1, 'account1');
  const account2 = mkAccount(2, 'account2');
  const account3 = mkAccount(3, 'account3');

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdAccountsService,
        AdAccountSequentialFailureService,
        AdAccountImportStatusService,
        AdAccountsProfile,
        OrganizationProfile,
        {
          provide: WorkspaceService,
          useValue: {
            findWorkspaceById: jest.fn(),
            isNonAdminUserAuthorizedInAllWorkspaces: jest.fn(),
            getNonAdminUserAuthorizedWorkspaces: jest.fn(),
          },
        },
        {
          provide: OrganizationsService,
          useValue: {
            getUserAuthorizedRoleInOrganization: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(PlatformAdAccount),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountToWorkspace),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(OrganizationPlatformAdAccountMap),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountSequentialFailure),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
        {
          provide: getEntityManagerToken(),
          useValue: {
            findBy: jest.fn(),
            findOneBy: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            remove: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: SqsService,
          useValue: {
            send: jest.fn(),
          },
        },
      ],
    }).compile();
    service = module.get<AdAccountsService>(AdAccountsService);
    orgService = module.get<OrganizationsService>(OrganizationsService);
    workspaceService = module.get<WorkspaceService>(WorkspaceService);
    platformAdAccountRepository = module.get<Repository<PlatformAdAccount>>(
      getRepositoryToken(PlatformAdAccount),
    );
    platformAdAccountToWorkspaceRepository = module.get<
      Repository<PlatformAdAccountToWorkspace | PlatformAdAccountToWorkspace[]>
    >(getRepositoryToken(PlatformAdAccountToWorkspace));
    platformAdAccountSequentialFailureRepository = module.get<
      Repository<PlatformAdAccountSequentialFailure>
    >(getRepositoryToken(PlatformAdAccountSequentialFailure));
    entityManager = module.get<EntityManager>(getEntityManagerToken());
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find ad accounts by id', async () => {
    const mockPlatformAdAccount: PlatformAdAccount = new PlatformAdAccount();
    mockPlatformAdAccount.id = 1;
    jest
      .spyOn(platformAdAccountRepository, 'findOneBy')
      .mockResolvedValue(mockPlatformAdAccount);
    const result = await service.findPlatformAdAccountById(1);
    expect(result.id).toEqual(1);
  });

  it('should list ad-accounts for organizations', async () => {
    const createQueryBuilder = {
      innerJoin: () => createQueryBuilder,
      leftJoinAndSelect: () => createQueryBuilder,
      where: () => createQueryBuilder,
      andWhere: () => createQueryBuilder,
      skip: () => createQueryBuilder,
      take: () => createQueryBuilder,
      orderBy: () => createQueryBuilder,
      addOrderBy: () => createQueryBuilder,
      getManyAndCount: () => [[new PlatformAdAccount()], 1],
    } as unknown as jest.Mocked<SelectQueryBuilder<PlatformAdAccount>>;
    platformAdAccountRepository.createQueryBuilder = jest.fn(() => {
      return createQueryBuilder;
    });
    const result = await service.getConnectedAdAccountsForAnOrganization(
      '123',
      1,
      {},
      {
        search: 'test',
      },
    );

    expect(result.totalCount).toEqual(1);
    expect(result.items.length).toEqual(1);
  });

  it('should find ad accounts by ids', async () => {
    const mockPlatformAdAccounts: PlatformAdAccount[] = [1, 2, 3].map((id) => {
      const mockPlatformAdAccount: PlatformAdAccount = new PlatformAdAccount();
      mockPlatformAdAccount.id = id;
      return mockPlatformAdAccount;
    });
    jest
      .spyOn(platformAdAccountRepository, 'findBy')
      .mockResolvedValue(mockPlatformAdAccounts);
    const result = await service.findAdAccounts([1, 2, 3]);
    expect(result.length).toEqual(3);
  });

  it('should find ad accounts by workspace id', async () => {
    platformAdAccountRepository.createQueryBuilder = jest.fn(() => {
      return {
        where: jest.fn().mockReturnThis(),
        innerJoin: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getMany: jest
          .fn()
          .mockResolvedValue([new PlatformAdAccountToWorkspace()]),
        // Add more mock methods if necessary
      } as unknown as jest.Mocked<SelectQueryBuilder<PlatformAdAccount>>;
    });
    const result = (await service.findAdAccountsByWorkspaceId({
      workspaceId: 1,
    })) as ReadPlatformAdAccountDto[];
    expect(result.length).toEqual(1);
  });

  describe('mapWorkspaceToAdAccounts', () => {
    const mockWorkspace: Workspace = new Workspace();
    const mockListOfCreateDtos = [
      { workspaceId: 1, platformAdAccountId: 1 },
      { workspaceId: 1, platformAdAccountId: 2 },
    ];
    mockWorkspace.id = 1;

    it('should map workspaces to ad account if ad accounts exist', async () => {
      jest
        .spyOn(entityManager, 'findBy')
        .mockResolvedValueOnce([account1, account2]);
      jest.spyOn(entityManager, 'save').mockResolvedValueOnce([
        {
          partnerId: 1,
          platformAdAccountId: 1,
          platformAdAccount: account1,
          workspace: mockWorkspace,
        },
        {
          partnerId: 1,
          platformAdAccountId: 2,
          platformAdAccount: account2,
          workspace: mockWorkspace,
        },
      ]);
      const result = await service.createWorkspacePlatformAdAccountConnections(
        entityManager,
        mockListOfCreateDtos,
      );
      expect(result.length).toEqual(2);
    });

    it('should throw an exception if some of the ad accounts do not exist', async () => {
      jest.spyOn(entityManager, 'findBy').mockResolvedValueOnce([account1]);
      await service
        .createWorkspacePlatformAdAccountConnections(
          entityManager,
          mockListOfCreateDtos,
        )
        .catch((e) => expect(e).toBeInstanceOf(NotFoundException));
    });
  });

  describe('deleteWorkspaceToAdAccount', () => {
    const mockMapping: PlatformAdAccountToWorkspace =
      new PlatformAdAccountToWorkspace();
    mockMapping.partnerId = 1;
    mockMapping.platformAdAccountId = 1;
    it('should delete link workspaces to ad accounts', async () => {
      let mockRepo = [mockMapping];
      jest.spyOn(entityManager, 'findBy').mockResolvedValue([mockMapping]);
      jest
        .spyOn(entityManager, 'remove')
        .mockImplementation((platformAdAccounts: any) => {
          const deleteMapping = [mockMapping];
          mockRepo = mockRepo.filter((item) => !deleteMapping.includes(item));
          return Promise.resolve(platformAdAccounts);
        });
      const result = await service.deleteWorkspacePlatformAdAccountConnections(
        entityManager,
        1,
        [1],
      );
      expect(mockRepo.length).toEqual(0);
      expect(result.length).toEqual(1);
    });
    it('should non existing mapping (204)', async () => {
      const mockRepo = [];
      jest.spyOn(entityManager, 'findBy').mockResolvedValue([]);
      jest.spyOn(entityManager, 'remove').mockResolvedValueOnce([]);
      const result = await service.deleteWorkspacePlatformAdAccountConnections(
        entityManager,
        1,
        [1],
      );
      expect(mockRepo.length).toEqual(0);
      expect(result.length).toEqual(0);
    });
  });

  describe('deleteMultipleWorkspacesFromAPlatformAdAccount', () => {
    const mockMapping: PlatformAdAccountToWorkspace =
      new PlatformAdAccountToWorkspace();
    mockMapping.partnerId = 1;
    mockMapping.platformAdAccountId = 1;
    it('should delete link workspaces to ad accounts', async () => {
      let mockRepo = [mockMapping];
      jest
        .spyOn(platformAdAccountToWorkspaceRepository, 'findBy')
        .mockResolvedValue([mockMapping]);
      jest
        .spyOn(platformAdAccountToWorkspaceRepository, 'remove')
        .mockImplementation(
          (platformAdAccounts: PlatformAdAccountToWorkspace) => {
            const deleteMapping = [mockMapping];
            mockRepo = mockRepo.filter((item) => !deleteMapping.includes(item));
            return Promise.resolve(platformAdAccounts);
          },
        );
      const result =
        await service.deleteMultipleWorkspacesConnectionsFromAPlatformAdAccount(
          [1],
          1,
        );
      expect(mockRepo.length).toEqual(0);
      expect(result.length).toEqual(1);
    });
    it('should non existing mapping (204)', async () => {
      const mockRepo = [];
      jest
        .spyOn(platformAdAccountToWorkspaceRepository, 'findBy')
        .mockResolvedValue([]);
      jest
        .spyOn(platformAdAccountToWorkspaceRepository, 'remove')
        .mockResolvedValueOnce([]);
      const result =
        await service.deleteMultipleWorkspacesConnectionsFromAPlatformAdAccount(
          [1],
          1,
        );
      expect(mockRepo.length).toEqual(0);
      expect(result.length).toEqual(0);
    });
  });

  describe('getAdAccountIdsFromPlatformAndPlatformId', () => {
    const mockAccount = mkAccount(1, '123');
    const mockImpl = (account: PlatformAdAccount) => {
      if (
        account.platformAccountId === mockAccount.platformAccountId &&
        account.platform === mockAccount.platform &&
        account.canAccess === mockAccount.canAccess
      ) {
        return Promise.resolve(mockAccount);
      }
      return Promise.resolve(null);
    };
    it('should return ad account ids', async () => {
      jest
        .spyOn(platformAdAccountRepository, 'findOneBy')
        .mockImplementation(mockImpl);
      const result =
        await service.getPlatformAdAccountIdFromPlatformAndPlatformId(
          '123',
          'facebook',
        );
      expect(result).toEqual(1);
    });
    it('should throw an exception if ad account does not exist', () => {
      jest
        .spyOn(platformAdAccountRepository, 'findOneBy')
        .mockImplementation(mockImpl);
      const result = service.getPlatformAdAccountIdFromPlatformAndPlatformId(
        '456',
        'facebook',
      );

      return expect(result).rejects.toThrowError(NotFoundException);
    });
    it('should throw an exception if ad account can not be accessed', () => {
      mockAccount.canAccess = 0;
      jest
        .spyOn(platformAdAccountRepository, 'findOneBy')
        .mockImplementation(mockImpl);
      const result = service.getPlatformAdAccountIdFromPlatformAndPlatformId(
        '123',
        'facebook',
      );

      return expect(result).rejects.toThrowError(NotFoundException);
    });
  });

  describe('getExistingAdAccountsIdsMappedToWorkspace', () => {
    const mockAdAccountWorkspaceMap: PlatformAdAccountToWorkspace =
      new PlatformAdAccountToWorkspace();
    mockAdAccountWorkspaceMap.partnerId = 1;
    mockAdAccountWorkspaceMap.platformAdAccountId = 1;

    it('should return all ad accounts ids mapped to workspace', async () => {
      jest
        .spyOn(entityManager, 'findBy')
        .mockResolvedValue([mockAdAccountWorkspaceMap]);
      const result = await service.getPlatformAdAccountIdsConnectedToAWorkspace(
        entityManager,
        1,
        [1],
      );
      expect(result).toEqual([1]);
    });

    it('should return empty array if no ad accounts ids mapped to workspace', async () => {
      jest.spyOn(entityManager, 'findBy').mockResolvedValue([]);
      const result = await service.getPlatformAdAccountIdsConnectedToAWorkspace(
        entityManager,
        1,
        [1],
      );
      expect(result).toEqual([]);
    });
  });

  describe('validateUserAccountsWithinOrganizationWorkspaces', () => {
    it('Not all accounts are in workspaces', async () => {
      jest.spyOn(platformAdAccountRepository, 'count').mockResolvedValue(1);
      jest
        .spyOn(orgService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce(OrganizationUserRoles.ORG_ADMIN);
      const result =
        await service.validateUserAccountsWithinOrganizationWorkspaces(
          'TEST_ORG_ID',
          123,
          ['account1', 'account2'],
          [1, 2, 3],
        );
      expect(result.success).toBe(false);
      expect(result.message).toContain(
        'One or more accounts in the request are not in the organization workspaces.',
      );
    });

    it('All accounts are in workspaces and user is an org admin', async () => {
      jest.spyOn(platformAdAccountRepository, 'count').mockResolvedValue(2);
      jest
        .spyOn(orgService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce(OrganizationUserRoles.ORG_ADMIN);

      const result =
        await service.validateUserAccountsWithinOrganizationWorkspaces(
          'TEST_ORG_ID',
          123,
          ['account1', 'account2'],
          [1, 2, 3],
        );
      expect(result.success).toBe(true);
      expect(result.message).toContain(
        'User 123 has access to the accounts in the organization TEST_ORG_ID.',
      );
      expect(
        orgService.getUserAuthorizedRoleInOrganization,
      ).toHaveBeenCalledTimes(1);
    });

    it('All accounts are in workspaces and user is an org standard user with access to all workspaces', async () => {
      jest.spyOn(platformAdAccountRepository, 'count').mockResolvedValue(2);
      jest
        .spyOn(orgService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce(OrganizationUserRoles.ORG_STANDARD);
      jest
        .spyOn(workspaceService, 'isNonAdminUserAuthorizedInAllWorkspaces')
        .mockResolvedValue(true);

      const result =
        await service.validateUserAccountsWithinOrganizationWorkspaces(
          'TEST_ORG_ID',
          123,
          ['account1', 'account2'],
          [1, 2, 3],
        );
      expect(result.success).toBe(true);
      expect(result.message).toContain(
        'User 123 has access to the accounts in the 3 workspaces.',
      );
    });

    it('All accounts are in workspaces and user is an org standard user with access to few workspaces', async () => {
      jest.spyOn(platformAdAccountRepository, 'count').mockResolvedValue(2);
      jest
        .spyOn(orgService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce(OrganizationUserRoles.ORG_STANDARD);
      jest
        .spyOn(workspaceService, 'isNonAdminUserAuthorizedInAllWorkspaces')
        .mockResolvedValue(false);

      const result =
        await service.validateUserAccountsWithinOrganizationWorkspaces(
          'TEST_ORG_ID',
          123,
          ['account1', 'account2'],
          [1, 2, 3],
        );
      expect(result.success).toBe(false);
      expect(result.message).toContain(
        'User 123 does not have access to the accounts in one or more of the 3 workspaces.',
      );
    });
  });

  describe('validateUserAccountsWithinOrganization', () => {
    it('should validate admin user accounts within organization', async () => {
      jest
        .spyOn(platformAdAccountToWorkspaceRepository, 'find')
        .mockResolvedValue([
          {
            partnerId: 1,
            platformAdAccountId: account1.id,
            platformAdAccount: account1,
            workspace: new Workspace(),
          },
          {
            partnerId: 2,
            platformAdAccountId: account2.id,
            platformAdAccount: account2,
            workspace: new Workspace(),
          },
        ]);
      jest
        .spyOn(orgService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce(OrganizationUserRoles.ORG_ADMIN);

      const result = await service.validateUserAccountsWithinOrganization(
        'TEST_ORG_ID',
        123,
        ['account1', 'account2'],
      );
      expect(result.success).toBe(true);
      expect(result.message).toContain(
        'User 123 has access to all accounts in the organization TEST_ORG_ID.',
      );
    });

    it('should not validate admin user when missing accounts in organization', async () => {
      jest
        .spyOn(platformAdAccountToWorkspaceRepository, 'find')
        .mockResolvedValue([
          {
            partnerId: 1,
            platformAdAccountId: account1.id,
            platformAdAccount: account1,
            workspace: new Workspace(),
          },
        ]);
      jest
        .spyOn(orgService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce(OrganizationUserRoles.ORG_ADMIN);

      const result = await service.validateUserAccountsWithinOrganization(
        'TEST_ORG_ID',
        123,
        ['account1', 'account2'],
      );
      expect(result.success).toBe(false);
      expect(result.message).toContain(
        'One or more accounts in the request are not in the organization and mapped to a workspace.',
      );
    });

    it('should validate standard user accounts within organization', async () => {
      jest
        .spyOn(platformAdAccountToWorkspaceRepository, 'find')
        .mockResolvedValue([
          {
            partnerId: 1,
            platformAdAccountId: account1.id,
            platformAdAccount: account1,
            workspace: new Workspace(),
          },
          {
            partnerId: 2,
            platformAdAccountId: account2.id,
            platformAdAccount: account2,
            workspace: new Workspace(),
          },
        ]);
      jest
        .spyOn(orgService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce(OrganizationUserRoles.ORG_STANDARD);

      jest
        .spyOn(workspaceService, 'getNonAdminUserAuthorizedWorkspaces')
        .mockResolvedValueOnce([1, 2]);

      const result = await service.validateUserAccountsWithinOrganization(
        'TEST_ORG_ID',
        123,
        ['account1', 'account2'],
      );
      expect(result.success).toBe(true);
      expect(result.message).toContain('User 123 has access to the accounts.');
    });

    it('should validate standard user accounts in multiple workspaces within organization', async () => {
      jest
        .spyOn(platformAdAccountToWorkspaceRepository, 'find')
        .mockResolvedValue([
          {
            partnerId: 1,
            platformAdAccountId: account1.id,
            platformAdAccount: account1,
            workspace: new Workspace(),
          },
          {
            partnerId: 2,
            platformAdAccountId: account1.id,
            platformAdAccount: account1,
            workspace: new Workspace(),
          },
          {
            partnerId: 3,
            platformAdAccountId: account1.id,
            platformAdAccount: account1,
            workspace: new Workspace(),
          },
          {
            partnerId: 1,
            platformAdAccountId: account2.id,
            platformAdAccount: account2,
            workspace: new Workspace(),
          },
          {
            partnerId: 2,
            platformAdAccountId: account2.id,
            platformAdAccount: account2,
            workspace: new Workspace(),
          },
        ]);
      jest
        .spyOn(orgService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce(OrganizationUserRoles.ORG_STANDARD);

      jest
        .spyOn(workspaceService, 'getNonAdminUserAuthorizedWorkspaces')
        .mockResolvedValueOnce([1]);

      const result = await service.validateUserAccountsWithinOrganization(
        'TEST_ORG_ID',
        123,
        ['account1', 'account2'],
      );
      expect(result.success).toBe(true);
      expect(result.message).toContain('User 123 has access to the accounts.');
    });

    it('should not validate standard user when missing accounts in organization', async () => {
      jest
        .spyOn(platformAdAccountToWorkspaceRepository, 'find')
        .mockResolvedValue([
          {
            partnerId: 1,
            platformAdAccountId: account1.id,
            platformAdAccount: account1,
            workspace: new Workspace(),
          },
        ]);
      jest
        .spyOn(orgService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce(OrganizationUserRoles.ORG_STANDARD);

      jest
        .spyOn(workspaceService, 'getNonAdminUserAuthorizedWorkspaces')
        .mockResolvedValueOnce([1]);

      const result = await service.validateUserAccountsWithinOrganization(
        'TEST_ORG_ID',
        123,
        ['account1', 'account2'],
      );
      expect(result.success).toBe(false);
      expect(result.message).toContain(
        'One or more accounts in the request are not in the organization and mapped to a workspace.',
      );
    });

    it('should not validate standard user when non-accessible accounts in organization', async () => {
      jest
        .spyOn(platformAdAccountToWorkspaceRepository, 'find')
        .mockResolvedValue([
          {
            partnerId: 1,
            platformAdAccountId: account1.id,
            platformAdAccount: account1,
            workspace: new Workspace(),
          },
          {
            partnerId: 2,
            platformAdAccountId: account2.id,
            platformAdAccount: account2,
            workspace: new Workspace(),
          },
        ]);
      jest
        .spyOn(orgService, 'getUserAuthorizedRoleInOrganization')
        .mockResolvedValueOnce(OrganizationUserRoles.ORG_STANDARD);

      // user only has access to workspace 1, but account 2 is in workspace 2
      jest
        .spyOn(workspaceService, 'getNonAdminUserAuthorizedWorkspaces')
        .mockResolvedValueOnce([1]);

      const result = await service.validateUserAccountsWithinOrganization(
        'TEST_ORG_ID',
        123,
        ['account1', 'account2'],
      );
      expect(result.success).toBe(false);
      expect(result.message).toContain(
        'User 123 does not have access to a workspace of the accounts.',
      );
    });
  });

  describe('validateWorkspacesUserAccessible', () => {
    it('should validate user access to workspaces', async () => {
      const userWorkspaces = [1, 2];

      const workspaceAccountMap: PlatformAdAccountToWorkspace[] = [
        {
          partnerId: 1,
          platformAdAccountId: account1.id,
          platformAdAccount: account1,
          workspace: new Workspace(),
        },
        {
          partnerId: 2,
          platformAdAccountId: account2.id,
          platformAdAccount: account2,
          workspace: new Workspace(),
        },
      ];

      const result = service.validateWorkspacesUserAccessible(
        userWorkspaces,
        workspaceAccountMap,
      );

      expect(result).toBe(true);
    });

    it('should not validate user access to workspaces', async () => {
      const userWorkspaces = [1];

      const workspaceAccountMap: PlatformAdAccountToWorkspace[] = [
        {
          partnerId: 1,
          platformAdAccountId: account1.id,
          platformAdAccount: account1,
          workspace: new Workspace(),
        },
        {
          partnerId: 2,
          platformAdAccountId: account2.id,
          platformAdAccount: account2,
          workspace: new Workspace(),
        },
      ];

      const result = service.validateWorkspacesUserAccessible(
        userWorkspaces,
        workspaceAccountMap,
      );

      expect(result).toBe(false);
    });

    it('should validate user access to workspaces with big list', async () => {
      const userWorkspaces = [1, 2, 3];

      const workspaceAccountMap: PlatformAdAccountToWorkspace[] = [
        {
          partnerId: 1,
          platformAdAccountId: account1.id,
          platformAdAccount: account1,
          workspace: new Workspace(),
        },
        {
          partnerId: 2,
          platformAdAccountId: account1.id,
          platformAdAccount: account1,
          workspace: new Workspace(),
        },
        {
          partnerId: 3,
          platformAdAccountId: account1.id,
          platformAdAccount: account1,
          workspace: new Workspace(),
        },
        {
          partnerId: 2,
          platformAdAccountId: account2.id,
          platformAdAccount: account2,
          workspace: new Workspace(),
        },
        {
          partnerId: 2,
          platformAdAccountId: account3.id,
          platformAdAccount: account3,
          workspace: new Workspace(),
        },
        {
          partnerId: 3,
          platformAdAccountId: account3.id,
          platformAdAccount: account3,
          workspace: new Workspace(),
        },
        {
          partnerId: 4,
          platformAdAccountId: account3.id,
          platformAdAccount: account3,
          workspace: new Workspace(),
        },
        {
          partnerId: 5,
          platformAdAccountId: account3.id,
          platformAdAccount: account3,
          workspace: new Workspace(),
        },
      ];

      const result = service.validateWorkspacesUserAccessible(
        userWorkspaces,
        workspaceAccountMap,
      );

      expect(result).toBe(true);
    });

    it('should not validate user access to workspaces with big list', async () => {
      const userWorkspaces = [3, 4];

      const workspaceAccountMap: PlatformAdAccountToWorkspace[] = [
        {
          partnerId: 1,
          platformAdAccountId: account1.id,
          platformAdAccount: account1,
          workspace: new Workspace(),
        },
        {
          partnerId: 2,
          platformAdAccountId: account1.id,
          platformAdAccount: account1,
          workspace: new Workspace(),
        },
        {
          partnerId: 3,
          platformAdAccountId: account1.id,
          platformAdAccount: account1,
          workspace: new Workspace(),
        },
        {
          partnerId: 2,
          platformAdAccountId: account2.id,
          platformAdAccount: account2,
          workspace: new Workspace(),
        },
        {
          partnerId: 2,
          platformAdAccountId: account3.id,
          platformAdAccount: account3,
          workspace: new Workspace(),
        },
        {
          partnerId: 3,
          platformAdAccountId: account3.id,
          platformAdAccount: account3,
          workspace: new Workspace(),
        },
        {
          partnerId: 4,
          platformAdAccountId: account3.id,
          platformAdAccount: account3,
          workspace: new Workspace(),
        },
        {
          partnerId: 5,
          platformAdAccountId: account3.id,
          platformAdAccount: account3,
          workspace: new Workspace(),
        },
      ];

      const result = service.validateWorkspacesUserAccessible(
        userWorkspaces,
        workspaceAccountMap,
      );

      expect(result).toBe(false);
    });
  });

  describe('Test getLinkedWorkspaceCountInOrganization method', () => {
    it('when account has no workspaces in the org', async () => {
      const account = {
        id: 1,
        workspaceAdAccountMap: [],
      } as PlatformAdAccount;

      const linkedWorkspaceCount =
        service.getLinkedWorkspaceCountInOrganization(TEST_ORG_ID, account);
      expect(linkedWorkspaceCount).toEqual(0);
    });

    it('when account has 2 workspaces in the org', async () => {
      const account = {
        id: 1,
        workspaceAdAccountMap: [
          {
            workspace: {
              organizationId: TEST_ORG_ID,
            },
            workspaceId: 1,
          },
          {
            workspace: {
              organizationId: TEST_ORG_ID,
            },
            workspaceId: 2,
          },
        ],
      } as PlatformAdAccount;

      const linkedWorkspaceCount =
        service.getLinkedWorkspaceCountInOrganization(TEST_ORG_ID, account);
      expect(linkedWorkspaceCount).toEqual(2);
    });

    it('when account has 1 workspace in current org and other org workspaces', async () => {
      const account = {
        id: 1,
        workspaceAdAccountMap: [
          {
            workspace: {
              organizationId: TEST_ORG_ID,
            },
            workspaceId: 1,
          },
          {
            workspace: {
              organizationId: 'OTHER_ORG_ID',
            },
            workspaceId: 2,
          },
        ],
      } as PlatformAdAccount;

      const linkedWorkspaceCount =
        service.getLinkedWorkspaceCountInOrganization(TEST_ORG_ID, account);
      expect(linkedWorkspaceCount).toEqual(1);
    });
  });

  describe('getPlatformAdAccountWithImportData', () => {
    const ACCOUNT_ID = '123456';
    const PLATFORM = 'MOCK';

    it('when valid data is returned', async () => {
      const mockPlatformAdAccount = mkAccount(1, ACCOUNT_ID, PLATFORM);
      mockPlatformAdAccount.userId = 2345;
      mockPlatformAdAccount.platformUserId = '23456';
      mockPlatformAdAccount.platformAccountName = 'Test Account';
      mockPlatformAdAccount.processingCompleted = 1;
      mockPlatformAdAccount.platformAdAccountSequentialFailures = [
        new PlatformAdAccountSequentialFailure(),
        new PlatformAdAccountSequentialFailure(),
      ];
      mockPlatformAdAccount.workspaceAdAccountMap = [
        {
          workspace: { organizationId: TEST_ORG_ID },
          workspaceId: 1,
        } as WorkspaceAdAccountMap,
        {
          workspace: { organizationId: TEST_ORG_ID },
          workspaceId: 2,
        } as WorkspaceAdAccountMap,
      ];

      const organization = {
        id: TEST_ORG_ID,
        spendEnabled: false,
      } as ReadOrganizationDto;
      jest
        .spyOn(platformAdAccountRepository, 'findOne')
        .mockResolvedValue(mockPlatformAdAccount);
      jest.spyOn(orgService, 'findOne').mockResolvedValue(organization);

      const result = service.getPlatformAdAccountWithImportData(
        TEST_ORG_ID,
        PLATFORM,
        ACCOUNT_ID,
      );
      await expect(result).resolves.toEqual({
        id: 1,
        userId: 2345,
        platform: PLATFORM,
        platformUserId: '23456',
        platformAccountId: ACCOUNT_ID,
        platformAccountName: 'Test Account',
        processingCompleted: 1,
        failuresCount: 2,
        linkedWorkspacesInOrg: 2,
        spendEnabled: false,
      });
    });

    it('when account data is not available', async () => {
      jest
        .spyOn(platformAdAccountRepository, 'findOne')
        .mockResolvedValue(null);
      const result = service.getPlatformAdAccountWithImportData(
        TEST_ORG_ID,
        PLATFORM,
        ACCOUNT_ID,
      );
      await expect(result).rejects.toThrowError(
        `Ad account with id ${ACCOUNT_ID} not found for platform ${PLATFORM}.`,
      );
    });
  });

  describe('savePlatformAdAccountSequentialFailure', () => {
    it('when the data saved correctly', async () => {
      const mockAccount = new PlatformAdAccount();
      const mockSequentialFailure: PlatformAdAccountSequentialFailure = {
        importId: '1234',
        dateCreated: expect.any(Date),
        lastUpdated: expect.any(Date),
        platformAdAccount: mockAccount,
      } as PlatformAdAccountSequentialFailure;
      jest
        .spyOn(platformAdAccountRepository, 'findOneBy')
        .mockResolvedValue(mockAccount);
      const saveSpy = jest
        .spyOn(platformAdAccountSequentialFailureRepository, 'save')
        .mockResolvedValue(mockSequentialFailure);
      await service.savePlatformAdAccountSequentialFailure('123456', {
        importId: '1234',
      } as CreatePlatformAdAccountSequentialFailureDto);
      expect(saveSpy).toHaveBeenCalledWith(mockSequentialFailure);
    });
  });

  describe('validateAccountUpdateRequestParameters', () => {
    describe('Missing required identifiers', () => {
      it('throws when organizationId is missing', () => {
        expect(() =>
          service.validateAccountUpdateRequestParameters(
            '',
            TEST_PLATFORM,
            TEST_ACCOUNT_ID,
            {},
          ),
        ).toThrow(BadRequestException);
      });

      it('throws when platform is missing', () => {
        expect(() =>
          service.validateAccountUpdateRequestParameters(
            TEST_ORG_ID,
            '',
            TEST_ACCOUNT_ID,
            {},
          ),
        ).toThrow(BadRequestException);
      });

      it('throws when adAccountId is missing', () => {
        expect(() =>
          service.validateAccountUpdateRequestParameters(
            TEST_ORG_ID,
            TEST_PLATFORM,
            '',
            {},
          ),
        ).toThrow(BadRequestException);
      });
    });

    describe('Missing update fields', () => {
      it('throws when no update fields are provided', () => {
        expect(() =>
          service.validateAccountUpdateRequestParameters(
            TEST_ORG_ID,
            TEST_PLATFORM,
            TEST_ACCOUNT_ID,
            {},
          ),
        ).toThrow(BadRequestException);
      });
    });

    describe('Empty accountImportData object', () => {
      it('throws when accountImportData is present but all fields are undefined', () => {
        expect(() =>
          service.validateAccountUpdateRequestParameters(
            TEST_ORG_ID,
            TEST_PLATFORM,
            TEST_ACCOUNT_ID,
            {
              accountImportData: {} as UpdateAdAccountImportDataDto,
            },
          ),
        ).toThrow(BadRequestException);
      });
    });

    describe('Valid cases', () => {
      it('does not throw when processingCompleted is present', () => {
        expect(() =>
          service.validateAccountUpdateRequestParameters(
            TEST_ORG_ID,
            TEST_PLATFORM,
            TEST_ACCOUNT_ID,
            { processingCompleted: false },
          ),
        ).not.toThrow();
      });

      it('does not throw when importV3Enabled is present', () => {
        expect(() =>
          service.validateAccountUpdateRequestParameters(
            TEST_ORG_ID,
            TEST_PLATFORM,
            TEST_ACCOUNT_ID,
            { importV3Enabled: true },
          ),
        ).not.toThrow();
      });

      it('does not throw when any accountImportData field is provided', () => {
        expect(() =>
          service.validateAccountUpdateRequestParameters(
            TEST_ORG_ID,
            TEST_PLATFORM,
            TEST_ACCOUNT_ID,
            {
              accountImportData: {
                importId: TEST_IMPORT_ID,
                importStatus: 'STARTED',
              },
            },
          ),
        ).not.toThrow();
      });
    });
  });

  describe('PlatformAdAccount update detection', () => {
    let account1: PlatformAdAccount;
    beforeEach(() => {
      account1 = mkAccount(1, TEST_ACCOUNT_ID);
      account1.userId = TEST_USER_ID;
      account1.importV3Enabled = 0;
      account1.processingCompleted = 0;
      account1.processingCompletedDate = new Date();
      account1.lastImportStatus = 'SUCCESS';
      account1.lastImportStartDate = new Date('2023-01-01T10:00:00.000Z');
      account1.lastImportCompleteDate = new Date('2023-01-01T12:00:00.000Z');
      account1.lastImportSuccessDate = null;
    });

    describe('isImportV3EnabledUpdated', () => {
      it('returns false if importV3Enabled is undefined', () => {
        expect(service['isImportV3EnabledUpdated']({}, account1)).toBe(false);
      });

      it('returns false if importV3Enabled matches db value', () => {
        expect(
          service['isImportV3EnabledUpdated'](
            { importV3Enabled: false },
            account1,
          ),
        ).toBe(false);
      });

      it('returns true if importV3Enabled differs from db', () => {
        expect(
          service['isImportV3EnabledUpdated'](
            { importV3Enabled: true },
            account1,
          ),
        ).toBe(true);
      });
    });

    describe('isProcessingCompletedUpdated', () => {
      it('returns false if processingCompleted is undefined', () => {
        expect(service['isProcessingCompletedUpdated']({}, account1)).toBe(
          false,
        );
      });

      it('returns false if value matches and date is present', () => {
        account1.processingCompleted = 1;
        account1.processingCompletedDate = new Date();
        expect(
          service['isProcessingCompletedUpdated'](
            { processingCompleted: true },
            account1,
          ),
        ).toBe(false);
      });

      it('returns true if value is different', () => {
        account1.processingCompleted = 0;
        expect(
          service['isProcessingCompletedUpdated'](
            { processingCompleted: true },
            account1,
          ),
        ).toBe(true);
      });

      it('returns true if value is true and processingCompletedDate is null', () => {
        account1.processingCompleted = 1;
        account1.processingCompletedDate = null;
        expect(
          service['isProcessingCompletedUpdated'](
            { processingCompleted: true },
            account1,
          ),
        ).toBe(true);
      });
    });

    describe('isAccountImportInformationUpdated', () => {
      it('returns false if accountImportData is undefined', () => {
        expect(service['isAccountImportInformationUpdated']({}, account1)).toBe(
          false,
        );
      });

      it('returns false if all fields match', () => {
        const dto: UpdatePlatformAdAccountRequestDto = {
          accountImportData: {
            userId: TEST_USER_ID,
            importId: TEST_IMPORT_ID,
            importStatus: 'SUCCESS',
            importStartDate: new Date('2023-01-01T10:00:00.000Z'),
            importCompleteDate: new Date('2023-01-01T12:00:00.000Z'),
            importSuccessDate: null,
          },
        };
        expect(
          service['isAccountImportInformationUpdated'](dto, account1),
        ).toBe(false);
      });

      it('returns true if any field differs', () => {
        const dto: UpdatePlatformAdAccountRequestDto = {
          accountImportData: {
            importId: TEST_IMPORT_ID,
            userId: 234,
          },
        };
        expect(
          service['isAccountImportInformationUpdated'](dto, account1),
        ).toBe(true);
      });
    });

    describe('isPlatformAdAccountUpdated', () => {
      it('returns false when nothing is updated', () => {
        const dto: UpdatePlatformAdAccountRequestDto = {};
        expect(service.isPlatformAdAccountUpdated(dto, account1)).toBe(false);
      });

      it('returns true when importV3Enabled is updated', () => {
        expect(
          service.isPlatformAdAccountUpdated(
            { importV3Enabled: true },
            account1,
          ),
        ).toBe(true);
      });

      it('returns true when processingCompleted is updated', () => {
        expect(
          service.isPlatformAdAccountUpdated(
            { processingCompleted: true },
            account1,
          ),
        ).toBe(true);
      });

      it('returns true when accountImportData changes', () => {
        const dto: UpdatePlatformAdAccountRequestDto = {
          accountImportData: { importId: TEST_IMPORT_ID, userId: 2 },
        };
        expect(service.isPlatformAdAccountUpdated(dto, account1)).toBe(true);
      });
    });
  });

  describe('Test isAccountNotImporting method', () => {
    it('test method when accountImportData is not present', async () => {
      expect(service.isAccountNotImporting({})).toEqual(false);
    });
    it('test method when accountImportData present with import completion fields', async () => {
      expect(
        service.isAccountNotImporting({
          rebuildRequestId: 165715,
          importCompleteDate: '2025-04-14T21:28:02.000Z',
          importSuccessDate: '2025-04-14T21:28:02.000Z',
          importStatus: 'SUCCESS',
        } as unknown as UpdateAdAccountImportDataDto),
      ).toEqual(false);
    });
    it('test method when accountImportData present with import completion fields', async () => {
      expect(
        service.isAccountNotImporting({
          importSkipReason: 'Ad account 1234 not connected to workspace.',
        } as unknown as UpdateAdAccountImportDataDto),
      ).toEqual(true);
    });
  });

  describe('Test handlePostImportCompletionUpdateMessage method', () => {
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();

    it('should log and return if both organizationId and platformAccountId are missing', async () => {
      const message = {
        Body: JSON.stringify({}),
      };
      const postImportCompletionSpy = jest.spyOn(
        service,
        'updateAccountDetailsPostImportCompletion',
      );

      await service.handlePostImportCompletionUpdateMessage(message as any);
      expect(postImportCompletionSpy).toBeCalledTimes(0);
    });

    it('should parse body and call updateAccountDetailsPostImportCompletion with correct args', async () => {
      const dto = {
        organizationId: TEST_ORG_ID,
        platformAccountId: TEST_ACCOUNT_ID,
        accountImportData: {},
      };
      const message = {
        Body: JSON.stringify(dto),
      };
      const createQueryBuilder = {
        leftJoinAndSelect: () => createQueryBuilder,
        where: () => createQueryBuilder,
        getOne: () => new PlatformAdAccount(),
      } as unknown as jest.Mocked<SelectQueryBuilder<PlatformAdAccount>>;
      platformAdAccountRepository.createQueryBuilder = jest.fn(() => {
        return createQueryBuilder;
      });
      const postImportCompletionSpy = jest.spyOn(
        service,
        'updateAccountDetailsPostImportCompletion',
      );

      await service.handlePostImportCompletionUpdateMessage(message as any);
      expect(postImportCompletionSpy).toHaveBeenCalledWith(
        TEST_ORG_ID,
        TEST_ACCOUNT_ID,
        dto.accountImportData,
      );
    });
  });
});
