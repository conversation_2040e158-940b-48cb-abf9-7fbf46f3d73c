import { Permission, Status } from '../common/constants/constants';
import { OrganizationPlatformAdAccountMap } from './entities/organization-platform-ad-account-map.entity';
import { User } from '../entities/user.entity';
import { PlatformAdAccount } from './entities/ad-account.entity';
import { OrganizationAdAccountsService } from './organization-ad-accounts.service';
import { <PERSON><PERSON>tyManager, QueryRunner, Repository } from 'typeorm';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AdAccountsService } from './ad-accounts.service';
import { createMapper } from '@automapper/core';
import { getMapperToken } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { OrganizationProfile } from '../organizations/mapper/organization.profile';
import { OrganizationAdAccountUserPermissions } from './entities/organization-ad-account-user-permissions.entity';
import { OrganizationAdAccountUserPermissionsProfile } from './mapper/organization-ad-account-user-permissions.profile';
import { Organization } from '../organizations/entities/organization.entity';
import { DEFAULT_SESSION_IDLE_TIMEOUT_MIN } from '../organizations/utils/constants';
import {
  mockAdAccount2,
  mockOrganization,
  mockWorkspaceAdAccountMap,
} from './organization-ad-accounts.mock';
import { PlatformAdAccountBrandMap } from './entities/platform-ad-account-brand-map.entity';
import { AdAccountsProfile } from './mapper/ad-accounts.profile';
import { PlatformAdAccountMarketMap } from './entities/platform-ad-account-market-map.entity';
import { ReadAdAccountMapDto } from './dto/read-ad-account-map.dto';
import { BrandService } from 'src/brands/brand.service';
import { Brand } from 'src/brands/entities/brand.entity';
import { MarketService } from 'src/markets/market.service';
import { Market } from 'src/markets/entities/market.entity';
import { PlatformAdAccountIndustryMap } from './entities/platform-ad-account-industry-map.entity';
import { ReadPlatformOrganizationUsersMapDto } from './dto/read-platform-organization-users-map.dto';
import { AdAccountImportStatusService } from './ad-account-import-status.service';
import { SqsService } from '@vidmob/vidmob-nestjs-common';
import { Logger } from '@nestjs/common';

const TEST_USER_ID = 1234;
const TEST_ACCOUNT_ID = 'act_1234567890';
const TEST_ACCOUNT_ID_2 = 'act_1234567891';
const TEST_ORG_ID = '1754c0a9-c620-4d34-8243-9c8b49cbcaa6';
const TEST_PLATFORM = 'facebook';

const getMockAccount = () => {
  const platformAdAccount: PlatformAdAccount = new PlatformAdAccount();
  platformAdAccount.platformAccountId = TEST_ACCOUNT_ID;
  return platformAdAccount;
};

const getMockAccount2 = () => {
  const platformAdAccount: PlatformAdAccount = new PlatformAdAccount();
  platformAdAccount.platformAccountId = TEST_ACCOUNT_ID_2;
  return platformAdAccount;
};

const mockOrganizationPlatformAdAccountMap =
  (): OrganizationPlatformAdAccountMap => {
    return {
      organizationId: TEST_ORG_ID,
      platformAdAccountId: 123,
      platformAdAccount: getMockAccount(),
      organization: {
        id: TEST_ORG_ID,
        name: 'test org',
        status: Status.ENABLED,
        dateCreated: new Date(),
        lastUpdated: new Date(),
        workspaces: [],
        isPersonal: false,
        sessionIdleTimeoutMin: DEFAULT_SESSION_IDLE_TIMEOUT_MIN,
      },
    };
  };

const TEST_ORGANIZATION_AD_ACCOUNT_USER_PERMISSIONS: OrganizationAdAccountUserPermissions =
  {
    organizationId: TEST_ORG_ID,
    platformAdAccountId: 1,
    userId: TEST_USER_ID,
    permission: Permission.ALLOW,
    dateCreated: new Date(),
    lastUpdated: new Date(),
    dateLastConnected: new Date(),
    accountAccessibleByUser: true,
    platformAdAccount: getMockAccount(),
    organization: new Organization(),
    user: new User(),
  };

const TEST_ORGANIZATION_AD_ACCOUNT_USER_PERMISSIONS_2: OrganizationAdAccountUserPermissions =
  {
    organizationId: TEST_ORG_ID,
    platformAdAccountId: 2,
    userId: TEST_USER_ID,
    permission: Permission.ALLOW,
    dateCreated: new Date(),
    lastUpdated: new Date(),
    dateLastConnected: null,
    accountAccessibleByUser: false,
    platformAdAccount: getMockAccount2(),
    organization: new Organization(),
    user: new User(),
  };

const createQueryBuilder: any = {
  insert: () => createQueryBuilder,
  into: () => createQueryBuilder,
  values: () => createQueryBuilder,
  orIgnore: () => createQueryBuilder,
  execute: () => createQueryBuilder,
  innerJoin: () => createQueryBuilder,
  where: () => createQueryBuilder,
  andWhere: () => createQueryBuilder,
  orWhere: () => createQueryBuilder,
  delete: () => createQueryBuilder,
  from: () => createQueryBuilder,
  getMany: () => jest.fn(),
  select: () => createQueryBuilder,
};

const mockEntityManager = {
  save: jest.fn(),
  delete: jest.fn(),
  remove: jest.fn(),
} as Partial<EntityManager>;

const mockedQueryRunner: Partial<QueryRunner> = {
  connect: jest.fn(),
  startTransaction: jest.fn(),
  commitTransaction: jest.fn(),
  rollbackTransaction: jest.fn(),
  release: jest.fn(),
  manager: mockEntityManager as EntityManager,
};

describe('OrganizationAdAccountsService', () => {
  const mockGetAdAccountsIdsFromPlatformAccountIds = jest.fn();
  let organizationPlatformAdAccountMapRepository: Repository<OrganizationPlatformAdAccountMap>;
  let organizationAdAccountUserPermissionsRepository: Repository<OrganizationAdAccountUserPermissions>;
  let platformAdAccountIndustryMapRepository: Repository<PlatformAdAccountIndustryMap>;
  let service: OrganizationAdAccountsService;
  let adAccountsService: AdAccountsService;
  let brandService: BrandService;
  let marketService: MarketService;
  let platformAdAccountBrandMapRepo: Repository<PlatformAdAccountBrandMap>;
  let platformAdAccountMarketMapRepo: Repository<PlatformAdAccountMarketMap>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationAdAccountsService,
        AdAccountsService,
        OrganizationProfile,
        AdAccountsProfile,
        OrganizationAdAccountUserPermissionsProfile,
        {
          provide: AdAccountsService,
          useFactory: () => ({
            findPlatformAdAccountByAccountId: jest
              .fn()
              .mockReturnValue(new PlatformAdAccount()),
            getAdAccountsIdsFromPlatformAccountIds:
              mockGetAdAccountsIdsFromPlatformAccountIds,
            deleteMultipleWorkspacesConnectionsFromAPlatformAdAccount:
              jest.fn(),
            updateAccountWithNotImportingStatus: jest.fn(),
          }),
        },
        {
          provide: AdAccountImportStatusService,
          useFactory: () => ({
            createImportStatus: jest.fn(),
          }),
        },
        {
          provide: BrandService,
          useFactory: () => ({
            findBrandsByPlatformAccountId: jest.fn(),
          }),
        },
        {
          provide: MarketService,
          useFactory: () => ({
            findMarketsByPlatformAccountId: jest.fn(),
          }),
        },
        {
          provide: getRepositoryToken(OrganizationPlatformAdAccountMap),
          useValue: {
            queryRunner: mockedQueryRunner,
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            manager: {
              transaction: jest
                .fn()
                .mockImplementation(async (cb) => cb(mockEntityManager)),
            },
          },
        },
        {
          provide: getRepositoryToken(PlatformAdAccountBrandMap),
          useValue: {
            queryRunner: mockedQueryRunner,
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(PlatformAdAccountMarketMap),
          useValue: {
            queryRunner: mockedQueryRunner,
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(PlatformAdAccountIndustryMap),
          useValue: {
            queryRunner: mockedQueryRunner,
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(OrganizationAdAccountUserPermissions),
          useFactory: () => ({
            find: jest
              .fn()
              .mockResolvedValue([
                TEST_ORGANIZATION_AD_ACCOUNT_USER_PERMISSIONS,
                TEST_ORGANIZATION_AD_ACCOUNT_USER_PERMISSIONS_2,
              ]),
            findOne: jest
              .fn()
              .mockResolvedValue(TEST_ORGANIZATION_AD_ACCOUNT_USER_PERMISSIONS),
            save: jest.fn(),
            update: jest.fn(),
            count: jest.fn().mockReturnValue(1),
            createQueryBuilder: jest.fn().mockReturnValue(createQueryBuilder),
            manager: {
              transaction: jest
                .fn()
                .mockImplementation(async (cb) => cb(mockEntityManager)),
            },
          }),
        },
        {
          provide: SqsService,
          useValue: {
            send: jest.fn(),
          },
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();
    service = module.get<OrganizationAdAccountsService>(
      OrganizationAdAccountsService,
    );
    organizationPlatformAdAccountMapRepository = module.get<
      Repository<OrganizationPlatformAdAccountMap>
    >(getRepositoryToken(OrganizationPlatformAdAccountMap));
    organizationAdAccountUserPermissionsRepository = module.get<
      Repository<OrganizationAdAccountUserPermissions>
    >(getRepositoryToken(OrganizationAdAccountUserPermissions));
    platformAdAccountIndustryMapRepository = module.get<
      Repository<PlatformAdAccountIndustryMap>
    >(getRepositoryToken(PlatformAdAccountIndustryMap));
    jest
      .spyOn(organizationPlatformAdAccountMapRepository, 'save')
      .mockResolvedValue(mockOrganizationPlatformAdAccountMap());
    organizationPlatformAdAccountMapRepository.delete = jest.fn();
    adAccountsService = module.get<AdAccountsService>(AdAccountsService);
    platformAdAccountBrandMapRepo = module.get<
      Repository<PlatformAdAccountBrandMap>
    >(getRepositoryToken(PlatformAdAccountBrandMap));
    platformAdAccountMarketMapRepo = module.get<
      Repository<PlatformAdAccountMarketMap>
    >(getRepositoryToken(PlatformAdAccountMarketMap));
    brandService = module.get<BrandService>(BrandService);
    marketService = module.get<MarketService>(MarketService);
  });

  const tSaveOrganizationPlatformAdAccountDto = async (
    permission: Permission,
    expectedResult = '',
    organizationPlatformAdAccountMap = mockOrganizationPlatformAdAccountMap(),
  ) => {
    const updateOrganizationPlatformAdAccountDto = {
      userId: TEST_USER_ID,
      platformAccountId: TEST_ACCOUNT_ID,
      permission: permission,
    };
    jest
      .spyOn(organizationPlatformAdAccountMapRepository, 'findOne')
      .mockResolvedValue(organizationPlatformAdAccountMap);
    const response = await service.mapAdAccountToOrganization(
      TEST_ORG_ID,
      updateOrganizationPlatformAdAccountDto,
    );
    await expect(response).toEqual(expectedResult);
  };

  it('Service should be defined', () => {
    expect(service).toBeDefined();
  });

  it('Map new ad account to organization', async () => {
    await tSaveOrganizationPlatformAdAccountDto(
      Permission.ALLOW,
      `Ad account linked to the organization successfully.`,
      null,
    );
  });

  it('Map existing ad account to organization with no changes', async () => {
    await tSaveOrganizationPlatformAdAccountDto(
      Permission.ALLOW,
      `Ad account already linked to the organization.`,
    );
  });

  it('Map existing ad account to organization with permission change when no users has ALLOW access', async () => {
    jest
      .spyOn(organizationAdAccountUserPermissionsRepository, 'count')
      .mockResolvedValueOnce(0);
    await tSaveOrganizationPlatformAdAccountDto(
      Permission.ALLOW,
      `Ad account unlinked from the organization successfully.`,
      {
        organizationId: '1',
        platformAdAccountId: 2,
        organization: mockOrganization,
        platformAdAccount: mockAdAccount2,
      },
    );
  });

  it('Map new ad account to organization', async () => {
    jest
      .spyOn(organizationAdAccountUserPermissionsRepository, 'count')
      .mockResolvedValueOnce(0);
    await tSaveOrganizationPlatformAdAccountDto(
      Permission.ALLOW,
      `Ad account not associated with the organization. No action will be taken as the requested permission is DENY.`,
      null,
    );
  });

  it('Update platform permissions method being called when saving org to ad account mapping', async () => {
    const organizationAdAccountUserPermissionsRepositoryUpdateSpy = jest.spyOn(
      organizationAdAccountUserPermissionsRepository,
      'update',
    );
    await tSaveOrganizationPlatformAdAccountDto(
      Permission.UNKNOWN,
      `Ad account already linked to the organization.`,
    );
    expect(
      organizationAdAccountUserPermissionsRepositoryUpdateSpy,
    ).toBeCalledTimes(1);
    TEST_ORGANIZATION_AD_ACCOUNT_USER_PERMISSIONS.permission = Permission.ALLOW;
  });

  it('Save platform permissions method being called when saving org to ad account mapping', async () => {
    jest
      .spyOn(organizationAdAccountUserPermissionsRepository, 'findOne')
      .mockResolvedValueOnce(null);
    const organizationAdAccountUserPermissionsRepositorySaveSpy = jest.spyOn(
      organizationAdAccountUserPermissionsRepository,
      'save',
    );
    await tSaveOrganizationPlatformAdAccountDto(
      Permission.ALLOW,
      `Ad account already linked to the organization.`,
    );
    expect(
      organizationAdAccountUserPermissionsRepositorySaveSpy,
    ).toBeCalledTimes(1);
  });

  it('test findOrganizationsForAdAccount method with no account Ids', async () => {
    mockGetAdAccountsIdsFromPlatformAccountIds.mockReturnValueOnce([]);
    await expect(service.findOrganizationsForAdAccount([])).resolves.toEqual(
      [],
    );
  });

  it('test findOrganizationsForAdAccount method with invalid account Ids', async () => {
    mockGetAdAccountsIdsFromPlatformAccountIds.mockReturnValueOnce([]);
    await expect(
      service.findOrganizationsForAdAccount([TEST_ACCOUNT_ID]),
    ).rejects.toThrowError(`Ad accounts ${TEST_ACCOUNT_ID} not found`);
  });

  it('test findOrganizationsForAdAccount method with duplicate organizations for ad account', async () => {
    const platformAdAccount = new PlatformAdAccount();
    platformAdAccount.id = 1;
    mockGetAdAccountsIdsFromPlatformAccountIds.mockReturnValueOnce([
      platformAdAccount,
    ]);
    const orgAdAccount1 = mockOrganizationPlatformAdAccountMap();
    const orgAdAccount2 = mockOrganizationPlatformAdAccountMap();
    orgAdAccount2.platformAdAccountId = 2;
    jest
      .spyOn(organizationPlatformAdAccountMapRepository, 'find')
      .mockResolvedValueOnce([orgAdAccount1, orgAdAccount2]);
    const organizations = await service.findOrganizationsForAdAccount([
      TEST_ACCOUNT_ID,
    ]);
    expect(organizations.length).toEqual(1);
    expect(organizations[0].id).toEqual(TEST_ORG_ID);
  });

  it('test getUserAccountsWithPermissionsInOrganization method', async () => {
    const result = await service.getUserAccountsWithPermissionsInOrganization(
      TEST_ORG_ID,
      TEST_PLATFORM,
      TEST_USER_ID,
    );
    expect(result).toBeDefined();
    expect(result[0].platformAccountId).toEqual(TEST_ACCOUNT_ID);
    expect(result[0].permission).toEqual(Permission.ALLOW);
  });

  it('test getUserAccountPermissionsAcrossOrganizations method', async () => {
    const result = await service.getUserAccountPermissionsAcrossOrganizations(
      TEST_ORG_ID,
      TEST_USER_ID,
    );
    expect(result).toBeDefined();
    expect(result[0].platformAccountId).toEqual(TEST_ACCOUNT_ID);
    expect(result[0].permission).toEqual(Permission.ALLOW);
  });

  describe('deletePlatformAdAccountMapFromOrganization', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });
    it('should delete platform ad account map from organization and there associated org workspaces', async () => {
      jest.spyOn(mockEntityManager, 'remove');
      mockAdAccount2.PlatformAdAccountToWorkspace = [mockWorkspaceAdAccountMap];
      await service.deletePlatformAdAccountMapFromOrganization({
        organizationId: '1',
        platformAdAccountId: 2,
        organization: mockOrganization,
        platformAdAccount: mockAdAccount2,
      });
      expect(mockEntityManager.remove).toBeCalledTimes(1);
      expect(
        adAccountsService.deleteMultipleWorkspacesConnectionsFromAPlatformAdAccount,
      ).toBeCalledTimes(1);
    });
    it('should not hit deleteWorkspacePlatformAdAccountConnections if no workspace are associated with the account', async () => {
      jest.spyOn(mockEntityManager, 'remove').mockClear();
      mockAdAccount2.PlatformAdAccountToWorkspace = [];
      await service.deletePlatformAdAccountMapFromOrganization({
        organizationId: '1',
        platformAdAccountId: 2,
        organization: mockOrganization,
        platformAdAccount: mockAdAccount2,
      });
      expect(mockEntityManager.remove).toBeCalledTimes(1);
      expect(
        adAccountsService.deleteMultipleWorkspacesConnectionsFromAPlatformAdAccount,
      ).not.toHaveBeenCalled();
    });
  });

  describe('mapPlatformAdAccountAndBrand', () => {
    it('should map 3 brands with 3 ad accounts and retrieve 9 brand ad account maps', () => {
      const accountIds = [123, 456, 789];
      const brandIds = ['123', '456', '789'];

      const accountBrandMap = service.mapPlatformAdAccountAndBrand(
        accountIds,
        brandIds,
      );
      expect(accountBrandMap.length).toBe(9);
    });
  });

  describe('mapPlatformAdAccountAndMarket', () => {
    it('should map 3 markets with 3 ad accounts and retrieve 9 market ad account maps', () => {
      const accountIds = [123, 456, 789];
      const countryIsoCodes = ['usa', 'bra', 'iot'];

      const accountMarketMap = service.mapPlatformAdAccountAndMarket(
        accountIds,
        countryIsoCodes,
      );
      expect(accountMarketMap.length).toBe(9);
    });
  });

  describe('saveBulkMapBetweenAdAccountsAndBrands', () => {
    afterEach(() => {
      jest.restoreAllMocks();
    });

    const insertOnlyBrandsBody = {
      accounts: ['act_123', 'act_456', 'act_789'],
      selected_brands: ['12', '34', '56'],
      unselected_brands: null,
    };

    const removeOnlyBrandsBody = {
      accounts: ['act_123', 'act_456', 'act_789'],
      unselected_brands: ['12', '34', '56'],
      selected_brands: null,
    };

    const insertAndRemoveBrandsBody = {
      accounts: ['act_123', 'act_456', 'act_789'],
      unselected_brands: ['12', '34'],
      selected_brands: ['78', '90'],
    };

    it('should only bulk insert ad account and brand successfully', async () => {
      const releaseConnectionSpy = jest.spyOn(mockedQueryRunner, 'release');
      const insertSpy = jest.spyOn(createQueryBuilder, 'insert');
      const deleteSpy = jest.spyOn(createQueryBuilder, 'delete');
      jest
        .spyOn(adAccountsService, 'getAdAccountsIdsFromPlatformAccountIds')
        .mockResolvedValueOnce([123, 456, 789]);
      jest
        .spyOn(platformAdAccountBrandMapRepo, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      const expectedResponse = {
        message:
          'Selected brands successfully added/removed from to/from 3 ad account(s).',
      };

      const response = await service.saveBulkMapBetweenAdAccountsAndBrands(
        '1234',
        1234,
        insertOnlyBrandsBody,
      );
      expect(response).toEqual(expectedResponse);
      expect(insertSpy).toBeCalled();
      expect(deleteSpy).not.toBeCalled();
      expect(releaseConnectionSpy).toBeCalled();
    });

    it('should only bulk remove ad account and brand successfully', async () => {
      const releaseConnectionSpy = jest.spyOn(mockedQueryRunner, 'release');
      const insertSpy = jest.spyOn(createQueryBuilder, 'insert');
      const deleteSpy = jest.spyOn(createQueryBuilder, 'delete');
      jest
        .spyOn(adAccountsService, 'getAdAccountsIdsFromPlatformAccountIds')
        .mockResolvedValueOnce([123, 456, 789]);
      jest
        .spyOn(platformAdAccountBrandMapRepo, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      const expectedResponse = {
        message:
          'Selected brands successfully added/removed from to/from 3 ad account(s).',
      };

      const response = await service.saveBulkMapBetweenAdAccountsAndBrands(
        '1234',
        1234,
        removeOnlyBrandsBody,
      );
      expect(response).toEqual(expectedResponse);
      expect(insertSpy).not.toBeCalled();
      expect(deleteSpy).toBeCalled();
      expect(releaseConnectionSpy).toBeCalled();
    });

    it('should bulk insert and remove ad account and brand successfully', async () => {
      const releaseConnectionSpy = jest.spyOn(mockedQueryRunner, 'release');
      const insertSpy = jest.spyOn(createQueryBuilder, 'insert');
      const deleteSpy = jest.spyOn(createQueryBuilder, 'delete');

      jest
        .spyOn(adAccountsService, 'getAdAccountsIdsFromPlatformAccountIds')
        .mockResolvedValueOnce([123, 456, 789]);
      jest
        .spyOn(platformAdAccountBrandMapRepo, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder)
        .mockImplementationOnce(() => createQueryBuilder);

      const expectedResponse = {
        message:
          'Selected brands successfully added/removed from to/from 3 ad account(s).',
      };

      const response = await service.saveBulkMapBetweenAdAccountsAndBrands(
        '1234',
        1234,
        insertAndRemoveBrandsBody,
      );
      expect(response).toEqual(expectedResponse);
      expect(insertSpy).toBeCalled();
      expect(deleteSpy).toBeCalled();
      expect(releaseConnectionSpy).toBeCalled();
    });
  });

  describe('saveBulkMapBetweenAdAccountsAndMarkets', () => {
    afterEach(() => {
      jest.restoreAllMocks();
    });

    const insertOnlyMarketsBody = {
      accounts: ['act_123', 'act_456', 'act_789'],
      selected_markets: ['usa', 'bra', 'iot'],
      unselected_markets: null,
    };

    const removeOnlyMarketsBody = {
      accounts: ['act_123', 'act_456', 'act_789'],
      unselected_markets: ['usa', 'bra', 'iot'],
      selected_markets: null,
    };

    const insertAndRemoveMarketsBody = {
      accounts: ['act_123', 'act_456', 'act_789'],
      selected_markets: ['usa', 'bra', 'iot'],
      unselected_markets: ['arg', 'alb'],
    };

    it('should only bulk insert ad account and market successfully', async () => {
      const releaseConnectionSpy = jest.spyOn(mockedQueryRunner, 'release');
      const insertSpy = jest.spyOn(createQueryBuilder, 'insert');
      const deleteSpy = jest.spyOn(createQueryBuilder, 'delete');
      jest
        .spyOn(adAccountsService, 'getAdAccountsIdsFromPlatformAccountIds')
        .mockResolvedValueOnce([123, 456, 789]);
      jest
        .spyOn(platformAdAccountMarketMapRepo, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      const expectedResponse: ReadAdAccountMapDto = {
        message:
          'Selected markets successfully added/removed from to/from 3 ad account(s).',
      };

      const response = await service.saveBulkMapBetweenAdAccountsAndMarkets(
        '1234',
        1234,
        insertOnlyMarketsBody,
      );
      expect(response).toEqual(expectedResponse);
      expect(insertSpy).toBeCalled();
      expect(deleteSpy).not.toBeCalled();
      expect(releaseConnectionSpy).toBeCalled();
    });

    it('should only bulk remove ad account and market successfully', async () => {
      const releaseConnectionSpy = jest.spyOn(mockedQueryRunner, 'release');
      const insertSpy = jest.spyOn(createQueryBuilder, 'insert');
      const deleteSpy = jest.spyOn(createQueryBuilder, 'delete');
      jest
        .spyOn(adAccountsService, 'getAdAccountsIdsFromPlatformAccountIds')
        .mockResolvedValueOnce([123, 456, 789]);
      jest
        .spyOn(platformAdAccountMarketMapRepo, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      const expectedResponse: ReadAdAccountMapDto = {
        message:
          'Selected markets successfully added/removed from to/from 3 ad account(s).',
      };

      const response = await service.saveBulkMapBetweenAdAccountsAndMarkets(
        '1234',
        1234,
        removeOnlyMarketsBody,
      );
      expect(response).toEqual(expectedResponse);
      expect(insertSpy).not.toBeCalled();
      expect(deleteSpy).toBeCalled();
      expect(releaseConnectionSpy).toBeCalled();
    });

    it('should bulk insert and remove ad account and market successfully', async () => {
      const releaseConnectionSpy = jest.spyOn(mockedQueryRunner, 'release');
      const insertSpy = jest.spyOn(createQueryBuilder, 'insert');
      const deleteSpy = jest.spyOn(createQueryBuilder, 'delete');
      jest
        .spyOn(adAccountsService, 'getAdAccountsIdsFromPlatformAccountIds')
        .mockResolvedValueOnce([123, 456, 789]);
      jest
        .spyOn(platformAdAccountMarketMapRepo, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder)
        .mockImplementationOnce(() => createQueryBuilder);

      const expectedResponse: ReadAdAccountMapDto = {
        message:
          'Selected markets successfully added/removed from to/from 3 ad account(s).',
      };

      const response = await service.saveBulkMapBetweenAdAccountsAndMarkets(
        '1234',
        1234,
        insertAndRemoveMarketsBody,
      );
      expect(response).toEqual(expectedResponse);
      expect(insertSpy).toBeCalled();
      expect(deleteSpy).toBeCalled();
      expect(releaseConnectionSpy).toBeCalled();
    });
  });

  describe('getOrganizationAdAccountBrands', () => {
    afterEach(() => {
      jest.restoreAllMocks();
    });

    const brands = [new Brand(), new Brand()];

    it('should get brands associated to ad account', async () => {
      jest
        .spyOn(brandService, 'findBrandsByPlatformAccountId')
        .mockResolvedValueOnce(brands);

      await expect(
        service.getOrganizationAdAccountBrands(TEST_ACCOUNT_ID),
      ).resolves.toStrictEqual({ brands });
    });
  });

  describe('getOrganizationAdAccountMarkets', () => {
    afterEach(() => {
      jest.restoreAllMocks();
    });

    const markets = [new Market(), new Market()];

    it('should get brands associated to ad account', async () => {
      jest
        .spyOn(marketService, 'findMarketsByPlatformAccountId')
        .mockResolvedValueOnce(markets);

      await expect(
        service.getOrganizationAdAccountMarkets(TEST_ACCOUNT_ID),
      ).resolves.toStrictEqual({ markets });
    });
  });

  describe('Test Platform Ad Account Industry functions', () => {
    afterEach(() => {
      jest.restoreAllMocks();
    });
    const platformAdAccountIndustryMap = [
      {
        platformAdAccount: {
          platformAccountId: '1234',
        },
        industry: {
          id: 1,
          parentId: null,
          rootId: null,
        },
      } as unknown as PlatformAdAccountIndustryMap,
      {
        platformAdAccount: {
          platformAccountId: '2345',
        },
        industry: {
          id: 2,
          parentId: 1,
          rootId: 1,
        },
      } as unknown as PlatformAdAccountIndustryMap,
      {
        platformAdAccount: {
          platformAccountId: '3456',
        },
        industry: {
          id: 3,
          parentId: 2,
          rootId: 1,
        },
      } as unknown as PlatformAdAccountIndustryMap,
    ];

    it('getPlatformAdAccountsIndustryGroups return values', async () => {
      jest
        .spyOn(platformAdAccountIndustryMapRepository, 'find')
        .mockResolvedValueOnce(platformAdAccountIndustryMap);

      await expect(
        service.getPlatformAdAccountsIndustryGroups(['1234']),
      ).resolves.toEqual([
        ['1234', 1],
        ['2345', 1],
        ['3456', 1],
      ]);
    });

    it('getPlatformAdAccountsIndustriesByIndustryGroup return values', async () => {
      jest
        .spyOn(platformAdAccountIndustryMapRepository, 'find')
        .mockResolvedValueOnce(platformAdAccountIndustryMap);

      await expect(
        service.getPlatformAdAccountsIndustriesByIndustryGroup(['1234'], 1),
      ).resolves.toEqual([
        ['2345', 2],
        ['3456', 2],
      ]);
    });

    it('getPlatformAdAccountsSubIndustriesByIndustry return values', async () => {
      jest
        .spyOn(platformAdAccountIndustryMapRepository, 'find')
        .mockResolvedValueOnce(platformAdAccountIndustryMap);

      await expect(
        service.getPlatformAdAccountsSubIndustriesByIndustry(['1234'], 2),
      ).resolves.toEqual([['3456', 3]]);
    });

    it('getPlatformAdAccountsIndustryMappings return values', async () => {
      jest
        .spyOn(platformAdAccountIndustryMapRepository, 'find')
        .mockResolvedValueOnce(platformAdAccountIndustryMap);

      await expect(
        service.getPlatformAdAccountsIndustryMappings(['1234'], 2),
      ).resolves.toEqual(platformAdAccountIndustryMap);
    });
  });

  describe('assignIndustryGroupToPlatformAdAccounts', () => {
    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('assignIndustryGroupToPlatformAdAccounts returns immediately when called with 0 accounts', async () => {
      await service.assignIndustryGroupToPlatformAdAccounts(
        TEST_USER_ID,
        [],
        1,
      );
      expect(
        platformAdAccountIndustryMapRepository.queryRunner.manager.save,
      ).toBeCalledTimes(0);
    });

    it('assignIndustrygroupToPlatformAdAccounts with valid account ids', async () => {
      jest
        .spyOn(adAccountsService, 'getAdAccountsIdsFromPlatformAccountIds')
        .mockResolvedValueOnce([1, 2]);
      await service.assignIndustryGroupToPlatformAdAccounts(
        TEST_USER_ID,
        ['1234', '2345'],
        1,
      );
      expect(
        platformAdAccountIndustryMapRepository.queryRunner.manager.save,
      ).toBeCalledTimes(1);
    });
  });

  describe('removeIndustryEntityFromPlatformAdAccounts', () => {
    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('removeIndustryEntityFromPlatformAdAccounts returns immediately when called with 0 accounts', async () => {
      await service.removeIndustryEntityFromPlatformAdAccounts(
        TEST_USER_ID,
        [],
        [1],
      );
      expect(
        platformAdAccountIndustryMapRepository.queryRunner.manager.delete,
      ).toBeCalledTimes(0);
    });

    it('removeIndustryEntityFromPlatformAdAccounts with valid account ids', async () => {
      jest
        .spyOn(platformAdAccountIndustryMapRepository, 'find')
        .mockResolvedValueOnce([new PlatformAdAccountIndustryMap()]);
      await service.removeIndustryEntityFromPlatformAdAccounts(
        TEST_USER_ID,
        ['1234', '2345'],
        [1],
      );
      expect(
        platformAdAccountIndustryMapRepository.queryRunner.manager.delete,
      ).toBeCalledTimes(1);
    });
  });

  describe('filterNewAccountsToBeAddedToPlatformAdAccountIndustry', () => {
    it('return all accounts with no database mappings ', function () {
      const accountIds =
        service.filterNewAccountsToBeAddedToPlatformAdAccountIndustry(
          [],
          ['1234', '2345'],
        );
      expect(accountIds).toEqual(['1234', '2345']);
    });
    it('return no accounts with when accounts are in database ', function () {
      const accountIndustries: [string, number][] = [
        ['1234', 1],
        ['2345', 2],
      ];
      const accountIds =
        service.filterNewAccountsToBeAddedToPlatformAdAccountIndustry(
          accountIndustries,
          ['1234', '2345'],
        );
      expect(accountIds.length).toEqual(0);
    });
  });

  describe('Test getOrganizationUsersPerPlatform method', () => {
    it('returns empty list of org users per platform', async () => {
      jest.spyOn(createQueryBuilder, 'getMany').mockResolvedValueOnce([]);
      const orgUsers = await service.getOrganizationUsersPerPlatform(
        'TEST_PLATFORM',
        false,
        false,
      );
      expect(orgUsers).toEqual([]);
    });

    it('returns list of org users per platform with no duplicates', async () => {
      const orgUsersList = [
        { organizationId: 'ORG_1', userId: 123 },
        { organizationId: 'ORG_2', userId: 234 },
        { organizationId: 'ORG_2', userId: 345 },
      ] as OrganizationAdAccountUserPermissions[];
      const expectedOrgUsers = [
        new ReadPlatformOrganizationUsersMapDto('ORG_1', [123]),
        new ReadPlatformOrganizationUsersMapDto('ORG_2', [234, 345]),
      ];
      jest
        .spyOn(createQueryBuilder, 'getMany')
        .mockResolvedValueOnce(orgUsersList);
      const orgUsers = await service.getOrganizationUsersPerPlatform(
        'TEST_PLATFORM',
        false,
        false,
      );
      expect(orgUsers).toEqual(expectedOrgUsers);
    });

    it('returns list of org users per platform with duplicates', async () => {
      const orgUsersList = [
        { organizationId: 'ORG_1', userId: 123 },
        { organizationId: 'ORG_2', userId: 234 },
        { organizationId: 'ORG_2', userId: 234 },
      ] as OrganizationAdAccountUserPermissions[];
      const expectedOrgUsers = [
        new ReadPlatformOrganizationUsersMapDto('ORG_1', [123]),
        new ReadPlatformOrganizationUsersMapDto('ORG_2', [234]),
      ];
      jest
        .spyOn(createQueryBuilder, 'getMany')
        .mockResolvedValueOnce(orgUsersList);
      const orgUsers = await service.getOrganizationUsersPerPlatform(
        'TEST_PLATFORM',
        false,
        false,
      );
      expect(orgUsers).toEqual(expectedOrgUsers);
    });
  });

  describe('Test getOrganizationAdAccountUserPermissions method', () => {
    it('return no accounts provided message when account ids are empty ', async () => {
      const logSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();
      jest
        .spyOn(organizationAdAccountUserPermissionsRepository, 'find')
        .mockResolvedValueOnce([]);
      await service.updateOrganizationUserAccountsAccess(
        TEST_ORG_ID,
        TEST_PLATFORM,
        TEST_USER_ID,
        [],
      );
      expect(logSpy).toHaveBeenCalledWith(
        `User's access is updated as false for 0 accounts and true for 0 accounts in ` +
          `organization '${TEST_ORG_ID}', User Id - ${TEST_USER_ID}, Platform - ${TEST_PLATFORM}.`,
      );
    });

    it('return correct message when one account lost access, one account gained ', async () => {
      const logSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();
      await service.updateOrganizationUserAccountsAccess(
        TEST_ORG_ID,
        TEST_PLATFORM,
        TEST_USER_ID,
        [TEST_ACCOUNT_ID_2],
      );
      expect(logSpy).toHaveBeenCalledWith(
        `User's access is updated as false for 1 accounts and true for 1 accounts in ` +
          `organization '${TEST_ORG_ID}', User Id - ${TEST_USER_ID}, Platform - ${TEST_PLATFORM}.`,
      );
    });

    it('return correct message when one account lost access, none gained ', async () => {
      const logSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();
      await service.updateOrganizationUserAccountsAccess(
        TEST_ORG_ID,
        TEST_PLATFORM,
        TEST_USER_ID,
        [],
      );
      expect(logSpy).toHaveBeenCalledWith(
        `User's access is updated as false for 1 accounts and true for 0 accounts in ` +
          `organization '${TEST_ORG_ID}', User Id - ${TEST_USER_ID}, Platform - ${TEST_PLATFORM}.`,
      );
    });

    it('return correct message when no account lost access, one account gained ', async () => {
      const logSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();
      await service.updateOrganizationUserAccountsAccess(
        TEST_ORG_ID,
        TEST_PLATFORM,
        TEST_USER_ID,
        [TEST_ACCOUNT_ID, TEST_ACCOUNT_ID_2],
      );
      expect(logSpy).toHaveBeenCalledWith(
        `User's access is updated as false for 0 accounts and true for 1 accounts in ` +
          `organization '${TEST_ORG_ID}', User Id - ${TEST_USER_ID}, Platform - ${TEST_PLATFORM}.`,
      );
    });
  });
});
