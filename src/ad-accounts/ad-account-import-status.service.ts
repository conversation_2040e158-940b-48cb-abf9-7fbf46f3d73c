import { Injectable, Logger } from '@nestjs/common';
import {
  isStatusActive,
  PlatformAdAccountImportStatus,
} from './entities/platform-ad-account-import-status.entity';
import { EntityManager } from 'typeorm';
import { CreateImportStatusDto } from './dto/create-import-status.dto';
import { PlatformAdAccount } from './entities/ad-account.entity';
import { Organization } from '../organizations/entities/organization.entity';
import { User } from '../entities/user.entity';
import {
  DEFAULT_HISTORIC_IMPORT_DAYS,
  DEFAULT_HISTORIC_IMPORT_DAYS_OFFSET,
  IAV3_ENABLED_PLATFORMS,
  ImportRebuildType,
  ImportStatus,
} from '../common/constants/constants';
import { SqsService } from '@vidmob/vidmob-nestjs-common';
import { UpdateAdAccountImportDataDto } from './dto/update-ad-account-import-data.dto';

@Injectable()
export class AdAccountImportStatusService {
  constructor(private readonly sqsService: SqsService) {}
  private readonly logger: Logger = new Logger(
    AdAccountImportStatusService.name,
  );
  async createImportStatus(
    entityManager: EntityManager,
    createStatusDto: CreateImportStatusDto,
  ) {
    const importStatus = await this.getImportStatusEntityFromCreateDto(
      entityManager,
      createStatusDto,
    );
    const createdImportStatus = await entityManager.save(importStatus);

    const platformAdAccount = importStatus.platformAdAccount;
    platformAdAccount.accountImportStatus = createdImportStatus;
    platformAdAccount.importActive = createdImportStatus.active ? 1 : 0;

    await entityManager.save(platformAdAccount);

    if (createdImportStatus.status === ImportStatus.PROCESSING) {
      try {
        await this.sendAccountImportMessage(importStatus);
      } catch (error) {
        this.logger.error(
          `Error sending ad account imports start message to the queue, ` +
            `account : ${platformAdAccount.platformAccountId}. Error: ${error.message}`,
        );
      }
    }

    this.logger.log(
      `Import status updated for ${platformAdAccount.platform} account ${platformAdAccount.platformAccountName} (${platformAdAccount.platformAccountId}) with status ${importStatus.status} (active: ${importStatus.active})`,
    );

    return createdImportStatus;
  }

  async getImportStatusEntityFromCreateDto(
    entityManager: EntityManager,
    createStatusDto: CreateImportStatusDto,
  ): Promise<PlatformAdAccountImportStatus> {
    const platformAdAccount = await entityManager.findOneBy(PlatformAdAccount, {
      id: createStatusDto.platformAdAccountId,
    });
    if (!platformAdAccount) {
      throw new Error('Platform Ad Account not found');
    }
    if (
      !platformAdAccount.id ||
      platformAdAccount.id !== createStatusDto.platformAdAccountId
    ) {
      throw new Error(
        `Platform Ad Account does not match ${platformAdAccount.id} !== ${createStatusDto.platformAdAccountId}`,
      );
    }
    const organization = await entityManager.findOneBy(Organization, {
      id: createStatusDto.organizationId,
    });
    if (!organization) {
      throw new Error('Organization not found');
    }
    let person = null;
    if (createStatusDto.personId) {
      person = await entityManager.findOneBy(User, {
        id: createStatusDto.personId,
      });
      if (!person) {
        throw new Error('Person not found');
      }
    }

    const importStatus = new PlatformAdAccountImportStatus();
    importStatus.platformAdAccount = platformAdAccount;
    importStatus.organization = organization; // Assuming organization is just an id for this example
    importStatus.active = isStatusActive(createStatusDto.status);
    importStatus.status = createStatusDto.status;
    importStatus.reason = createStatusDto.reason;
    importStatus.importId = createStatusDto.importId;
    importStatus.rebuildRequestId = createStatusDto.rebuildRequestId;
    importStatus.person = person;

    return importStatus;
  }

  /**
   * Sends an import start message to the Import Management Service.
   * @param importStatus
   */
  async sendImportStartMessageToImportManagementService(
    importStatus: PlatformAdAccountImportStatus,
  ) {
    this.logger.log(
      `Sending account import message to IMS import start queue for ${importStatus.platformAdAccount.platform}, ` +
        `account ${importStatus.platformAdAccount.platformAccountId}`,
    );
    const lastSuccessfulImportDate =
      importStatus.platformAdAccount.lastImportSuccessDate;
    await this.sqsService.send('importStartQueueIAv3', {
      id: importStatus.id,
      body: JSON.stringify({
        platform: importStatus.platformAdAccount.platform,
        organizationId: importStatus.organization.id,
        accountId: importStatus.platformAdAccount.platformAccountId,
        userId: importStatus.platformAdAccount.userId,
        ...(lastSuccessfulImportDate && {
          days: this.getHistoricImportDays(lastSuccessfulImportDate),
        }),
      }),
    });
  }

  /**
   * Sends an import start message to the Analytics Service.
   * @param importStatus
   */
  async sendImportStartMessageToAnalyticsServiceImportQueue(
    importStatus: PlatformAdAccountImportStatus,
  ) {
    this.logger.log(
      `Sending account import message to analytics service queue for ${importStatus.platformAdAccount.platform}, ` +
        `account ${importStatus.platformAdAccount.platformAccountId}`,
    );
    const lastSuccessfulImportDate =
      importStatus.platformAdAccount.lastImportSuccessDate;
    await this.sqsService.send('importStartQueueLegacy', {
      id: importStatus.id,
      body: JSON.stringify({
        network: importStatus.platformAdAccount.platform.toLowerCase(),
        organizationId: importStatus.organization.id,
        adAccountId: importStatus.platformAdAccount.platformAccountId,
        userId: importStatus.platformAdAccount.userId,
        rebuildType: ImportRebuildType.HISTORIC,
        ...(lastSuccessfulImportDate && {
          daysFrom: this.getHistoricImportDays(lastSuccessfulImportDate),
        }),
      }),
    });
  }

  /**
   * Calculates the number of days since the last successful import.
   * This is used to determine how far back in time to import data along with incremental offset days
   * If difference is greater than the default historic import days, it will be capped at the default value.
   * @param lastImportSuccessDate Date of the last successful import
   */
  getHistoricImportDays(lastImportSuccessDate: Date): number {
    if (lastImportSuccessDate === null) {
      return DEFAULT_HISTORIC_IMPORT_DAYS;
    }
    const currentDate = new Date();
    const diffTime = Math.abs(
      currentDate.getTime() - lastImportSuccessDate.getTime(),
    );
    const lastImportSinceDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const toImportDays =
      lastImportSinceDays + DEFAULT_HISTORIC_IMPORT_DAYS_OFFSET;
    return Math.min(toImportDays, DEFAULT_HISTORIC_IMPORT_DAYS);
  }

  /**
   * Sends an account import message to the appropriate queue based on platform.
   * @param importStatus import status object with platform ad account details
   */
  async sendAccountImportMessage(importStatus: PlatformAdAccountImportStatus) {
    const platform = importStatus.platformAdAccount.platform;
    if (IAV3_ENABLED_PLATFORMS.includes(platform.toUpperCase())) {
      await this.sendImportStartMessageToImportManagementService(importStatus);
    } else {
      await this.sendImportStartMessageToAnalyticsServiceImportQueue(
        importStatus,
      );
    }
  }

  getImportStatusReason(
    status: ImportStatus,
    accountId: string,
    importSkipReason?: string,
  ): string {
    switch (status) {
      case ImportStatus.FAILED:
        return `Failed to import ad account '${accountId}'. Import status updated to FAILED.`;
      case ImportStatus.SUSPENDED:
        return `Maximum attempts reached while trying to import ad account '${accountId}'. Account imports are now suspended.`;
      case ImportStatus.ENABLED:
        return `Ad account '${accountId}' imported successfully. Updating status to ENABLED.`;
      default:
        return importSkipReason || 'Unknown';
    }
  }

  /**
   * Inserts an account import status record, if it doesn't already exist.
   * @param entityManager entity manager for database operations
   * @param organizationId organization ID
   * @param accountImportDto Account import DTO
   * @param platformAdAccount platform ad account object
   * @param newStatus new import status (FAILED, SUSPENDED, ENABLED, NOT_IMPORTING)
   */
  async insertAccountImportStatusRecord(
    entityManager: EntityManager,
    organizationId: string,
    accountImportDto: UpdateAdAccountImportDataDto,
    platformAdAccount: PlatformAdAccount,
    newStatus: ImportStatus,
  ) {
    if (platformAdAccount.accountImportStatus?.status === newStatus) {
      this.logger.log(
        `Import status ${newStatus} already exists for account ${platformAdAccount.platformAccountId}`,
      );
      return;
    }
    this.logger.log(
      `Inserting import status ${newStatus} for account ${platformAdAccount.platformAccountId}`,
    );
    const createStatusDto: CreateImportStatusDto = {
      platformAdAccountId: platformAdAccount.id,
      organizationId: organizationId,
      importId: accountImportDto.importId,
      rebuildRequestId: accountImportDto.rebuildRequestId,
      personId: platformAdAccount.userId,
      status: newStatus,
      reason: {
        message: this.getImportStatusReason(
          newStatus,
          platformAdAccount.platformAccountId,
          accountImportDto.importSkipReason,
        ),
      },
    };
    await this.createImportStatus(entityManager, createStatusDto);
  }
}
