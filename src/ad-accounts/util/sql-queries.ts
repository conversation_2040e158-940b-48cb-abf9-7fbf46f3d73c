import {
  AdAccountConnectionStatus,
  PlatformAdAccountHealthSortBy,
  SortBy,
  SortOrder,
} from '../../common/constants/constants';
import { isEmpty } from 'class-validator';
import { AccountSearchParamsDto } from '../dto/ad-account-search-params.dto';
import { AdAccountHealthSearchParamsDto } from '../dto/ad-account-health-search-params.dto';

const getOrganizationAdAccountsForDashboardWhereClause = (
  searchParams: AdAccountHealthSearchParamsDto,
) => {
  let whereClause =
    searchParams.search && searchParams.search.trim().length > 0
      ? `WHERE opaam.organization_id = ? ` +
        `AND (paa.platform_account_id = ? ` +
        `OR paa.platform_account_name like ? ` +
        `OR paa.platform like ? ` +
        `OR p.email like ?)`
      : `WHERE opaam.organization_id = ? `;

  if (searchParams.brands === 'none') {
    whereClause += ` AND brnds.platform_ad_account_id IS NULL `;
  } else if (searchParams.brands === 'any') {
    whereClause += ` AND brnds.platform_ad_account_id IS NOT NULL `;
  }

  if (searchParams.markets === 'none') {
    whereClause += ` AND mrkts.platform_ad_account_id IS NULL `;
  } else if (searchParams.markets === 'any') {
    whereClause += ` AND mrkts.platform_ad_account_id IS NOT NULL `;
  }

  if (searchParams.workspaces === 'none') {
    whereClause += ` AND prtnrs.platform_ad_account_id IS NULL `;
  } else if (searchParams.workspaces === 'any') {
    whereClause += ` AND prtnrs.platform_ad_account_id IS NOT NULL `;
  }

  if (searchParams?.importStatus?.length > 0) {
    const importStatusInClause = searchParams.importStatus
      .map((importStatus) => `'${importStatus}'`)
      .join(',');

    whereClause += ` AND paais.status IN (${importStatusInClause})`;
  }

  return whereClause;
};

const getWorkspaceAdAccountsForDashboardWhereClause = (
  searchParams: AdAccountHealthSearchParamsDto,
) => {
  let whereClause =
    searchParams.search && searchParams.search.trim().length > 0
      ? `WHERE ws.organization_id = ? AND wpaam.partner_id = ? ` +
        `AND (paa.platform_account_id = ? ` +
        `OR paa.platform_account_name like ? ` +
        `OR paa.platform like ? ` +
        `OR p.email like ?)`
      : `WHERE ws.organization_id = ? AND wpaam.partner_id = ?`;
  if (searchParams.brands === 'none') {
    whereClause += ` AND brnds.platform_ad_account_id IS NULL `;
  } else if (searchParams.brands === 'any') {
    whereClause += ` AND brnds.platform_ad_account_id IS NOT NULL `;
  }

  if (searchParams.markets === 'none') {
    whereClause += ` AND mrkts.platform_ad_account_id IS NULL `;
  } else if (searchParams.markets === 'any') {
    whereClause += ` AND mrkts.platform_ad_account_id IS NOT NULL `;
  }

  if (searchParams.workspaces === 'none') {
    whereClause += ` AND prtnrs.platform_ad_account_id IS NULL `;
  } else if (searchParams.workspaces === 'any') {
    whereClause += ` AND prtnrs.platform_ad_account_id IS NOT NULL `;
  }

  if (searchParams?.importStatus?.length > 0) {
    const importStatusInClause = searchParams.importStatus
      .map((importStatus) => `'${importStatus}'`)
      .join(',');

    whereClause += ` AND paais.status IN (${importStatusInClause})`;
  }

  return whereClause;
};

const getAccountsDashboardSortClause = (
  searchParams: AdAccountHealthSearchParamsDto,
) => {
  const sortOrder = getAccountsDashboardSortOrder(searchParams);
  if (
    searchParams?.sortBy === PlatformAdAccountHealthSortBy.CONNECTION_STATUS
  ) {
    return `connection_status ${sortOrder}, paa.platform ASC, paa.platform_account_name ASC`;
  } else if (searchParams?.sortBy === PlatformAdAccountHealthSortBy.CHANNEL) {
    return `paa.platform ${sortOrder}, paa.platform_account_name ASC, connection_status ASC`;
  } else if (
    searchParams?.sortBy === PlatformAdAccountHealthSortBy.IMPORT_STATUS
  ) {
    return `import_status ${sortOrder}, paa.platform_account_name ASC, connection_status ASC`;
  } else if (
    searchParams?.sortBy === PlatformAdAccountHealthSortBy.LAST_CONNECTED_ON
  ) {
    return `last_connected_on ${sortOrder}, paa.platform_account_name ASC, connection_status ASC`;
  } else if (
    searchParams?.sortBy === PlatformAdAccountHealthSortBy.LAST_IMPORTED_ON
  ) {
    return `paa.last_import_success_date ${sortOrder}, paa.platform_account_name ASC, connection_status ASC`;
  } else {
    return `paa.platform_account_name ${sortOrder}, paa.platform ASC, connection_status ASC`;
  }
};

const getAccountsDashboardSortOrder = (
  searchParams: AdAccountHealthSearchParamsDto,
): string => {
  return !isEmpty(searchParams.sortOrder) &&
    searchParams.sortOrder.toUpperCase() === SortOrder.DESC.toString()
    ? SortOrder.DESC
    : SortOrder.ASC;
};

const getAccountsDashboardHavingClause = (
  searchParams: AdAccountHealthSearchParamsDto,
) => {
  let havingClause = '';

  if (searchParams?.connectionStatus?.length > 0) {
    const connectionStatusInClause = searchParams.connectionStatus
      .map((connectionStatus) => `'${connectionStatus}'`)
      .join(',');

    havingClause = ` HAVING connection_status IN (${connectionStatusInClause})`;
  }

  return havingClause;
};

export const GET_ORGANIZATION_AD_ACCOUNTS_FOR_DASHBOARD_QUERY = (
  organizationId: string,
  searchParams: AdAccountHealthSearchParamsDto,
) => {
  return `
    SELECT paa.platform_account_id, paa.platform_account_name, paa.platform, paa.currency_code,
       CASE
         WHEN paa.can_access = TRUE AND SUM(IF(oaaup.account_accessible_by_user = TRUE AND oaaup.permission = 'ALLOW', 1,0)) > 0 THEN '${
           AdAccountConnectionStatus.CONNECTED
         }'
         WHEN paa.can_access = TRUE THEN '${
           AdAccountConnectionStatus.MISSING_PERMISSIONS
         }'
         ELSE '${AdAccountConnectionStatus.DISCONNECTED}'
         END AS connection_status,
       MAX(CASE WHEN oaaup.permission = 'ALLOW' THEN oaaup.date_last_connected END) AS last_connected_on,
       paa.last_import_success_date,
       GROUP_CONCAT(DISTINCT p.email SEPARATOR ',,') AS connected_by,
       prtnrs.workspaces,
       brnds.brands,
       mrkts.markets,
       MAX(DISTINCT CASE WHEN i1.parent_id IS NULL AND i1.root_id IS NULL THEN i1.id WHEN i1.parent_id=i1.root_id THEN i2.id ELSE i3.id END) as industry_group_id,
       MAX(DISTINCT CASE WHEN i1.parent_id IS NULL AND i1.root_id IS NULL THEN i1.name WHEN i1.parent_id=i1.root_id THEN i2.name ELSE i3.name END) as industry_group_name,
       MAX(DISTINCT CASE WHEN i1.parent_id=i1.root_id THEN i1.id ELSE i2.id END) as industry_id,
       MAX(DISTINCT CASE WHEN i1.parent_id=i1.root_id THEN i1.name ELSE i2.name END) as industry_name,
       MAX(DISTINCT CASE WHEN i1.parent_id<>i1.root_id THEN i1.id END) as sub_industry_id,
       MAX(DISTINCT CASE WHEN i1.parent_id<>i1.root_id THEN i1.name END) as sub_industry_name,
       COUNT(CASE WHEN p.id = ? THEN 1 END) > 0 AS is_connected_by_user,
       paais.status as import_status
    FROM organization_platform_ad_account_map opaam
    LEFT JOIN platform_ad_account paa ON opaam.platform_ad_account_id = paa.id
    LEFT JOIN platform_ad_account_import_status paais ON paais.id = paa.last_import_status_id
    LEFT JOIN organization_ad_account_user_permissions oaaup ON paa.id = oaaup.platform_ad_account_id AND oaaup.organization_id = opaam.organization_id 
    LEFT JOIN person p ON oaaup.user_id = p.id
    LEFT JOIN (
      SELECT opaam.organization_id, opaam.platform_ad_account_id,
             GROUP_CONCAT(DISTINCT p2.name SEPARATOR ',,') AS workspaces
      FROM workspace_platform_ad_account_map wpaam
             INNER JOIN partner p2 ON wpaam.partner_id = p2.id
             INNER JOIN organization_platform_ad_account_map opaam ON opaam.platform_ad_account_id = wpaam.platform_ad_account_id AND p2.organization_id = opaam.organization_id
      WHERE opaam.organization_id = ?
      GROUP BY opaam.organization_id, opaam.platform_ad_account_id
    ) prtnrs ON (paa.id = prtnrs.platform_ad_account_id)
    LEFT JOIN (
      SELECT opaam.platform_ad_account_id,
             GROUP_CONCAT(DISTINCT b.name SEPARATOR ',,') AS brands
      FROM platform_ad_account_brand_map paabm
             INNER JOIN brand b ON paabm.brand_id = b.id
             INNER JOIN organization_platform_ad_account_map opaam ON opaam.platform_ad_account_id = paabm.platform_ad_account_id AND b.organization_id = opaam.organization_id
      WHERE opaam.organization_id = ?
      GROUP BY opaam.platform_ad_account_id
    ) brnds on (paa.id = brnds.platform_ad_account_id)
    LEFT JOIN (
      select paamm.platform_ad_account_id,
             GROUP_CONCAT(DISTINCT m.name SEPARATOR ',,') AS markets
      FROM platform_ad_account_market_map paamm
             INNER JOIN organization_platform_ad_account_map opaam ON opaam.platform_ad_account_id = paamm.platform_ad_account_id
             LEFT JOIN country m ON paamm.country_iso_code = m.iso_code
      WHERE opaam.organization_id = ?
      GROUP BY paamm.platform_ad_account_id
    ) mrkts on (paa.id = mrkts.platform_ad_account_id)
    LEFT JOIN platform_ad_account_industry paai ON paai.platform_ad_account_id = paa.id
    LEFT JOIN industry i1 ON paai.industry_id = i1.id
    LEFT JOIN industry i2 ON i1.parent_id = i2.id
    LEFT JOIN industry i3 ON i2.parent_id = i3.id
    ${getOrganizationAdAccountsForDashboardWhereClause(searchParams)}
    GROUP BY paa.platform_account_id, paa.platform_account_name, paa.platform, paa.can_access
    ${getAccountsDashboardHavingClause(searchParams)}
    ORDER BY ${getAccountsDashboardSortClause(searchParams)}
    LIMIT ? OFFSET ?
  `;
};

export const GET_ORGANIZATION_AD_ACCOUNTS_FOR_DASHBOARD_TOTAL_COUNT = (
  organizationId: string,
  searchParams: AccountSearchParamsDto,
) => {
  return `
  SELECT COUNT(opaam.platform_ad_account_id) as count,
    CASE
      WHEN paa.can_access = TRUE AND SUM(IF(oaaup.account_accessible_by_user = TRUE, 1,0)) > 0 THEN '${
        AdAccountConnectionStatus.CONNECTED
      }'
      WHEN paa.can_access = TRUE THEN '${
        AdAccountConnectionStatus.MISSING_PERMISSIONS
      }'
      ELSE '${AdAccountConnectionStatus.DISCONNECTED}'
      END AS connection_status
  FROM organization_platform_ad_account_map opaam
  LEFT JOIN platform_ad_account paa ON opaam.platform_ad_account_id = paa.id
  LEFT JOIN platform_ad_account_import_status paais ON paais.id = paa.last_import_status_id
  LEFT JOIN organization_ad_account_user_permissions oaaup ON paa.id = oaaup.platform_ad_account_id AND oaaup.organization_id = opaam.organization_id 
  LEFT JOIN person p ON oaaup.user_id = p.id
  LEFT JOIN (
    SELECT opaam.platform_ad_account_id,
           GROUP_CONCAT(DISTINCT b.name SEPARATOR ',,') AS brands
    FROM platform_ad_account_brand_map paabm
           INNER JOIN brand b ON paabm.brand_id = b.id
           INNER JOIN organization_platform_ad_account_map opaam ON opaam.platform_ad_account_id = paabm.platform_ad_account_id AND b.organization_id = opaam.organization_id
    WHERE opaam.organization_id = ?
    GROUP BY opaam.platform_ad_account_id
  ) brnds on (paa.id = brnds.platform_ad_account_id)
  LEFT JOIN (
    select paamm.platform_ad_account_id,
           GROUP_CONCAT(DISTINCT m.name SEPARATOR ',,') AS markets
    FROM platform_ad_account_market_map paamm
           INNER JOIN organization_platform_ad_account_map opaam ON opaam.platform_ad_account_id = paamm.platform_ad_account_id
           LEFT JOIN country m ON paamm.country_iso_code = m.iso_code
    WHERE opaam.organization_id = ?
    GROUP BY paamm.platform_ad_account_id
  ) mrkts on (paa.id = mrkts.platform_ad_account_id)
  LEFT JOIN (
    SELECT opaam.organization_id, opaam.platform_ad_account_id,
            GROUP_CONCAT(DISTINCT p2.name SEPARATOR ',,') AS workspaces
    FROM workspace_platform_ad_account_map wpaam
            INNER JOIN partner p2 ON wpaam.partner_id = p2.id
            INNER JOIN organization_platform_ad_account_map opaam ON opaam.platform_ad_account_id = wpaam.platform_ad_account_id AND p2.organization_id = opaam.organization_id
    WHERE opaam.organization_id = ?
    GROUP BY opaam.organization_id, opaam.platform_ad_account_id
  ) prtnrs ON (paa.id = prtnrs.platform_ad_account_id)
  ${getOrganizationAdAccountsForDashboardWhereClause(searchParams)}
  GROUP BY opaam.platform_ad_account_id
  ${getAccountsDashboardHavingClause(searchParams)}`;
};

export const GET_WORKSPACE_AD_ACCOUNTS_FOR_DASHBOARD_QUERY = (
  organizationId: string,
  workspaceId: number,
  searchParams: AdAccountHealthSearchParamsDto,
) => {
  return `
    SELECT paa.platform_account_id, paa.platform_account_name, paa.platform, paa.currency_code,
      CASE
        WHEN paa.can_access = TRUE AND SUM(IF(oaaup.account_accessible_by_user = TRUE AND oaaup.permission = 'ALLOW', 1,0)) > 0 THEN '${
          AdAccountConnectionStatus.CONNECTED
        }'
        WHEN paa.can_access = TRUE THEN '${
          AdAccountConnectionStatus.MISSING_PERMISSIONS
        }'
        ELSE '${AdAccountConnectionStatus.DISCONNECTED}'
      END AS connection_status,
      MAX(CASE WHEN oaaup.permission = 'ALLOW' THEN oaaup.date_last_connected END) AS last_connected_on,
      paa.last_import_success_date,
      GROUP_CONCAT(DISTINCT p.email SEPARATOR ',,') AS connected_by,
      prtnrs.workspaces,
      brnds.brands,
      mrkts.markets,
      MAX(DISTINCT CASE WHEN i1.parent_id IS NULL AND i1.root_id IS NULL THEN i1.id WHEN i1.parent_id=i1.root_id THEN i2.id ELSE i3.id END) as industry_group_id,
      MAX(DISTINCT CASE WHEN i1.parent_id IS NULL AND i1.root_id IS NULL THEN i1.name WHEN i1.parent_id=i1.root_id THEN i2.name ELSE i3.name END) as industry_group_name,
      MAX(DISTINCT CASE WHEN i1.parent_id=i1.root_id THEN i1.id ELSE i2.id END) as industry_id,
      MAX(DISTINCT CASE WHEN i1.parent_id=i1.root_id THEN i1.name ELSE i2.name END) as industry_name,
      MAX(DISTINCT CASE WHEN i1.parent_id<>i1.root_id THEN i1.id END) as sub_industry_id,
      MAX(DISTINCT CASE WHEN i1.parent_id<>i1.root_id THEN i1.name END) as sub_industry_name,
      COUNT(CASE WHEN p.id = ? THEN 1 END) > 0 AS is_connected_by_user,
      paais.status as import_status
    FROM workspace_platform_ad_account_map wpaam
    LEFT JOIN partner ws ON wpaam.partner_id  = ws.id
    LEFT JOIN platform_ad_account paa ON wpaam.platform_ad_account_id = paa.id
    LEFT JOIN platform_ad_account_import_status paais ON paais.id = paa.last_import_status_id
    LEFT JOIN organization_ad_account_user_permissions oaaup ON paa.id = oaaup.platform_ad_account_id AND oaaup.organization_id = ws.organization_id 
    LEFT JOIN person p ON oaaup.user_id = p.id
    LEFT JOIN (
      SELECT wpaam2.platform_ad_account_id,
             GROUP_CONCAT(DISTINCT p1.name SEPARATOR ',,') AS workspaces
      FROM workspace_platform_ad_account_map wpaam1
             INNER JOIN partner p1 ON wpaam1.partner_id = p1.id
             INNER JOIN workspace_platform_ad_account_map wpaam2 ON wpaam2.platform_ad_account_id = wpaam1.platform_ad_account_id
             INNER JOIN partner p2 ON wpaam2.partner_id = p2.id AND p2.organization_id = p1.organization_id
      WHERE p2.organization_id = ? and wpaam2.partner_id = ?
      GROUP BY wpaam2.platform_ad_account_id
    ) prtnrs ON (paa.id = prtnrs.platform_ad_account_id)
    LEFT JOIN (
      SELECT wpaam2.platform_ad_account_id,
             GROUP_CONCAT(DISTINCT b.name SEPARATOR ',,') AS brands
      FROM platform_ad_account_brand_map paabm
             INNER JOIN brand b ON paabm.brand_id = b.id
             INNER JOIN workspace_platform_ad_account_map wpaam2 ON wpaam2.platform_ad_account_id = paabm.platform_ad_account_id
             INNER JOIN partner p2 ON wpaam2.partner_id = p2.id AND p2.organization_id = b.organization_id
      WHERE p2.organization_id = ? and wpaam2.partner_id = ?
      GROUP BY wpaam2.platform_ad_account_id
    ) brnds on (paa.id = brnds.platform_ad_account_id)
    LEFT JOIN (
      select wpaam2.platform_ad_account_id,
             GROUP_CONCAT(DISTINCT m.name SEPARATOR ',,') AS markets
      FROM platform_ad_account_market_map paamm
             LEFT JOIN country m ON paamm.country_iso_code = m.iso_code
             INNER JOIN workspace_platform_ad_account_map wpaam2 ON wpaam2.platform_ad_account_id = paamm.platform_ad_account_id
             INNER JOIN partner p2 ON wpaam2.partner_id = p2.id
      WHERE p2.organization_id = ? and wpaam2.partner_id = ?
      GROUP BY wpaam2.platform_ad_account_id
    ) mrkts on (paa.id = mrkts.platform_ad_account_id)
    LEFT JOIN platform_ad_account_industry paai ON paai.platform_ad_account_id = paa.id
    LEFT JOIN industry i1 ON paai.industry_id = i1.id
    LEFT JOIN industry i2 ON i1.parent_id = i2.id
    LEFT JOIN industry i3 ON i2.parent_id = i3.id
    ${getWorkspaceAdAccountsForDashboardWhereClause(searchParams)}
    GROUP BY paa.platform_account_id, paa.platform_account_name, paa.platform, paa.can_access
    ${getAccountsDashboardHavingClause(searchParams)}
    ORDER BY ${getAccountsDashboardSortClause(searchParams)}
    LIMIT ? OFFSET ?
  `;
};

export const GET_WORKSPACE_AD_ACCOUNTS_FOR_DASHBOARD_TOTAL_COUNT = (
  organizationId: string,
  workspaceId: number,
  searchParams: AdAccountHealthSearchParamsDto,
) => {
  return `
    SELECT COUNT(wpaam.platform_ad_account_id) as count,
      CASE
         WHEN paa.can_access = TRUE AND SUM(IF(oaaup.account_accessible_by_user = TRUE, 1,0)) > 0 THEN '${
           AdAccountConnectionStatus.CONNECTED
         }'
         WHEN paa.can_access = TRUE THEN '${
           AdAccountConnectionStatus.MISSING_PERMISSIONS
         }'
         ELSE '${AdAccountConnectionStatus.DISCONNECTED}'
         END AS connection_status
    FROM workspace_platform_ad_account_map wpaam
    LEFT JOIN partner ws ON wpaam.partner_id  = ws.id
    LEFT JOIN platform_ad_account paa ON wpaam.platform_ad_account_id = paa.id
    LEFT JOIN platform_ad_account_import_status paais ON paais.id = paa.last_import_status_id
    LEFT JOIN organization_ad_account_user_permissions oaaup ON paa.id = oaaup.platform_ad_account_id AND oaaup.organization_id = ws.organization_id 
    LEFT JOIN person p ON oaaup.user_id = p.id
    LEFT JOIN (
      SELECT wpaam2.platform_ad_account_id,
             GROUP_CONCAT(DISTINCT b.name SEPARATOR ',,') AS brands
      FROM platform_ad_account_brand_map paabm
             INNER JOIN brand b ON paabm.brand_id = b.id
             INNER JOIN workspace_platform_ad_account_map wpaam2 ON wpaam2.platform_ad_account_id = paabm.platform_ad_account_id
             INNER JOIN partner p2 ON wpaam2.partner_id = p2.id AND p2.organization_id = b.organization_id
      WHERE p2.organization_id = ? and wpaam2.partner_id = ?
      GROUP BY wpaam2.platform_ad_account_id
    ) brnds on (paa.id = brnds.platform_ad_account_id)
    LEFT JOIN (
      select wpaam2.platform_ad_account_id,
             GROUP_CONCAT(DISTINCT m.name SEPARATOR ',,') AS markets
      FROM platform_ad_account_market_map paamm
             LEFT JOIN country m ON paamm.country_iso_code = m.iso_code
             INNER JOIN workspace_platform_ad_account_map wpaam2 ON wpaam2.platform_ad_account_id = paamm.platform_ad_account_id
             INNER JOIN partner p2 ON wpaam2.partner_id = p2.id
      WHERE p2.organization_id = ? and wpaam2.partner_id = ?
      GROUP BY wpaam2.platform_ad_account_id
    ) mrkts on (paa.id = mrkts.platform_ad_account_id)
    LEFT JOIN (
      SELECT wpaam2.platform_ad_account_id,
             GROUP_CONCAT(DISTINCT p1.name SEPARATOR ',,') AS workspaces
      FROM workspace_platform_ad_account_map wpaam1
             INNER JOIN partner p1 ON wpaam1.partner_id = p1.id
             INNER JOIN workspace_platform_ad_account_map wpaam2 ON wpaam2.platform_ad_account_id = wpaam1.platform_ad_account_id
             INNER JOIN partner p2 ON wpaam2.partner_id = p2.id AND p2.organization_id = p1.organization_id
      WHERE p2.organization_id = ? and wpaam2.partner_id = ?
      GROUP BY wpaam2.platform_ad_account_id
    ) prtnrs ON (paa.id = prtnrs.platform_ad_account_id)
    ${getWorkspaceAdAccountsForDashboardWhereClause(searchParams)}
    GROUP BY wpaam.platform_ad_account_id
    ${getAccountsDashboardHavingClause(searchParams)}`;
};
