import { Test, TestingModule } from '@nestjs/testing';
import { AdAccountsController } from './ad-accounts.controller';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { AdAccountsProfile } from './mapper/ad-accounts.profile';
import { getRepositoryToken } from '@nestjs/typeorm';
import { PlatformAdAccount } from './entities/ad-account.entity';
import { Repository } from 'typeorm';
import { PlatformAdAccountToWorkspace } from './entities/ad-account-workspace-map.entity';
import { OrganizationPlatformAdAccountMap } from './entities/organization-platform-ad-account-map.entity';
import { OrganizationAdAccountsService } from './organization-ad-accounts.service';
import { AdAccountsService } from './ad-accounts.service';
import { OrganizationAdAccountUserPermissions } from './entities/organization-ad-account-user-permissions.entity';
import { PlatformAdAccountBrandMap } from './entities/platform-ad-account-brand-map.entity';
import { PlatformAdAccountMarketMap } from './entities/platform-ad-account-market-map.entity';
import { BrandService } from 'src/brands/brand.service';
import { Brand } from 'src/brands/entities/brand.entity';
import { Market } from 'src/markets/entities/market.entity';
import { MarketService } from 'src/markets/market.service';
import { WorkspaceMarket } from 'src/workspaces/entities/workspace-market.entity';
import { WorkspaceService } from '../workspaces/workspaces.service';
import { PlatformAdAccountIndustryMap } from './entities/platform-ad-account-industry-map.entity';
import { AdAccountImportStatusService } from './ad-account-import-status.service';
import { SqsService } from '@vidmob/vidmob-nestjs-common';

const TEST_AD_ACCOUNT_ID = 'act_1234567890';
const TEST_USER_ID = 1234;

describe('AdAccountsController', () => {
  let controller: AdAccountsController;
  let organizationAdAccountsService: OrganizationAdAccountsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdAccountsController],
      providers: [
        OrganizationAdAccountsService,
        AdAccountsService,
        SqsService,
        AdAccountImportStatusService,
        AdAccountsProfile,
        BrandService,
        MarketService,
        {
          provide: getRepositoryToken(PlatformAdAccount),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountToWorkspace),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(OrganizationPlatformAdAccountMap),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(OrganizationAdAccountUserPermissions),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountBrandMap),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountMarketMap),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(Brand),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(Market),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(WorkspaceMarket),
          useValue: Repository,
        },
        {
          provide: getRepositoryToken(PlatformAdAccountIndustryMap),
          useValue: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    })
      .overrideProvider(WorkspaceService)
      .useValue({})
      .overrideProvider(AdAccountsService)
      .useValue({})
      .overrideProvider(SqsService)
      .useValue({})
      .compile();

    controller = module.get<AdAccountsController>(AdAccountsController);
    organizationAdAccountsService = module.get<OrganizationAdAccountsService>(
      OrganizationAdAccountsService,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('test findOrganizationsForAdAccount method', async () => {
    const findOrganizationsForAdAccountSpy = jest
      .spyOn(organizationAdAccountsService, 'findOrganizationsForAdAccount')
      .mockResolvedValue([]);
    await controller.findOrganizationsForAdAccount(TEST_AD_ACCOUNT_ID);
    expect(findOrganizationsForAdAccountSpy).toHaveBeenCalledWith([
      TEST_AD_ACCOUNT_ID,
    ]);
  });

  it('test getUserAccountPermissionsAcrossOrganizations method', async () => {
    const getUserAccountPermissionsAcrossOrganizationsSpy = jest
      .spyOn(
        organizationAdAccountsService,
        'getUserAccountPermissionsAcrossOrganizations',
      )
      .mockResolvedValue([]);
    await controller.getUserAccountPermissionsAcrossOrganizations(
      TEST_AD_ACCOUNT_ID,
      TEST_USER_ID,
    );
    expect(
      getUserAccountPermissionsAcrossOrganizationsSpy,
    ).toHaveBeenCalledWith(TEST_AD_ACCOUNT_ID, TEST_USER_ID);
  });
});
