import { AutoMap } from '@automapper/classes';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm';
import { PlatformAdAccount } from './ad-account.entity';
import { Organization } from '../../organizations/entities/organization.entity';
import { User } from '../../entities/user.entity';
import { Exclude } from 'class-transformer';

@Entity('organization_ad_account_user_permissions')
export class OrganizationAdAccountUserPermissions {
  @AutoMap()
  @PrimaryColumn({
    name: 'organization_id',
  })
  organizationId: string;

  @AutoMap()
  @PrimaryColumn({
    name: 'platform_ad_account_id',
  })
  platformAdAccountId: number;

  @AutoMap()
  @PrimaryColumn({
    name: 'user_id',
  })
  userId: number;

  @AutoMap()
  @Column({
    name: 'permission',
    type: 'varchar',
    length: 50,
  })
  permission: string;

  @AutoMap()
  @Column({
    name: 'date_last_connected',
  })
  dateLastConnected: Date;

  @AutoMap()
  @Column({
    name: 'date_created',
  })
  dateCreated: Date;

  @AutoMap()
  @Column({
    name: 'last_updated',
  })
  lastUpdated: Date;

  @AutoMap()
  @Column({
    name: 'account_accessible_by_user',
  })
  accountAccessibleByUser: boolean;

  @ManyToOne(() => PlatformAdAccount)
  @JoinColumn({ name: 'platform_ad_account_id' })
  @Exclude()
  platformAdAccount: PlatformAdAccount;

  @ManyToOne(() => Organization)
  @Exclude()
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @ManyToOne(() => User)
  @Exclude()
  @JoinColumn({ name: 'user_id' })
  user: User;
}
