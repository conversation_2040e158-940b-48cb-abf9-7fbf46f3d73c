import { AutoMap } from '@automapper/classes';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { PlatformAdAccount } from './ad-account.entity';
import { Industry } from '../../industry/entities/industry.entity';

@Entity('platform_ad_account_industry')
export class PlatformAdAccountIndustryMap {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @PrimaryColumn({ name: 'platform_ad_account_id', type: 'bigint' })
  platformAdAccountId: number;

  @ManyToOne(
    () => PlatformAdAccount,
    (platformAdAccount) => platformAdAccount.id,
  )
  @JoinColumn({ name: 'platform_ad_account_id' })
  platformAdAccount: PlatformAdAccount;

  @AutoMap()
  @PrimaryColumn({ name: 'industry_id', type: 'bigint' })
  industryId: number;

  @ManyToOne(() => Industry)
  @JoinColumn({ name: 'industry_id' })
  industry: Industry;

  @AutoMap()
  @PrimaryColumn({ name: 'assigning_person_id', type: 'bigint' })
  assigningPersonId: number;

  @AutoMap()
  @Column({ name: 'date_created' })
  dateCreated: Date;

  @AutoMap()
  @Column({ name: 'last_updated' })
  lastUpdated: Date;
}
