import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { PlatformAdAccount } from './ad-account.entity';
import { Workspace } from '../../workspaces/entities/workspace.entity';

@Entity('workspace_platform_ad_account_map')
export class PlatformAdAccountToWorkspace {
  @AutoMap()
  @PrimaryColumn({
    name: 'partner_id',
  })
  partnerId: number;

  @AutoMap()
  @PrimaryColumn({
    name: 'platform_ad_account_id',
  })
  platformAdAccountId: number;

  @ManyToOne(
    () => PlatformAdAccount,
    (platformAdAccount) => platformAdAccount.id,
  )
  @JoinColumn({ name: 'platform_ad_account_id' })
  platformAdAccount: PlatformAdAccount;

  @ManyToOne(() => Workspace, (workspace) => workspace.id)
  @JoinColumn({ name: 'partner_id' })
  workspace: Workspace;
}
