import { AutoMap } from '@automapper/classes';
import { Market } from 'src/markets/entities/market.entity';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm';
import { PlatformAdAccount } from './ad-account.entity';
import { Exclude } from 'class-transformer';

@Entity('platform_ad_account_market_map')
export class PlatformAdAccountMarketMap {
  @AutoMap()
  @PrimaryColumn({ name: 'platform_ad_account_id', type: 'bigint' })
  platformAdAccountId: number;

  @ManyToOne(() => PlatformAdAccount)
  @JoinColumn({ name: 'platform_ad_account_id' })
  @Exclude()
  platformAdAccount: PlatformAdAccount;

  @AutoMap()
  @PrimaryColumn({ name: 'country_iso_code', type: 'varchar', length: 3 })
  countryIsoCode: string;

  @ManyToOne(() => Market, (market) => market.isoCode)
  @JoinColumn({ name: 'country_iso_code' })
  market: Market;

  @AutoMap()
  @Column({ name: 'date_created' })
  dateCreated: Date;
}
