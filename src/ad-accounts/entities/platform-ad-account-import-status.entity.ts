/**

*/
import { AutoMap } from '@automapper/classes';
import {
  Column,
  <PERSON><PERSON><PERSON>,
  Join<PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { PlatformAdAccount } from './ad-account.entity';
import { Organization } from '../../organizations/entities/organization.entity';
import { User } from '../../entities/user.entity';
import { ImportStatus } from '../../common/constants/constants';

export function isStatusActive(status: ImportStatus): boolean {
  return status === ImportStatus.ENABLED;
}

@Entity('platform_ad_account_import_status')
export class PlatformAdAccountImportStatus {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => PlatformAdAccount)
  @JoinColumn({ name: 'platform_ad_account_id' })
  platformAdAccount: PlatformAdAccount;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @AutoMap()
  @Column({ name: 'active', type: 'boolean' })
  active: boolean;

  @AutoMap()
  @Column({
    name: 'status',
    type: 'enum',
    enum: ImportStatus,
  })
  status: ImportStatus;

  @AutoMap()
  @Column({
    name: 'reason',
    nullable: true,
    type: 'json',
  })
  reason: any;

  @AutoMap()
  @Column({ name: 'import_id', nullable: true })
  importId: string | null;

  @AutoMap()
  @Column({ name: 'rebuild_request_id', nullable: true })
  rebuildRequestId: number | null;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'person_id' })
  person: User | null;

  @AutoMap()
  @Column({
    name: 'date_created',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  dateCreated: Date;
}
