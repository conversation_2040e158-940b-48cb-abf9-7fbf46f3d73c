import { AutoMap } from '@automapper/classes';
import {
  Column,
  <PERSON>tity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { PlatformAdAccount } from './ad-account.entity';
import { Exclude } from 'class-transformer';

@Entity('platform_ad_account_sequential_failure')
export class PlatformAdAccountSequentialFailure {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({
    name: 'rebuild_request_id',
    nullable: true,
  })
  rebuildRequestId: number;

  @AutoMap()
  @Column({
    name: 'platform_ad_account_id',
    nullable: false,
  })
  platformAdAccountId: number;

  @AutoMap()
  @Column({
    name: 'import_id',
    nullable: true,
  })
  importId: string;

  @AutoMap()
  @Column({
    name: 'failure_reason',
    nullable: true,
    type: 'json',
  })
  failureReason: any;

  @AutoMap()
  @Column({
    name: 'date_created',
    type: 'datetime',
    nullable: true,
  })
  dateCreated: Date;

  @AutoMap()
  @Column({
    name: 'last_updated',
    type: 'datetime',
    nullable: true,
  })
  lastUpdated: Date;

  @ManyToOne(() => PlatformAdAccount)
  @JoinColumn({ name: 'platform_ad_account_id' })
  @Exclude()
  platformAdAccount: PlatformAdAccount;
}
