import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { PlatformAdAccountToWorkspace } from './ad-account-workspace-map.entity';
import { User } from '../../entities/user.entity';
import { OrganizationPlatformAdAccountMap } from './organization-platform-ad-account-map.entity';
import { OrganizationAdAccountUserPermissions } from './organization-ad-account-user-permissions.entity';
import { PlatformAdAccountBrandMap } from './platform-ad-account-brand-map.entity';
import { Exclude } from 'class-transformer';
import { PlatformAdAccountMarketMap } from './platform-ad-account-market-map.entity';
import { WorkspaceAdAccountMap } from '../../workspaces/workspace-ad-account/entities/workspace-ad-account-map.entities';
import { PlatformAdAccountSequentialFailure } from './platform-ad-account-sequential-failure.entity';
import { PlatformAdAccountImportStatus } from './platform-ad-account-import-status.entity';

@Entity('platform_ad_account')
export class PlatformAdAccount {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ name: 'user_id' })
  userId: number;

  @AutoMap()
  @Column({ length: 50 })
  platform: string;

  @AutoMap()
  @Column({
    name: 'platform_user_id',
    length: 100,
  })
  platformUserId: string;

  @AutoMap()
  @Column({
    name: 'platform_organization_id',
    length: 100,
    nullable: true,
  })
  platformOrganizationId: string;

  @AutoMap()
  @Column({
    name: 'platform_account_id',
    length: 100,
  })
  platformAccountId: string;

  @AutoMap()
  @Column({
    name: 'currency_code',
    length: 3,
    type: 'char',
  })
  currencyCode: string;

  @AutoMap()
  @Column({
    name: 'platform_account_name',
    length: 256,
    type: 'char',
  })
  platformAccountName: string;

  @AutoMap()
  @Column({
    name: 'can_access',
    type: 'tinyint',
    default: 0,
  })
  canAccess: number;

  @AutoMap()
  @Column({
    name: 'processing_completed',
    type: 'tinyint',
    nullable: true,
  })
  processingCompleted: number;

  @AutoMap()
  @Column({
    name: 'processing_completed_date',
    type: 'datetime',
    nullable: true,
  })
  processingCompletedDate: Date;

  @AutoMap()
  @Column({
    name: 'import_v3_enabled',
    type: 'tinyint',
    nullable: true,
  })
  importV3Enabled: number;

  @AutoMap()
  @Column({
    name: 'date_created',
    type: 'datetime',
    nullable: true,
  })
  dateCreated: Date;

  @AutoMap()
  @Column({
    name: 'last_updated',
    type: 'datetime',
    nullable: true,
  })
  lastUpdated: Date;

  @AutoMap()
  @Column({
    name: 'last_import_start_date',
    type: 'datetime',
    nullable: true,
  })
  lastImportStartDate: Date;

  @AutoMap()
  @Column({
    name: 'last_import_complete_date',
    type: 'datetime',
    nullable: true,
  })
  lastImportCompleteDate: Date;

  @AutoMap()
  @Column({
    name: 'last_import_success_date',
    type: 'datetime',
    nullable: true,
  })
  lastImportSuccessDate: Date;

  /**
   * import status from very recent import
   */
  @AutoMap()
  @Column({
    name: 'last_import_status',
    length: 20,
    type: 'char',
  })
  lastImportStatus: string;

  /**
   * Calculated account overall import status from platform_ad_account_import_status table
   */
  @ManyToOne(() => PlatformAdAccountImportStatus, { nullable: true })
  @JoinColumn({ name: 'last_import_status_id' })
  @Exclude()
  accountImportStatus: PlatformAdAccountImportStatus | null;

  @AutoMap()
  @Column({
    name: 'import_active',
    type: 'tinyint',
    default: 0,
  })
  importActive: number;

  @OneToMany(
    () => PlatformAdAccountToWorkspace,
    (PlatformAdAccountToWorkspace) =>
      PlatformAdAccountToWorkspace.platformAdAccount,
  )
  @JoinColumn({ name: 'id' })
  public PlatformAdAccountToWorkspace: PlatformAdAccountToWorkspace[];

  @OneToMany(
    () => OrganizationPlatformAdAccountMap,
    (organizationPlatformAdAccountMap) =>
      organizationPlatformAdAccountMap.platformAdAccount,
  )
  @JoinColumn({ name: 'id' })
  public organizationPlatformAdAccountMap: OrganizationPlatformAdAccountMap[];

  @OneToMany(
    () => OrganizationAdAccountUserPermissions,
    (organizationAdAccountUserPermissions) =>
      organizationAdAccountUserPermissions.platformAdAccount,
  )
  @JoinColumn({ name: 'id' })
  public organizationAdAccountUserPermissions: OrganizationAdAccountUserPermissions[];

  @OneToOne(() => User, (User) => User.id)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(
    () => PlatformAdAccountImportStatus,
    (platformAdAccountImportStatus) =>
      platformAdAccountImportStatus.platformAdAccount,
  )
  @Exclude()
  @JoinColumn({ name: 'id' })
  public importStatuses: PlatformAdAccountImportStatus[];

  @OneToMany(
    () => PlatformAdAccountBrandMap,
    (platformAdAccountBrandMap) => platformAdAccountBrandMap.platformAdAccount,
  )
  @Exclude()
  @JoinColumn({ name: 'id' })
  public platformAdAccountBrandMap: PlatformAdAccountBrandMap[];

  @OneToMany(
    () => PlatformAdAccountMarketMap,
    (platformAdAccountMarketMap) =>
      platformAdAccountMarketMap.platformAdAccount,
  )
  @Exclude()
  @JoinColumn({ name: 'id' })
  public platformAdAccountMarketMap: PlatformAdAccountMarketMap[];

  @OneToMany(
    () => WorkspaceAdAccountMap,
    (workspaceAdAccountMap) => workspaceAdAccountMap.platformAdAccount,
  )
  @Exclude()
  @JoinColumn({ name: 'id' })
  public workspaceAdAccountMap: WorkspaceAdAccountMap[];

  @OneToMany(
    () => PlatformAdAccountSequentialFailure,
    (platformAdAccountSequentialFailure) =>
      platformAdAccountSequentialFailure.platformAdAccount,
  )
  @Exclude()
  @JoinColumn({ name: 'id' })
  public platformAdAccountSequentialFailures: PlatformAdAccountSequentialFailure[];
}
