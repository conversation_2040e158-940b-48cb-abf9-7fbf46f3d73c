import { AutoMap } from '@automapper/classes';
import { Brand } from 'src/brands/entities/brand.entity';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm';
import { PlatformAdAccount } from './ad-account.entity';
import { Exclude } from 'class-transformer';

@Entity('platform_ad_account_brand_map')
export class PlatformAdAccountBrandMap {
  @AutoMap()
  @PrimaryColumn({ name: 'platform_ad_account_id', type: 'bigint' })
  platformAdAccountId: number;

  @ManyToOne(() => PlatformAdAccount)
  @JoinColumn({ name: 'platform_ad_account_id' })
  @Exclude()
  platformAdAccount: PlatformAdAccount;

  @AutoMap()
  @PrimaryColumn({ name: 'brand_id', type: 'uuid' })
  brandId: string;

  @ManyToOne(() => Brand, (brand) => brand.id)
  @JoinColumn({ name: 'brand_id' })
  brand: Brand;

  @AutoMap()
  @Column({ name: 'date_created' })
  dateCreated: Date;
}
