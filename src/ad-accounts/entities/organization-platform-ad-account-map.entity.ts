import { AutoMap } from '@automapper/classes';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { PlatformAdAccount } from './ad-account.entity';
import { Organization } from '../../organizations/entities/organization.entity';
import { Exclude } from 'class-transformer';

@Entity('organization_platform_ad_account_map')
export class OrganizationPlatformAdAccountMap {
  /**
   * Organization Id
   */
  @AutoMap()
  @PrimaryColumn({
    name: 'organization_id',
  })
  organizationId: string;

  /**
   * Platform Ad Account Id
   */
  @AutoMap()
  @PrimaryColumn({
    name: 'platform_ad_account_id',
  })
  platformAdAccountId: number;

  /**
   * Platform Ad Account associated with the platform ad account id
   */
  @ManyToOne(() => PlatformAdAccount)
  @JoinColumn({ name: 'platform_ad_account_id' })
  @Exclude()
  platformAdAccount: PlatformAdAccount;

  /**
   * Organization associated with the organization id
   */
  @ManyToOne(() => Organization)
  @Exclude()
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;
}
