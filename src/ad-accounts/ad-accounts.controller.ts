import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  NotFoundException,
  Param,
  ParseBoolPipe,
  Post,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ReadOrganizationDto } from '../organizations/dto/read-organization.dto';
import { ApiExtraModels, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { AD_ACCOUNT_API_TAG_NAME } from '../common/constants/api.constants';
import { OrganizationAdAccountsService } from './organization-ad-accounts.service';
import { ReadOrganizationAdAccountUserPermissionsDto } from './dto/read-organization-ad-account-user-permissions.dto';
import { VmApiOkUnPaginatedArrayResponse } from '@vidmob/vidmob-nestjs-common/dist/docs/vmresponse.decorator';
import { ReadAdAccountOrganizationsDto } from './dto/read-ad-account-organizations.dto';
import { ReadAdAccountIdsDto } from './dto/read-ad-account-ids.dto';
import { VmApiOkResponse } from '@vidmob/vidmob-nestjs-common';
import { ReadAdAccountMapDto } from './dto/read-ad-account-map.dto';
import { AccountOrganizationIdsBodyDto } from './dto/account-organization-ids-body.dto';
import { ReadPlatformOrganizationUsersMapDto } from './dto/read-platform-organization-users-map.dto';
import { AdAccountsService } from './ad-accounts.service';
import { ReadAdAccountWithImportData } from './dto/read-ad-account-with-import-data.dto';
import { CreatePlatformAdAccountSequentialFailureDto } from './dto/create-platform-ad-account-sequential-failure.dto';
import { SuccessfulMessageResponseDto } from '../organization-invite/dto/successful-message-response.dto';
import { UpdatePlatformAdAccountRequestDto } from './dto/update-platform-ad-account-request.dto';
import { ReadPlatformAdAccountDto } from './dto/read-platform-ad-account.dto';
import { ReadAdAccountFiltersDto } from './dto/read-ad-account-filters.dto';
import { FilterType, ImportStatusForBFF } from 'src/common/constants/constants';
import { AccountsPreImportTasksSqsMessageBody } from './dto/read-accounts-pre-import-tasks-sqs-message-body.dto';

/**
 * Ad Account Controller
 */
@ApiTags(AD_ACCOUNT_API_TAG_NAME)
@ApiExtraModels(ReadOrganizationDto)
@Controller('ad-account')
export class AdAccountsController {
  constructor(
    private readonly adAccountsService: AdAccountsService,
    private readonly organizationAdAccountsService: OrganizationAdAccountsService,
  ) {}

  /**
   * This endpoint returns all organizations for the ad account id
   * @param adAccountId Ad Account Id
   * @returns list of organizations
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadOrganizationDto,
  })
  @ApiParam({ name: 'adAccountId', description: 'Ad Account Id' })
  @Get(':adAccountId/organizations')
  findOrganizationsForAdAccount(@Param('adAccountId') adAccountId: string) {
    return this.organizationAdAccountsService.findOrganizationsForAdAccount([
      adAccountId,
    ]);
  }

  /**
   * Get user account permissions across organizations.
   * @param accountId account id
   * @param userId user id
   * @returns ReadOrganizationAdAccountUserPermissionsDtoList user's list of ad account permissions across organizations
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadOrganizationAdAccountUserPermissionsDto,
    description: `Returns user's list of ad account permissions across organizations.`,
  })
  @ApiParam({
    name: 'accountId',
    description: 'Ad Account Id',
  })
  @ApiParam({
    name: 'userId',
    description: 'Logged in user Id',
  })
  @Get(':accountId/user/:userId/organization/permissions')
  async getUserAccountPermissionsAcrossOrganizations(
    @Param('accountId') accountId: string,
    @Param('userId') userId: number,
  ): Promise<ReadOrganizationAdAccountUserPermissionsDto[]> {
    return this.organizationAdAccountsService.getUserAccountPermissionsAcrossOrganizations(
      accountId,
      userId,
    );
  }

  @VmApiOkUnPaginatedArrayResponse({
    type: ReadAdAccountOrganizationsDto,
    description: `Returns organizations associated with ad accounts.`,
  })
  @Post('org-associations')
  async getAdAccountAssociatedOrganizations(
    @Body() dto: ReadAdAccountIdsDto,
  ): Promise<ReadAdAccountOrganizationsDto[]> {
    return this.organizationAdAccountsService.getAdAccountAssociatedOrganizations(
      dto.platformAccountIds,
    );
  }

  /**
   * Removes an ad account from organizations
   * @param accountId Account id
   * @param dto accountIds
   */
  @VmApiOkResponse({
    type: ReadAdAccountMapDto,
  })
  @ApiParam({ name: 'accountId', description: 'Ad Account Id' })
  @Post(':accountId/remove-organizations')
  async removeAdAccountFromOrganizations(
    @Param('accountId') accountId: string,
    @Body() removeOrganizationsDto: AccountOrganizationIdsBodyDto,
  ): Promise<ReadAdAccountMapDto> {
    await this.organizationAdAccountsService.removeAdAccountFromOrganizations(
      accountId,
      removeOrganizationsDto.organizationIds,
    );
    return {
      message: `Ad account - ${accountId} removed from organizations - ${removeOrganizationsDto.organizationIds}`,
    };
  }

  /**
   * Get list of all organizations with users for a platform.
   * @param platform platform name
   * @param importV3EnabledOnly import v3 enabled only
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadPlatformOrganizationUsersMapDto,
  })
  @ApiParam({
    name: 'platform',
    type: 'string',
    description: 'platform name',
  })
  @ApiQuery({
    name: 'importV3EnabledOnly',
    type: 'boolean',
    description: 'import v3 enabled only (default: false)',
    required: false,
  })
  @ApiQuery({
    name: 'importEnabledOrganizationsOnly',
    type: 'boolean',
    description: 'import enabled organizations only (default: true)',
    required: false,
  })
  @Get('import/platform/:platform/organization-users')
  async getOrganizationUsersPerPlatform(
    @Param('platform') platform: string,
    @Query('importV3EnabledOnly', new DefaultValuePipe(false), ParseBoolPipe)
    importV3EnabledOnly: boolean,
    @Query(
      'importEnabledOrganizationsOnly',
      new DefaultValuePipe(true),
      ParseBoolPipe,
    )
    importEnabledOrganizationsOnly: boolean,
  ): Promise<ReadPlatformOrganizationUsersMapDto[]> {
    if (!platform) {
      throw new NotFoundException(
        'Platform is required to list organization users.',
      );
    }
    return this.organizationAdAccountsService.getOrganizationUsersPerPlatform(
      platform,
      importV3EnabledOnly,
      importEnabledOrganizationsOnly,
    );
  }

  /**
   * Get ad account information along with import info
   * @deprecated Use getOrgPlatformAdAccountWithImportInfo instead
   * @param platform
   * @param adAccountId
   * @returns ReadAdAccountWithImportData ad account with import info
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadAdAccountWithImportData,
  })
  @ApiParam({
    name: 'platform',
    type: 'string',
    description: 'platform name',
  })
  @ApiParam({
    name: 'adAccountId',
    type: 'string',
    description: 'ad account id',
  })
  @Get('import/platform/:platform/account/:adAccountId')
  async getPlatformAdAccountWithImportInfo(
    @Param('platform') platform: string,
    @Param('adAccountId') adAccountId: string,
  ): Promise<ReadAdAccountWithImportData> {
    return this.adAccountsService.getPlatformAdAccountWithImportData(
      '',
      platform,
      adAccountId,
    );
  }

  /**
   * Get ad account information in organization along with import info
   * @param organizationId organization Id
   * @param platform platform name
   * @param adAccountId ad account id
   * @returns ReadAdAccountWithImportData ad account with import info
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadAdAccountWithImportData,
  })
  @ApiParam({
    name: 'platform',
    type: 'string',
    description: 'platform name',
  })
  @ApiParam({
    name: 'adAccountId',
    type: 'string',
    description: 'ad account id',
  })
  @ApiParam({
    name: 'organizationId',
    type: 'string',
    description: 'organization id',
  })
  @Get(
    'import/organization/:organizationId/platform/:platform/account/:adAccountId',
  )
  async getOrgPlatformAdAccountWithImportInfo(
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
    @Param('adAccountId') adAccountId: string,
  ): Promise<ReadAdAccountWithImportData> {
    return this.adAccountsService.getPlatformAdAccountWithImportData(
      organizationId,
      platform,
      adAccountId,
    );
  }

  /**
   * Save platform ad account sequential failure record
   * @deprecated should no longer be used by IMS
   * @param createPlatformAdAccountSequentialFailureDto The sequential failure DTO
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: SuccessfulMessageResponseDto,
  })
  @ApiParam({
    name: 'adAccountId',
    type: 'string',
    description: 'ad account id',
  })
  @Post('import/account/:adAccountId/failure')
  async savePlatformAdAccountSequentialFailure(
    @Param('adAccountId') adAccountId: string,
    @Body() dto: CreatePlatformAdAccountSequentialFailureDto,
  ): Promise<SuccessfulMessageResponseDto> {
    await this.adAccountsService.savePlatformAdAccountSequentialFailure(
      adAccountId,
      dto,
    );
    return {
      message: `Platform ad account sequential failure record saved successfully for account id: ${adAccountId}`,
    };
  }

  /**
   * Delete all sequential failures for a platform ad account
   * @deprecated should no longer be used by IMS
   * @param adAccountId The ad account id
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: SuccessfulMessageResponseDto,
  })
  @ApiParam({
    name: 'adAccountId',
    type: 'string',
    description: 'ad account id',
  })
  @Delete('import/account/:adAccountId/failure')
  async deleteSequentialFailuresByPlatformAdAccountId(
    @Param('adAccountId') adAccountId: string,
  ): Promise<SuccessfulMessageResponseDto> {
    await this.adAccountsService.deleteSequentialFailuresByPlatformAdAccountId(
      adAccountId,
    );
    return {
      message: `Platform ad account sequential failure records deleted successfully for account id: ${adAccountId}`,
    };
  }

  /**
   * Update ad account upon import completion
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadPlatformAdAccountDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: 'string',
    description: 'organization id',
  })
  @ApiParam({
    name: 'platform',
    type: 'string',
    description: 'platform name',
  })
  @ApiParam({
    name: 'adAccountId',
    type: 'string',
    description: 'ad account id',
  })
  @Post('organization/:organizationId/platform/:platform/account/:adAccountId')
  @UsePipes(new ValidationPipe({ transform: true }))
  async updatePlatformAdAccount(
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
    @Param('adAccountId') adAccountId: string,
    @Body() updateAccountDto: UpdatePlatformAdAccountRequestDto,
  ) {
    this.adAccountsService.validateAccountUpdateRequestParameters(
      organizationId,
      platform,
      adAccountId,
      updateAccountDto,
    );
    return this.adAccountsService.updatePlatformAdAccount(
      organizationId,
      platform,
      adAccountId,
      updateAccountDto,
    );
  }

  @VmApiOkUnPaginatedArrayResponse({
    type: ReadAdAccountFiltersDto,
  })
  @Get('filters')
  getAvailableFiltersForAdAccounts(): ReadAdAccountFiltersDto {
    return {
      importStatus: {
        type: FilterType.MULTI_SELECT,
        values: Object.values(ImportStatusForBFF),
      },
    };
  }

  /**
   * Sync ad account ids received from platform to database.
   * @param organizationId organization Id
   * @param platform platform name
   * @param userId user Id
   * @param syncPlatformAdAccountsDto The DTO containing ad account ids
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: SuccessfulMessageResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: 'string',
  })
  @ApiParam({
    name: 'platform',
    type: 'string',
  })
  @ApiParam({
    name: 'userId',
    type: 'number',
  })
  @Post('sync/organization/:organizationId/platform/:platform/user/:userId')
  syncPlatformAdAccountsToDatabase(
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
    @Param('userId') userId: number,
    @Body() syncPlatformAdAccountsDto: ReadAdAccountIdsDto,
  ): Promise<SuccessfulMessageResponseDto> {
    const syncAccountsRequest: AccountsPreImportTasksSqsMessageBody = {
      organizationId,
      platform,
      userId,
      platformAccountIds: syncPlatformAdAccountsDto.platformAccountIds,
    };
    return this.organizationAdAccountsService.sendSyncPlatformAdAccountsMessage(
      syncAccountsRequest,
    );
  }
}
