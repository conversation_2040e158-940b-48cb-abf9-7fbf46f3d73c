import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { writeFileSync } from 'fs';
import * as yaml from 'js-yaml';
import { getSwaggerDoc } from './swagger';
import { VersioningType } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  const document = getSwaggerDoc(app);
  const docYaml = yaml.dump(document);
  writeFileSync('oas_sdk.yaml', docYaml);

  await app.close();
}
bootstrap();
