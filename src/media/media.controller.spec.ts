import { Test, TestingModule } from '@nestjs/testing';
import { MediaController } from './media.controller';
import { MediaService } from './media.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { PlatformMedia } from './entities/platform-media.entity';
import { Repository } from 'typeorm';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { OrganizationAdAccountsService } from '../ad-accounts/organization-ad-accounts.service';
import { Media, MediaType } from './entities/media.entity';
import { WorkspaceAssetVersion } from '../workspaces/entities/workspace-asset-version.entity';
import { OrganizationProfile } from '../organizations/mapper/organization.profile';
import { OrganizationUserQueryService } from '../organizations/organization-user/organization-user-query.service';
import { MediaThumbnail } from './entities/media-thumbnail.entity';
import { MediaPlaylist } from './entities/media-playlist.entity';
import { ConfigService } from '@nestjs/config';
import { StreamPlaylist } from './entities/stream-playlist.entity';
import { MediaVariant } from './entities/media-variant.entity';
import { MediaProfile } from './mapper/media.profile';

const TEST_MEDIA_ID = 1234;

describe('MediaController', () => {
  let mediaRepository: Repository<Media>;
  let mediaController: MediaController;
  let mediaService: MediaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MediaController],
      providers: [
        MediaService,
        OrganizationProfile,
        MediaProfile,
        ConfigService,
        {
          provide: getRepositoryToken(PlatformMedia),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(Media),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(MediaThumbnail),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(MediaPlaylist),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(StreamPlaylist),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(MediaVariant),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(WorkspaceAssetVersion),
          useClass: Repository,
        },
        {
          provide: OrganizationAdAccountsService,
          useValue: {
            findOrganizationsForMedia: jest.fn(),
          },
        },
        {
          provide: OrganizationUserQueryService,
          useValue: {
            getOrgIDsWithReadWorkspaceAllForPerson: jest.fn(),
          },
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    mediaService = module.get<MediaService>(MediaService);
    mediaController = module.get<MediaController>(MediaController);
    mediaRepository = module.get<Repository<Media>>(getRepositoryToken(Media));
  });

  it('should be defined', () => {
    expect(mediaController).toBeDefined();
  });

  it('test findOrganizationsForMedia method', async () => {
    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce({
      id: TEST_MEDIA_ID,
      mediaType: MediaType.BACKGROUND_PLATFORM,
    } as Media);
    const findOrganizationsForMediaSpy = jest
      .spyOn(mediaService, 'findOrganizationsForMedia')
      .mockResolvedValue([]);
    await mediaController.findOrganizationsForMedia(TEST_MEDIA_ID);
    expect(findOrganizationsForMediaSpy).toHaveBeenCalledWith(TEST_MEDIA_ID);
  });

  it('test getPlatformMedia method', async () => {
    const platformMediaId = 'abc';
    const getPlatformMediaSpy = jest
      .spyOn(mediaService, 'getPlatformMediaById')
      .mockResolvedValue({
        mediaId: 123,
        platformMediaId,
        platformAccountId: 'act_14839483',
      });
    await mediaController.getPlatformMedia(platformMediaId);
    expect(getPlatformMediaSpy).toHaveBeenCalledWith(platformMediaId);
  });
});
