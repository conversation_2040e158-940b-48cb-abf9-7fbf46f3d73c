import {
  Injectable,
  NotFoundException,
  NotImplementedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { PlatformMedia } from './entities/platform-media.entity';
import { FACEBOOK_PLATFORM_NAME } from '../common/constants/constants';
import { ReadOrganizationDto } from '../organizations/dto/read-organization.dto';
import { OrganizationAdAccountsService } from '../ad-accounts/organization-ad-accounts.service';
import { Media, MediaType } from './entities/media.entity';
import { WorkspaceAssetVersion } from 'src/workspaces/entities/workspace-asset-version.entity';
import { Organization } from 'src/organizations/entities/organization.entity';
import { OrganizationUserQueryService } from '../organizations/organization-user/organization-user-query.service';
import {
  MEDIA_URL_EXPIRATION_TIME_SECONDS,
  MEDIA_MIME_TYPE_TO_EXTENSION_MAP,
  PLATFORM_MEDIA_PLATFORMS_TO_AD_ACCOUNT_PLATFORMS,
} from './media.constants';
import { ReadPlatformMediaDto } from './dto/read-platform-media.dto';
import { ReadMediaDto } from './dto/read-media.dto';
import { ConfigService } from '@nestjs/config';
import { S3 } from 'aws-sdk';
import { MediaAndUrlInfo } from './mapper/media.profile';

@Injectable()
export class MediaService {
  private readonly s3: S3;

  constructor(
    private readonly configService: ConfigService,
    @InjectRepository(PlatformMedia)
    private platformMediaRepository: Repository<PlatformMedia>,
    @InjectRepository(Media)
    private mediaRepository: Repository<Media>,
    @InjectRepository(WorkspaceAssetVersion)
    private workspaceAssetVersionRepository: Repository<WorkspaceAssetVersion>,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly organizationAdAccountService: OrganizationAdAccountsService,
    private readonly organizationUserQueryService: OrganizationUserQueryService,
  ) {
    this.s3 = new S3({
      signatureVersion: 'v4',
      region: this.configService.get('AWS_REGION'),
    });
  }

  /**
   * Extracts the platform account id from the platform media by removing 'act_' from facebook account ids
   * @param platformMedia platform media to extract the platform account id from
   * @return platform account id
   */
  private static extractPlatformAccountIdFromPlatformMedia(
    platformMedia: PlatformMedia,
  ): string {
    if (platformMedia.platform === FACEBOOK_PLATFORM_NAME) {
      return platformMedia.platformAccountId.replace('act_', '');
    } else {
      return platformMedia.platformAccountId;
    }
  }

  /**
   * Converts a platform name from platform_media table
   * to platform name from platform_ad_account table
   * @param platformMediaPlatform
   */
  private static convertPlatformMediaPlatformToPlatformAdAccountPlatform = (
    platformMediaPlatform: string,
  ): string => {
    const platformAdAccountPlatform =
      PLATFORM_MEDIA_PLATFORMS_TO_AD_ACCOUNT_PLATFORMS[platformMediaPlatform];
    if (!platformAdAccountPlatform) {
      throw new NotImplementedException(
        `Platform media platform ${platformMediaPlatform} not implemented.`,
      );
    }
    return platformAdAccountPlatform;
  };

  /**
   * Finds the platform account ids for a media id
   * @param mediaId media id to find the platform account ids for
   * @return platform account ids
   */
  private async findPlatformAccountIdsForMedia(
    mediaId: number,
  ): Promise<string[]> {
    const platformMedia = await this.platformMediaRepository.find({
      where: { mediaId: mediaId },
    });

    if (!platformMedia || platformMedia.length === 0) {
      throw new NotFoundException(
        `Media id ${mediaId} not found or associated with an ad account.`,
      );
    }
    const adAccountIds = platformMedia.map(
      MediaService.extractPlatformAccountIdFromPlatformMedia,
    );
    /*
     * This deduplication accounts for a past bug that caused the same platform media
     * for facebook to be created multiple times with the same account id, with and without "act_".
     */
    return [...new Set(adAccountIds)];
  }

  private async getS3SignedUrl(
    bucketName: string,
    key: string,
    mediaFileNameForDownload?: string | null,
  ) {
    const params: S3.GetObjectAclRequest & {
      Expires?: number;
      ResponseContentDisposition?: string;
    } = {
      Bucket: bucketName,
      Key: key,
      Expires: MEDIA_URL_EXPIRATION_TIME_SECONDS,
    };

    if (mediaFileNameForDownload) {
      const encodedMediaFileName = encodeURIComponent(
        mediaFileNameForDownload ?? '',
      );
      params.ResponseContentDisposition = `attachment; filename="${encodedMediaFileName}"`;
    }

    return this.s3.getSignedUrl('getObject', params);
  }

  private getMediaFileName(media: Media) {
    const extension =
      MEDIA_MIME_TYPE_TO_EXTENSION_MAP[media.mimeType ?? ''] || '';
    const mediaName = media.displayName || media.name;
    return mediaName.endsWith(extension)
      ? mediaName
      : `${mediaName}.${extension}`;
  }

  private async getStreamUrlsPromise(media: Media) {
    return Promise.all(
      media.mediaPlaylists.map((mediaPlaylist) =>
        this.getS3SignedUrl(
          media.bucketName,
          `${mediaPlaylist.path}/${mediaPlaylist.name}`,
        ),
      ),
    ).then((streamUrls) =>
      media.mediaPlaylists.reduce(
        (acc, playlist, i) => ({
          ...acc,
          [playlist.streamPlaylist.networkType]: streamUrls[i],
        }),
        {},
      ),
    );
  }

  private async getThumbnailUrlsPromise(media: Media) {
    return Promise.all(
      media.thumbnails.map((thumbnail) =>
        this.getS3SignedUrl(
          media.bucketName,
          `${media.path}/${thumbnail.name}`,
        ),
      ),
    ).then((thumbnailUrls) =>
      media.thumbnails.reduce(
        (acc, thumbnail, i) => ({
          ...acc,
          [thumbnail.width]: thumbnailUrls[i],
        }),
        {},
      ),
    );
  }

  async getMediaById(mediaId: number): Promise<ReadMediaDto> {
    const media = await this.mediaRepository.findOneBy({ id: mediaId });
    if (!media) {
      throw new NotFoundException(`Media id ${mediaId} not found.`);
    }

    const mediaKey = `${media.path}/${media.name}`;
    const mediaFileName = this.getMediaFileName(media);
    const thumbnailUrlsKeyedByWidthPromise =
      this.getThumbnailUrlsPromise(media);
    const streamsUrlKeyedByTypePromise = this.getStreamUrlsPromise(media);

    const [
      thumbnailUrlsKeyedByWidth,
      streamUrlsKeyedByType,
      mediaUrl,
      mediaDownloadUrl,
      mediaVariantUrl,
      mediaVariantDownloadUrl,
    ] = await Promise.all([
      thumbnailUrlsKeyedByWidthPromise,
      streamsUrlKeyedByTypePromise,
      this.getS3SignedUrl(media.bucketName, mediaKey),
      this.getS3SignedUrl(media.bucketName, mediaKey, mediaFileName),
      media.mediaVariant
        ? this.getS3SignedUrl(
            media.mediaVariant.s3Bucket,
            media.mediaVariant.s3Key,
          )
        : null,
      media.mediaVariant
        ? this.getS3SignedUrl(
            media.mediaVariant.s3Bucket,
            media.mediaVariant.s3Key,
            mediaFileName,
          )
        : null,
    ]);

    const mediaAndUrlInfo: MediaAndUrlInfo = {
      ...media,
      thumbnailUrlsKeyedByWidth,
      streamUrlsKeyedByType,
      mediaUrl,
      mediaDownloadUrl,
      mediaVariantUrl,
      mediaVariantDownloadUrl,
    };

    return this.classMapper.map(mediaAndUrlInfo, MediaAndUrlInfo, ReadMediaDto);
  }

  /**
   * Returns all organizations for the media id
   * @param mediaId Media Id
   * @returns list of organizations
   */
  async findOrganizationsForMedia(
    mediaId: number,
  ): Promise<ReadOrganizationDto[]> {
    const media = await this.mediaRepository.findOneBy({ id: mediaId });
    if (!media) {
      throw new NotFoundException(`Media id ${mediaId} not found.`);
    }

    switch (media.mediaType) {
      case MediaType.BACKGROUND_PLATFORM:
        return this.findOrganizationsForPlatformMedia(mediaId);
      case MediaType.PARTNER_ASSET:
        return this.findOrganizationsForWorkspaceAssetMedia(mediaId);
      case MediaType.ITERATION:
        return this.findOrganizationsForProjectIterationMedia(mediaId);
      case MediaType.ASSETSERVE:
        return this.findOrganizationsForAssetServeMedia(mediaId);
      case MediaType.PROJECT:
        return this.findOrganizationsForProjectMedia(mediaId);
      default:
        throw new NotImplementedException(
          `Media id ${mediaId} supported, type ${media.mediaType} not implemented.`,
        );
    }
  }

  /**
   * Returns all organizations for the media id by way of platform media
   */
  async findOrganizationsForPlatformMedia(
    mediaId: number,
  ): Promise<ReadOrganizationDto[]> {
    const platformAccountIds = await this.findPlatformAccountIdsForMedia(
      mediaId,
    );
    return this.organizationAdAccountService.findOrganizationsForAdAccount(
      platformAccountIds,
    );
  }

  /**
   * Returns all organizations for the media id by way of workspace/partner asset media
   */
  async findOrganizationsForWorkspaceAssetMedia(
    mediaId: number,
  ): Promise<ReadOrganizationDto[]> {
    const workspaceAsset = await this.workspaceAssetVersionRepository.findOne({
      relations: {
        workspaceAsset: {
          workspace: {
            organization: true,
          },
        },
      },
      where: { mediaId, workspaceAsset: { deleted: false } },
    });
    if (!workspaceAsset) {
      throw new NotFoundException(`Media id ${mediaId} not found.`);
    }

    const readOrganization = this.classMapper.map(
      workspaceAsset.workspaceAsset.workspace.organization,
      Organization,
      ReadOrganizationDto,
    );

    return [readOrganization];
  }

  /**
   * Returns the organization for the media id by way of project iteration media
   */
  async findOrganizationsForProjectIterationMedia(
    mediaId: number,
  ): Promise<ReadOrganizationDto[]> {
    const query = `
      SELECT 
          organization.* 
      FROM 
          organization
      INNER JOIN 
          partner ON organization.id = partner.organization_id
      INNER JOIN 
          project ON partner.id = project.partner_id
      INNER JOIN 
          iteration_media ON project.id = iteration_media.project_id
      WHERE 
          iteration_media.media_id = ?
      LIMIT 1
    `;

    const organization = await this.mediaRepository.query(query, [mediaId]);

    if (!organization.length) {
      throw new NotFoundException(
        `Organization for iteration media id ${mediaId} not found.`,
      );
    }

    const readOrganization = this.classMapper.map(
      organization[0],
      Organization,
      ReadOrganizationDto,
    );

    return [readOrganization];
  }

  /**
   * Returns the organization for the ASSETSERVE media id
   */
  async findOrganizationsForAssetServeMedia(
    mediaId: number,
  ): Promise<ReadOrganizationDto[]> {
    const query = `
      SELECT 
          organization.* 
      FROM 
          organization
      INNER JOIN 
          partner ON organization.id = partner.organization_id
      INNER JOIN 
          partner_asset ON partner.id = partner_asset.partner_id
      INNER JOIN 
          partner_asset_version ON partner_asset.id = partner_asset_version.partner_asset_id
      WHERE 
          partner_asset_version.media_id = ?

      UNION ALL

      SELECT
          organization.*
      FROM
          organization
              INNER JOIN
          api_media ON organization.id = api_media.organization_id
      WHERE
          api_media.media_id = ?

      UNION ALL

      SELECT
          organization.*
      FROM
          organization
      INNER JOIN
          async_report_job_request ON async_report_job_request.organization_id = organization.id
      INNER JOIN
          creative_aperture_request_media ON creative_aperture_request_media.job_id = async_report_job_request.job_id
      WHERE
        creative_aperture_request_media.media_id = ?

      LIMIT 1
    `;

    const organization = await this.mediaRepository.query(query, [
      mediaId,
      mediaId,
      mediaId,
    ]);

    if (!organization.length) {
      throw new NotFoundException(
        `Organization for ASSETSERVE media id ${mediaId} not found.`,
      );
    }

    const readOrganization = this.classMapper.map(
      organization[0],
      Organization,
      ReadOrganizationDto,
    );

    return [readOrganization];
  }

  /**
   * Returns the organization for the PROJECT media id
   */
  async findOrganizationsForProjectMedia(
    mediaId: number,
  ): Promise<ReadOrganizationDto[]> {
    const query = `
      SELECT 
          organization.* 
      FROM 
          organization
      INNER JOIN 
          partner ON organization.id = partner.organization_id
      INNER JOIN 
          project ON partner.id = project.partner_id
      INNER JOIN 
          project_media ON project.id = project_media.project_id
      WHERE 
          project_media.media_id = ?
      LIMIT 1
    `;

    const organization = await this.mediaRepository.query(query, [mediaId]);

    if (!organization.length) {
      throw new NotFoundException(
        `Organization for PROJECT media id ${mediaId} not found.`,
      );
    }

    const readOrganization = this.classMapper.map(
      organization[0],
      Organization,
      ReadOrganizationDto,
    );

    return [readOrganization];
  }

  private async isPersonInOrgWithPlatformMedia(
    platformAccountId: string,
    personId: number,
    orgIds: string[],
    platform: string,
  ): Promise<boolean> {
    if (!orgIds?.length) {
      return false;
    }
    const query = `
      SELECT opaam.organization_id 
        FROM organization_platform_ad_account_map opaam
        INNER JOIN person_organization_map pom ON pom.organization_id = opaam.organization_id
        INNER JOIN platform_ad_account paa ON paa.id = opaam.platform_ad_account_id
        WHERE paa.platform_account_id = ? AND pom.person_id = ? AND opaam.organization_id in (?) AND paa.platform = ?;
    `;

    const orgsWithPersonAndPlatformMedia = await this.mediaRepository.query(
      query,
      [platformAccountId, personId, orgIds, platform],
    );

    return orgsWithPersonAndPlatformMedia?.length > 0;
  }

  private async isPersonInWorkspaceWithPlatformMediaAdAccount(
    platformAdAccountId: string,
    personId: number,
    platform: string,
  ): Promise<boolean> {
    const query = `
      SELECT p.id, p.organization_id, pp.active FROM partner p
        INNER JOIN partner_person pp ON p.id = pp.partner_id
        INNER JOIN organization_platform_ad_account_map opaam ON opaam.organization_id = p.organization_id
        INNER JOIN platform_ad_account paa ON paa.id = opaam.platform_ad_account_id
        WHERE paa.platform_account_id = ? AND pp.person_id = ? AND paa.platform = ?;
    `;

    const workspacesWithPersonAndMedia = await this.mediaRepository.query(
      query,
      [platformAdAccountId, personId, platform],
    );

    // The above query is much more performant without pp.active = 1 in the where clause
    const workspacesWithPersonAndMediaActive =
      workspacesWithPersonAndMedia?.filter(
        (partnerPerson) => partnerPerson.active === 1,
      );

    return workspacesWithPersonAndMediaActive?.length > 0;
  }

  private async isPersonPartnerOrAccountManagerInWorkspaceWithPlatformMediaAdAccount(
    platformAdAccountId: string,
    personId: number,
    platform: string,
  ): Promise<boolean> {
    const query = `
      SELECT p.id, p.organization_id FROM partner p
        INNER JOIN partner_manager pm ON p.id = pm.partner_id
        INNER JOIN organization_platform_ad_account_map opaam ON opaam.organization_id = p.organization_id
        INNER JOIN platform_ad_account paa ON paa.id = opaam.platform_ad_account_id
        WHERE paa.platform_account_id = ? AND pm.manager_id = ? AND paa.platform = ? AND pm.role in ('ACCOUNT_MANAGER', 'PROJECT_MANAGER');
    `;

    const workspacesWithPersonAndMedia = await this.mediaRepository.query(
      query,
      [platformAdAccountId, personId, platform],
    );

    return workspacesWithPersonAndMedia?.length > 0;
  }

  /**
   * Returns true if person in workspace that has platform media ad account connected
   */
  async validatePersonCanViewPlatformMedia(
    mediaId: number,
    personId: number,
  ): Promise<boolean> {
    const platformMedia = await this.platformMediaRepository.findOne({
      where: { mediaId: mediaId },
    });
    if (!platformMedia) {
      throw new NotFoundException(
        `Platform Media for media id ${mediaId} not found.`,
      );
    }

    const platformAccountId =
      MediaService.extractPlatformAccountIdFromPlatformMedia(platformMedia);

    const platform =
      MediaService.convertPlatformMediaPlatformToPlatformAdAccountPlatform(
        platformMedia.platform,
      );

    const workspacesWithPersonAndMedia =
      await this.isPersonInWorkspaceWithPlatformMediaAdAccount(
        platformAccountId,
        personId,
        platform,
      );
    if (workspacesWithPersonAndMedia) {
      return true;
    }

    const workspacesWithPersonAMorPMAndMedia =
      await this.isPersonPartnerOrAccountManagerInWorkspaceWithPlatformMediaAdAccount(
        platformAccountId,
        personId,
        platform,
      );
    if (workspacesWithPersonAMorPMAndMedia) {
      return true;
    }

    const orgIDsWithReadWorkspaceAllForPerson =
      await this.organizationUserQueryService.getOrgIDsWithReadWorkspaceAllForPerson(
        personId,
      );
    if (!orgIDsWithReadWorkspaceAllForPerson.length) {
      return false;
    }

    return this.isPersonInOrgWithPlatformMedia(
      platformAccountId,
      personId,
      orgIDsWithReadWorkspaceAllForPerson,
      platform,
    );
  }

  async isPersonOnWorkspaceWithPartnerOrIterationMedia({
    mediaId,
    personId,
  }: {
    mediaId: number;
    personId: number;
  }): Promise<boolean> {
    const query = `
      SELECT distinct cb.partner_id, p.organization_id, pp.active FROM compliance_batch cb
        INNER JOIN compliance_batch_media_map cbmm ON cbmm.compliance_batch_id = cb.id
        INNER JOIN partner p ON cb.partner_id = p.id
        INNER JOIN partner_person pp ON p.id = pp.partner_id
            WHERE media_id = ? AND pp.person_id = ?
    `;
    const personOnWorkspaceResult = await this.mediaRepository.query(query, [
      mediaId,
      personId,
    ]);

    // The above query is much more performant without pp.active = 1 in the where clause
    const personOnWorkspaceResultActive = personOnWorkspaceResult?.filter(
      (result) => result.active === 1,
    );

    return personOnWorkspaceResultActive?.length > 0;
  }

  async isPersonPartnerOrAccountManagerInWorkspaceWithPartnerOrIterationMedia(
    mediaId: number,
    personId: number,
  ): Promise<boolean> {
    const query = `
      SELECT distinct cb.partner_id FROM compliance_batch cb
        INNER JOIN compliance_batch_media_map cbmm ON cbmm.compliance_batch_id = cb.id
        INNER JOIN partner p on cb.partner_id = p.id
        INNER JOIN partner_manager pm ON p.id = pm.partner_id
            WHERE cbmm.media_id = ? AND pm.manager_id = ? AND pm.role in ('ACCOUNT_MANAGER', 'PROJECT_MANAGER');
    `;

    const pmOrAMOnWorkspaceResult = await this.mediaRepository.query(query, [
      mediaId,
      personId,
    ]);

    return pmOrAMOnWorkspaceResult?.length > 0;
  }

  async isPartnerOrIterationMediaInOrgs(
    mediaId: number,
    orgIds: string[],
  ): Promise<boolean> {
    const query = `
      SELECT distinct cb.partner_id, p.organization_id FROM compliance_batch cb
        INNER JOIN compliance_batch_media_map cbmm ON cbmm.compliance_batch_id = cb.id
        INNER JOIN partner p ON cb.partner_id = p.id
            WHERE media_id = ? and p.organization_id in (?);
    `;

    const partnerOrIterationMediaInOrgs = await this.mediaRepository.query(
      query,
      [mediaId, orgIds],
    );

    return partnerOrIterationMediaInOrgs?.length > 0;
  }

  async validatePersonCanViewPartnerOrIterationMedia(
    mediaId: number,
    personId: number,
  ): Promise<boolean> {
    const personOnWorkspaceResult =
      await this.isPersonOnWorkspaceWithPartnerOrIterationMedia({
        mediaId,
        personId,
      });

    if (personOnWorkspaceResult) {
      return true;
    }

    const pmOrAMOnWorkspaceResult =
      await this.isPersonPartnerOrAccountManagerInWorkspaceWithPartnerOrIterationMedia(
        mediaId,
        personId,
      );

    if (pmOrAMOnWorkspaceResult) {
      return true;
    }

    const orgIDsWithReadWorkspaceAllForPerson =
      await this.organizationUserQueryService.getOrgIDsWithReadWorkspaceAllForPerson(
        personId,
      );

    if (!orgIDsWithReadWorkspaceAllForPerson.length) {
      return false;
    }

    return this.isPartnerOrIterationMediaInOrgs(
      mediaId,
      orgIDsWithReadWorkspaceAllForPerson,
    );
  }

  async validatePersonCanViewAssetServeMedia(
    mediaId: number,
    personId: number,
  ) {
    const [orgIdsForPerson, organizationsForMedia] = await Promise.all([
      this.organizationUserQueryService.getOrganizationIdsForPerson(personId),
      this.findOrganizationsForAssetServeMedia(mediaId),
    ]);

    if (!orgIdsForPerson.length || !organizationsForMedia.length) {
      return false;
    }

    if (organizationsForMedia.length > 1) {
      throw new NotFoundException('Multiple organizations found for media.');
    }

    return orgIdsForPerson.some(
      (orgId) => orgId === organizationsForMedia[0].id,
    );
  }

  async validatePersonCanViewProjectMedia(mediaId: number, personId: number) {
    const [orgIdsForPerson, organizationsForProjectMedia] = await Promise.all([
      this.organizationUserQueryService.getOrganizationIdsForPerson(personId),
      this.findOrganizationsForProjectMedia(mediaId),
    ]);

    if (!orgIdsForPerson.length || !organizationsForProjectMedia.length) {
      return false;
    }

    if (organizationsForProjectMedia.length > 1) {
      throw new NotFoundException(
        'Multiple organizations found for project media.',
      );
    }

    return orgIdsForPerson.some(
      (orgId) => orgId === organizationsForProjectMedia[0].id,
    );
  }

  async validatePersonCanViewMedia(
    mediaId: number,
    personId: number,
  ): Promise<boolean> {
    const media = await this.mediaRepository.findOneBy({ id: mediaId });
    if (!media) {
      throw new NotFoundException(`Media id ${mediaId} not found.`);
    }

    switch (media.mediaType) {
      case MediaType.BACKGROUND_PLATFORM:
        return this.validatePersonCanViewPlatformMedia(mediaId, personId);
      case MediaType.PARTNER_ASSET:
      case MediaType.ITERATION:
        return this.validatePersonCanViewPartnerOrIterationMedia(
          mediaId,
          personId,
        );
      case MediaType.ASSETSERVE:
        return this.validatePersonCanViewAssetServeMedia(mediaId, personId);
      case MediaType.PROJECT:
        return this.validatePersonCanViewProjectMedia(mediaId, personId);
      default:
        throw new NotImplementedException(
          `Media id ${mediaId} supported, type ${media.mediaType} not implemented.`,
        );
    }
  }

  async getPlatformMediaById(
    platformMediaId: string,
  ): Promise<ReadPlatformMediaDto> {
    const platformMedia = await this.platformMediaRepository.findOneBy({
      platformMediaId,
    });

    if (!platformMedia) {
      throw new NotFoundException(
        `Platform Media for platform media id ${platformMediaId} not found.`,
      );
    }

    return this.classMapper.map(
      platformMedia,
      PlatformMedia,
      ReadPlatformMediaDto,
    );
  }
}
