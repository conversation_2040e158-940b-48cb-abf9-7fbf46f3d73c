import { Module } from '@nestjs/common';
import { MediaController } from './media.controller';
import { MediaService } from './media.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PlatformMedia } from './entities/platform-media.entity';
import { AdAccountsModule } from '../ad-accounts/ad-accounts.module';
import { Media } from './entities/media.entity';
import { WorkspaceAssetVersion } from 'src/workspaces/entities/workspace-asset-version.entity';
import { WorkspaceAsset } from 'src/workspaces/entities/workspace-asset.entity';
import { Workspace } from 'src/workspaces/entities/workspace.entity';
import { Organization } from 'src/organizations/entities/organization.entity';
import { OrganizationProfile } from 'src/organizations/mapper/organization.profile';
import { OrganizationUserModule } from '../organizations/organization-user/organization-user.module';
import { MediaPlaylist } from './entities/media-playlist.entity';
import { MediaThumbnail } from './entities/media-thumbnail.entity';
import { MediaVariant } from './entities/media-variant.entity';
import { StreamPlaylist } from './entities/stream-playlist.entity';
import { MediaProfile } from './mapper/media.profile';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Media,
      MediaThumbnail,
      MediaPlaylist,
      StreamPlaylist,
      MediaVariant,
      PlatformMedia,
      WorkspaceAssetVersion,
      WorkspaceAsset,
      Workspace,
      Organization,
    ]),
    AdAccountsModule,
    OrganizationUserModule,
  ],
  controllers: [MediaController],
  providers: [MediaService, OrganizationProfile, MediaProfile],
})
export class MediaModule {}
