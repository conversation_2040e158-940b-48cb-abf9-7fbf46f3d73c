import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  <PERSON><PERSON><PERSON>umn,
  <PERSON>To<PERSON>any,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON><PERSON>,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { IsDate, IsNumber, IsOptional, IsString } from 'class-validator';
import { MediaThumbnail } from './media-thumbnail.entity';
import { MediaPlaylist } from './media-playlist.entity';
import { MediaVariant } from './media-variant.entity';
import { SourceFileType } from '../media.constants';

export enum MediaType {
  PROJECT = 'PROJECT',
  ITERATION = 'ITERATION',
  PORTFOLIO = 'PORTFOLIO',
  PROJECT_FINAL = 'PROJECT_FINAL',
  FINAL_ASSET = 'FINAL_ASSET',
  CAMPAIGN = 'CAMPAIGN',
  DOCUMENT = 'DOCUMENT',
  PROJECT_BRIEF = 'PROJECT_BRIEF',
  ABOUT_ME = 'ABOUT_ME',
  ABOUT_ME_HEADER = 'ABOUT_ME_HEADER',
  CHAT_ATTACHMENT = 'CHAT_ATTACHMENT',
  PARTNER_ASSET = 'PARTNER_ASSET',
  BACKGROUND_PLATFORM = 'BACKGROUND_PLATFORM',
  ASSETSERVE = 'ASSETSERVE',
}

export enum FileType {
  VIDEO = 'VIDEO',
  IMAGE = 'IMAGE',
  AUDIO = 'AUDIO',
  OTHER = 'OTHER',
  UNKNOWN = 'UNKNOWN',
  DOCUMENT = 'DOCUMENT',
}

export enum MediaProcessingState {
  PENDING = 'PENDING',
  ACCUMULATING = 'ACCUMULATING',
  ARCHIVING = 'ARCHIVING',
  COMPLETE = 'COMPLETE',
  FAILED = 'FAILED',
}

@Entity({ name: 'media' })
/**
 * Database entity for media (images, videos) uploaded to the platform.
 */
export class Media {
  /**
   * The ID of the media.
   */
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * The version of the media.
   */
  @AutoMap()
  @IsNumber()
  @IsString()
  @VersionColumn({ nullable: false })
  version: number;

  /**
   * The ID of the person who uploaded the media.
   */
  @AutoMap()
  @IsNumber()
  @Column({
    name: 'person_id',
    type: 'bigint',
    transformer: { to: (value) => value, from: (value) => parseInt(value) },
  })
  personId: number;

  /**
   * The type of the media.
   */
  @AutoMap()
  @IsString()
  @Column({ name: 'media_type', type: 'enum', enum: MediaType })
  mediaType: MediaType;

  /**
   *The duration of the media, rounded to the nearest second.
   */
  @AutoMap()
  @IsNumber()
  @Column({ type: 'bigint' })
  @Column({
    transformer: { to: (value) => value, from: (value) => parseInt(value) },
  })
  duration: number;

  /**
   *The preicse duration of the media.
   */
  @AutoMap()
  @IsNumber()
  @Column({
    name: 'duration_precise',
    type: 'decimal',
    precision: 19,
    scale: 6,
    nullable: true,
    transformer: { to: (value) => value, from: (value) => parseFloat(value) },
  })
  durationPrecise: number;

  /**
   * The name of the bucket the media is stored in.
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  @Column({ name: 'bucket_name', type: 'varchar', length: 255, nullable: true })
  bucketName?: string;

  /**
   * The path to the media.
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  @Column({ type: 'varchar', length: 255, nullable: true })
  path?: string;

  /**
   * The name of the media.
   */
  @AutoMap()
  @IsString()
  @Column({ type: 'varchar', length: 512 })
  name: string;

  /**
   * The date the media was created.
   */
  @AutoMap()
  @IsDate()
  @IsOptional()
  @Column({ name: 'date_created', type: 'timestamp', nullable: true })
  dateCreated?: Date;

  /**
   * The date the media was uploaded.
   */
  @AutoMap()
  @IsDate()
  @IsOptional()
  @Column({ name: 'date_uploaded', type: 'timestamp', nullable: true })
  dateUploaded?: Date;

  /**
   * The processing state of the media.
   */
  @AutoMap()
  @IsString()
  @Column({
    name: 'processing_state',
    type: 'enum',
    enum: MediaProcessingState,
  })
  processingState: MediaProcessingState;

  /**
   * The ID of the media source.
   */
  @AutoMap()
  @IsNumber()
  @Column({
    name: 'media_source_id',
    type: 'bigint',
    transformer: { to: (value) => value, from: (value) => parseInt(value) },
  })
  mediaSourceId: number;

  /**
   * The media's TUS asset identifier.
   */
  @AutoMap()
  @IsString()
  @Column({ name: 'asset_identifier', type: 'varchar', length: 512 })
  assetIdentifier: string;

  /**
   * The description of the media.
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  @Column({ type: 'longtext', nullable: true })
  description?: string | null;

  /**
   * The height of the media in pixels.
   */
  @AutoMap()
  @IsNumber()
  @IsOptional()
  @Column({ type: 'int', nullable: true })
  height?: number;

  /**
   * The width of the media in pixels.
   */
  @AutoMap()
  @IsNumber()
  @IsOptional()
  @Column({ type: 'int', nullable: true })
  width?: number;

  /**
   * The media's file type.
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  @Column({
    name: 'file_type',
    type: 'enum',
    enum: FileType,
    nullable: true,
  })
  fileType?: FileType;

  /**
   * The date the media was taken, if it is an image.
   */
  @AutoMap()
  @IsNumber()
  @IsOptional()
  @Column({ name: 'date_taken', type: 'datetime', nullable: true })
  dateTaken?: Date | null;

  /**
   * The size of the media in bytes.
   */
  @AutoMap()
  @IsNumber()
  @IsOptional()
  @Column({ type: 'bigint', nullable: true })
  @Column({
    transformer: { to: (value) => value, from: (value) => parseInt(value) },
  })
  size?: number;

  /**
   * Where the media was uploaded from.
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  @Column({
    name: 'device_identifier',
    type: 'varchar',
    length: 190,
    nullable: true,
  })
  deviceIdentifier?: string | null;

  /**
   * The media's mime type.
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  @Column({ name: 'mime_type', type: 'varchar', length: 255, nullable: true })
  mimeType?: string;

  /**
   * The media's source file type.
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  @Column({
    name: 'source_file_type',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  sourceFileType?: SourceFileType;

  /**
   * The media's display name.
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  @Column({
    name: 'display_name',
    type: 'varchar',
    length: 512,
    nullable: true,
  })
  displayName?: string;

  @OneToMany(() => MediaThumbnail, (thumbnail) => thumbnail.media, {
    eager: true,
  })
  @JoinColumn({ name: 'id' })
  thumbnails: MediaThumbnail[];

  @OneToMany(() => MediaPlaylist, (mediaPlaylist) => mediaPlaylist.media, {
    eager: true,
  })
  @JoinColumn({ name: 'id' })
  mediaPlaylists: MediaPlaylist[];

  @OneToOne(() => MediaVariant, (mediaVariant) => mediaVariant.media, {
    eager: true,
  })
  mediaVariant: MediaVariant;
}
