import {
  Column,
  <PERSON><PERSON>ty,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  VersionColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { IsNumber, IsString } from 'class-validator';
import { Media } from './media.entity';
import { StreamPlaylist } from './stream-playlist.entity';

@Entity({ name: 'media_playlist' })
export class MediaPlaylist {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @IsNumber()
  @VersionColumn({ nullable: false })
  version: number;

  @AutoMap()
  @IsString()
  @Column({ name: 'path', type: 'varchar', length: 191 })
  path: string;

  @AutoMap()
  @IsString()
  @Column({ name: 'name', type: 'varchar', length: 191 })
  name: string;

  @AutoMap()
  @IsString()
  @Column({ name: 'job_id', type: 'varchar', length: 20 })
  jobId: string;

  @AutoMap()
  @IsString()
  @Column({ name: 'status', type: 'varchar', length: 20 })
  status: string;

  @ManyToOne(() => Media, (media) => media.mediaPlaylists)
  @JoinColumn({ name: 'media_id' })
  media: Media;

  @OneToOne(
    () => StreamPlaylist,
    (streamPlaylist) => streamPlaylist.mediaPlaylist,
    { eager: true },
  )
  @JoinColumn({ name: 'stream_playlist_id' })
  streamPlaylist: StreamPlaylist;
}
