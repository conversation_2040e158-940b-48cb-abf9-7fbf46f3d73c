import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Media } from './media.entity';

@Entity({ name: 'media_variant' })
export class MediaVariant {
  @AutoMap()
  @PrimaryColumn({ type: 'char', length: 36 })
  id: string;

  @AutoMap()
  @Column({ name: 'media_conversion_job_id', type: 'char', length: 36 })
  mediaConversionJobId: string;

  @AutoMap()
  @Column({ name: 'mime_type', type: 'varchar', length: 128 })
  mimeType: string;

  @AutoMap()
  @Column({
    name: 'duration_precise',
    type: 'decimal',
    precision: 19,
    scale: 6,
    transformer: { to: (value) => value, from: (value) => parseFloat(value) },
  })
  durationPrecise: number;

  @AutoMap()
  @Column({ name: 's3_bucket', type: 'varchar', length: 64 })
  s3Bucket: string;

  @AutoMap()
  @Column({ name: 's3_key', type: 'varchar', length: 1024 })
  s3Key: string;

  @OneToOne(() => Media, (media) => media.mediaVariant)
  @JoinColumn({ name: 'media_id' })
  media: Media;
}
