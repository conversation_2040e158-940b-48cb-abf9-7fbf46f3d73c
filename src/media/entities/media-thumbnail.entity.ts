import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  VersionColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { IsNumber, IsString } from 'class-validator';
import { Media } from './media.entity';

@Entity({ name: 'media_thumbnail' })
/**
 * Database entity for media_thumbnail linked to media.
 */
export class MediaThumbnail {
  /**
   * The ID of the media.
   */
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * The version of the media.
   */
  @AutoMap()
  @IsNumber()
  @VersionColumn({ nullable: false })
  version: number;

  /**
   * The height of the media in pixels.
   */
  @AutoMap()
  @IsNumber()
  @Column({ type: 'int', nullable: true })
  height: number;

  /**
   * The width of the media in pixels.
   */
  @AutoMap()
  @IsNumber()
  @Column({ type: 'int', nullable: true })
  width: number;

  /**
   * The name of the thumbnail.
   */
  @AutoMap()
  @IsString()
  @Column({ type: 'int', nullable: true })
  name: string;

  @ManyToOne(() => Media, (media) => media.thumbnails)
  @JoinColumn({ name: 'media_id' })
  media: Media;
}
