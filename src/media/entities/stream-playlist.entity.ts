import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  OneTo<PERSON>ne,
  PrimaryGeneratedColumn,
  VersionColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { IsNumber, IsString } from 'class-validator';

@Entity({ name: 'stream_playlist' })
export class StreamPlaylist {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @IsNumber()
  @VersionColumn({ nullable: false })
  version: number;

  @AutoMap()
  @IsString()
  @Column({ name: 'identifier', type: 'varchar', length: 50 })
  identifier: string;

  @AutoMap()
  @IsString()
  @Column({ name: 'network_type', type: 'varchar', length: 20 })
  networkType: string;

  @AutoMap()
  @IsString()
  @Column({ name: 'format', type: 'varchar', length: 255 })
  format: string;

  @OneToOne(
    () => StreamPlaylist,
    (streamPlaylist) => streamPlaylist.mediaPlaylist,
  )
  mediaPlaylist: StreamPlaylist;
}
