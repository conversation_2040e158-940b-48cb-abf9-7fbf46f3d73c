import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';

@Entity('platform_media')
export class PlatformMedia {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ name: 'media_id' })
  mediaId: number;

  @AutoMap()
  @Column()
  platform: string;

  @AutoMap()
  @Column({ name: 'platform_account_id' })
  platformAccountId: string;

  @AutoMap()
  @Column({ name: 'platform_media_id' })
  platformMediaId: string;
}
