import { <PERSON>, Get, Param, ParseIntPipe } from '@nestjs/common';
import { MediaService } from './media.service';
import { ApiExtraModels, ApiParam, ApiTags } from '@nestjs/swagger';
import { MEDIA_API_TAG_NAME } from '../common/constants/api.constants';
import { ReadOrganizationDto } from '../organizations/dto/read-organization.dto';
import { VmApiOkResponse } from '@vidmob/vidmob-nestjs-common';
import { CanViewMediaDto } from './dto/can-view-media.dto';
import { ReadPlatformMediaDto } from './dto/read-platform-media.dto';
import { ReadMediaDto } from './dto/read-media.dto';

/**
 * Media Controller
 */
@ApiTags(MEDIA_API_TAG_NAME)
@ApiExtraModels(ReadOrganizationDto)
@Controller('media')
export class MediaController {
  constructor(private readonly mediaService: MediaService) {}

  /**
   * This endpoint returns media object by media id
   */
  @VmApiOkResponse({
    type: ReadMediaDto,
  })
  @ApiParam({ name: 'mediaId', description: 'Media Id' })
  @Get(':mediaId')
  getMediaById(
    @Param('mediaId', ParseIntPipe) mediaId: number,
  ): Promise<ReadMediaDto> {
    return this.mediaService.getMediaById(mediaId);
  }

  /**
   * This endpoint returns all organizations for the media id
   * @param mediaId Media Id
   * @returns list of organizations
   */
  @VmApiOkResponse({
    isArray: true,
    type: ReadOrganizationDto,
  })
  @ApiParam({ name: 'mediaId', description: 'Media Id' })
  @Get(':mediaId/organizations')
  findOrganizationsForMedia(@Param('mediaId') mediaId: number) {
    return this.mediaService.findOrganizationsForMedia(mediaId);
  }

  /**
   * Depending on the media type this endpoint will
   * validate a person can view the media
   * @param mediaId Media ID
   * @param personId  Person ID
   * @returns list of organizations
   */
  @VmApiOkResponse({
    type: CanViewMediaDto,
  })
  @ApiParam({ name: 'mediaId', description: 'Media Id' })
  @ApiParam({ name: 'personId', description: 'Person Id' })
  @Get(':mediaId/person/:personId/validate')
  async validatePersonCanViewMedia(
    @Param('mediaId') mediaId: number,
    @Param('personId') personId: number,
  ) {
    const canView = await this.mediaService.validatePersonCanViewMedia(
      mediaId,
      personId,
    );
    return { canView };
  }

  /**
   * This endpoint returns the platform media from the platform media id
   * @param platformMediaId
   */
  @VmApiOkResponse({
    type: ReadPlatformMediaDto,
  })
  @ApiParam({ name: 'platformMediaId', description: 'Platform Media Id' })
  @Get('/platform-media/:platformMediaId')
  async getPlatformMedia(@Param('platformMediaId') platformMediaId: string) {
    return await this.mediaService.getPlatformMediaById(platformMediaId);
  }
}
