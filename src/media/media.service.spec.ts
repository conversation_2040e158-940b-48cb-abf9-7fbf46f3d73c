import { Test, TestingModule } from '@nestjs/testing';
import { MediaService } from './media.service';
import { Repository } from 'typeorm';
import { PlatformMedia } from './entities/platform-media.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getMapperToken } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { FACEBOOK_PLATFORM_NAME } from '../common/constants/constants';
import { OrganizationAdAccountsService } from '../ad-accounts/organization-ad-accounts.service';
import { WorkspaceAssetVersion } from '../workspaces/entities/workspace-asset-version.entity';
import { Media, MediaType } from './entities/media.entity';
import { Organization } from '../organizations/entities/organization.entity';
import { Workspace } from '../workspaces/entities/workspace.entity';
import { WorkspaceAsset } from '../workspaces/entities/workspace-asset.entity';
import { Mapper, createMapper } from '@automapper/core';
import { OrganizationProfile } from '../organizations/mapper/organization.profile';
import { OrganizationUserQueryService } from '../organizations/organization-user/organization-user-query.service';
import { NotFoundException } from '@nestjs/common';
import { MediaThumbnail } from './entities/media-thumbnail.entity';
import { MediaPlaylist } from './entities/media-playlist.entity';
import { ConfigService } from '@nestjs/config';
import { StreamPlaylist } from './entities/stream-playlist.entity';
import { MediaVariant } from './entities/media-variant.entity';
import { MediaProfile } from './mapper/media.profile';

const TEST_MEDIA_ID = 1234;
const TEST_FB_PLATFORM_MEDIA = {
  id: 1,
  mediaId: TEST_MEDIA_ID,
  platform: FACEBOOK_PLATFORM_NAME,
  platformAccountId: 'act_**********',
  platformMediaId: '**********',
};
const TEST_FB_PLATFORM_MEDIA_DUPLICATION_BUG = {
  id: 1,
  mediaId: TEST_MEDIA_ID,
  platform: FACEBOOK_PLATFORM_NAME,
  platformAccountId: '**********',
  platformMediaId: '**********',
};
const TEST_NON_FB_PLATFORM_MEDIA = {
  id: 2,
  mediaId: TEST_MEDIA_ID,
  platform: 'BACKGROUND_REDDIT',
  platformAccountId: '**********',
  platformMediaId: '**********',
};
const TEST_ORGANIZATION = new Organization();
TEST_ORGANIZATION.id = '123-456';

const TEST_WORKSPACE_ASSET_VERSION = {
  id: 1,
  workspaceAsset: {
    id: 123,
    workspace: {
      organization: TEST_ORGANIZATION,
    } as Workspace,
  } as WorkspaceAsset,
  mediaId: TEST_MEDIA_ID,
} as WorkspaceAssetVersion;

describe('MediaService', () => {
  let mediaService: MediaService;
  let organizationAdAccountService: OrganizationAdAccountsService;
  let organizationUserQueryService: OrganizationUserQueryService;
  let platformMediaRepository: Repository<PlatformMedia>;
  let mediaRepository: Repository<Media>;
  let workspaceAssetVersionRepository: Repository<WorkspaceAssetVersion>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MediaService,
        OrganizationProfile,
        MediaProfile,
        ConfigService,
        {
          provide: getRepositoryToken(PlatformMedia),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(Media),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(MediaThumbnail),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(MediaPlaylist),
          useClass: Repository,
        },
        { provide: getRepositoryToken(StreamPlaylist), useClass: Repository },
        {
          provide: getRepositoryToken(MediaVariant),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(WorkspaceAssetVersion),
          useClass: Repository,
        },
        {
          provide: OrganizationUserQueryService,
          useValue: {
            getOrgIDsWithReadWorkspaceAllForPerson: jest.fn(),
            getOrganizationIdsForPerson: jest
              .fn()
              .mockResolvedValue(['mock-org-id-one', 'mock-org-id-two']),
          },
        },
        {
          provide: OrganizationAdAccountsService,
          useValue: {
            findOrganizationsForAdAccount: jest.fn(),
          },
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    module.get<Mapper>(getMapperToken());

    mediaService = module.get<MediaService>(MediaService);
    organizationAdAccountService = module.get<OrganizationAdAccountsService>(
      OrganizationAdAccountsService,
    );
    organizationUserQueryService = module.get<OrganizationUserQueryService>(
      OrganizationUserQueryService,
    );
    platformMediaRepository = module.get<Repository<PlatformMedia>>(
      getRepositoryToken(PlatformMedia),
    );
    mediaRepository = module.get<Repository<Media>>(getRepositoryToken(Media));
    workspaceAssetVersionRepository = module.get<
      Repository<WorkspaceAssetVersion>
    >(getRepositoryToken(WorkspaceAssetVersion));
  });

  it('should be defined', () => {
    expect(mediaService).toBeDefined();
  });

  it('get by id should throw error if media not found', async () => {
    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce(null);
    await expect(
      mediaService.findOrganizationsForMedia(TEST_MEDIA_ID),
    ).rejects.toThrowError(`Media id ${TEST_MEDIA_ID} not found.`);
  });

  it('test findOrganizationsForMedia method with invalid media', async () => {
    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce(null);
    await expect(
      mediaService.findOrganizationsForMedia(TEST_MEDIA_ID),
    ).rejects.toThrowError(`Media id ${TEST_MEDIA_ID} not found.`);

    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce({
      id: TEST_MEDIA_ID,
      mediaType: MediaType.PORTFOLIO,
    } as Media);
    await expect(
      mediaService.findOrganizationsForMedia(TEST_MEDIA_ID),
    ).rejects.toThrowError(
      `Media id ${TEST_MEDIA_ID} supported, type ${MediaType.PORTFOLIO} not implemented.`,
    );
  });

  it('test findOrganizationsForMedia method with facebook media', async () => {
    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce({
      id: TEST_MEDIA_ID,
      mediaType: MediaType.BACKGROUND_PLATFORM,
    } as Media);
    jest
      .spyOn(platformMediaRepository, 'find')
      .mockResolvedValueOnce([TEST_FB_PLATFORM_MEDIA]);
    const findOrganizationsForAdAccountSpy = jest.spyOn(
      organizationAdAccountService,
      'findOrganizationsForAdAccount',
    );
    await mediaService.findOrganizationsForMedia(TEST_MEDIA_ID);
    expect(findOrganizationsForAdAccountSpy).toBeCalledWith([
      TEST_FB_PLATFORM_MEDIA.platformAccountId.replace('act_', ''),
    ]);
  });

  it('test findOrganizationsForMedia method with facebook media with duplicated platform media bug', async () => {
    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce({
      id: TEST_MEDIA_ID,
      mediaType: MediaType.BACKGROUND_PLATFORM,
    } as Media);
    jest
      .spyOn(platformMediaRepository, 'find')
      .mockResolvedValueOnce([
        TEST_FB_PLATFORM_MEDIA,
        TEST_FB_PLATFORM_MEDIA_DUPLICATION_BUG,
      ]);
    const findOrganizationsForAdAccountSpy = jest.spyOn(
      organizationAdAccountService,
      'findOrganizationsForAdAccount',
    );
    await mediaService.findOrganizationsForMedia(TEST_MEDIA_ID);
    expect(findOrganizationsForAdAccountSpy).toBeCalledWith([
      TEST_FB_PLATFORM_MEDIA.platformAccountId.replace('act_', ''),
    ]);
  });

  it('test findOrganizationsForMedia method with non facebook media', async () => {
    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce({
      id: TEST_MEDIA_ID,
      mediaType: MediaType.BACKGROUND_PLATFORM,
    } as Media);
    jest
      .spyOn(platformMediaRepository, 'find')
      .mockResolvedValueOnce([TEST_NON_FB_PLATFORM_MEDIA]);
    const findOrganizationsForAdAccountSpy = jest.spyOn(
      organizationAdAccountService,
      'findOrganizationsForAdAccount',
    );
    await mediaService.findOrganizationsForMedia(TEST_MEDIA_ID);
    expect(findOrganizationsForAdAccountSpy).toBeCalledWith([
      TEST_NON_FB_PLATFORM_MEDIA.platformAccountId,
    ]);
  });

  it('test findOrganizationsForMedia method with Workspace Asset', async () => {
    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce({
      id: TEST_MEDIA_ID,
      mediaType: MediaType.PARTNER_ASSET,
    } as Media);
    const workspaceAssetVersionRepositorySpy = jest
      .spyOn(workspaceAssetVersionRepository, 'findOne')
      .mockResolvedValueOnce(TEST_WORKSPACE_ASSET_VERSION);
    const result = await mediaService.findOrganizationsForMedia(TEST_MEDIA_ID);
    expect(result).toMatchObject([TEST_ORGANIZATION]);
    expect(workspaceAssetVersionRepositorySpy).toBeCalled();
  });

  it('findOrganizationsForProjectIterationMedia - should find organizations for media by project iteration media', async () => {
    const mediaType = MediaType.ITERATION;
    const mockMedia = { id: TEST_MEDIA_ID, mediaType } as Media;

    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce(mockMedia);

    const mockOrganizations = [{ id: '123', name: 'Test Organization' }]; // Mocked raw SQL query result
    jest
      .spyOn(mediaRepository, 'query')
      .mockResolvedValueOnce(mockOrganizations);

    const result = await mediaService.findOrganizationsForMedia(TEST_MEDIA_ID);

    expect(result).toMatchObject(mockOrganizations); // Adjust assertion based on your actual DTO mapping
    expect(mediaRepository.findOneBy).toHaveBeenCalledWith({
      id: TEST_MEDIA_ID,
    });
    expect(mediaRepository.query).toHaveBeenCalled(); // Validate that the raw SQL query was executed
  });

  it('findOrganizationsForAssetServeMedia - should find organizations for media by project iteration media', async () => {
    const mediaType = MediaType.ASSETSERVE;
    const mockMedia = { id: TEST_MEDIA_ID, mediaType } as Media;

    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce(mockMedia);

    const mockOrganizations = [{ id: '123', name: 'Test Organization' }]; // Mocked raw SQL query result
    jest
      .spyOn(mediaRepository, 'query')
      .mockResolvedValueOnce(mockOrganizations);

    const result = await mediaService.findOrganizationsForMedia(TEST_MEDIA_ID);

    expect(result).toMatchObject(mockOrganizations); // Adjust assertion based on your actual DTO mapping
    expect(mediaRepository.findOneBy).toHaveBeenCalledWith({
      id: TEST_MEDIA_ID,
    });
    expect(mediaRepository.query).toHaveBeenCalled(); // Validate that the raw SQL query was executed
  });

  it('findOrganizationsForProjectMedia - should find organizations for media by project iteration media', async () => {
    const mediaType = MediaType.PROJECT;
    const mockMedia = { id: TEST_MEDIA_ID, mediaType } as Media;

    jest.spyOn(mediaRepository, 'findOneBy').mockResolvedValueOnce(mockMedia);

    const mockOrganizations = [{ id: '123', name: 'Test Organization' }]; // Mocked raw SQL query result
    jest
      .spyOn(mediaRepository, 'query')
      .mockResolvedValueOnce(mockOrganizations);

    const result = await mediaService.findOrganizationsForMedia(TEST_MEDIA_ID);

    expect(result).toMatchObject(mockOrganizations); // Adjust assertion based on your actual DTO mapping
    expect(mediaRepository.findOneBy).toHaveBeenCalledWith({
      id: TEST_MEDIA_ID,
    });
    expect(mediaRepository.query).toHaveBeenCalled(); // Validate that the raw SQL query was executed
  });

  it('getPlatformMediaById - should find platform media by platform media id', async () => {
    const platformMediaId = 'abc';
    const mockPlatformMedia = {
      mediaId: 123,
      platformMediaId,
      platformAccountId: 'act_14839483',
    };
    jest
      .spyOn(mediaService, 'getPlatformMediaById')
      .mockResolvedValueOnce(mockPlatformMedia);

    const result = await mediaService.getPlatformMediaById(platformMediaId);

    expect(result).toMatchObject(mockPlatformMedia);
    expect(mediaService.getPlatformMediaById).toHaveBeenCalledWith(
      platformMediaId,
    );
  });

  it('getPlatformMediaById - should throw an error when platform media is not found', async () => {
    const platformMediaId = 'abc';
    jest.spyOn(platformMediaRepository, 'findOneBy').mockResolvedValue(null);

    await expect(
      mediaService.getPlatformMediaById(platformMediaId),
    ).rejects.toThrowError(
      new NotFoundException(
        `Platform Media for platform media id ${platformMediaId} not found.`,
      ),
    );
  });

  it('correctly validates user access to ASSETSERVE media', async () => {
    jest
      .spyOn(mediaService, 'findOrganizationsForAssetServeMedia')
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      .mockResolvedValueOnce([{ id: 'mock-org-id-one' }]);
    const response = await mediaService.validatePersonCanViewAssetServeMedia(
      1111,
      2222,
    );
    expect(response).toBe(true);

    jest
      .spyOn(mediaService, 'findOrganizationsForAssetServeMedia')
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      .mockResolvedValueOnce([{ id: 'mock-org-idzzzzz' }]);
    const response2 = await mediaService.validatePersonCanViewAssetServeMedia(
      1111,
      2222,
    );
    expect(response2).toBe(false);
  });
});
