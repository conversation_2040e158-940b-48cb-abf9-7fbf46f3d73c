import { AutoMap } from '@automapper/classes';
import { FileType, MediaType } from '../entities/media.entity';
import { ASPECT_RATIO_FORMATS, SourceFileType } from '../media.constants';

class MediaVariantDto {
  @AutoMap()
  mimeType: string;

  @AutoMap()
  duration: number;

  @AutoMap()
  mediaUrl: string;

  @AutoMap()
  downloadUrl: string;
}

class ThumbnailDto {
  @AutoMap()
  height: number;

  @AutoMap()
  width: number;

  @AutoMap()
  url: string;
}

class StreamDto {
  @AutoMap()
  status: string;

  @AutoMap()
  url: string;
}

export class ReadMediaDto {
  @AutoMap()
  id: number;

  /**
   * The type of the media.
   */
  @AutoMap()
  mediaType: MediaType;

  /**
   *The duration of the media, rounded to the nearest second.
   */
  @AutoMap()
  duration: number;

  /**
   * The name of the media.
   */
  @AutoMap()
  name: string;

  /**
   * The height of the media in pixels.
   */
  @AutoMap()
  height?: number;

  /**
   * The width of the media in pixels.
   */
  @AutoMap()
  width?: number;

  /**
   * Aspect ratio of the media.
   */
  @AutoMap()
  format: ASPECT_RATIO_FORMATS;

  /**
   * The media's file type.
   */
  @AutoMap()
  fileType?: FileType;

  /**
   * The media's inferred source file type, mime type and source file type (in database).
   * Value is either IMAGE, VIDEO, ANIMATED_IMAGE, HTML with fallback to the media's file type.
   */
  @AutoMap()
  sourceFileType?: SourceFileType;

  /**
   * The media's display name.
   */
  @AutoMap()
  displayName?: string;

  @AutoMap()
  mediaUrl: string;

  /**
   * The media's download URL.
   */
  @AutoMap()
  downloadUrl: string;

  /**
   * Where the media was uploaded from.
   */
  @AutoMap()
  deviceIdentifier?: string;

  /**
   * Media's processing state
   */
  @AutoMap()
  processingState: string;

  /**
   * The media's thumbnails.
   */
  @AutoMap()
  thumbnails: Record<number, ThumbnailDto>;

  /**
   * The media's streams.
   */
  @AutoMap()
  streams?: Record<string, StreamDto>;

  /**
   * Variant of this media if available. Can be a different mime type or duration.
   */
  @AutoMap()
  mediaVariant?: MediaVariantDto;
}
