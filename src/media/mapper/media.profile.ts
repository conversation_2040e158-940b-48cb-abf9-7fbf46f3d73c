import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { ReadMediaDto } from '../dto/read-media.dto';
import { FileType, Media } from '../entities/media.entity';
import {
  ASPECT_RATIO_FORMATS_AND_RATIOS,
  SourceFileType,
} from '../media.constants';

export class MediaAndUrlInfo extends Media {
  thumbnailUrlsKeyedByWidth: Record<string, string>;
  streamUrlsKeyedByType: Record<string, string>;
  mediaUrl: string;
  mediaDownloadUrl: string;
  mediaVariantUrl: string;
  mediaVariantDownloadUrl: string;
}

@Injectable()
export class MediaProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper) => {
      createMap(
        mapper,
        MediaAndUrlInfo,
        ReadMediaDto,
        forMember(
          (destination) => destination.id,
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          mapFrom((source) => parseInt(source.id, 10)),
        ),
        forMember(
          (destination) => destination.duration,
          mapFrom((source) => {
            return isNaN(source.durationPrecise) &&
              source.fileType === FileType.VIDEO
              ? source.duration
              : source.durationPrecise;
          }),
        ),
        forMember(
          (destination) => destination.format,
          mapFrom((source) => {
            const ratio = source.width / source.height;
            const ratiosSortedByClosestToMedia =
              ASPECT_RATIO_FORMATS_AND_RATIOS.sort(
                (a, b) => Math.abs(ratio - a.ratio) - Math.abs(ratio - b.ratio),
              );
            return ratiosSortedByClosestToMedia[0].format;
          }),
        ),
        forMember(
          (destination) => destination.sourceFileType,
          mapFrom((source) => {
            const { fileType, mimeType, sourceFileType } = source;
            if (
              fileType &&
              fileType === FileType.IMAGE &&
              mimeType === 'image/gif'
            ) {
              return SourceFileType.ANIMATED_IMAGE;
            }
            if (
              fileType &&
              fileType === FileType.VIDEO &&
              sourceFileType === SourceFileType.HTML
            ) {
              return SourceFileType.HTML;
            }

            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore // fall back on fileType
            return fileType as SourceFileType;
          }),
        ),
        forMember(
          (destination) => destination.mediaUrl,
          mapFrom((source) => source.mediaUrl),
        ),
        forMember(
          (destination) => destination.downloadUrl,
          mapFrom((source) => source.mediaDownloadUrl),
        ),
        forMember(
          (destination) => destination.thumbnails,
          mapFrom((source) =>
            source.thumbnails.reduce((acc, curr) => {
              acc[curr.width] = {
                height: curr.height,
                width: curr.width,
                url: source.thumbnailUrlsKeyedByWidth[curr.width],
              };
              return acc;
            }, {}),
          ),
        ),
        forMember(
          (destination) => destination.streams,
          mapFrom((source) => {
            if (source.mediaPlaylists?.length) {
              return source.mediaPlaylists.reduce((acc, curr) => {
                acc[curr.streamPlaylist.networkType.toLowerCase()] =
                  source.streamUrlsKeyedByType[curr.streamPlaylist.networkType];
                return acc;
              }, {});
            }

            return null;
          }),
        ),
        forMember(
          (destination) => destination.mediaVariant,
          mapFrom((source) => {
            return source.mediaVariant
              ? {
                  mimeType: source.mediaVariant.mimeType,
                  duration: source.mediaVariant.durationPrecise,
                  mediaUrl: source.mediaVariantUrl,
                  downloadUrl: source.mediaVariantDownloadUrl,
                }
              : null;
          }),
        ),
      );
    };
  }
}
