import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { CreateFeatureWhitelistDto } from './dto/create-feature-whitelist.dto';
import { OrganizationFeatureWhitelist } from '../organizations/entities/organization-feature-whitelist.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { FeatureWhitelist } from '../workspaces/entities/feature-whitelist.entity';
import { FeatureFeatureWhitelist } from './entities/feature-feature-whitelist.entity';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { ScheduledFeatureChangesService } from './scheduled-feature-changes.service';
import { Feature } from './entities/feature.entity';
import { SuccessfulMessageResponseDto } from '../organization-invite/dto/successful-message-response.dto';

@Injectable()
export class FeaturesService {
  private readonly logger: Logger = new Logger(FeaturesService.name);
  constructor(
    @InjectRepository(OrganizationFeatureWhitelist)
    private readonly organizationFeatureWhitelistRepo: Repository<OrganizationFeatureWhitelist>,
    @InjectRepository(FeatureWhitelist)
    private readonly featureWhitelistRepo: Repository<FeatureWhitelist>,
    @InjectRepository(FeatureFeatureWhitelist)
    private readonly featureFeatureWhitelistRepo: Repository<FeatureFeatureWhitelist>,
    @Inject(ScheduledFeatureChangesService)
    private readonly scheduledFeatureChangesService: ScheduledFeatureChangesService,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  /**
   * Get features by feature whitelist.
   * @param identifier - The identifier of the feature whitelist.
   * @return The feature whitelist object.
   * @throws BadRequestException if the feature whitelist does not exist.
   */
  async getFeaturesByFeatureWhitelist(
    identifier: string,
  ): Promise<FeatureFeatureWhitelist[]> {
    const featureFeatureWhitelists =
      await this.featureFeatureWhitelistRepo.find({
        where: { featureWhitelist: { identifier } },
        relations: ['feature', 'featureWhitelist'],
      });
    if (featureFeatureWhitelists.length === 0) {
      throw new BadRequestException(
        `Invalid request. Feature whitelist with identifier ${identifier} not found.`,
      );
    }
    return featureFeatureWhitelists;
  }

  /**
   * Validate organization feature whitelists.
   * @param organizationId organization id
   * @param featureWhitelistId feature whitelist id
   * @param featureEnabled boolean indicating if the feature is enabled or not
   * @return The organization feature whitelist object.
   * @throws BadRequestException if the feature whitelist already exists for the organization or does not exist when it should.
   */
  async validateOrganizationFeatureWhitelists(
    organizationId: string,
    featureWhitelistId: number,
    featureEnabled: boolean,
  ): Promise<boolean> {
    const orgFeatureWhitelist =
      await this.organizationFeatureWhitelistRepo.findOne({
        where: {
          organizationId,
          featureWhitelistId,
        },
      });
    if (orgFeatureWhitelist && featureEnabled) {
      throw new BadRequestException(
        `The feature whitelist already exists for organization ${organizationId}.`,
      );
    } else if (!orgFeatureWhitelist && !featureEnabled) {
      throw new BadRequestException(
        `The feature whitelist does not exist for organization ${organizationId}.`,
      );
    }
    return true;
  }

  /**
   * Save the organization feature whitelist.
   * @param organizationId organization id
   * @param featureWhitelist feature whitelist object containing the details of the feature whitelist
   * @param entityManager EntityManager instance for database operations
   */
  async saveOrganizationFeatureWhitelist(
    organizationId: string,
    featureWhitelist: FeatureWhitelist,
    entityManager: EntityManager,
  ) {
    const orgFeatureWhitelist = new OrganizationFeatureWhitelist();
    orgFeatureWhitelist.organizationId = organizationId;
    orgFeatureWhitelist.featureWhitelists = featureWhitelist;
    orgFeatureWhitelist.dateCreated = new Date();

    await entityManager.save(orgFeatureWhitelist);
    this.logger.log(
      `Feature whitelist '${featureWhitelist.identifier}' is enabled for organization ${organizationId}.`,
    );
  }

  /**
   * Delete the organization feature whitelist.
   * @param organizationId organization id
   * @param featureWhitelist feature whitelist
   * @param entityManager EntityManager instance for database operations
   */
  async deleteOrganizationFeatureWhitelist(
    organizationId: string,
    featureWhitelist: FeatureWhitelist,
    entityManager: EntityManager,
  ) {
    await entityManager.delete(OrganizationFeatureWhitelist, {
      organizationId: organizationId,
      featureWhitelistId: featureWhitelist.id,
    });
    this.logger.log(
      `Feature whitelist '${featureWhitelist.identifier}' is deleted from the organization ${organizationId}.`,
    );
  }

  /**
   * Enable/Disable organization feature whitelist.
   * @param organizationId organization id
   * @param featureWhitelist feature whitelist
   * @param features feature feature whitelist objects containing the details of the features
   * @param createFeatureWhitelistDto DTO containing the details of the feature whitelist to be created
   * @param entityManager EntityManager instance for database operations
   */
  async enableOrganizationFeatureWhitelist(
    organizationId: string,
    featureWhitelist: FeatureWhitelist,
    features: Feature[],
    createFeatureWhitelistDto: CreateFeatureWhitelistDto,
    entityManager: EntityManager,
  ): Promise<void> {
    await this.saveOrganizationFeatureWhitelist(
      organizationId,
      featureWhitelist,
      entityManager,
    );
    for (const feature of features) {
      await this.scheduledFeatureChangesService.saveScheduledFeatureChanges(
        organizationId,
        feature,
        createFeatureWhitelistDto,
        entityManager,
      );
    }
  }

  /**
   * Disable organization feature whitelist.
   * @param organizationId organization id
   * @param featureWhitelist feature whitelist
   * @param features feature feature whitelist objects containing the details of the features
   * @param createFeatureWhitelistDto DTO containing the details of the feature whitelist to be created
   * @param entityManager EntityManager instance for database operations
   */
  async disableOrganizationFeatureWhitelist(
    organizationId: string,
    featureWhitelist: FeatureWhitelist,
    features: Feature[],
    createFeatureWhitelistDto: CreateFeatureWhitelistDto,
    entityManager: EntityManager,
  ): Promise<void> {
    await this.deleteOrganizationFeatureWhitelist(
      organizationId,
      featureWhitelist,
      entityManager,
    );
    for (const feature of features) {
      await this.scheduledFeatureChangesService.cancelScheduledFeatureChanges(
        organizationId,
        feature,
        entityManager,
      );
      await this.scheduledFeatureChangesService.saveScheduledFeatureChanges(
        organizationId,
        feature,
        createFeatureWhitelistDto,
        entityManager,
      );
    }
  }

  /**
   * Enable or disable organization feature whitelist based on the featureEnabled flag.
   * @param organizationId organization id
   * @param featureFeatureWhitelists feature feature whitelist objects containing the details of the features
   * @param createFeatureWhitelistDto DTO containing the details of the feature whitelist to be created
   * @param entityManager EntityManager instance for database operations
   */
  async enableOrDisableOrganizationFeatureWhitelist(
    organizationId: string,
    featureFeatureWhitelists: FeatureFeatureWhitelist[],
    createFeatureWhitelistDto: CreateFeatureWhitelistDto,
    entityManager: EntityManager,
  ): Promise<SuccessfulMessageResponseDto> {
    const featureWhitelist = featureFeatureWhitelists[0].featureWhitelist;
    const features = featureFeatureWhitelists.map((ffw) => ffw.feature);
    if (createFeatureWhitelistDto.featureEnabled) {
      await this.enableOrganizationFeatureWhitelist(
        organizationId,
        featureWhitelist,
        features,
        createFeatureWhitelistDto,
        entityManager,
      );
    } else {
      await this.disableOrganizationFeatureWhitelist(
        organizationId,
        featureWhitelist,
        features,
        createFeatureWhitelistDto,
        entityManager,
      );
    }
    const successMessage = `Feature whitelist '${
      featureWhitelist.identifier
    }' is successfully ${
      createFeatureWhitelistDto.featureEnabled ? 'added to' : 'removed from'
    } the organization ${organizationId}.`;
    this.logger.log(successMessage);
    return {
      message: successMessage,
    };
  }

  /**
   * Enable or disable a feature whitelist for an organization.
   * @param organizationId organization id
   * @param createFeatureWhitelistDto includes featureWhitelist identifier, featureEnabled flag and other parameters.
   */
  async handleOrganizationFeatureWhitelist(
    organizationId: string,
    createFeatureWhitelistDto: CreateFeatureWhitelistDto,
  ): Promise<SuccessfulMessageResponseDto> {
    const featureFeatureWhitelists = await this.getFeaturesByFeatureWhitelist(
      createFeatureWhitelistDto.featureWhitelist,
    );
    const featureWhitelistId = featureFeatureWhitelists[0].featureWhitelistId;
    await this.validateOrganizationFeatureWhitelists(
      organizationId,
      featureWhitelistId,
      createFeatureWhitelistDto.featureEnabled,
    );
    return this.organizationFeatureWhitelistRepo.manager.transaction(
      (entityManager) =>
        this.enableOrDisableOrganizationFeatureWhitelist(
          organizationId,
          featureFeatureWhitelists,
          createFeatureWhitelistDto,
          entityManager,
        ),
    );
  }
}
