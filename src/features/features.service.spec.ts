import { FeaturesService } from './features.service';
import { Test, TestingModule } from '@nestjs/testing';
import { getEntityManagerToken, getRepositoryToken } from '@nestjs/typeorm';
import { OrganizationFeatureWhitelist } from '../organizations/entities/organization-feature-whitelist.entity';
import { EntityManager, Repository } from 'typeorm';
import { FeatureWhitelist } from '../workspaces/entities/feature-whitelist.entity';
import { FeatureFeatureWhitelist } from './entities/feature-feature-whitelist.entity';
import { ScheduledFeatureChangesService } from './scheduled-feature-changes.service';
import { createMapper } from '@automapper/core';
import { getMapperToken } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { BadRequestException } from '@nestjs/common';
import { CreateFeatureWhitelistDto } from './dto/create-feature-whitelist.dto';
import { Feature } from './entities/feature.entity';

describe('Test FeaturesService class', () => {
  let service: FeaturesService;
  let featureFeatureWhitelistRepo: Repository<FeatureFeatureWhitelist>;
  let organizationFeatureWhitelistRepo: Repository<OrganizationFeatureWhitelist>;
  let entityManager: EntityManager;

  const TEST_ORG_ID = 'test-org-id';
  const TEST_USER_ID = 1234;
  const TEST_FEATURE_WHITELIST_ID = 1;
  const TEST_FEATURE_WHITELIST_IDENTIFIER = 'test-feature-whitelist-id';

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FeaturesService,
        {
          provide: ScheduledFeatureChangesService,
          useValue: {
            saveScheduledFeatureChanges: jest.fn(),
            cancelScheduledFeatureChanges: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(OrganizationFeatureWhitelist),
          useFactory: () => ({
            findOne: jest.fn(),
            manager: {
              transaction: jest
                .fn()
                .mockImplementation(async (cb) => cb(entityManager)),
            },
          }),
        },
        {
          provide: getRepositoryToken(FeatureWhitelist),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(FeatureFeatureWhitelist),
          useClass: Repository,
        },
        {
          provide: getEntityManagerToken(),
          useValue: {
            save: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    service = module.get<FeaturesService>(FeaturesService);
    featureFeatureWhitelistRepo = module.get<
      Repository<FeatureFeatureWhitelist>
    >(getRepositoryToken(FeatureFeatureWhitelist));
    organizationFeatureWhitelistRepo = module.get<
      Repository<OrganizationFeatureWhitelist>
    >(getRepositoryToken(OrganizationFeatureWhitelist));
    entityManager = module.get<EntityManager>(getEntityManagerToken());
  });

  describe('Test getFeaturesByFeatureWhitelist method', () => {
    it('should return FeatureFeatureWhitelist list when identifier exists', async () => {
      const featureFeatureWhitelists = [new FeatureFeatureWhitelist()];
      jest
        .spyOn(featureFeatureWhitelistRepo, 'find')
        .mockResolvedValue(featureFeatureWhitelists);
      const result = await service.getFeaturesByFeatureWhitelist(
        TEST_FEATURE_WHITELIST_IDENTIFIER,
      );
      expect(result).toBe(featureFeatureWhitelists);
    });

    it('should return exception when identifier does not exists', async () => {
      jest.spyOn(featureFeatureWhitelistRepo, 'find').mockResolvedValue([]);
      await expect(
        service.getFeaturesByFeatureWhitelist(
          TEST_FEATURE_WHITELIST_IDENTIFIER,
        ),
      ).rejects.toThrow(
        new BadRequestException(
          `Invalid request. Feature whitelist with identifier ${TEST_FEATURE_WHITELIST_IDENTIFIER} not found.`,
        ),
      );
    });
  });

  describe('Test validateOrganizationFeatureWhitelists method', () => {
    it('should return true when org feature whitelist exists and feature is disabled', async () => {
      jest
        .spyOn(organizationFeatureWhitelistRepo, 'findOne')
        .mockResolvedValue(new OrganizationFeatureWhitelist());
      const result = await service.validateOrganizationFeatureWhitelists(
        TEST_ORG_ID,
        TEST_FEATURE_WHITELIST_ID,
        false,
      );
      expect(result).toBe(true);
    });

    it('should throw exception when org feature whitelist exists and feature is enabled', async () => {
      jest
        .spyOn(organizationFeatureWhitelistRepo, 'findOne')
        .mockResolvedValue(new OrganizationFeatureWhitelist());
      await expect(
        service.validateOrganizationFeatureWhitelists(
          TEST_ORG_ID,
          TEST_FEATURE_WHITELIST_ID,
          true,
        ),
      ).rejects.toThrow(
        new BadRequestException(
          `The feature whitelist already exists for organization ${TEST_ORG_ID}.`,
        ),
      );
    });

    it('should return true when org feature whitelist does not exists and feature is enabled', async () => {
      jest
        .spyOn(organizationFeatureWhitelistRepo, 'findOne')
        .mockResolvedValue(null);
      const result = await service.validateOrganizationFeatureWhitelists(
        TEST_ORG_ID,
        TEST_FEATURE_WHITELIST_ID,
        true,
      );
      expect(result).toBe(true);
    });

    it('should throw exception when org feature whitelist does not exists and feature is disabled', async () => {
      jest
        .spyOn(organizationFeatureWhitelistRepo, 'findOne')
        .mockResolvedValue(null);
      await expect(
        service.validateOrganizationFeatureWhitelists(
          TEST_ORG_ID,
          TEST_FEATURE_WHITELIST_ID,
          false,
        ),
      ).rejects.toThrow(
        new BadRequestException(
          `The feature whitelist does not exist for organization ${TEST_ORG_ID}.`,
        ),
      );
    });
  });

  describe('Test saveOrganizationFeatureWhitelist method', () => {
    it('should save OrganizationFeatureWhitelist successfully', async () => {
      const featureWhitelist = {
        identifier: TEST_FEATURE_WHITELIST_IDENTIFIER,
      } as FeatureWhitelist;
      const logSpy = jest.spyOn(service['logger'], 'log');
      await service.saveOrganizationFeatureWhitelist(
        TEST_ORG_ID,
        featureWhitelist,
        entityManager,
      );
      expect(logSpy).toBeCalledWith(
        `Feature whitelist '${TEST_FEATURE_WHITELIST_IDENTIFIER}' is enabled for organization ${TEST_ORG_ID}.`,
      );
    });
  });

  describe('Test deleteOrganizationFeatureWhitelist method', () => {
    it('should delete OrganizationFeatureWhitelist successfully', async () => {
      const featureWhitelist = {
        identifier: TEST_FEATURE_WHITELIST_IDENTIFIER,
      } as FeatureWhitelist;
      const logSpy = jest.spyOn(service['logger'], 'log');
      await service.deleteOrganizationFeatureWhitelist(
        TEST_ORG_ID,
        featureWhitelist,
        entityManager,
      );
      expect(logSpy).toBeCalledWith(
        `Feature whitelist '${TEST_FEATURE_WHITELIST_IDENTIFIER}' is deleted from the organization ${TEST_ORG_ID}.`,
      );
    });
  });

  describe('Test enable/disable OrganizationFeatureWhitelist method', () => {
    const feature = new Feature();
    const featureWhitelist = new FeatureWhitelist();
    const featureFeatureWhitelist = new FeatureFeatureWhitelist();
    featureFeatureWhitelist.feature = feature;
    featureFeatureWhitelist.featureWhitelist = featureWhitelist;
    const dto = new CreateFeatureWhitelistDto();
    let enableFeatureWhitelistSpy;
    let disableFeatureWhitelistSpy;
    let scheduleFeatureChangeServiceSpy;

    beforeEach(() => {
      jest.resetAllMocks();
      enableFeatureWhitelistSpy = jest.spyOn(
        service,
        'enableOrganizationFeatureWhitelist',
      );
      disableFeatureWhitelistSpy = jest.spyOn(
        service,
        'disableOrganizationFeatureWhitelist',
      );
      scheduleFeatureChangeServiceSpy = jest.spyOn(
        service['scheduledFeatureChangesService'],
        'saveScheduledFeatureChanges',
      );
    });

    it('should save org feature whitelist and schedule job successfully', async () => {
      const saveSpy = jest.spyOn(service, 'saveOrganizationFeatureWhitelist');
      await service.enableOrganizationFeatureWhitelist(
        TEST_ORG_ID,
        featureWhitelist,
        [feature],
        dto,
        entityManager,
      );
      expect(saveSpy).toBeCalledWith(
        TEST_ORG_ID,
        featureWhitelist,
        entityManager,
      );
      expect(scheduleFeatureChangeServiceSpy).toBeCalledWith(
        TEST_ORG_ID,
        feature,
        dto,
        entityManager,
      );
    });

    it('should delete org feature whitelist and schedule job successfully', async () => {
      const deleteSpy = jest.spyOn(
        service,
        'deleteOrganizationFeatureWhitelist',
      );
      const scheduleFeatureCancelServiceSpy = jest.spyOn(
        service['scheduledFeatureChangesService'],
        'cancelScheduledFeatureChanges',
      );
      await service.disableOrganizationFeatureWhitelist(
        TEST_ORG_ID,
        featureWhitelist,
        [feature],
        dto,
        entityManager,
      );
      expect(deleteSpy).toBeCalledWith(
        TEST_ORG_ID,
        featureWhitelist,
        entityManager,
      );
      expect(scheduleFeatureCancelServiceSpy).toBeCalledWith(
        TEST_ORG_ID,
        feature,
        entityManager,
      );
      expect(scheduleFeatureChangeServiceSpy).toBeCalledWith(
        TEST_ORG_ID,
        feature,
        dto,
        entityManager,
      );
    });

    it('should call enableOrganizationFeatureWhitelist when feature whitelist is enabled', async () => {
      dto.featureEnabled = true;
      const result = await service.enableOrDisableOrganizationFeatureWhitelist(
        TEST_ORG_ID,
        [featureFeatureWhitelist],
        dto,
        entityManager,
      );
      expect(enableFeatureWhitelistSpy).toBeCalledWith(
        TEST_ORG_ID,
        featureWhitelist,
        [feature],
        dto,
        entityManager,
      );
      expect(disableFeatureWhitelistSpy).not.toBeCalled();
      expect(result).toEqual({
        message: `Feature whitelist '${featureWhitelist.identifier}' is successfully added to the organization ${TEST_ORG_ID}.`,
      });
    });

    it('should call disableOrganizationFeatureWhitelist when feature whitelist is disabled', async () => {
      dto.featureEnabled = false;
      const result = await service.enableOrDisableOrganizationFeatureWhitelist(
        TEST_ORG_ID,
        [featureFeatureWhitelist],
        dto,
        entityManager,
      );
      expect(disableFeatureWhitelistSpy).toBeCalledWith(
        TEST_ORG_ID,
        featureWhitelist,
        [feature],
        dto,
        entityManager,
      );
      expect(enableFeatureWhitelistSpy).not.toBeCalled();
      expect(result).toEqual({
        message: `Feature whitelist '${featureWhitelist.identifier}' is successfully removed from the organization ${TEST_ORG_ID}.`,
      });
    });
  });

  describe('Test handleOrganizationFeatureWhitelist method', () => {
    const featureWhitelist = new FeatureWhitelist();
    featureWhitelist.id = TEST_FEATURE_WHITELIST_ID;
    const featureFeatureWhitelist = new FeatureFeatureWhitelist();
    featureFeatureWhitelist.featureWhitelistId = TEST_FEATURE_WHITELIST_ID;
    featureFeatureWhitelist.featureWhitelist = featureWhitelist;
    featureFeatureWhitelist.feature = new Feature();
    const dto = {
      userId: TEST_USER_ID,
      featureWhitelist,
    } as unknown as CreateFeatureWhitelistDto;
    let enableOrDisableMethodSpy;

    beforeEach(() => {
      jest
        .spyOn(featureFeatureWhitelistRepo, 'find')
        .mockResolvedValue([featureFeatureWhitelist]);
      enableOrDisableMethodSpy = jest.spyOn(
        service,
        'enableOrDisableOrganizationFeatureWhitelist',
      );
    });

    it('test enable Organization FeatureWhitelist successfully', async () => {
      dto.featureEnabled = false;
      jest
        .spyOn(organizationFeatureWhitelistRepo, 'findOne')
        .mockResolvedValue(new OrganizationFeatureWhitelist());
      const result = await service.handleOrganizationFeatureWhitelist(
        TEST_ORG_ID,
        dto,
      );
      expect(enableOrDisableMethodSpy).toBeCalledWith(
        TEST_ORG_ID,
        [featureFeatureWhitelist],
        dto,
        entityManager,
      );
      expect(result).toEqual({
        message: `Feature whitelist '${featureWhitelist.identifier}' is successfully removed from the organization ${TEST_ORG_ID}.`,
      });
    });

    it('test disable Organization FeatureWhitelist successfully', async () => {
      dto.featureEnabled = true;
      jest
        .spyOn(organizationFeatureWhitelistRepo, 'findOne')
        .mockResolvedValue(null);
      const result = await service.handleOrganizationFeatureWhitelist(
        TEST_ORG_ID,
        dto,
      );
      expect(enableOrDisableMethodSpy).toBeCalledWith(
        TEST_ORG_ID,
        [featureFeatureWhitelist],
        dto,
        entityManager,
      );
      expect(result).toEqual({
        message: `Feature whitelist '${featureWhitelist.identifier}' is successfully added to the organization ${TEST_ORG_ID}.`,
      });
    });
  });
});
