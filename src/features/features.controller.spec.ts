import { Test, TestingModule } from '@nestjs/testing';
import { FeaturesService } from './features.service';
import { FeaturesController } from './features.controller';
import { SuccessfulMessageResponseDto } from '../organization-invite/dto/successful-message-response.dto';
import { CreateFeatureWhitelistDto } from './dto/create-feature-whitelist.dto';

describe('FeaturesController', () => {
  let controller: FeaturesController;
  let featuresService: FeaturesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FeaturesController],
      providers: [
        {
          provide: FeaturesService,
          useValue: {
            handleOrganizationFeatureWhitelist: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<FeaturesController>(FeaturesController);
    featuresService = module.get<FeaturesService>(FeaturesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should call service with valid CreateWorkspaceMarketMapDto', async () => {
    const TEST_ORG_ID = 'ORG_ID';
    const dto: CreateFeatureWhitelistDto = {
      userId: 1,
      featureWhitelist: 'TEST_FEATURE',
      featureEnabled: true,
    };
    const expectedMessage: SuccessfulMessageResponseDto = {
      message: 'Feature whitelist updated successfully',
    };
    featuresService.handleOrganizationFeatureWhitelist = jest
      .fn()
      .mockResolvedValue(expectedMessage);
    const response = await controller.handleOrganizationFeatureWhitelist(
      TEST_ORG_ID,
      dto,
    );

    expect(response.message).toEqual(expectedMessage.message);
  });
});
