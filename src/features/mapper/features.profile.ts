import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { CreateFeatureWhitelistDto } from '../dto/create-feature-whitelist.dto';
import { ScheduledFeatureChanges } from '../entities/scheduled-feature-changes.entity';
import { FeatureState } from '../../common/constants/constants';

@Injectable()
export class FeaturesProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper) => {
      createMap(
        mapper,
        CreateFeatureWhitelistDto,
        ScheduledFeatureChanges,
        forMember(
          (dest) => dest.featureState,
          mapFrom((dest) =>
            dest.featureEnabled ? FeatureState.ENABLED : FeatureState.DISABLED,
          ),
        ),
        forMember(
          (dest) => dest.parameters,
          mapFrom((src) => src.parameters),
        ),
        forMember(
          (dest) => dest.dateCreated,
          mapFrom(() => new Date()),
        ),
        forMember(
          (dest) => dest.lastUpdated,
          mapFrom(() => new Date()),
        ),
      );
    };
  }
}
