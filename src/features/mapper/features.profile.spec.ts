import { Mapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { CreateFeatureWhitelistDto } from '../dto/create-feature-whitelist.dto';
import { ScheduledFeatureChanges } from '../entities/scheduled-feature-changes.entity';
import { FeatureState } from '../../common/constants/constants';
import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { FeaturesProfile } from './features.profile';

describe('FeaturesProfile', () => {
  let mapper: Mapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [FeaturesProfile],
    }).compile();

    const featureProfile: FeaturesProfile =
      module.get<FeaturesProfile>(FeaturesProfile);

    mapper = module.get<Mapper>(getMapperToken());
    featureProfile.profile(mapper);
  });

  it('should map CreateFeatureWhitelistDto to ScheduledFeatureChanges correctly', () => {
    const dto: CreateFeatureWhitelistDto = {
      userId: 1,
      featureWhitelist: 'TEST_FEATURE',
      featureEnabled: true,
      parameters: { preserveHistoricData: true },
    };

    const result = mapper.map(
      dto,
      CreateFeatureWhitelistDto,
      ScheduledFeatureChanges,
    );

    expect(result.userId).toBe(dto.userId);
    expect(result.featureState).toBe(FeatureState.ENABLED);
    expect(result.parameters).toEqual({ preserveHistoricData: true });
    expect(result.dateCreated).toBeInstanceOf(Date);
    expect(result.lastUpdated).toBeInstanceOf(Date);
  });

  it('should map featureState to DISABLED when featureEnabled is false', () => {
    const dto: CreateFeatureWhitelistDto = {
      userId: 1,
      featureWhitelist: 'TEST_FEATURE',
      featureEnabled: false,
    };

    const result = mapper.map(
      dto,
      CreateFeatureWhitelistDto,
      ScheduledFeatureChanges,
    );

    expect(result.userId).toBe(dto.userId);
    expect(result.featureState).toBe(FeatureState.DISABLED);
  });
});
