import { Body, Controller, Param, Post } from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { CreateFeatureWhitelistDto } from './dto/create-feature-whitelist.dto';
import { UUID } from 'typeorm/driver/mongodb/bson.typings';
import { FeaturesService } from './features.service';
import { SuccessfulMessageResponseDto } from '../organization-invite/dto/successful-message-response.dto';
import { VmApiOkUnPaginatedArrayResponse } from '@vidmob/vidmob-nestjs-common/dist/docs/vmresponse.decorator';

@ApiTags('Features')
@Controller('feature')
/**
 * features are used to enable or disable specific functionalities in the application.
 * can be added at the organization level. Future versions can support account level.
 */
export class FeaturesController {
  constructor(private readonly featuresService: FeaturesService) {}
  @VmApiOkUnPaginatedArrayResponse({
    type: SuccessfulMessageResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @Post('organization/:organizationId/whitelist')
  handleOrganizationFeatureWhitelist(
    @Param('organizationId') organizationId: string,
    @Body() createFeatureWhitelistDto: CreateFeatureWhitelistDto,
  ): Promise<SuccessfulMessageResponseDto> {
    return this.featuresService.handleOrganizationFeatureWhitelist(
      organizationId,
      createFeatureWhitelistDto,
    );
  }
}
