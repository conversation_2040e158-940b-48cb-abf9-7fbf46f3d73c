import { Injectable, Logger } from '@nestjs/common';
import { ScheduledFeatureChanges } from './entities/scheduled-feature-changes.entity';
import { EntityManager } from 'typeorm';
import { CreateFeatureWhitelistDto } from './dto/create-feature-whitelist.dto';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { Feature } from './entities/feature.entity';
import { FeatureChangeJobStatus } from '../common/constants/constants';
import { FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED } from 'src/organizations/utils/constants';

const SCHEDULE_SUPPORTED_FEATURES = [FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED];

@Injectable()
export class ScheduledFeatureChangesService {
  private readonly logger: Logger = new Logger(
    ScheduledFeatureChangesService.name,
  );
  constructor(@InjectMapper() private readonly classMapper: Mapper) {}

  /**
   * Get initial job status for scheduled feature changes based on implementation availability.
   * @param featureIdentifier feature identifier
   * @returns FeatureChangeJobStatus
   */
  getInitialScheduleChangeJobStatus(
    featureIdentifier: string,
  ): FeatureChangeJobStatus {
    return SCHEDULE_SUPPORTED_FEATURES.includes(featureIdentifier)
      ? FeatureChangeJobStatus.SCHEDULED
      : FeatureChangeJobStatus.COMPLETED;
  }

  /**
   * Save the organization feature whitelist.
   * @param organizationId organization id
   * @param feature feature entity
   * @param createFeatureWhitelistDto DTO containing the details of the feature whitelist to be created
   * @param entityManager EntityManager instance for database operations
   */
  async saveScheduledFeatureChanges(
    organizationId: string,
    feature: Feature,
    createFeatureWhitelistDto: CreateFeatureWhitelistDto,
    entityManager: EntityManager,
  ): Promise<void> {
    const currentScheduledFeatureChanges =
      await this.getScheduledFeatureChangesByJobStatus(
        organizationId,
        feature.id,
        FeatureChangeJobStatus.SCHEDULED,
        entityManager,
      );
    if (currentScheduledFeatureChanges.length > 0) {
      this.logger.warn(
        `Feature changes for ${feature.identifier} already scheduled in organization ${organizationId}. ` +
          'Skipping the creation of new scheduled feature changes.',
      );
      return;
    }
    const scheduledFeatureChanges = this.classMapper.map(
      createFeatureWhitelistDto,
      CreateFeatureWhitelistDto,
      ScheduledFeatureChanges,
    );
    scheduledFeatureChanges.organizationId = organizationId;
    scheduledFeatureChanges.featureId = feature.id;
    scheduledFeatureChanges.jobStatus = this.getInitialScheduleChangeJobStatus(
      feature.identifier,
    );
    await entityManager.save(scheduledFeatureChanges);
    this.logger.log(
      `Scheduled feature changes for ${feature.identifier}=${createFeatureWhitelistDto.featureEnabled} in ` +
        `organization ${organizationId}.`,
    );
  }

  async cancelScheduledFeatureChanges(
    organizationId: string,
    feature: Feature,
    entityManager: EntityManager,
  ): Promise<void> {
    const scheduledFeatureChanges =
      await this.getScheduledFeatureChangesByJobStatus(
        organizationId,
        feature.id,
        FeatureChangeJobStatus.SCHEDULED,
        entityManager,
      );
    if (scheduledFeatureChanges.length === 0) {
      this.logger.warn(
        `No scheduled feature changes found for ${feature.identifier} in organization ${organizationId}.`,
      );
      return;
    }
    await entityManager.update(
      ScheduledFeatureChanges,
      {
        organizationId,
        featureId: feature.id,
        jobStatus: FeatureChangeJobStatus.SCHEDULED,
      },
      {
        jobStatus: FeatureChangeJobStatus.CANCELLED,
        lastUpdated: new Date(),
      },
    );
    this.logger.log(
      `Cancelled scheduled feature changes for ${feature.identifier} in organization ${organizationId}.`,
    );
  }

  /**
   * Get scheduled feature changes by job status.
   * @param organizationId organization id
   * @param featureId feature id
   * @param jobStatus FeatureChangeJobStatus
   * @param entityManager EntityManager instance for database operations
   */
  async getScheduledFeatureChangesByJobStatus(
    organizationId: string,
    featureId: number,
    jobStatus: FeatureChangeJobStatus,
    entityManager: EntityManager,
  ) {
    return entityManager.find(ScheduledFeatureChanges, {
      where: {
        organizationId,
        featureId,
        jobStatus,
      },
    });
  }
}
