import { Test, TestingModule } from '@nestjs/testing';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { FeatureChangeJobStatus } from '../common/constants/constants';
import { ScheduledFeatureChangesService } from './scheduled-feature-changes.service';
import { getEntityManagerToken } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { Feature } from './entities/feature.entity';
import { CreateFeatureWhitelistDto } from './dto/create-feature-whitelist.dto';
import { ScheduledFeatureChanges } from './entities/scheduled-feature-changes.entity';
import { FeaturesProfile } from './mapper/features.profile';

describe('Test Scheduled Feature Changes Service class', () => {
  let service: ScheduledFeatureChangesService;
  let entityManager: EntityManager;

  const TEST_ORG_ID = 'test-org-id';
  const TEST_FEATURE_ID = 1;
  const TEST_FEATURE = {
    id: TEST_FEATURE_ID,
    identifier: 'TEST_FEATURE',
  } as Feature;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduledFeatureChangesService,
        FeaturesProfile,
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
        {
          provide: getEntityManagerToken(),
          useValue: {
            find: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ScheduledFeatureChangesService>(
      ScheduledFeatureChangesService,
    );
    entityManager = module.get<EntityManager>(getEntityManagerToken());
  });

  describe('Test getInitialScheduleChangeJobStatus method', () => {
    it('should return the initial status of the scheduled feature changes job', async () => {
      expect(
        service.getInitialScheduleChangeJobStatus('IMPORT_SPEND_ENABLED'),
      ).toEqual(FeatureChangeJobStatus.COMPLETED);
    });
  });

  describe('Test saveScheduledFeatureChanges method', () => {
    const dto = {
      featureEnabled: true,
    } as CreateFeatureWhitelistDto;

    it('should return when scheduled job already exists', async () => {
      const warnSpy = jest.spyOn(service['logger'], 'warn');
      const entityManagerSaveSpy = jest.spyOn(entityManager, 'save');
      jest
        .spyOn(entityManager, 'find')
        .mockResolvedValueOnce([new ScheduledFeatureChanges()]);
      const result = await service.saveScheduledFeatureChanges(
        TEST_ORG_ID,
        TEST_FEATURE,
        dto,
        entityManager,
      );
      expect(result).toBeUndefined();
      expect(warnSpy).toHaveBeenCalledWith(
        `Feature changes for ${TEST_FEATURE.identifier} already scheduled in organization ${TEST_ORG_ID}. ` +
          'Skipping the creation of new scheduled feature changes.',
      );
      expect(entityManagerSaveSpy).not.toHaveBeenCalled();
    });

    it('should save when scheduled job does not exists', async () => {
      const logSpy = jest.spyOn(service['logger'], 'log');
      const entityManagerSaveSpy = jest.spyOn(entityManager, 'save');
      jest.spyOn(entityManager, 'find').mockResolvedValueOnce([]);
      await service.saveScheduledFeatureChanges(
        TEST_ORG_ID,
        TEST_FEATURE,
        dto,
        entityManager,
      );
      expect(logSpy).toHaveBeenCalledWith(
        `Scheduled feature changes for ${TEST_FEATURE.identifier}=${dto.featureEnabled} in ` +
          `organization ${TEST_ORG_ID}.`,
      );
      expect(entityManagerSaveSpy).toHaveBeenCalled();
    });
  });

  describe('Test cancelScheduledFeatureChanges method', () => {
    it('should return when scheduled job do not exists', async () => {
      const warnSpy = jest.spyOn(service['logger'], 'warn');
      const entityManagerUpdateSpy = jest.spyOn(entityManager, 'update');
      jest.spyOn(entityManager, 'find').mockResolvedValueOnce([]);
      const result = await service.cancelScheduledFeatureChanges(
        TEST_ORG_ID,
        TEST_FEATURE,
        entityManager,
      );
      expect(result).toBeUndefined();
      expect(warnSpy).toHaveBeenCalledWith(
        `No scheduled feature changes found for ${TEST_FEATURE.identifier} in organization ${TEST_ORG_ID}.`,
      );
      expect(entityManagerUpdateSpy).not.toHaveBeenCalled();
    });

    it('should call update when scheduled job already exists', async () => {
      const logSpy = jest.spyOn(service['logger'], 'log');
      const entityManagerUpdateSpy = jest.spyOn(entityManager, 'update');
      jest
        .spyOn(entityManager, 'find')
        .mockResolvedValueOnce([new ScheduledFeatureChanges()]);
      await service.cancelScheduledFeatureChanges(
        TEST_ORG_ID,
        TEST_FEATURE,
        entityManager,
      );
      expect(logSpy).toHaveBeenCalledWith(
        `Cancelled scheduled feature changes for ${TEST_FEATURE.identifier} in organization ${TEST_ORG_ID}.`,
      );
      expect(entityManagerUpdateSpy).toHaveBeenCalled();
    });
  });

  describe('Test getScheduledFeatureChangesByJobStatus method', () => {
    it('should return scheduled feature changes jobs', async () => {
      const expectedResult = [new ScheduledFeatureChanges()];
      jest.spyOn(entityManager, 'find').mockResolvedValueOnce(expectedResult);
      const result = await service.getScheduledFeatureChangesByJobStatus(
        TEST_ORG_ID,
        TEST_FEATURE_ID,
        FeatureChangeJobStatus.SCHEDULED,
        entityManager,
      );
      expect(result).toEqual(expectedResult);
    });
  });
});
