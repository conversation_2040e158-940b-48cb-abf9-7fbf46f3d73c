import { AutoMap } from '@automapper/classes';
import { IsBoolean, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class CreateFeatureWhitelistDto {
  /**
   * User ID
   */
  @AutoMap()
  @IsNumber()
  userId: number;

  /**
   * Feature Whitelist identifier
   */
  @AutoMap()
  @IsString()
  featureWhitelist: string;

  /**
   * Feature Enabled
   */
  @AutoMap()
  @IsBoolean()
  featureEnabled: boolean;

  /**
   * Additional parameters for the feature whitelist.
   */
  @AutoMap()
  @IsOptional()
  parameters?: AdditionalParameters;
}

/**
 * Additional parameters that can be passed along with the feature whitelist.
 */
export interface AdditionalParameters {
  /**
   * Applicable only for following features
   * - `Spend Enabled`
   */
  preserveHistoricData?: boolean;
}
