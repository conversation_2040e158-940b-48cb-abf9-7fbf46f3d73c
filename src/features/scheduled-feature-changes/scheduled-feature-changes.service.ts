import {
  Inject,
  Injectable,
  Logger,
  OnApplicationBootstrap,
} from '@nestjs/common';
import {
  ScheduleClient,
  ScheduleHandle,
  ScheduleOptions,
  ScheduleOptionsAction,
  ScheduleUpdateOptions,
} from '@temporalio/client';
import { InjectTemporalClient } from '@vidmob/vidmob-nestjs-temporal';
import {
  SCHEDULED_FEATURES_CHANGES_TEMPORAL_OPTIONS_TOKEN,
  SCHEDULED_FEATURES_CHANGES_TEMPORAL_CLIENT_TOKEN,
} from './scheduled-features-changes.constants';
import { ConfigService } from '@nestjs/config';
import { CONFIG_TEMPORAL_DISABLED } from 'src/common/constants/configuration.constants';

@Injectable()
export class ScheduledFeatureChangesService implements OnApplicationBootstrap {
  readonly logger = new Logger(ScheduledFeatureChangesService.name);

  constructor(
    @InjectTemporalClient(SCHEDULED_FEATURES_CHANGES_TEMPORAL_CLIENT_TOKEN)
    readonly temporalClient: ScheduleClient,
    @Inject(SCHEDULED_FEATURES_CHANGES_TEMPORAL_OPTIONS_TOKEN)
    readonly options: ScheduleOptions<ScheduleOptionsAction>,
    private readonly configService: ConfigService,
  ) {}

  async onApplicationBootstrap() {
    // if not specified, we can assume it's not disabled (this will/should only be set on sdk configs)
    const isTemporalDisabled = this.configService.get<boolean>(
      CONFIG_TEMPORAL_DISABLED,
      false,
    );

    if (isTemporalDisabled) {
      return;
    }

    try {
      const handle = await this.getCurrentSchedule();
      // There is an already created schedule, so we just need to update it with current configs
      await this.updateSchedule(handle);
    } catch (error) {
      // No schedule created yet
      await this.createSchedule();
    }
  }

  async getCurrentSchedule() {
    const scheduleId = this.options.scheduleId;
    const handle = this.temporalClient.getHandle(scheduleId);

    // get handle doesn't validate, so describe here will validate and throw an error if doesn't exists
    await handle.describe();

    return handle;
  }

  async createSchedule() {
    try {
      const schedule = await this.temporalClient.create(this.options);
      this.logger.log(`Started schedule '${schedule.scheduleId}'.`);
    } catch (error) {
      this.logger.error(
        `Failed to start schedule for ${this.options.scheduleId}`,
        error,
      );
    }
  }

  async updateSchedule(handle: ScheduleHandle) {
    try {
      await handle.update((schedule: ScheduleUpdateOptions) => {
        schedule.spec = this.options.spec;

        return schedule;
      });

      this.logger.log(`Updated schedule '${handle.scheduleId}'.`);
    } catch (error) {
      this.logger.error(
        `Failed to update schedule for ${this.options.scheduleId}`,
        error,
      );
    }
  }
}
