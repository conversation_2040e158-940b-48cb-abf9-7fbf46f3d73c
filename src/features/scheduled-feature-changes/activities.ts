import {
  FeatureChangeJobStatus,
  FeatureState,
} from 'src/common/constants/constants';
import { ScheduledFeatureChanges } from '../entities/scheduled-feature-changes.entity';

export interface FeatureChangesActions {
  getScheduleFeatureActions: () => Promise<ScheduledFeatureChanges[]>;

  updateScheduleFeatureActions: (
    id: string,
    jobStatus: FeatureChangeJobStatus,
  ) => Promise<void>;

  triggerFeatureChangeWorkflow: (
    id: string,
    featureState: FeatureState,
    featureIdentifier: string,
  ) => Promise<boolean>;
}

export interface ScheduledFeatureChangesActions extends FeatureChangesActions {}
