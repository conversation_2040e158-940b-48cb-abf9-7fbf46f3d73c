import { Test, TestingModule } from '@nestjs/testing';
import { ScheduledFeatureChangesService } from './scheduled-feature-changes.service';
import {
  SCHEDULED_FEATURES_CHANGES_TEMPORAL_CLIENT_TOKEN,
  SCHEDULED_FEATURES_CHANGES_TEMPORAL_OPTIONS_TOKEN,
} from './scheduled-features-changes.constants';
import { ConfigService } from '@nestjs/config';

describe('ScheduledFeatureChangesService', () => {
  let service: ScheduledFeatureChangesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduledFeatureChangesService,
        {
          provide: `TemporalQueue_${SCHEDULED_FEATURES_CHANGES_TEMPORAL_CLIENT_TOKEN}`, // under the hood this is the inject temporal client
          useValue: {
            create: jest.fn(),
            getHandle: jest.fn(),
            connection: {
              close: jest.fn(),
            },
          },
        },
        {
          provide: SCHEDULED_FEATURES_CHANGES_TEMPORAL_OPTIONS_TOKEN, // under the hood this is the inject schedule options
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {},
        },
      ],
    }).compile();
    service = module.get<ScheduledFeatureChangesService>(
      ScheduledFeatureChangesService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
