import { Module } from '@nestjs/common';
import { ScheduledFeatureChangesService } from './scheduled-feature-changes.service';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  ScheduleStartWorkflowOptionsModule,
  TemporalClientModule,
  TemporalScheduleClientModule,
  TemporalWorkerModule,
} from '@vidmob/vidmob-nestjs-common';
import {
  SCHEDULED_FEATURES_CHANGES_TEMPORAL_OPTIONS_TOKEN,
  SCHEDULED_FEATURES_CHANGES_TEMPORAL_SCHEDULE_ID,
  SCHEDULED_FEATURES_CHANGES_TEMPORAL_TASK_QUEUE,
  SCHEDULED_FEATURES_CHANGES_TEMPORAL_CLIENT_TOKEN,
} from './scheduled-features-changes.constants';
import {
  CONFIG_TEMPORAL_SCHEDULED_FEATURE_CHANGES_NAMESPACE,
  CONFIG_TEMPORAL_SCHEDULED_FEATURE_CHANGES_SCHEDULE,
  CONFIG_TEMPORAL_SPEND_DISABLED_NAMESPACE,
  CONFIG_TEMPORAL_WORKER_TIMER_MILISECONDS,
} from 'src/common/constants/configuration.constants';
import { TEMPORAL_WORKFLOW_FUNCTION } from 'src/common/constants/temporal.constants';
import { SPEND_DISABLED_TEMPORAL_CLIENT_TOKEN } from 'src/features/spend-disabled/spend-disabled.constants';
import { FeatureChangesActivities } from './activities/feature-changes.activities';
import { Feature } from '../entities/feature.entity';
import { ScheduledFeatureChanges } from '../entities/scheduled-feature-changes.entity';

@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forFeature([Feature, ScheduledFeatureChanges]),
    TemporalWorkerModule.forRoot({
      taskQueue: SCHEDULED_FEATURES_CHANGES_TEMPORAL_TASK_QUEUE,
      workflowsPath: require.resolve('./scheduled-features-changes.workflow'),
      temporalNamespaceConfig:
        CONFIG_TEMPORAL_SCHEDULED_FEATURE_CHANGES_NAMESPACE,
      workerTimerMilisecondsConfig: CONFIG_TEMPORAL_WORKER_TIMER_MILISECONDS,
    }),
    // Client to trigger the schedule workflow
    TemporalScheduleClientModule.forRoot(
      {
        temporalNamespaceConfig:
          CONFIG_TEMPORAL_SCHEDULED_FEATURE_CHANGES_NAMESPACE,
      },
      SCHEDULED_FEATURES_CHANGES_TEMPORAL_CLIENT_TOKEN,
    ),
    // Client to trigger Spend Disabled workflow
    TemporalClientModule.forRoot(
      {
        temporalNamespaceConfig: CONFIG_TEMPORAL_SPEND_DISABLED_NAMESPACE,
      },
      SPEND_DISABLED_TEMPORAL_CLIENT_TOKEN,
    ),
    ScheduleStartWorkflowOptionsModule.forRoot({
      injectionToken: SCHEDULED_FEATURES_CHANGES_TEMPORAL_OPTIONS_TOKEN,
      scheduleConfigName: CONFIG_TEMPORAL_SCHEDULED_FEATURE_CHANGES_SCHEDULE,
      taskQueue: SCHEDULED_FEATURES_CHANGES_TEMPORAL_TASK_QUEUE,
      workflowType: TEMPORAL_WORKFLOW_FUNCTION.SCHEDULED_FEATURE_CHANGES,
      scheduleId: SCHEDULED_FEATURES_CHANGES_TEMPORAL_SCHEDULE_ID,
      args: [],
    }),
  ],
  providers: [ScheduledFeatureChangesService, FeatureChangesActivities],
})
export class ScheduledFeatureChangesModule {}
