import { Repository } from 'typeorm';
import { FeatureChangesActivities } from './feature-changes.activities';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Test, TestingModule } from '@nestjs/testing';
import {
  SPEND_DISABLED_TEMPORAL_CLIENT_TOKEN,
  SPEND_DISABLED_TEMPORAL_TASK_QUEUE,
} from 'src/features/spend-disabled/spend-disabled.constants';
import { ConfigService } from '@nestjs/config';
import { CONFIG_SCHEDULED_FEATURE_CHANGES_START_DELAY } from 'src/common/constants/configuration.constants';
import { FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED } from 'src/organizations/utils/constants';
import { WorkflowClient } from '@temporalio/client';
import { TEMPORAL_WORKFLOW_FUNCTION } from 'src/common/constants/temporal.constants';
import { ScheduledFeatureChanges } from 'src/features/entities/scheduled-feature-changes.entity';
import { FeatureState } from 'src/common/constants/constants';

// under the hood this is the inject temporal client
const SPEND_DISABLED_TEMPORAL_QUEUE = `TemporalQueue_${SPEND_DISABLED_TEMPORAL_CLIENT_TOKEN}`;

describe('FeatureChangesActivities', () => {
  let service: FeatureChangesActivities;
  let scheduledFeatureChangesRepository: Repository<ScheduledFeatureChanges>;
  let spendDisabledWorkflowClient: WorkflowClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FeatureChangesActivities,
        {
          provide: getRepositoryToken(ScheduledFeatureChanges),
          useValue: {},
        },
        {
          provide: SPEND_DISABLED_TEMPORAL_QUEUE,
          useValue: {
            start: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key) => {
              if (key === CONFIG_SCHEDULED_FEATURE_CHANGES_START_DELAY) {
                return 0;
              }
            }),
          },
        },
      ],
    }).compile();

    service = module.get<FeatureChangesActivities>(FeatureChangesActivities);
    scheduledFeatureChangesRepository = module.get(
      getRepositoryToken(ScheduledFeatureChanges),
    );
    spendDisabledWorkflowClient = module.get(SPEND_DISABLED_TEMPORAL_QUEUE);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('triggerFeatureChangeWorkflow', () => {
    it('should trigger spend disabled successfully', async () => {
      const temporalClient = jest.spyOn(spendDisabledWorkflowClient, 'start');

      await expect(
        service.triggerFeatureChangeWorkflow(
          'xxx',
          FeatureState.DISABLED,
          FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED,
        ),
      ).resolves.toBeTruthy();

      expect(temporalClient).toHaveBeenCalledWith(
        TEMPORAL_WORKFLOW_FUNCTION.SPEND_DISABLED,
        {
          workflowId: 'xxx',
          taskQueue: SPEND_DISABLED_TEMPORAL_TASK_QUEUE,
          args: [{ jobId: 'xxx' }],
          startDelay: 0,
        },
      );
    });

    it('should trigger anything else unsuccessfully', async () => {
      await expect(
        service.triggerFeatureChangeWorkflow(
          'xxx',
          FeatureState.ENABLED,
          'feature-anything',
        ),
      ).resolves.toBeFalsy();
    });
  });
});
