import { Injectable } from '@nestjs/common';
import { FeatureChangesActions } from '../activities';
import {
  Activities,
  Activity,
  InjectTemporalClient,
} from '@vidmob/vidmob-nestjs-temporal';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { WorkflowClient } from '@temporalio/client';
import { FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED } from 'src/organizations/utils/constants';
import { TEMPORAL_WORKFLOW_FUNCTION } from 'src/common/constants/temporal.constants';
import {
  SPEND_DISABLED_TEMPORAL_CLIENT_TOKEN,
  SPEND_DISABLED_TEMPORAL_TASK_QUEUE,
} from 'src/features/spend-disabled/spend-disabled.constants';
import { ConfigService } from '@nestjs/config';
import { CONFIG_SCHEDULED_FEATURE_CHANGES_START_DELAY } from 'src/common/constants/configuration.constants';
import { Duration } from '@temporalio/common/lib/time';
import { ScheduledFeatureChanges } from 'src/features/entities/scheduled-feature-changes.entity';
import {
  FeatureChangeJobStatus,
  FeatureState,
} from 'src/common/constants/constants';

@Injectable()
@Activities()
export class FeatureChangesActivities implements FeatureChangesActions {
  private startWorkflowDelay: Duration;

  constructor(
    @InjectRepository(ScheduledFeatureChanges)
    private scheduledFeatureChangesRepository: Repository<ScheduledFeatureChanges>,
    @InjectTemporalClient(SPEND_DISABLED_TEMPORAL_CLIENT_TOKEN)
    readonly temporalClientSpendDisabled: WorkflowClient,
    private readonly configService: ConfigService,
  ) {
    this.startWorkflowDelay = this.configService.get<Duration>(
      CONFIG_SCHEDULED_FEATURE_CHANGES_START_DELAY,
    );
  }

  @Activity()
  async getScheduleFeatureActions() {
    return await this.scheduledFeatureChangesRepository.find({
      where: {
        jobStatus: FeatureChangeJobStatus.SCHEDULED,
      },
      relations: {
        feature: true,
      },
    });
  }

  @Activity()
  async updateScheduleFeatureActions(
    id: string,
    jobStatus: FeatureChangeJobStatus,
  ) {
    await this.scheduledFeatureChangesRepository.update(
      { jobId: id },
      {
        jobStatus,
        lastUpdated: new Date(),
      },
    );
  }

  @Activity()
  /**
   * It triggers a delayed workflow and returns boolean indicating if it was successful or not
   */
  async triggerFeatureChangeWorkflow(
    id: string,
    featureState: FeatureState,
    featureIdentifier: string,
  ): Promise<boolean> {
    if (
      featureIdentifier === FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED &&
      featureState === FeatureState.DISABLED
    ) {
      // trigger a delayed workflow for spend disabled
      await this.temporalClientSpendDisabled.start(
        TEMPORAL_WORKFLOW_FUNCTION.SPEND_DISABLED,
        {
          workflowId: id,
          taskQueue: SPEND_DISABLED_TEMPORAL_TASK_QUEUE,
          args: [{ jobId: id }],
          startDelay: this.startWorkflowDelay,
        },
      );
    } else {
      return false;
    }

    return true;
  }
}
