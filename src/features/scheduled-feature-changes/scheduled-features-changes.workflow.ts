import { proxyActivities } from '@temporalio/workflow';
import { ScheduledFeatureChangesActions } from './activities';
import { FeatureChangeJobStatus } from 'src/common/constants/constants';

const {
  getScheduleFeatureActions,
  updateScheduleFeatureActions,
  triggerFeatureChangeWorkflow,
} = proxyActivities<ScheduledFeatureChangesActions>({
  startToCloseTimeout: '2 minute',
  cancellationType: 'TRY_CANCEL',
});

export async function ScheduledFeatureChangesWorkflow() {
  // Get all scheduled feacture change
  const scheduleFeatureActions = await getScheduleFeatureActions();

  for (const scheduleFeatureAction of scheduleFeatureActions) {
    const successfulTrigger = await triggerFeatureChangeWorkflow(
      scheduleFeatureAction.jobId,
      scheduleFeatureAction.featureState,
      scheduleFeatureAction.feature.identifier,
    );

    if (successfulTrigger) {
      // update schedule feature change to be processing
      await updateScheduleFeatureActions(
        scheduleFeatureAction.jobId,
        FeatureChangeJobStatus.PROCESSING,
      );
    } else {
      // set job to be failed
      await updateScheduleFeatureActions(
        scheduleFeatureAction.jobId,
        FeatureChangeJobStatus.FAILED,
      );
    }
  }

  return {
    scheduleFeatureActionsProcessed: scheduleFeatureActions.length,
  };
}
