import { Module } from '@nestjs/common';
import { FeaturesController } from './features.controller';
import { FeaturesService } from './features.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrganizationsModule } from '../organizations/organizations.module';
import { ScheduledFeatureChangesService } from './scheduled-feature-changes.service';
import { Feature } from './entities/feature.entity';
import { ScheduledFeatureChanges } from './entities/scheduled-feature-changes.entity';
import { OrganizationUserModule } from '../organizations/organization-user/organization-user.module';
import { FeatureWhitelist } from '../workspaces/entities/feature-whitelist.entity';
import { FeatureFeatureWhitelist } from './entities/feature-feature-whitelist.entity';
import { OrganizationFeatureWhitelist } from '../organizations/entities/organization-feature-whitelist.entity';
import { FeaturesProfile } from './mapper/features.profile';
import { ScheduledFeatureChangesModule } from './scheduled-feature-changes/scheduled-feature-changes.module';
import { SpendDisabledModule } from './spend-disabled/spend-disabled.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Feature,
      FeatureWhitelist,
      FeatureFeatureWhitelist,
      ScheduledFeatureChanges,
      OrganizationFeatureWhitelist,
    ]),
    OrganizationsModule,
    OrganizationUserModule,
    ScheduledFeatureChangesModule,
    SpendDisabledModule,
  ],
  controllers: [FeaturesController],
  providers: [FeaturesService, ScheduledFeatureChangesService, FeaturesProfile],
  exports: [],
})
export class FeaturesModule {}
