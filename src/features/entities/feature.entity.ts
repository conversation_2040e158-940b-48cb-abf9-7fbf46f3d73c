import { AutoMap } from '@automapper/classes';
import { Column, CreateDateColumn, Entity, PrimaryColumn } from 'typeorm';

@Entity('feature')
export class Feature {
  @AutoMap()
  @PrimaryColumn({ name: 'id' })
  id: number;

  @AutoMap()
  @Column({ name: 'version' })
  version: number;

  @AutoMap()
  @Column({ name: 'name' })
  name: string;

  @AutoMap()
  @Column({ name: 'identifier' })
  identifier: string;

  @AutoMap()
  @Column({ name: 'feature_type' })
  featureType: string;

  @AutoMap()
  @Column({ name: 'restricted_access', type: 'bit', default: false })
  restrictedAccess: boolean;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;
}
