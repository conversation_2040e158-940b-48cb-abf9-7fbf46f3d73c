import {
  CreateDateColumn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryColumn,
} from 'typeorm';
import { Feature } from './feature.entity';
import { AutoMap } from '@automapper/classes';
import { FeatureWhitelist } from '../../workspaces/entities/feature-whitelist.entity';

@Entity('feature_feature_whitelist')
export class FeatureFeatureWhitelist {
  @AutoMap()
  @PrimaryColumn({ name: 'feature_id', type: 'bigint' })
  featureId: number;

  @AutoMap()
  @PrimaryColumn({ name: 'feature_whitelist_id', type: 'bigint' })
  featureWhitelistId: number;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;

  @ManyToOne(() => Feature)
  @JoinColumn({ name: 'feature_id' })
  feature?: Feature;

  @ManyToOne(() => FeatureWhitelist)
  @JoinColumn({ name: 'feature_whitelist_id' })
  featureWhitelist: FeatureWhitelist;
}
