import { AutoMap } from '@automapper/classes';
import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { IsEnum } from 'class-validator';
import {
  FeatureChangeJobStatus,
  FeatureState,
} from '../../common/constants/constants';
import { Organization } from '../../organizations/entities/organization.entity';
import { User } from '../../entities/user.entity';
import { Feature } from './feature.entity';

@Entity('scheduled_feature_changes')
export class ScheduledFeatureChanges {
  /**
   * Auto-generated unique identifier for the job
   */
  @AutoMap()
  @PrimaryGeneratedColumn('uuid', { name: 'job_id' })
  jobId: string;

  @AutoMap()
  @Column({ name: 'organization_id' })
  organizationId: string;

  @AutoMap()
  @Column({
    name: 'platform_ad_account_id',
    type: 'bigint',
    nullable: true,
  })
  platformAdAccountId?: number;

  @AutoMap()
  @Column({ name: 'feature_id', type: 'bigint' })
  featureId: number;

  @AutoMap()
  @Column({
    name: 'feature_state',
    type: 'enum',
    enum: FeatureState,
  })
  @IsEnum(FeatureState)
  featureState: FeatureState;

  @AutoMap()
  @Column({
    name: 'parameters',
    nullable: true,
    type: 'json',
  })
  parameters?: any;

  @AutoMap()
  @Column({
    name: 'job_status',
    type: 'enum',
    enum: FeatureChangeJobStatus,
  })
  @IsEnum(FeatureChangeJobStatus)
  jobStatus: FeatureChangeJobStatus;

  @AutoMap()
  @Column({ name: 'user_id', type: 'bigint' })
  userId: number;

  @AutoMap()
  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @CreateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Feature)
  @JoinColumn({ name: 'feature_id' })
  feature?: Feature;
}
