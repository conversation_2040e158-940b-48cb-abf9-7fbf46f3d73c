import { Test, TestingModule } from '@nestjs/testing';
import { SpendDisabledSnowflakeServiceService } from './spend-disabled-snowflake-service.service';
import {
  snowflakeProviderName,
  SnowFlakeWrappedConnection,
  VidMobSnowflakeProvider,
} from '@vidmob/vidmob-nestjs-common';
import { CONFIG_SNOWFLAKE_RW_PROVIDER_NAME } from 'src/common/constants/configuration.constants';
import {
  PLATFORM_SPEND_BREAKDOWN,
  PLATFORM_TABLES_BREAKDOWN,
} from './spend-disabled-snowflake-service.constants';
import { Platform } from 'src/common/constants/constants';
describe('SpendDisabledSnowflakeServiceService', () => {
  let service: SpendDisabledSnowflakeServiceService;
  let snowFlakeService: VidMobSnowflakeProvider;
  const mockSnowflakeConnection = { name: 'mock-snowflake-conn' };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SpendDisabledSnowflakeServiceService,
        {
          provide: snowflakeProviderName(CONFIG_SNOWFLAKE_RW_PROVIDER_NAME),
          useValue: {
            createConnection: jest
              .fn()
              .mockReturnValue(mockSnowflakeConnection),
            executeQuery: jest.fn(),
            closeConnection: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SpendDisabledSnowflakeServiceService>(
      SpendDisabledSnowflakeServiceService,
    );
    snowFlakeService = module.get<VidMobSnowflakeProvider>(
      snowflakeProviderName(CONFIG_SNOWFLAKE_RW_PROVIDER_NAME),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('deletePlatformV4SpendData', () => {
    it('should test delete spend data on v4 tables', async () => {
      jest.spyOn(snowFlakeService, 'executeQuery').mockResolvedValue({});

      await service.deletePlatformV4SpendData(
        {
          snowFlakeConnection: undefined,
        },
        Platform.PLATFORM_PINTEREST,
        "'1','2','3','4','5'",
      );

      // 2 V4 Tables = 2 delete calls
      expect(snowFlakeService.executeQuery).toBeCalledTimes(2);
    });
  });

  describe('deletePlatformV5SpendData', () => {
    it('should test delete spend data on v5 tables for a platform without sub metrics', async () => {
      const pinterestSchema = [
        { column_name: 'SPEND_IN_MICRO_DOLLAR' },
        { column_name: 'SPEND_IN_MICRO_DOLLAR_USD' },
        { column_name: 'APP_INSTALL_COST_PER_ACTION' },
        { column_name: 'APP_INSTALL_COST_PER_ACTION_USD' },
        { column_name: 'SOMETHING_NOT_SPEND' },
      ];
      jest
        .spyOn(snowFlakeService, 'executeQuery')
        .mockResolvedValue(pinterestSchema);

      await service.deletePlatformV5SpendData(
        {
          snowFlakeConnection: undefined,
        },
        Platform.PLATFORM_PINTEREST,
        "'1','2','3','4','5'",
      );

      // 2 V5 tables = 4 calls (1 schema per table + 1 update query per table)
      expect(snowFlakeService.executeQuery).toBeCalledTimes(4);
    });

    it('should test delete spend data on v5 tables for a platform with sub metrics', async () => {
      const facebookSchema = [
        { column_name: 'spend' },
        { column_name: 'spend_usd' },
        { column_name: 'cost_per_estimated_ad_recallers' },
        { column_name: 'social_spend' },
        { column_name: 'cpp' },
        { column_name: 'catalog_segment_value_omni_purchase_roas' },
        { column_name: 'catalog_segment_value_website_purchase_roas' },
        { column_name: 'catalog_segment_value_mobile_purchase_roas' },
        { column_name: 'actions' },
        { column_name: 'action_values' },
        { column_name: 'catalog_segment_actions' },
        { column_name: 'catalog_segment_value' },
      ];

      jest
        .spyOn(snowFlakeService, 'executeQuery')
        .mockResolvedValue(facebookSchema);

      await service.deletePlatformV5SpendData(
        {
          snowFlakeConnection: undefined,
        },
        Platform.PLATFORM_FACEBOOK,
        "'1','2','3','4','5'",
      );

      // 5 V5 tables = 10 calls (1 schema per table + 1 update query per table)
      expect(snowFlakeService.executeQuery).toBeCalledTimes(10);
    });
  });

  describe('getV4TableDeleteQuery', () => {
    it('should return the update metrics query', () => {
      let exactMetricAndSubMetricClause = ` (METRIC = 'spend') OR (METRIC = 'cost_per_estimated_ad_recallers') OR (METRIC = 'social_spend') OR (METRIC = 'cpp')`;
      exactMetricAndSubMetricClause += ` OR (METRIC = 'catalog_segment_value_omni_purchase_roas') OR (METRIC = 'catalog_segment_value_website_purchase_roas')`;
      exactMetricAndSubMetricClause += ` OR (METRIC = 'catalog_segment_value_mobile_purchase_roas') OR (METRIC = 'actions' AND SUB_METRIC IN ('offsite_conversion.fb_pixel_purchase', 'app_custom_event.fb_mobile_purchase'))`;
      exactMetricAndSubMetricClause += ` OR (METRIC = 'action_values' AND SUB_METRIC IN ('omni_purchase', 'offline_conversion.purchase'))`;
      exactMetricAndSubMetricClause += ` OR (METRIC = 'catalog_segment_actions' AND SUB_METRIC IN ('omni_purchase'))`;
      exactMetricAndSubMetricClause += ` OR (METRIC = 'catalog_segment_value' AND SUB_METRIC IN ('omni_purchase')) `;

      const metricsJoined =
        'spend|cost_per_estimated_ad_recallers|social_spend|cpp|catalog_segment_value_omni_purchase_roas|catalog_segment_value_website_purchase_roas|catalog_segment_value_mobile_purchase_roas|' +
        'actions|action_values|catalog_segment_actions|catalog_segment_value';

      const regexpMetricClause = `METRIC REGEXP '^(${metricsJoined})_([a-zA-Z]{{3}})_rate$'`;

      const metricUsdVariations =
        "'spend_usd','cost_per_estimated_ad_recallers_usd','social_spend_usd','cpp_usd','catalog_segment_value_omni_purchase_roas_usd'," +
        "'catalog_segment_value_website_purchase_roas_usd','catalog_segment_value_mobile_purchase_roas_usd'," +
        "'actions_usd','action_values_usd','catalog_segment_actions_usd','catalog_segment_value_usd'";

      const metricUsdVariationsClause = `METRIC IN (${metricUsdVariations})`;

      const metricClause = `${exactMetricAndSubMetricClause} OR ${regexpMetricClause} OR ${metricUsdVariationsClause}`;

      expect(
        service.getV4TableDeleteQuery(
          Platform.PLATFORM_FACEBOOK,
          'TEST_V4_TABLE',
          "'1','2','3','4','5'",
        ),
      ).toBe(`
      DELETE FROM TEST_V4_TABLE
      WHERE ACCOUNT IN ('1','2','3','4','5') AND (${metricClause})
    `);
    });
  });

  describe('getV5TableUpdateQuery', () => {
    it('should return the update metrics query', () => {
      expect(
        service.getV5TableUpdateQuery(
          'TEST_V5_TABLE',
          "'1','2','3','4','5'",
          [
            {
              metric: 'spend_with_submetric',
              subMetrics: ['submetric_1', 'submetric_2'],
            },
          ],
          ['spend_1', 'spend_1_usd'],
        ),
      ).toBe(`
      UPDATE TEST_V5_TABLE
      SET spend_1 = NULL, spend_1_usd = NULL, spend_with_submetric = OBJECT_INSERT(OBJECT_INSERT(spend_with_submetric, 'submetric_2', null, true), 'submetric_1', null, true)
      WHERE ACCOUNT IN ('1','2','3','4','5')
    `);
    });
  });

  describe('getV5UpdateMetricWithSubMetricsQuery', () => {
    it('should return the update submetrics query for a given metric', () => {
      expect(
        service.getV5UpdateMetricWithSubMetricsQuery({
          metric: 'test_metric',
          subMetrics: ['test_sub_metric_1', 'test_sub_metric_2'],
        }),
      ).toBe(
        "test_metric = OBJECT_INSERT(OBJECT_INSERT(test_metric, 'test_sub_metric_2', null, true), 'test_sub_metric_1', null, true)",
      );
    });
  });

  describe('getAllV5TableSpendColumns', () => {
    it('should return spend and spend currency columns', async () => {
      jest
        .spyOn(snowFlakeService, 'executeQuery')
        .mockResolvedValue([
          { column_name: 'SPEND_IN_MICRO_DOLLAR' },
          { column_name: 'SPEND_IN_MICRO_DOLLAR_USD' },
          { column_name: 'APP_INSTALL_COST_PER_ACTION' },
          { column_name: 'APP_INSTALL_COST_PER_ACTION_USD' },
          { column_name: 'SOMETHING_NOT_SPEND' },
        ]);

      await expect(
        service.getAllV5TableSpendColumns(
          {
            snowFlakeConnection: undefined,
          },
          PLATFORM_TABLES_BREAKDOWN.PINTEREST.V5[0],
          PLATFORM_SPEND_BREAKDOWN.PINTEREST,
        ),
      ).resolves.toEqual([
        'SPEND_IN_MICRO_DOLLAR',
        'SPEND_IN_MICRO_DOLLAR_USD',
        'APP_INSTALL_COST_PER_ACTION',
        'APP_INSTALL_COST_PER_ACTION_USD',
      ]);
    });
  });
});
