import { Platform } from 'src/common/constants/constants';

/**
 * To onboard a new spend metric for a given platform:
 *  - please update PLATFORM_SPEND_BREAKDOWN with the given metric (or spend metric, check Facebook to see examples on how to add new one)
 *
 * To onboard a new Analytics table for a given platform:
 *  - update the PLATFORM_TABLES_BREAKDOWN with its V4 and/or V5 version for the given platform
 *
 * To onboard a new Platform:
 *  - If the platform has spend metrics, update the necessary tables on PLATFORM_TABLES_BREAKDOWN and spend metrics on PLATFORM_SPEND_BREAKDOWN with above instructions.
 *
 *  - If it doesn't include, then doesn't need to add anything.
 */

export enum SnowflakeTableVersion {
  'V4' = 'V4',
  'V5' = 'V5',
}

// V4 and V5 tables breakdown by platform
export const PLATFORM_TABLES_BREAKDOWN: {
  [keyof in Platform]?: {
    [key in keyof typeof SnowflakeTableVersion]: string[];
  };
} = {
  [Platform.PLATFORM_ADWORDS]: {
    [SnowflakeTableVersion.V4]: [
      'ANALYTICS_RECORD_V4_ADWORDS_AD',
      'ANALYTICS_RECORD_V4_ADWORDS_AD_AGE',
      'ANALYTICS_RECORD_V4_ADWORDS_AD_ASSET',
      'ANALYTICS_RECORD_V4_ADWORDS_AD_GENDER',
    ],
    [SnowflakeTableVersion.V5]: [
      'ANALYTICS_RECORD_V5_ADWORDS_AD',
      'ANALYTICS_RECORD_V5_ADWORDS_AD_AGE',
      'ANALYTICS_RECORD_V5_ADWORDS_AD_GENDER',
    ],
  },
  [Platform.PLATFORM_AMAZON_ADVERTISING]: {
    [SnowflakeTableVersion.V4]: [
      'ANALYTICS_RECORD_V4_AMAZONADVERTISING_SPONSORED_BRAND_CAMPAIGN',
      'ANALYTICS_RECORD_V4_AMAZONADVERTISING_SPONSORED_BRAND_CAMPAIGN_PLACEMENT',
      'ANALYTICS_RECORD_V4_AMAZONADVERTISING_SPONSORED_DISPLAY_CAMPAIGN',
    ],
    [SnowflakeTableVersion.V5]: [
      'ANALYTICS_RECORD_V5_AMAZONADVERTISING_SPONSORED_BRAND_CAMPAIGN',
      'ANALYTICS_RECORD_V5_AMAZONADVERTISING_SPONSORED_BRAND_CAMPAIGN_PLACEMENT',
    ],
  },
  [Platform.PLATFORM_AMAZON_ADVERTISING_DSP]: {
    [SnowflakeTableVersion.V4]: [
      'ANALYTICS_RECORD_V4_AMAZONADVERTISINGDSP_CAMPAIGN',
      'ANALYTICS_RECORD_V4_AMAZONADVERTISINGDSP_PLACEMENT',
    ],
    [SnowflakeTableVersion.V5]: [
      'ANALYTICS_RECORD_V5_AMAZONADVERTISINGDSP_CAMPAIGN',
      'ANALYTICS_RECORD_V5_AMAZONADVERTISINGDSP_PLACEMENT',
    ],
  },
  [Platform.PLATFORM_DV360]: {
    [SnowflakeTableVersion.V4]: [
      'ANALYTICS_RECORD_V4_DV360_AD',
      'ANALYTICS_RECORD_V4_DV360_AGE_GENDER',
      'ANALYTICS_RECORD_V4_DV360_PLACEMENT',
    ],
    [SnowflakeTableVersion.V5]: [
      'ANALYTICS_RECORD_V5_DV360_AD',
      'ANALYTICS_RECORD_V5_DV360_AGE_GENDER',
      'ANALYTICS_RECORD_V5_DV360_PLACEMENT',
    ],
  },
  [Platform.PLATFORM_FACEBOOK]: {
    [SnowflakeTableVersion.V4]: [
      'ANALYTICS_RECORD_V4_FACEBOOK_AD',
      'ANALYTICS_RECORD_V4_FACEBOOK_AD_AGE_GENDER',
      'ANALYTICS_RECORD_V4_FACEBOOK_AD_COUNTRY',
      'ANALYTICS_RECORD_V4_FACEBOOK_AD_ASSET',
      'ANALYTICS_RECORD_V4_FACEBOOK_AD_DMA',
      'ANALYTICS_RECORD_V4_FACEBOOK_AD_PLACEMENT',
    ],
    [SnowflakeTableVersion.V5]: [
      'ANALYTICS_RECORD_V5_FACEBOOK_AD',
      'ANALYTICS_RECORD_V5_FACEBOOK_AD_AGE_GENDER',
      'ANALYTICS_RECORD_V5_FACEBOOK_AD_COUNTRY',
      'ANALYTICS_RECORD_V5_FACEBOOK_AD_DMA',
      'ANALYTICS_RECORD_V5_FACEBOOK_AD_PLACEMENT',
    ],
  },
  [Platform.PLATFORM_LINKEDIN]: {
    [SnowflakeTableVersion.V4]: [
      'ANALYTICS_RECORD_V4_LINKEDIN_AD',
      'ANALYTICS_RECORD_V4_LINKEDIN_AD_MEMBER_COMPANY',
      'ANALYTICS_RECORD_V4_LINKEDIN_AD_MEMBER_COMPANY_SIZE',
      'ANALYTICS_RECORD_V4_LINKEDIN_AD_MEMBER_COUNTRY',
      'ANALYTICS_RECORD_V4_LINKEDIN_AD_MEMBER_INDUSTRY',
      'ANALYTICS_RECORD_V4_LINKEDIN_AD_MEMBER_JOB_FUNCTION',
      'ANALYTICS_RECORD_V4_LINKEDIN_AD_MEMBER_JOB_TITLE',
      'ANALYTICS_RECORD_V4_LINKEDIN_AD_MEMBER_REGION',
      'ANALYTICS_RECORD_V4_LINKEDIN_AD_MEMBER_SENIORITY',
      'ANALYTICS_RECORD_V4_LINKEDIN_AD_PLACEMENT',
    ],
    [SnowflakeTableVersion.V5]: [
      'ANALYTICS_RECORD_V5_LINKEDIN_AD',
      'ANALYTICS_RECORD_V5_LINKEDIN_AD_MEMBER_COMPANY',
      'ANALYTICS_RECORD_V5_LINKEDIN_AD_MEMBER_COMPANY_SIZE',
      'ANALYTICS_RECORD_V5_LINKEDIN_AD_MEMBER_COUNTRY',
      'ANALYTICS_RECORD_V5_LINKEDIN_AD_MEMBER_INDUSTRY',
      'ANALYTICS_RECORD_V5_LINKEDIN_AD_MEMBER_JOB_FUNCTION',
      'ANALYTICS_RECORD_V5_LINKEDIN_AD_MEMBER_JOB_TITLE',
      'ANALYTICS_RECORD_V5_LINKEDIN_AD_MEMBER_REGION',
      'ANALYTICS_RECORD_V5_LINKEDIN_AD_MEMBER_SENIORITY',
      'ANALYTICS_RECORD_V5_LINKEDIN_AD_PLACEMENT',
    ],
  },
  [Platform.PLATFORM_PINTEREST]: {
    [SnowflakeTableVersion.V4]: [
      'ANALYTICS_RECORD_V4_PINTEREST_AD',
      'ANALYTICS_RECORD_V4_PINTEREST_AD_TARGETING',
    ],
    [SnowflakeTableVersion.V5]: [
      'ANALYTICS_RECORD_V5_PINTEREST_AD',
      'ANALYTICS_RECORD_V5_PINTEREST_AD_TARGETING',
    ],
  },
  [Platform.PLATFORM_REDDIT]: {
    [SnowflakeTableVersion.V4]: ['ANALYTICS_RECORD_V4_REDDIT_AD'],
    [SnowflakeTableVersion.V5]: ['ANALYTICS_RECORD_V5_REDDIT_AD'],
  },
  [Platform.PLATFORM_SNAPCHAT]: {
    [SnowflakeTableVersion.V4]: [
      'ANALYTICS_RECORD_V4_SNAP_AD',
      'ANALYTICS_RECORD_V4_SNAP_AD_DEMO',
      'ANALYTICS_RECORD_V4_SNAP_AD_DMA',
      'ANALYTICS_RECORD_V4_SNAP_AD_STORY',
    ],
    [SnowflakeTableVersion.V5]: [
      'ANALYTICS_RECORD_V5_SNAP_AD',
      'ANALYTICS_RECORD_V5_SNAP_AD_DEMO',
      'ANALYTICS_RECORD_V5_SNAP_AD_DMA',
      'ANALYTICS_RECORD_V5_SNAP_AD_STORY',
    ],
  },
  [Platform.PLATFORM_TIKTOK]: {
    [SnowflakeTableVersion.V4]: [
      'ANALYTICS_RECORD_V4_TIKTOK_AD',
      'ANALYTICS_RECORD_V4_TIKTOK_AD_AC',
      'ANALYTICS_RECORD_V4_TIKTOK_AD_AGE',
      'ANALYTICS_RECORD_V4_TIKTOK_AD_COUNTRY',
      'ANALYTICS_RECORD_V4_TIKTOK_AD_GENDER',
      'ANALYTICS_RECORD_V4_TIKTOK_AD_LANGUAGE',
      'ANALYTICS_RECORD_V4_TIKTOK_AD_PLACEMENT',
      'ANALYTICS_RECORD_V4_TIKTOK_AD_PLATFORM',
      'ANALYTICS_RECORD_V4_TIKTOK_AD_TAG',
    ],
    [SnowflakeTableVersion.V5]: [
      'ANALYTICS_RECORD_V5_TIKTOK_AD',
      'ANALYTICS_RECORD_V5_TIKTOK_AD_AC',
      'ANALYTICS_RECORD_V5_TIKTOK_AD_AGE',
      'ANALYTICS_RECORD_V5_TIKTOK_AD_COUNTRY',
      'ANALYTICS_RECORD_V5_TIKTOK_AD_GENDER',
      'ANALYTICS_RECORD_V5_TIKTOK_AD_LANGUAGE',
      'ANALYTICS_RECORD_V5_TIKTOK_AD_PLACEMENT',
      'ANALYTICS_RECORD_V5_TIKTOK_AD_PLATFORM',
      'ANALYTICS_RECORD_V5_TIKTOK_AD_TAG',
    ],
  },
  [Platform.PLATFORM_TWITTER]: {
    [SnowflakeTableVersion.V4]: [
      'ANALYTICS_RECORD_V4_TWITTER_AD',
      'ANALYTICS_RECORD_V4_TWITTER_AD_AGE',
      'ANALYTICS_RECORD_V4_TWITTER_AD_GENDER',
    ],
    [SnowflakeTableVersion.V5]: [
      'ANALYTICS_RECORD_V5_TWITTER_AD',
      'ANALYTICS_RECORD_V5_TWITTER_AD_AGE',
      'ANALYTICS_RECORD_V5_TWITTER_AD_GENDER',
    ],
  },
};

export type METRIC_OBJECT_TYPE = { metric: string; subMetrics?: string[] };

/**
 * Spend metrics/submetrics by platform for V4 and V5 tables.
 * If the submetrics is not specified, it's because it's a top level metric without submetric.
 *
 * If top level metric have submetric, just add the subMetrics array and specify only the submetric part of the whole metric.
 *
 */
export const PLATFORM_SPEND_BREAKDOWN: {
  [keyof in Platform]?: METRIC_OBJECT_TYPE[];
} = {
  [Platform.PLATFORM_ADWORDS]: [
    {
      metric: 'all_conversions_value_per_cost',
    },
    {
      metric: 'conversions_value_per_cost',
    },
    {
      metric: 'cost',
    },
    {
      metric: 'cost_per_all_conversion',
    },
    {
      metric: 'cost_per_all_conversions',
    },
    {
      metric: 'cost_per_conversion',
    },
    {
      metric: 'cost_per_all_conversions',
    },
    {
      metric: 'active_view_cpm',
    },
    {
      metric: 'all_conversion_rate',
    },
    {
      metric: 'average_cost',
    },
    {
      metric: 'average_cpc',
    },
    {
      metric: 'average_cpe',
    },
    {
      metric: 'average_cpm',
    },
    {
      metric: 'average_cpv',
    },
  ],
  [Platform.PLATFORM_AMAZON_ADVERTISING]: [
    {
      metric: 'campaignBudget',
    },
    {
      metric: 'cost',
    },
    {
      metric: 'attributedSales14d',
    },
    {
      metric: 'attributedSales14dSameSKU',
    },
    {
      metric: 'viewAttributedSales14d',
    },
    {
      metric: 'viewAttributedConversions14d',
    },
    {
      metric: 'attributedConversions14d',
    },
    {
      metric: 'attributedConversions14dSameSKU',
    },
    {
      metric: 'attributedSalesNewToBrand14d',
    },
    {
      metric: 'attributedSales7dSameSKU',
    },
  ],
  [Platform.PLATFORM_AMAZON_ADVERTISING_DSP]: [
    {
      metric: 'totalCost',
    },
    {
      metric: 'purchases14d',
    },
    {
      metric: 'purchasesViews14d',
    },
    {
      metric: 'purchasesClicks14d',
    },
    {
      metric: 'purchaseRate14d',
    },
    {
      metric: 'newToBrandPurchases14d',
    },
    {
      metric: 'newToBrandPurchasesViews14d',
    },
    {
      metric: 'newToBrandPurchasesClicks14d',
    },
    {
      metric: 'newToBrandPurchaseRate14d',
    },
    {
      metric: 'newToBrandROAS14d',
    },
    {
      metric: 'percentOfPurchasesNewToBrand14d',
    },
    {
      metric: 'addedToShoppingCart14d',
    },
    {
      metric: 'addedToShoppingCartViews14d',
    },
    {
      metric: 'addedToShoppingCartClicks14d',
    },
    {
      metric: 'addedToShoppingCartCVR14d',
    },
    {
      metric: 'addedToShoppingCartCPA14d',
    },
    {
      metric: 'productPurchased',
    },
    {
      metric: 'productPurchasedViews',
    },
    {
      metric: 'sales14d',
    },
    {
      metric: 'totalROAS14d',
    },
  ],
  [Platform.PLATFORM_DV360]: [
    {
      metric: 'media_cost_advertiser', // METRIC_MEDIA_COST_ADVERTISER
    },
    {
      metric: 'total_media_cost_advertiser', // METRIC_TOTAL_MEDIA_COST_ADVERTISER
    },
    {
      metric: 'revenue_advertiser', // METRIC_REVENUE_ADVERTISER
    },
    {
      metric: 'revenue_ecpc_advertiser', // METRIC_REVENUE_ECPC_ADVERTISER
    },
    {
      metric: 'revenue_ecpm_advertiser', // METRIC_REVENUE_ECPM_ADVERTISER
    },
    {
      metric: 'revenue_viewable_ecpm_advertiser', // METRIC_REVENUE_VIEWABLE_ECPM_ADVERTISER
    },
    {
      metric: 'trueview_cpv_advertiser', // METRIC_TRUEVIEW_CPV_ADVERTISER
    },
  ],
  [Platform.PLATFORM_FACEBOOK]: [
    {
      metric: 'spend',
    },
    {
      metric: 'cost_per_estimated_ad_recallers',
    },
    {
      metric: 'social_spend',
    },
    {
      metric: 'cpp',
    },
    {
      metric: 'catalog_segment_value_omni_purchase_roas',
    },
    {
      metric: 'catalog_segment_value_website_purchase_roas',
    },
    {
      metric: 'catalog_segment_value_mobile_purchase_roas',
    },
    {
      metric: 'actions',
      subMetrics: [
        'offsite_conversion.fb_pixel_purchase',
        'app_custom_event.fb_mobile_purchase',
      ],
    },
    {
      metric: 'action_values',
      subMetrics: ['omni_purchase', 'offline_conversion.purchase'],
    },
    {
      metric: 'catalog_segment_actions',
      subMetrics: ['omni_purchase'],
    },
    {
      metric: 'catalog_segment_value',
      subMetrics: ['omni_purchase'],
    },
  ],
  [Platform.PLATFORM_LINKEDIN]: [
    {
      metric: 'costInUsd',
    },
    {
      metric: 'costInLocalCurrency',
    },
    {
      metric: 'conversionValueInLocalCurrency',
    },
  ],
  [Platform.PLATFORM_PINTEREST]: [
    {
      metric: 'SPEND_IN_MICRO_DOLLAR',
    },
    {
      metric: 'APP_INSTALL_COST_PER_ACTION',
    },
  ],
  [Platform.PLATFORM_REDDIT]: [
    {
      metric: 'purchase',
    },
    {
      metric: 'cpc',
    },
    {
      metric: 'cpv',
    },
    {
      metric: 'ecpm',
    },
    {
      metric: 'spend',
    },
    {
      metric: 'app_install_mmp_spend_credits_cvr',
    },
    {
      metric: 'app_install_mmp_spend_credits_ecpa',
    },
    {
      metric: 'app_install_spend_credits_count',
    },
    {
      metric: 'app_install_spend_credits_cvr',
    },
    {
      metric: 'app_install_spend_credits_ecpa',
    },
    {
      metric: 'app_install_mmp_purchase_count',
    },
    {
      metric: 'app_install_mmp_purchase_cvr',
    },
    {
      metric: 'app_install_mmp_purchase_ecpa',
    },
    {
      metric: 'app_install_purchase_count',
    },
    {
      metric: 'app_install_purchase_cvr',
    },
    {
      metric: 'app_install_purchase_ecpa',
    },
    {
      metric: 'conversion_purchase_avg_value',
    },
    {
      metric: 'conversion_purchase_clicks',
    },
    {
      metric: 'conversion_purchase_ecpa',
    },
    {
      metric: 'conversion_purchase_total_items',
    },
    {
      metric: 'conversion_purchase_total_value',
    },
    {
      metric: 'conversion_purchase_views',
    },
    {
      metric: 'app_install_add_payment_info_ecpa',
    },
    {
      metric: 'app_install_add_to_cart_ecpa',
    },
    {
      metric: 'app_install_app_launch_ecpa',
    },
    {
      metric: 'app_install_install_ecpa',
    },
    {
      metric: 'app_install_level_achieved_ecpa',
    },
    {
      metric: 'app_install_mmp_add_to_cart_ecpa',
    },
    {
      metric: 'app_install_mmp_app_launch_ecpa',
    },
    {
      metric: 'app_install_mmp_completed_tutorial_ecpa',
    },
    {
      metric: 'app_install_mmp_install_ecpa',
    },
    {
      metric: 'app_install_mmp_level_achieved_ecpa',
    },
    {
      metric: 'app_install_mmp_reinstall_ecpa',
    },
    {
      metric: 'app_install_mmp_roas',
    },
    {
      metric: 'app_install_mmp_search_ecpa',
    },
    {
      metric: 'app_install_mmp_sign_up_ecpa',
    },
    {
      metric: 'app_install_mmp_view_content_ecpa',
    },
    {
      metric: 'app_install_revenue',
    },
    {
      metric: 'app_install_roas_double',
    },
    {
      metric: 'app_install_search_ecpa',
    },
    {
      metric: 'app_install_sign_up_ecpa',
    },
    {
      metric: 'app_install_skan_install_ecpa',
    },
    {
      metric: 'app_install_skan_reinstall_ecpa',
    },
    {
      metric: 'app_install_skan_total_install_ecpa',
    },
    {
      metric: 'app_install_view_content_ecpa',
    },
    {
      metric: 'conversion_add_to_cart_ecpa',
    },
    {
      metric: 'conversion_add_to_wishlist_ecpa',
    },
    {
      metric: 'conversion_lead_ecpa',
    },
    {
      metric: 'conversion_page_visit_ecpa',
    },
    {
      metric: 'conversion_roas',
    },
    {
      metric: 'conversion_search_ecpa',
    },
    {
      metric: 'conversion_sign_up_ecpa',
    },
    {
      metric: 'conversion_view_content_ecpa',
    },
  ],
  [Platform.PLATFORM_SNAPCHAT]: [
    {
      metric: 'spend',
    },
    {
      metric: 'conversion_purchases',
    },
    {
      metric: 'conversion_purchases_value',
    },
    {
      metric: 'conversion_add_billing',
    },
  ],
  [Platform.PLATFORM_TIKTOK]: [
    {
      metric: 'stat_cost', // spend after renaming
    },
    {
      metric: 'click_cost', // cpc after renaming
    },
    {
      metric: 'time_attr_effect_cost', // cost_per_result after renaming
    },
    {
      metric: 'time_attr_deep_convert_cost', // cost_per_secondary_goal_result after renaming
    },
    {
      metric: 'cost_per_show_uv_mille', // cost_per_1000_reached after renaming
    },
    {
      metric: 'active_click_cost', // cost_per_cta_conversion after renaming
    },
    {
      metric: 'effect_cost', // real_time_cost_per_result after renaming
    },
    {
      metric: 'time_attr_conversion_cost', // cost_per_conversion after renaming
    },
    {
      metric: 'active_pay_click_cost', // cost_per_cta_purchase after renaming
    },
    {
      metric: 'time_attr_active_cost', // cost_per_app_install after renaming
    },
    {
      metric: 'active_pay_cost', // cost_per_purchase after renaming
    },
    {
      metric: 'active_show_cost', // cost_per_vta_conversion after renaming
    },
    {
      metric: 'active_cost', // real_time_app_install_cost after renaming
    },
    {
      metric: 'active_register_cost', // cost_per_registration after renaming
    },
    {
      metric: 'conversion_cost', // real_time_cost_per_conversion after renaming
    },
    {
      metric: 'active_pay_show_cost', // cost_per_vta_purchase after renaming
    },
    {
      metric: '', // cpm after renaming
    },
  ],
  [Platform.PLATFORM_TWITTER]: [
    {
      metric: 'conversion_purchases',
    },
    {
      metric: 'mobile_conversion_spent_credits',
    },
    {
      metric: 'billed_charge_local_micro',
    },
    {
      metric: 'billed_engagements',
    },
    {
      metric: 'mobile_conversion_lifetime_value_purchases',
    },
    {
      metric: 'mobile_conversion_lifetime_value_payment_info_additions',
    },
    {
      metric: 'mobile_conversion_lifetime_value_spent_credits',
    },
    {
      metric: 'mobile_conversion_lifetime_value_rates',
    },
  ],
};
