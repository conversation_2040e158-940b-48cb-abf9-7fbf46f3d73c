import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  snowflakeProviderName,
  SnowFlakeWrappedConnection,
  VidMobSnowflakeProvider,
} from '@vidmob/vidmob-nestjs-common';
import { CONFIG_SNOWFLAKE_RW_PROVIDER_NAME } from 'src/common/constants/configuration.constants';
import { Platform } from 'src/common/constants/constants';
import {
  METRIC_OBJECT_TYPE,
  PLATFORM_SPEND_BREAKDOWN,
  PLATFORM_TABLES_BREAKDOWN,
} from './spend-disabled-snowflake-service.constants';

@Injectable()
export class SpendDisabledSnowflakeServiceService {
  private readonly logger = new Logger(
    SpendDisabledSnowflakeServiceService.name,
  );

  constructor(
    @Inject(snowflakeProviderName(CONFIG_SNOWFLAKE_RW_PROVIDER_NAME))
    private readonly snowFlakeService: VidMobSnowflakeProvider,
  ) {}

  /**
   * Start the deletion
   * @param allOrganizationPlatformAccountIds
   */
  async onDeleteSpendMetricsForOrganization(
    allOrganizationPlatformAccountIds: string[],
  ) {
    const snowflakeConnection: SnowFlakeWrappedConnection =
      await this.snowFlakeService.createConnection();

    const platformsWithSpend = Object.values(Platform).filter(
      (platform) => !!PLATFORM_TABLES_BREAKDOWN[platform],
    );

    const quotedAccountIds = allOrganizationPlatformAccountIds
      .map((accountId) => `'${accountId}'`)
      .join(',');

    try {
      for (const platform of platformsWithSpend) {
        await this.deletePlatformV4SpendData(
          snowflakeConnection,
          platform,
          quotedAccountIds,
        );
        await this.deletePlatformV5SpendData(
          snowflakeConnection,
          platform,
          quotedAccountIds,
        );
      }
    } catch (error) {
      this.logger.error(error);
      throw error;
    } finally {
      // in case anything wrong happens, we close the connection
      await this.snowFlakeService.closeConnection(snowflakeConnection);
    }
  }

  /**
   * For a given platform delete all spend data across all their V4 tables
   * @param platform
   * @param quotedAccountIds
   * @returns
   */
  public async deletePlatformV4SpendData(
    snowflakeConnection: SnowFlakeWrappedConnection,
    platform: Platform,
    quotedAccountIds: string,
  ): Promise<void> {
    const v4Tables = PLATFORM_TABLES_BREAKDOWN[platform].V4;

    const deleteQueries = v4Tables.map((v4Table) =>
      this.getV4TableDeleteQuery(platform, v4Table, quotedAccountIds),
    );

    await Promise.all(
      deleteQueries.map(async (deleteQuery) => {
        await this.runQuery(snowflakeConnection, deleteQuery);
      }),
    );
  }

  /**
   * For a given platform delete all spend data across all their V5 tables
   * @param snowflakeConnection
   * @param platform
   * @param quotedAccountIds
   * @returns
   */
  public async deletePlatformV5SpendData(
    snowflakeConnection: SnowFlakeWrappedConnection,
    platform: Platform,
    quotedAccountIds: string,
  ): Promise<void> {
    const v5Tables = PLATFORM_TABLES_BREAKDOWN[platform].V5;
    const spendMetrics = PLATFORM_SPEND_BREAKDOWN[platform];

    const updateQueries = await Promise.all(
      v5Tables.map(async (v5Table) => {
        const spendColumns = await this.getAllV5TableSpendColumns(
          snowflakeConnection,
          v5Table,
          spendMetrics,
        );

        return this.getV5TableUpdateQuery(
          v5Table,
          quotedAccountIds,
          spendMetrics,
          spendColumns,
        );
      }),
    );

    await Promise.all(
      updateQueries.map(async (updateQuery) => {
        await this.runQuery(snowflakeConnection, updateQuery);
      }),
    );
  }

  /**
   * V4 Tables includes spend metrics on the metric and also on the sub_metric columns.
   * Also, V4 tables includes two variations:
   *  - <metric>_usd -> V5 include that as well;
   *  - <metric>_<currency>_rate -> only on V4.
   *
   * These variations will be deleted along with the metrics altogether
   * @param platform
   * @param v4Table
   * @param accountIds
   * @returns
   */
  public getV4TableDeleteQuery(
    platform: Platform,
    v4Table: string,
    accountIdsClause: string,
  ): string {
    var spendMetrics = PLATFORM_SPEND_BREAKDOWN[platform];

    const spendMetricsWhereClause =
      this.convertMetricAndSubMetricForV4WhereClause(spendMetrics);

    const whereClause = `ACCOUNT IN (${accountIdsClause}) AND (${spendMetricsWhereClause})`;

    return `
      DELETE FROM ${v4Table}
      WHERE ${whereClause}
    `;
  }

  /**
   * V5 tables are normalized ones, so we cannot just deleting data, we need to update spend data and set as NULL.
   *
   * @param platform
   * @param v5Table
   * @param accountIds
   * @returns
   */
  public getV5TableUpdateQuery(
    v5Table: string,
    accountIdsClause: string,
    spendMetrics: METRIC_OBJECT_TYPE[],
    spendColumns: string[],
  ): string {
    let spendMetricsSetClause = this.convertMetricV5SetClause(spendColumns);

    const includesSpendSubMetrics =
      this.doesSpendMetricsIncludeSubMetric(spendMetrics);

    if (includesSpendSubMetrics) {
      const spendSubMetricsSetClause = spendMetrics
        .map((spendMetric) =>
          this.getV5UpdateMetricWithSubMetricsQuery(spendMetric),
        )
        // removing queries where the spend metric doesn't include submetrics
        .filter(
          (spendMetricWithSubmetricQuery) =>
            spendMetricWithSubmetricQuery !== '',
        )
        .join(', ');

      spendMetricsSetClause += `, ${spendSubMetricsSetClause}`;
    }

    const whereClause = `ACCOUNT IN (${accountIdsClause})`;

    return `
      UPDATE ${v5Table}
      SET ${spendMetricsSetClause}
      WHERE ${whereClause}
    `;
  }

  /**
   * Returns a single string to be used on SET clause to update all the submetrics for a given metric.
   *
   * Note: We use OBJECT_INSERT rather than the OBJECT_DELETE, because if the key doesn't exists the delete will throw an error,
   * the insert with update flag set to true won't throw an error and with NULL the value won't appear.
   *
   * Note2: we use nested OBJECT_INSERT so we can update all submetrics for a given metric
   * @param spendMetric
   * @returns
   */
  public getV5UpdateMetricWithSubMetricsQuery(spendMetric: METRIC_OBJECT_TYPE) {
    // doesn't include submetrics
    if (!this.doesSpendMetricIncludeSubMetric(spendMetric)) {
      return '';
    }

    const subMetrics = spendMetric.subMetrics.map((subMetric) => subMetric);

    const setSubMetricClause = subMetrics.reduce(
      (objectInsertQuery: string, subMetric: string) => {
        const query = `OBJECT_INSERT(${spendMetric.metric}, '${subMetric}', null, true)`;

        // if current query is empty, just set the query
        if (objectInsertQuery === '') {
          return query;
        }

        // otherwise, replace the column with nested query, this way we can do in a single update clause
        return objectInsertQuery.replace(spendMetric.metric, query);
      },
      '',
    );

    return `${spendMetric.metric} = ${setSubMetricClause}`;
  }

  /**
   * Usually, we wouldn't need to get all spend columns because we do have it,
   * but since we are storing some spend metrics variations on the <metric>_usd, we are getting all the columns and return all that we know + the usd variations ones.
   *
   * This will allow new <metric>_usd variations to be included by Analytics' team without needing code change.
   * @param snowflakeConnection
   * @param v5Table
   * @param spendMetrics
   */
  public async getAllV5TableSpendColumns(
    snowflakeConnection: SnowFlakeWrappedConnection,
    v5Table: string,
    spendMetrics: METRIC_OBJECT_TYPE[],
  ): Promise<string[]> {
    const listColumnsQuery = `SHOW COLUMNS IN TABLE ${v5Table}`;
    const rows = (await this.runQuery(
      snowflakeConnection,
      listColumnsQuery,
    )) as [{ column_name: string }];

    // filter only the metrics which doesn't have sub metrics, since submetrics is being excluded from the variations for now
    const spendMetricsArray = spendMetrics
      .filter(
        (spendMetric) => !this.doesSpendMetricIncludeSubMetric(spendMetric),
      )
      .map((spendMetric) => spendMetric.metric.toLowerCase());

    const spendColunsWithVariations = rows
      .filter((row) => {
        const columnName = row.column_name.toLowerCase();

        if (columnName.endsWith('_usd')) {
          // remove latest 4 characters, i.e, the "_usd"
          return spendMetricsArray.includes(columnName.slice(0, -4));
        }

        return spendMetricsArray.includes(columnName);
      })
      .map((row) => row.column_name);

    return spendColunsWithVariations;
  }

  private convertMetricV5SetClause(spendColumns: string[]) {
    const spendMetricsSetClause = spendColumns.map(
      (spendColumn) => `${spendColumn} = NULL`,
    );

    return spendMetricsSetClause.join(', ');
  }

  private convertMetricAndSubMetricForV4WhereClause(
    spendMetrics: METRIC_OBJECT_TYPE[],
  ): string {
    const stringSpendMetris = spendMetrics.map((spendMetric) => {
      if (this.doesSpendMetricIncludeSubMetric(spendMetric)) {
        const subMetricsQuoted = spendMetric.subMetrics.map(
          (subMetric) => `'${subMetric}'`,
        );

        // look only the metric with submetric specified
        return ` (METRIC = '${
          spendMetric.metric
        }' AND SUB_METRIC IN (${subMetricsQuoted.join(', ')})) `;
      }

      // look for the metric without caring for submetrics
      return ` (METRIC = '${spendMetric.metric}') `;
    });

    const spendMetricsString = stringSpendMetris.join('OR');

    const regexRateWhereClause =
      this.convertMetricForVariationRegexpV4WhereClause(spendMetrics);

    const usdWhereClause =
      this.convertMetricForVariationUsdWhereClause(spendMetrics);

    return `${spendMetricsString} OR ${regexRateWhereClause} OR ${usdWhereClause}`;
  }

  /**
   * Some (we will assume all) spend metrics have variations to generate the <metric>_usd format,
   * in order to achive the value for this new variation, a lot of <metric>_<currency>_rate metrics are generated,
   * so we will remove/zero out then.
   * @param spendMetrics
   */
  private convertMetricForVariationRegexpV4WhereClause(
    spendMetrics: METRIC_OBJECT_TYPE[],
  ) {
    // gather all metrics (not submetrics)
    const metrics = spendMetrics.map((spendMetric) => spendMetric.metric);

    // join with | so later we can combine to form a group on the regex
    const metricsJoined = metrics.join('|');

    const metricRegexRate = `'^(${metricsJoined})_([a-zA-Z]{{3}})_rate$'`;

    return `METRIC REGEXP ${metricRegexRate}`;
  }

  /**
   * Get all metrics which are in the following format <metric>_usd
   * @param spendMetrics
   * @returns
   */
  private convertMetricForVariationUsdWhereClause(
    spendMetrics: METRIC_OBJECT_TYPE[],
  ) {
    // metrics with '<metric>_usd'
    const quotedMetrics = spendMetrics.map(
      (spendMetric) => `'${spendMetric.metric}_usd'`,
    );

    const metricsJoined = quotedMetrics.join(',');

    return `METRIC IN (${metricsJoined})`;
  }

  private async runQuery(
    snowflakeConnection: SnowFlakeWrappedConnection,
    query: string,
  ) {
    return new Promise((resolve, reject) => {
      this.snowFlakeService
        .executeQuery(snowflakeConnection, query)
        .then((rows) => {
          resolve(rows);
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  private doesSpendMetricsIncludeSubMetric(
    spendMetrics: METRIC_OBJECT_TYPE[],
  ): boolean {
    return spendMetrics.some((spendMetric) =>
      this.doesSpendMetricIncludeSubMetric(spendMetric),
    );
  }

  private doesSpendMetricIncludeSubMetric(
    spendMetric: METRIC_OBJECT_TYPE,
  ): boolean {
    return spendMetric.subMetrics?.length > 0;
  }
}
