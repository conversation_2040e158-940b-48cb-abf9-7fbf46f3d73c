import { proxyActivities } from '@temporalio/workflow';
import { SpendDisabledActions } from './activities';
import { FeatureChangeJobStatus } from 'src/common/constants/constants';

const {
  getSpendDisabledFeatureChange,
  doesOrganizationHaveSpendDisabled,
  updateScheduledFeatureChange,
  getAllAccountIdsFromOrganization,
  doesJobIdIsTheMostRecentSpendDisabledJob,
  getOrganizationIdFromJobId,
  startSpendDeletionJob,
} = proxyActivities<SpendDisabledActions>({
  startToCloseTimeout: '2 minute',
  cancellationType: 'TRY_CANCEL',
});

const { logEventEmitter, errorEventEmitter } =
  proxyActivities<SpendDisabledActions>({
    startToCloseTimeout: '1 minute',
    cancellationType: 'TRY_CANCEL',
  });

const { deleteOrganizationSpend } = proxyActivities<SpendDisabledActions>({
  startToCloseTimeout: '10 minute',
  cancellationType: 'TRY_CANCEL',
});

const { waitJobToComplete } = proxyActivities<SpendDisabledActions>({
  startToCloseTimeout: '30 minute',
  heartbeatTimeout: '2 minute',
});

export async function SpendDisabledWorkflow(input: { jobId: string }) {
  const { jobId } = input;
  await logEventEmitter(jobId, 'Starting Spend Disabled Request job....');

  const featureChange = await getSpendDisabledFeatureChange(jobId);

  if (!featureChange) {
    await logEventEmitter(
      jobId,
      'Returning... job not found or not a spend disabled job',
    );

    return;
  }

  if (featureChange.jobStatus !== FeatureChangeJobStatus.PROCESSING) {
    await logEventEmitter(jobId, 'Returning... job not in processing status');

    return;
  }

  // TODO: update entity to have a type
  if (featureChange.parameters?.['preserveHistoricData']) {
    await updateScheduledFeatureChange(jobId, FeatureChangeJobStatus.COMPLETED);
    await logEventEmitter(
      jobId,
      'Job completed - no spend data deleted, since it has preserve historic data',
    );

    return;
  }

  const spendDisabled = await doesOrganizationHaveSpendDisabled(jobId);

  if (!spendDisabled) {
    await updateScheduledFeatureChange(jobId, FeatureChangeJobStatus.CANCELLED);
    await logEventEmitter(jobId, 'Cancelling job, since org has spend enabled');

    return;
  }

  /**
   * check if for this org there is any feature change happening and keep only the new one:
   * - to avoid cases where people disable - enable - disable
   */
  const jobIsTheMostRecentOne = await doesJobIdIsTheMostRecentSpendDisabledJob(
    jobId,
  );

  if (!jobIsTheMostRecentOne) {
    await updateScheduledFeatureChange(jobId, FeatureChangeJobStatus.CANCELLED);
    await logEventEmitter(
      jobId,
      'Cancelling job, since there are other recents spend disabled jobs scheduled or running',
    );

    return;
  }

  const organizationId = await getOrganizationIdFromJobId(jobId);

  const adAccountIds = await getAllAccountIdsFromOrganization(organizationId);

  await logEventEmitter(jobId, 'Starting deleting Snowflake spend data');

  await deleteOrganizationSpend(adAccountIds);

  await logEventEmitter(jobId, 'Starting deleting Databricks spend data');

  const runId = await startSpendDeletionJob(organizationId);

  await waitJobToComplete(runId);

  await updateScheduledFeatureChange(jobId, FeatureChangeJobStatus.COMPLETED);
}
