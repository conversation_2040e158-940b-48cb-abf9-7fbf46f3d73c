import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  createSnowflakeProvider,
  SecretsManagerModule,
  TemporalWorkerModule,
} from '@vidmob/vidmob-nestjs-common';
import { SPEND_DISABLED_TEMPORAL_TASK_QUEUE } from './spend-disabled.constants';
import {
  CONFIG_SNOWFLAKE_RW_PROVIDER_NAME,
  CONFIG_TEMPORAL_SPEND_DISABLED_NAMESPACE,
  CONFIG_TEMPORAL_WORKER_TIMER_MILISECONDS,
  DEFAULT_SNOWFLAKE_CONNECTION_POOL_OPTIONS,
} from 'src/common/constants/configuration.constants';
import { LoggingActivities } from './activities/logging.activities';
import { FeatureChangesActivities } from './activities/feature-changes.activities';
import { Organization } from 'src/organizations/entities/organization.entity';
import { ScheduledFeatureChanges } from '../entities/scheduled-feature-changes.entity';
import { AdAccountsModule } from 'src/ad-accounts/ad-accounts.module';
import { SpendDisabledSnowflakeActivities } from './activities/spend-disabled-snowflake.activities';
import { OrganizationAdAccountActivities } from './activities/organization-ad-account.activities';
import { SpendDisabledSnowflakeServiceService } from './snowflake/spend-disabled-snowflake-service/spend-disabled-snowflake-service.service';
import { DatabricksActivities } from './activities/databricks-activities';
import { DatabricksApiModule } from 'src/databricks-api/databricks-api.module';

@Module({
  imports: [
    SecretsManagerModule,
    ConfigModule.forRoot(),
    TypeOrmModule.forFeature([Organization, ScheduledFeatureChanges]),
    TemporalWorkerModule.forRoot({
      taskQueue: SPEND_DISABLED_TEMPORAL_TASK_QUEUE,
      workflowsPath: require.resolve('./spend-disabled.workflow'),
      temporalNamespaceConfig: CONFIG_TEMPORAL_SPEND_DISABLED_NAMESPACE,
      workerTimerMilisecondsConfig: CONFIG_TEMPORAL_WORKER_TIMER_MILISECONDS,
    }),
    AdAccountsModule,
    DatabricksApiModule,
  ],
  providers: [
    LoggingActivities,
    FeatureChangesActivities,
    SpendDisabledSnowflakeActivities,
    OrganizationAdAccountActivities,
    DatabricksActivities,
    createSnowflakeProvider(
      CONFIG_SNOWFLAKE_RW_PROVIDER_NAME,
      DEFAULT_SNOWFLAKE_CONNECTION_POOL_OPTIONS,
    ),
    SpendDisabledSnowflakeServiceService,
  ],
})
export class SpendDisabledModule {}
