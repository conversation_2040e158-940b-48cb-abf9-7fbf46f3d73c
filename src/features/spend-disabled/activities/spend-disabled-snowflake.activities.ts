import { Injectable } from '@nestjs/common';
import { Activities, Activity } from '@vidmob/vidmob-nestjs-temporal';
import { SpendDeletionSnowflakeActions } from '../activities';
import { SpendDisabledSnowflakeServiceService } from '../snowflake/spend-disabled-snowflake-service/spend-disabled-snowflake-service.service';

@Injectable()
@Activities()
export class SpendDisabledSnowflakeActivities
  implements SpendDeletionSnowflakeActions
{
  constructor(
    private readonly spendDisabledSnowflakeServiceService: SpendDisabledSnowflakeServiceService,
  ) {}

  @Activity()
  async deleteOrganizationSpend(adAccountIds: string[]) {
    await this.spendDisabledSnowflakeServiceService.onDeleteSpendMetricsForOrganization(
      adAccountIds,
    );
  }
}
