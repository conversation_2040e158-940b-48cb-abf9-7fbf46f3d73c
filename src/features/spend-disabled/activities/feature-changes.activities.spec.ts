import { Test, TestingModule } from '@nestjs/testing';
import { FeatureChangesActivities } from './feature-changes.activities';
import { Repository } from 'typeorm';
import { Organization } from 'src/organizations/entities/organization.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ScheduledFeatureChanges } from 'src/features/entities/scheduled-feature-changes.entity';

describe('FeatureChangesActivities', () => {
  let service: FeatureChangesActivities;
  let scheduledFeatureChangesRepository: Repository<ScheduledFeatureChanges>;
  let organizationRepository: Repository<Organization>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FeatureChangesActivities,
        {
          provide: getRepositoryToken(ScheduledFeatureChanges),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Organization),
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<FeatureChangesActivities>(FeatureChangesActivities);
    scheduledFeatureChangesRepository = module.get(
      getRepositoryToken(ScheduledFeatureChanges),
    );
    organizationRepository = module.get(getRepositoryToken(Organization));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('doesOrganizationHaveSpendDisabled', () => {
    beforeEach(() => {
      jest
        .spyOn(scheduledFeatureChangesRepository, 'findOne')
        .mockResolvedValue({
          jobId: 'xxxxx',
        } as ScheduledFeatureChanges);
    });

    it('should return the org has spend disabled', async () => {
      jest.spyOn(organizationRepository, 'findOne').mockResolvedValue(null);

      await expect(
        service.doesOrganizationHaveSpendDisabled('xxxx'),
      ).resolves.toBeTruthy();
    });

    it('should return the org has spend disabled', async () => {
      jest
        .spyOn(organizationRepository, 'findOne')
        .mockResolvedValue(new Organization());

      await expect(
        service.doesOrganizationHaveSpendDisabled('xxxx'),
      ).resolves.toBeFalsy();
    });
  });
});
