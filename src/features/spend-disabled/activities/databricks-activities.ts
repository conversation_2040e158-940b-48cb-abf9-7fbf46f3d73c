import { Injectable } from '@nestjs/common';
import { Activities, Activity } from '@vidmob/vidmob-nestjs-temporal';
import { DatabricksApiService } from 'src/databricks-api/databricks-api.service';
import { DatabricksActions } from '../activities';
import { DatabricksState } from 'src/databricks-api/databricks-api.types';
import { heartbeat, sleep } from '@temporalio/activity';
import { ConfigService } from '@nestjs/config';
import { CONFIG_SPEND_DISABLED_DATABRICK_JOB_ID } from 'src/common/constants/configuration.constants';

@Injectable()
@Activities()
export class DatabricksActivities implements DatabricksActions {
  private JOB_ID: number;

  constructor(
    private readonly databricksApiService: DatabricksApiService,
    private readonly configService: ConfigService,
  ) {
    this.JOB_ID = this.configService.get<number>(
      CONFIG_SPEND_DISABLED_DATABRICK_JOB_ID,
    );
  }

  @Activity()
  async startSpendDeletionJob(organizationId: string): Promise<number> {
    const response = await this.databricksApiService.postJobRunNow({
      job_id: this.JOB_ID,
      job_parameters: {
        organization_id: organizationId,
      },
    });

    return response.run_id;
  }

  @Activity()
  async waitJobToComplete(runId: number): Promise<void> {
    let state: DatabricksState;

    do {
      const response = await this.databricksApiService.getJob(runId);
      state = response.status.state;

      await sleep('1Min');

      heartbeat();
    } while (state !== DatabricksState.TERMINATED);
  }
}
