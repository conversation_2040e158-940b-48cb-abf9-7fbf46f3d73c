import { Injectable } from '@nestjs/common';
import { FeatureChangesActions } from '../activities';
import { Activities, Activity } from '@vidmob/vidmob-nestjs-temporal';
import { InjectRepository } from '@nestjs/typeorm';
import { In, MoreThan, Not, Repository } from 'typeorm';
import { Organization } from 'src/organizations/entities/organization.entity';
import { FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED } from 'src/organizations/utils/constants';
import { ScheduledFeatureChanges } from 'src/features/entities/scheduled-feature-changes.entity';
import {
  FeatureChangeJobStatus,
  FeatureState,
} from 'src/common/constants/constants';

@Injectable()
@Activities()
export class FeatureChangesActivities implements FeatureChangesActions {
  constructor(
    @InjectRepository(ScheduledFeatureChanges)
    private scheduledFeatureChangesRepository: Repository<ScheduledFeatureChanges>,
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
  ) {}

  @Activity()
  async getSpendDisabledFeatureChange(jobId: string) {
    return await this.scheduledFeatureChangesRepository.findOne({
      where: {
        jobId,
        featureState: FeatureState.DISABLED,
        feature: {
          identifier: FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED,
        },
      },
      relations: {
        feature: true,
      },
    });
  }

  @Activity()
  async doesOrganizationHaveSpendDisabled(jobId: string) {
    const featureChange = await this.getSpendDisabledFeatureChange(jobId);

    const organization = await this.organizationRepository.findOne({
      where: {
        id: featureChange.organizationId,
        organizationFeatureWhitelists: {
          featureWhitelists: {
            identifier: FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED,
          },
        },
      },
      relations: {
        organizationFeatureWhitelists: {
          featureWhitelists: true,
        },
      },
    });

    // if no organization has returned, it means the org doesn't have the feature enabled
    if (!organization) {
      return true;
    }

    return false;
  }

  @Activity()
  /**
   * Check if there is a spend disabled job more recent than this one
   */
  async doesJobIdIsTheMostRecentSpendDisabledJob(
    jobId: string,
  ): Promise<boolean> {
    const featureChange = await this.getSpendDisabledFeatureChange(jobId);

    const mostRecentFeatureChange =
      await this.scheduledFeatureChangesRepository.findOne({
        where: {
          // anything that is not this one
          jobId: Not(featureChange.jobId),
          organizationId: featureChange.organizationId,
          jobStatus: In([
            FeatureChangeJobStatus.PROCESSING,
            FeatureChangeJobStatus.SCHEDULED,
          ]),
          featureId: featureChange.featureId,
          featureState: FeatureState.DISABLED,
          dateCreated: MoreThan(featureChange.dateCreated),
        },
      });

    // if there isn't a feature change more recent than the current one, return true
    // so temporal can continue the job
    if (!mostRecentFeatureChange) {
      return true;
    }

    return false;
  }

  @Activity()
  async getOrganizationIdFromJobId(jobId: string): Promise<string> {
    const featureChange = await this.getSpendDisabledFeatureChange(jobId);

    return featureChange.organizationId;
  }

  @Activity()
  async updateScheduledFeatureChange(
    id: string,
    jobStatus: FeatureChangeJobStatus,
  ) {
    await this.scheduledFeatureChangesRepository.update(
      { jobId: id },
      {
        jobStatus,
        lastUpdated: new Date(),
      },
    );
  }
}
