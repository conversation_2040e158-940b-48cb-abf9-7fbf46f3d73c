import { Injectable } from '@nestjs/common';
import { Activities, Activity } from '@vidmob/vidmob-nestjs-temporal';
import { OrganizationAdAccountActions } from '../activities';
import { OrganizationAdAccountsService } from 'src/ad-accounts/organization-ad-accounts.service';

@Injectable()
@Activities()
export class OrganizationAdAccountActivities
  implements OrganizationAdAccountActions
{
  constructor(
    private readonly organizationAdAccountsService: OrganizationAdAccountsService,
  ) {}

  @Activity()
  async getAllAccountIdsFromOrganization(
    organizationId: string,
  ): Promise<string[]> {
    const platformAdAccounts =
      await this.organizationAdAccountsService.getPlatformAdAccountFromAnOrganization(
        organizationId,
      );

    return platformAdAccounts.map(
      (platformAdAccount) => platformAdAccount.platformAccountId,
    );
  }
}
