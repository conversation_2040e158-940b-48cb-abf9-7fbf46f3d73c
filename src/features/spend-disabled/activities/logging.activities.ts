import { Injectable, Logger } from '@nestjs/common';
import { LoggingActions } from '../activities';
import { Activities, Activity } from '@vidmob/vidmob-nestjs-temporal';
import { TEMPORAL_WORKFLOW_FUNCTION } from 'src/common/constants/temporal.constants';

@Injectable()
@Activities()
export class LoggingActivities implements LoggingActions {
  private logger: Logger = new Logger(
    TEMPORAL_WORKFLOW_FUNCTION.SPEND_DISABLED,
  );

  @Activity()
  async errorEventEmitter(jobId: string, msg: string) {
    this.logger.error(`[Spend Disabled ${jobId}]: ${msg}`);
  }

  @Activity()
  async logEventEmitter(jobId: string, msg: string) {
    this.logger.log(`[Spend Disabled ${jobId}]: ${msg}`);
  }
}
