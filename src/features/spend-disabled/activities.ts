import { FeatureChangeJobStatus } from 'src/common/constants/constants';
import { ScheduledFeatureChanges } from '../entities/scheduled-feature-changes.entity';

export interface LoggingActions {
  logEventEmitter: (jobId: string, msg: string) => Promise<void>;
  errorEventEmitter: (jobId: string, msg: string) => Promise<void>;
}

export interface FeatureChangesActions {
  getSpendDisabledFeatureChange: (
    jobId: string,
  ) => Promise<ScheduledFeatureChanges>;

  doesOrganizationHaveSpendDisabled: (jobId: string) => Promise<boolean>;

  updateScheduledFeatureChange: (
    id: string,
    status: FeatureChangeJobStatus,
  ) => Promise<void>;

  doesJobIdIsTheMostRecentSpendDisabledJob: (jobId: string) => Promise<boolean>;

  getOrganizationIdFromJobId: (jobId: string) => Promise<string>;
}

export interface OrganizationAdAccountActions {
  getAllAccountIdsFromOrganization: (
    organizationId: string,
  ) => Promise<string[]>;
}

export interface SpendDeletionSnowflakeActions {
  deleteOrganizationSpend: (adAccountIds: string[]) => Promise<void>;
}

export interface DatabricksActions {
  startSpendDeletionJob: (organizationId: string) => Promise<number>;

  waitJobToComplete: (runId: number) => Promise<void>;
}

export interface SpendDisabledActions
  extends LoggingActions,
    FeatureChangesActions,
    OrganizationAdAccountActions,
    SpendDeletionSnowflakeActions,
    DatabricksActions {}
