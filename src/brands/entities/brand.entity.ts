import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToMany,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Workspace } from '../../workspaces/entities/workspace.entity';
import { Exclude } from 'class-transformer';
import { WorkspaceBrand } from '../../workspaces/entities/workspace-brand.entity';

@Entity()
export class Brand {
  /**
   * System assigned Id of the Brand
   */
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Name of the Organization
   */
  @AutoMap()
  @Column({ length: 255 })
  name: string;

  /**
   * Description of the Organization
   */
  @AutoMap()
  @Column({ length: 1024 })
  description: string;

  /**
   * Deleted
   */
  @AutoMap()
  @Exclude()
  @Column({ default: 0 })
  deleted: number;

  /**
   * updatedByPersonId
   */
  @AutoMap()
  @Exclude()
  @Column({ name: 'updated_by_person_id', type: 'bigint', nullable: false })
  updatedByPersonId: number;

  /**
   * ID of the Organization
   */
  @AutoMap()
  @Exclude()
  @Column({
    name: 'organization_id',
    type: 'char',
    length: 36,
    nullable: false,
  })
  organizationId: string;

  /**
   * Creation date of the Org. This is of the format YYYY-MM-DD
   */
  @AutoMap()
  @Exclude()
  @CreateDateColumn({ name: 'date_created' })
  dateCreated: Date;

  /**
   * Updated date of the Org
   */
  @AutoMap()
  @Exclude()
  @UpdateDateColumn({ name: 'last_updated' })
  lastUpdated: Date;

  /**
   * The relationship between the brand and the workspace
   */
  @AutoMap()
  @ManyToMany(() => Workspace, (workspace) => workspace.brands)
  workspaces: Workspace[];

  @OneToMany(() => WorkspaceBrand, (workspaceBrand) => workspaceBrand.brand)
  workspaceBrands: WorkspaceBrand[];
}
