import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectMapper } from '@automapper/nestjs';
import { Brand } from './entities/brand.entity';
import { In, Repository } from 'typeorm';
import { Mapper } from '@automapper/core';
import { PlatformAdAccountBrandMap } from 'src/ad-accounts/entities/platform-ad-account-brand-map.entity';
import { PlatformAdAccount } from 'src/ad-accounts/entities/ad-account.entity';

@Injectable()
export class BrandService {
  constructor(
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  /**
   * Retieves a list of brands by ids scoped to an organization
   */
  async findBrandsByIdsAndOrganization(
    brandIds: string[],
    organizationId,
  ): Promise<Brand[]> {
    if (!brandIds?.length) return [];

    return this.brandRepository.find({
      where: {
        id: In(brandIds),
        organizationId: organizationId,
        deleted: 0,
      },
    });
  }

  async isAllBrandsAssociatedWithOrganizationId(
    brandIds: string[],
    organizationId: string,
  ) {
    const brandsAssociatedToOrganization = await this.brandRepository.count({
      where: {
        id: In([...brandIds]),
        organizationId,
      },
    });

    return brandsAssociatedToOrganization === brandIds.length;
  }

  async findBrandsByPlatformAccountId(
    platformAccountId: string,
  ): Promise<Brand[]> {
    return await this.brandRepository
      .createQueryBuilder('brand')
      .select('brand.id')
      .addSelect('brand.name')
      .innerJoin(
        PlatformAdAccountBrandMap,
        'platformAdAccountBrandMap',
        'platformAdAccountBrandMap.brand_id = brand.id',
      )
      .innerJoin(
        PlatformAdAccount,
        'platformAdAccount',
        'platformAdAccount.id = platformAdAccountBrandMap.platform_ad_account_id',
      )
      .where('platformAdAccount.platform_account_id = :platformAccountId', {
        platformAccountId,
      })
      .getMany();
  }

  async getFilteredBrands(
    organizationId: string,
    adAccountIds: string[],
  ): Promise<Brand[]> {
    return this.brandRepository
      .createQueryBuilder('brand')
      .select(['brand.id', 'brand.name'])
      .innerJoin(
        PlatformAdAccountBrandMap,
        'platformAdAccountBrandMap',
        'platformAdAccountBrandMap.brandId = brand.id',
      )
      .innerJoin(
        PlatformAdAccount,
        'platformAdAccount',
        'platformAdAccount.id = platformAdAccountBrandMap.platformAdAccountId',
      )
      .where('brand.organizationId = :organizationId', { organizationId })
      .andWhere('platformAdAccount.platformAccountId IN (:...adAccountIds)', {
        adAccountIds,
      })
      .getMany();
  }
}
