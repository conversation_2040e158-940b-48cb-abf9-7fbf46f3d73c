import { ClassSerializerInterceptor, Module } from '@nestjs/common';
import { OrganizationsModule } from './organizations/organizations.module';
import { WorkspacesModule } from './workspaces/workspaces.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_INTERCEPTOR } from '@nestjs/core';
import {
  configuration,
  databaseProvider,
  HealthModule,
  VidmobCommonModule,
  RookoutModule,
} from '@vidmob/vidmob-nestjs-common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';

import {
  DEFAULT_RESPONSE_DESCRIPTION,
  DEFAULT_RESPONSE_ERROR_TYPE,
  ORGANIZATION_API_TAG_NAME,
} from './common/constants/api.constants';
import { VidmobResponseInterceptorFactory } from '@vidmob/vidmob-nestjs-common';
import { AdAccountsModule } from './ad-accounts/ad-accounts.module';
import { AdAccountScopeFilterModule } from './ad-account-scope-filter/ad-account-scope-filter.module';
import { MediaModule } from './media/media.module';
import { BrandModule } from './brands/brand.module';
import { MarketModule } from './markets/market.module';
import { WorkspaceAdAccountModule } from './workspaces/workspace-ad-account/workspace-ad-account.module';
import { OrganizationInviteModule } from './organization-invite/organization-invite.module';
import { WorkspaceUserModule } from './workspace-user/workspace-user.module';

import {
  ApiModule as NotificationApiModule,
  Configuration as NotificationConfiguration,
} from '@vidmob/vidmob-soa-notification-service-sdk';
import { DataExportModule } from './data-export/data-export.module';
import { DatabricksApiModule } from './databricks-api/databricks-api.module';
import { FeaturesModule } from './features/features.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configuration],
      isGlobal: true,
    }),
    AutomapperModule.forRoot({ strategyInitializer: classes() }),
    VidmobCommonModule,
    HealthModule,
    TypeOrmModule.forRootAsync(databaseProvider()),
    OrganizationsModule,
    WorkspacesModule,
    WorkspaceAdAccountModule,
    AdAccountsModule,
    AdAccountScopeFilterModule,
    MediaModule,
    MarketModule,
    BrandModule,
    RookoutModule,
    OrganizationInviteModule,
    WorkspaceUserModule,
    DataExportModule,
    DatabricksApiModule,
    FeaturesModule,
    NotificationApiModule.forRoot(
      (configService: ConfigService) => {
        const basePath = configService.get<string>('notificationServiceUrl');
        return new NotificationConfiguration({ basePath });
      },
      [ConfigService],
    ),
  ],
  controllers: [],
  providers: [
    { provide: APP_INTERCEPTOR, useClass: ClassSerializerInterceptor },
    {
      provide: APP_INTERCEPTOR,
      useValue: new VidmobResponseInterceptorFactory().create({
        errorConfiguration: {
          identifierPrefix: ORGANIZATION_API_TAG_NAME,
          serviceSystem: ORGANIZATION_API_TAG_NAME,
          defaultErrorMessage: DEFAULT_RESPONSE_DESCRIPTION,
          defaultErrorType: DEFAULT_RESPONSE_ERROR_TYPE,
        },
      }),
    },
  ],
})
export class AppModule {}
