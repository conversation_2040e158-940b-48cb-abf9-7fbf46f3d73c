import { DataExportService } from './data-export.service';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  AnalyticsDataExportQuery,
  ReportType,
  State,
} from './entities/analytics-data-export-query.entity';
import { Repository } from 'typeorm';
import {
  AnalyticsDataExportReport,
  Status,
} from './entities/analytics-data-export-report.entity';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper, Mapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { createMockAnalyticsDataExportReport } from './data/create-mock.data';
import { DataExportProfile } from './profile/data-export.profile';
import { DataExportUpdateService } from './data-export-update/data-export-update.service';
import { S3Service } from '@vidmob/vidmob-nestjs-common';

describe('DataExportService', () => {
  let service: DataExportService;
  let reportRepo: Repository<AnalyticsDataExportReport>;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: DataExportUpdateService,
          useValue: {
            queueDatabricksJobStart: jest
              .fn()
              .mockReturnValue(Promise.resolve(true)),
          },
        },
        {
          provide: S3Service,
          useValue: {
            getSignedUrl: jest
              .fn()
              .mockReturnValue(Promise.resolve('https://...')),
          },
        },
        DataExportService,
        DataExportProfile,
        {
          provide: getRepositoryToken(AnalyticsDataExportQuery),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(AnalyticsDataExportReport),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();
    module.get<Mapper>(getMapperToken());
    service = module.get<DataExportService>(DataExportService);
    reportRepo = module.get<Repository<AnalyticsDataExportReport>>(
      getRepositoryToken(AnalyticsDataExportReport),
    );
  });
  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  describe('getFilterDataByOrganization', () => {
    const MOCK_REPORTS: AnalyticsDataExportReport[] = [
      createMockAnalyticsDataExportReport(
        Status.QUEUED,
        State.ACTIVE,
        ReportType.CREATIVE_SCORING,
      ),
      createMockAnalyticsDataExportReport(
        Status.PROCESSING,
        State.ACTIVE,
        ReportType.CREATIVE_SCORING,
      ),
      createMockAnalyticsDataExportReport(
        Status.COMPLETED,
        State.ACTIVE,
        ReportType.CREATIVE_SCORING,
        true,
        true,
        true,
      ),
      createMockAnalyticsDataExportReport(
        Status.FAILED,
        State.ACTIVE,
        ReportType.CREATIVE_SCORING,
        true,
      ),
    ];
  });
});
