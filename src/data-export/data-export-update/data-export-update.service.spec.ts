import { Test, TestingModule } from '@nestjs/testing';
import { DataExportUpdateService } from './data-export-update.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { classes } from '@automapper/classes';
import { createMapper } from '@automapper/core';
import { getMapperToken } from '@automapper/nestjs';
import { ConfigService } from '@nestjs/config';
import { DatabricksApiService } from '../../databricks-api/databricks-api.service';
import { Repository } from 'typeorm';
import { AnalyticsDataExportReport } from '../entities/analytics-data-export-report.entity';
import { SqsService } from '@vidmob/vidmob-nestjs-common';
import { OrganizationsService } from 'src/organizations/organizations.service';
import { FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED } from 'src/organizations/utils/constants';
import { ReadOrganizationDto } from 'src/organizations/dto/read-organization.dto';

describe('DataExportUpdateService', () => {
  let service: DataExportUpdateService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue({
              CREATIVE_SCORING: { enabled: true, jobId: 1 },
            }),
          },
        },
        {
          provide: DatabricksApiService,
          useValue: {
            postJobRunNow: jest.fn().mockReturnValue({
              run_id: 1,
            }),
          },
        },
        {
          provide: SqsService,
          useValue: {
            send: jest.fn().mockReturnValue(Promise.resolve(true)),
          },
        },
        {
          provide: OrganizationsService,
          useValue: {
            findOne: jest.fn().mockReturnValue(
              Promise.resolve({
                spendEnabled: true,
              } as ReadOrganizationDto),
            ),
          },
        },
        DataExportUpdateService,
        {
          provide: getRepositoryToken(AnalyticsDataExportReport),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    service = module.get<DataExportUpdateService>(DataExportUpdateService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
