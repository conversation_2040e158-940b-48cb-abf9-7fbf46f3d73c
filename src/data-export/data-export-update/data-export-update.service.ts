import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import {
  AnalyticsDataExportReport,
  ReportResults,
  Status,
} from '../entities/analytics-data-export-report.entity';
import {
  DataExportReportUpdate,
  DataExportTypesConfig,
} from '../data-export.type';
import { AnalyticsDataExportQuery } from '../entities/analytics-data-export-query.entity';
import {
  DatabricksJobRunNowRequest,
  DatabricksJobRunNowResponse,
} from '../../databricks-api/databricks-api.types';
import { ConfigService } from '@nestjs/config';
import { DatabricksApiService } from '../../databricks-api/databricks-api.service';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  SqsConsumerEventHandler,
  SqsMessageHandler,
  SqsService,
} from '@vidmob/vidmob-nestjs-common';
import { Message } from '@aws-sdk/client-sqs';
import { OrganizationsService } from 'src/organizations/organizations.service';
import { FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED } from 'src/organizations/utils/constants';

const SQS_QUEUE_NAME = 'dataExportUpdate';

@Injectable()
export class DataExportUpdateService {
  private logger: Logger = new Logger(DataExportUpdateService.name);

  private readonly typeConfig: DataExportTypesConfig;

  constructor(
    private readonly configService: ConfigService,
    private readonly databricksApiService: DatabricksApiService,
    private readonly sqsService: SqsService,
    private readonly organizationsService: OrganizationsService,
    @InjectRepository(AnalyticsDataExportReport)
    private dataExportReportRepository: Repository<AnalyticsDataExportReport>,
  ) {
    this.typeConfig = this.configService.get('dataExport.exportTypes');
  }

  async queueDatabricksJobStart(reportId: string) {
    this.logger.log(`Queuing Databricks Job Start for report ${reportId}`);
    const status = Status.QUEUED;
    const body = {
      reportId,
      status,
    };
    await this.sqsService.send(SQS_QUEUE_NAME, {
      id: `${reportId}-${status}`,
      body: JSON.stringify(body),
    });
  }

  @SqsMessageHandler(SQS_QUEUE_NAME, false)
  async receiveReportUpdateMessage(message: Message) {
    this.logger.log(`Received message: ${message.Body}`);
    const reportUpdate = JSON.parse(message.Body) as DataExportReportUpdate;
    await this.receiveReportUpdate(reportUpdate);
  }

  @SqsConsumerEventHandler(SQS_QUEUE_NAME, 'processing_error')
  public onProcessingError(error: Error, message: Message) {
    this.logger.error(
      'Processing error on message: ' + JSON.stringify(message.Body),
      error,
    );
    this.logger.error(error);
  }

  async receiveReportUpdate(
    reportUpdate: DataExportReportUpdate,
  ): Promise<void> {
    const { reportId, status } = reportUpdate;
    const report = await this.dataExportReportRepository.findOne({
      where: { reportId },
      relations: ['dataExportQuery'],
    });
    if (!report) {
      throw new BadRequestException(
        `Failed to find report with id ${reportId} for databricks job update`,
      );
    }
    if (status === Status.QUEUED) {
      await this.handleDatabricksJobUpdateQueued(report);
    } else if (status === Status.PROCESSING) {
      await this.handleDatabricksJobUpdateProcessing(report, reportUpdate);
    } else if (status === Status.COMPLETED) {
      await this.handleDatabricksJobUpdateCompleted(report, reportUpdate);
    } else if (status === Status.FAILED) {
      await this.handleDatabricksJobUpdateFailed(report, reportUpdate);
    } else {
      throw new BadRequestException(
        `Received an unexpected status ${status} for report ${reportId}`,
      );
    }
  }

  async handleDatabricksJobUpdateQueued(report: AnalyticsDataExportReport) {
    if (report.status !== Status.QUEUED) {
      throw new BadRequestException(
        `Received a QUEUED status for report ${report.reportId} but the report in unexpected status ${report.status}`,
      );
    }

    const jobStartResponse = await this.runDatabricksJobStart(
      report.dataExportQuery,
      report,
    );

    this.logger.log(
      `Databricks job ${jobStartResponse.run_id} started for report ${report.reportId}`,
    );

    report.status = Status.STARTED;

    this.updateReportResults(report, { jobRunId: jobStartResponse.run_id });

    await this.dataExportReportRepository.save(report);
  }

  async handleDatabricksJobUpdateProcessing(
    report: AnalyticsDataExportReport,
    reportUpdate: DataExportReportUpdate,
  ) {
    if (
      report.status !== Status.STARTED &&
      report.status !== Status.PROCESSING
    ) {
      throw new BadRequestException(
        `Received a ${reportUpdate.status} status for report ${report.reportId} but the report in unexpected status ${report.status}`,
      );
    }

    report.status = reportUpdate.status;

    // no report results to update yet, so just update status

    await this.dataExportReportRepository.save(report);
  }

  async handleDatabricksJobUpdateCompleted(
    report: AnalyticsDataExportReport,
    reportUpdate: DataExportReportUpdate,
  ) {
    if (
      report.status !== Status.STARTED &&
      report.status !== Status.PROCESSING
    ) {
      throw new BadRequestException(
        `Received a COMPLETED status for report ${report.reportId} but the report in unexpected status ${report.status}`,
      );
    }

    report.status = Status.COMPLETED;

    const reportResultsUpdate: ReportResults = {};

    if (
      reportUpdate.reportResults?.s3Bucket &&
      reportUpdate.reportResults?.s3Key
    ) {
      reportResultsUpdate.s3Bucket = reportUpdate.reportResults.s3Bucket;
      reportResultsUpdate.s3Key = reportUpdate.reportResults.s3Key;
    }

    // check null and undefined because 0 is a valid value
    if (
      reportUpdate.reportResults?.records !== undefined &&
      reportUpdate.reportResults?.records !== null
    ) {
      reportResultsUpdate.records = reportUpdate.reportResults.records;
    }

    this.updateReportResults(report, reportResultsUpdate);

    await this.dataExportReportRepository.save(report);
  }

  updateReportResults(
    report: AnalyticsDataExportReport,
    reportResultsUpdate: ReportResults,
  ) {
    const updateEntries = Object.entries(reportResultsUpdate);
    if (updateEntries.length > 0) {
      report.reportResults = report.reportResults || {};

      updateEntries.forEach(([key, value]) => {
        report.reportResults[key] = value;
      });
    }
  }

  async handleDatabricksJobUpdateFailed(
    report: AnalyticsDataExportReport,
    reportUpdate: DataExportReportUpdate,
  ) {
    report.status = Status.FAILED;

    if (reportUpdate.reportResults?.failureReason) {
      this.updateReportResults(report, {
        failureReason: reportUpdate.reportResults?.failureReason,
      });
    }

    await this.dataExportReportRepository.save(report);
  }

  async runDatabricksJobStart(
    dataExportQueryEntity: AnalyticsDataExportQuery,
    dataExportEntity: AnalyticsDataExportReport,
  ): Promise<DatabricksJobRunNowResponse> {
    this.validateReportType(dataExportQueryEntity.reportType);

    const dataExportTypeConfig =
      this.typeConfig[dataExportQueryEntity.reportType];

    const reportId = dataExportEntity.reportId;
    const reportParams = JSON.stringify(dataExportQueryEntity.parameters);

    const organization = await this.organizationsService.findOne(
      dataExportQueryEntity.organizationId,
    );

    const jobParams = {
      organization_id: dataExportQueryEntity.organizationId,
      report_name: dataExportQueryEntity.reportQueryName,
      report_id: reportId,
      report_parameters: reportParams,
      spend_enabled: organization.spendEnabled.toString(),
    };
    const reportRequest: DatabricksJobRunNowRequest = {
      job_id: dataExportTypeConfig.jobId,
      job_parameters: jobParams,
    };
    return this.databricksApiService.postJobRunNow(reportRequest);
  }

  private validateReportType(reportType: string) {
    if (
      !(reportType in this.typeConfig) ||
      !this.typeConfig[reportType].enabled
    ) {
      throw new BadRequestException(
        `Invalid report type - ${reportType}. Please provide a valid report type.`,
      );
    }
  }
}
