import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreateDataExportRequestDto } from './dto/create-data-export-request.dto';
import {
  AnalyticsDataExportQuery,
  ReportType,
  State,
} from './entities/analytics-data-export-query.entity';
import { EntityManager, FindOneOptions, Not, Raw, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { CreateDataExportResponseDto } from './dto/create-data-export-response.dto';
import {
  AnalyticsDataExportReport,
  Status,
} from './entities/analytics-data-export-report.entity';
import { isDateFormatValid, isFilenameValid } from '../common/utils/helper';
import {
  IdAndName,
  ReadDataExportReportDto,
  ReadDataExportReportSQLDto,
} from './dto/read-data-export-report.dto';
import { PaginationOptions, S3Service } from '@vidmob/vidmob-nestjs-common';
import { FilterSortByOptions } from './dto/read-data-export-filter-query.dto';
import { DataExportUpdateService } from './data-export-update/data-export-update.service';
import {
  buildOrderByStatement,
  buildWhereStatement,
  MAX_ACTIVE_DAYS,
  NewToOldChannelRecord,
  onlyUniqueFilterValues,
  ReportSelectRelations,
} from './data-export.common';
import { DataExportFilterValue } from './dto/data-export-filter-values.dto';
import { DataExportFilterRequestDto } from './dto/data-export-filter-request.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { PlatformAdAccount } from 'src/ad-accounts/entities/ad-account.entity';
import { AdAccountAnalyticsDataExportQuery } from './entities/ad-account-analytics-data-export-query.entity';
import { BrandAnalyticsDataExportQuery } from './entities/brand-analytics-data-export-query.entity';
import { Brand } from 'src/brands/entities/brand.entity';
import { Market } from 'src/markets/entities/market.entity';
import { MarketAnalyticsDataExportQuery } from './entities/market-analytics-data-export-query.entity';
import { Workspace } from 'src/workspaces/entities/workspace.entity';
import { WorkspaceAnalyticsDataExportQuery } from './entities/workspace-analytics-data-export-query.entity';
import { FindOptionsWhere } from 'typeorm/find-options/FindOptionsWhere';

@Injectable()
export class DataExportService {
  private readonly logger = new Logger(DataExportService.name);

  constructor(
    private readonly dataExportUpdateService: DataExportUpdateService,
    private readonly s3Service: S3Service,
    @InjectRepository(AnalyticsDataExportQuery)
    private dataExportQueryRepository: Repository<AnalyticsDataExportQuery>,
    @InjectRepository(AnalyticsDataExportReport)
    private dataExportReportRepository: Repository<AnalyticsDataExportReport>,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  private async createAndGetDataExportQuery(
    organizationId: string,
    userId: number,
    createDataExportRequestDto: CreateDataExportRequestDto,
    manager: EntityManager,
  ): Promise<AnalyticsDataExportQuery> {
    const dataExportEntity = this.classMapper.map(
      createDataExportRequestDto,
      CreateDataExportRequestDto,
      AnalyticsDataExportQuery,
    );
    dataExportEntity.organizationId = organizationId;
    dataExportEntity.state = State.ACTIVE;
    dataExportEntity.personId = userId;
    return await manager.save(dataExportEntity);
  }

  private async createAndGetDataExportReport(
    name: string,
    dataExportQuery: AnalyticsDataExportQuery,
    manager: EntityManager,
  ): Promise<AnalyticsDataExportReport> {
    const dataExportReportEntity = this.classMapper.map(
      dataExportQuery,
      AnalyticsDataExportQuery,
      AnalyticsDataExportReport,
    );
    dataExportReportEntity.reportName = name;
    return await manager.save(dataExportReportEntity);
  }

  private async updateDataExportReportFailure(
    dataExportReport: AnalyticsDataExportReport,
    failureReason: string,
  ) {
    dataExportReport.status = Status.FAILED;
    dataExportReport.reportResults = dataExportReport.reportResults || {};
    dataExportReport.reportResults.failureReason = failureReason;
    return await this.dataExportReportRepository.save(dataExportReport);
  }

  async createDataExport(
    organizationId: string,
    userId: number,
    createDataExportRequestDto: CreateDataExportRequestDto,
  ) {
    if (!isDateFormatValid(createDataExportRequestDto.startDate)) {
      throw new BadRequestException(
        `Invalid start date - ${createDataExportRequestDto.startDate}. Please provide a valid date format 'YYYY-MM-DD'.`,
      );
    }
    if (!isDateFormatValid(createDataExportRequestDto.endDate)) {
      throw new BadRequestException(
        `Invalid end date - ${createDataExportRequestDto.endDate}. Please provide a valid date format 'YYYY-MM-DD'. `,
      );
    }
    if (!isFilenameValid(createDataExportRequestDto.reportName)) {
      throw new BadRequestException(
        `Invalid report name - ${createDataExportRequestDto.reportName}. Please provide a valid report name with characters, digits, and valid punctuation (-_:.,' ).`,
      );
    }
    try {
      const dataExportReport =
        await this.dataExportQueryRepository.manager.transaction(
          (entityManager) => {
            return this.createDataExportEntities(
              organizationId,
              userId,
              createDataExportRequestDto,
              entityManager,
            );
          },
        );
      await this.dataExportUpdateService.queueDatabricksJobStart(
        dataExportReport.reportId,
      );
      return this.classMapper.map(
        dataExportReport,
        AnalyticsDataExportReport,
        CreateDataExportResponseDto,
      );
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  async createDataExportEntities(
    organizationId: string,
    userId: number,
    createDataExportRequestDto: CreateDataExportRequestDto,
    manager: EntityManager,
  ) {
    const dataExportQuery = await this.createAndGetDataExportQuery(
      organizationId,
      userId,
      createDataExportRequestDto,
      manager,
    );
    const dataExportReport = await this.createAndGetDataExportReport(
      createDataExportRequestDto.reportName,
      dataExportQuery,
      manager,
    );

    if (createDataExportRequestDto.adAccounts?.length > 0) {
      await this.createDataExportAdAccountMapEntities(
        dataExportQuery,
        createDataExportRequestDto.adAccounts,
        manager,
      );
    }

    if (createDataExportRequestDto.brands?.length > 0) {
      await this.createDataExportBrandMapEntities(
        dataExportQuery,
        createDataExportRequestDto.brands,
        manager,
      );
    }

    if (createDataExportRequestDto.markets?.length > 0) {
      await this.createDataExportMarketMapEntities(
        dataExportQuery,
        createDataExportRequestDto.markets,
        manager,
      );
    }

    if (createDataExportRequestDto.workspaces?.length > 0) {
      await this.createDataExportWorkspaceMapEntities(
        dataExportQuery,
        createDataExportRequestDto.workspaces,
        manager,
      );
    }

    return dataExportReport;
  }

  async createDataExportAdAccountMapEntities(
    dataExportQuery: AnalyticsDataExportQuery,
    adAccounts: string[],
    manager: EntityManager,
  ) {
    const adAccountCreates = adAccounts.map((adAccount) => {
      return this.createDataExportAdAccountMapEntity(
        dataExportQuery,
        adAccount,
        manager,
      );
    });
    await Promise.all(adAccountCreates);
  }

  async createDataExportAdAccountMapEntity(
    dataExportQuery: AnalyticsDataExportQuery,
    adAccount: string,
    manager: EntityManager,
  ) {
    const options = {
      relations: {
        organizationPlatformAdAccountMap: true,
      },
      where: {
        platformAccountId: adAccount,
        canAccess: 1,
        organizationPlatformAdAccountMap: {
          organizationId: dataExportQuery.organizationId,
        },
      },
    };
    const platformAdAccount = await manager.findOne(PlatformAdAccount, options);
    if (!platformAdAccount) {
      throw new NotFoundException(
        `Ad Account with platform account id ${adAccount} not found`,
      );
    }
    const accountMapEntity = new AdAccountAnalyticsDataExportQuery();
    accountMapEntity.reportQueryId = dataExportQuery.reportQueryId;
    accountMapEntity.platformAdAccountId = platformAdAccount.id;
    return await manager.save(accountMapEntity);
  }

  async createDataExportBrandMapEntities(
    dataExportQuery: AnalyticsDataExportQuery,
    brands: string[],
    manager: EntityManager,
  ) {
    const brandCreates = brands.map((brand) => {
      return this.createDataExportBrandMapEntity(
        dataExportQuery,
        brand,
        manager,
      );
    });
    await Promise.all(brandCreates);
  }

  async createDataExportBrandMapEntity(
    dataExportQuery: AnalyticsDataExportQuery,
    brandId: string,
    manager: EntityManager,
  ) {
    const options = {
      where: {
        id: brandId,
        organizationId: dataExportQuery.organizationId,
      },
    };
    const brand = await manager.findOne(Brand, options);
    if (!brand) {
      throw new NotFoundException(`Brand with id ${brandId} not found`);
    }
    const brandMapEntity = new BrandAnalyticsDataExportQuery();
    brandMapEntity.reportQueryId = dataExportQuery.reportQueryId;
    brandMapEntity.brandId = brandId;
    return await manager.save(brandMapEntity);
  }

  async createDataExportMarketMapEntities(
    dataExportQuery: AnalyticsDataExportQuery,
    markets: string[],
    manager: EntityManager,
  ) {
    const marketCreates = markets.map((market) => {
      return this.createDataExportMarketMapEntity(
        dataExportQuery,
        market,
        manager,
      );
    });
    await Promise.all(marketCreates);
  }

  async createDataExportMarketMapEntity(
    dataExportQuery: AnalyticsDataExportQuery,
    market: string,
    manager: EntityManager,
  ) {
    const options = {
      where: {
        isoCode: market,
      },
    };
    const marketEntity = await manager.findOne(Market, options);
    if (!marketEntity) {
      throw new NotFoundException(`Market with id ${market} not found`);
    }
    const marketMapEntity = new MarketAnalyticsDataExportQuery();
    marketMapEntity.reportQueryId = dataExportQuery.reportQueryId;
    marketMapEntity.isoCode = market;
    return await manager.save(marketMapEntity);
  }

  async createDataExportWorkspaceMapEntities(
    dataExportQuery: AnalyticsDataExportQuery,
    workspaces: number[],
    manager: EntityManager,
  ) {
    const workspaceCreates = workspaces.map((workspace) => {
      return this.createDataExportWorkspaceMapEntity(
        dataExportQuery,
        workspace,
        manager,
      );
    });
    await Promise.all(workspaceCreates);
  }

  async createDataExportWorkspaceMapEntity(
    dataExportQuery: AnalyticsDataExportQuery,
    workspaceId: number,
    manager: EntityManager,
  ) {
    const options = {
      where: {
        id: workspaceId,
        organizationId: dataExportQuery.organizationId,
      },
    };
    const workspaceEntity = await manager.findOne(Workspace, options);
    if (!workspaceEntity) {
      throw new NotFoundException(`Workspace with id ${workspaceId} not found`);
    }
    const workspaceMapEntity = new WorkspaceAnalyticsDataExportQuery();
    workspaceMapEntity.reportQueryId = dataExportQuery.reportQueryId;
    workspaceMapEntity.workspaceId = workspaceId;
    return await manager.save(workspaceMapEntity);
  }

  /**
   * Creates a key value object containing all information for
   * an organization to filter on for data export reports.
   *
   * @param organizationId
   * @param filters
   */
  async getFilterValues(
    organizationId: string,
    filters?: DataExportFilterRequestDto,
  ): Promise<DataExportFilterValue> {
    const result = await this.runFilterValueSQLQuery(organizationId, filters);
    if (!result) {
      return {} as DataExportFilterValue;
    }
    return result;
  }

  /**
   * Get all the Data Export Report Metadata associated to an organization.
   * The service fetches all reports corresponding to the organization,
   * groups them by there status, and transforms them to a list of
   * ReadDataExportReportDto
   *
   * @param organizationId
   * @param paginationOptions
   */
  async getAllDataExportReportMetadata(
    organizationId: string,
    paginationOptions?: PaginationOptions,
  ) {
    const { total_count } = await this.getReportsCountFromRawSQL(
      organizationId,
    );
    const { reportIds } = await this.getReportIdsFromRawSQL(
      organizationId,
      undefined,
      paginationOptions,
    );
    const values = await this.getReportsFromRawSQL(reportIds);
    values.forEach((dto) => {
      dto.hasData = this.convertToBooleanValue(dto);
    });
    return new PaginatedResultArray(values, Number.parseInt(total_count));
  }

  /**
   * Updates a report status
   *
   * @param organizationId
   * @param reportId
   * @param status
   */
  async updateStatus(organizationId: string, reportId: string, status: Status) {
    const whereStatement: FindOneOptions<AnalyticsDataExportReport> = {
      where: {
        reportId,
        status: Not(Status.DELETED),
        dataExportQuery: { organizationId },
      },
      relations: { dataExportQuery: true },
    };
    const report = await this.dataExportReportRepository.findOne(
      whereStatement,
    );
    if (!report) {
      throw new NotFoundException(
        `Report ${reportId} doesn't exist for the org ${organizationId}.`,
      );
    } else {
      await this.dataExportReportRepository.update(
        {
          reportId: report.reportId,
        },
        {
          status,
        },
      );
    }
  }

  async getDataExportReport(
    organizationId: string,
    reportId: string,
  ): Promise<ReadDataExportReportDto> {
    const { reportIds } = await this.getReportIdsFromRawSQL(organizationId, {
      filter: { reportIds: [reportId] },
    });
    const reportDtoList: ReadDataExportReportSQLDto[] =
      await this.getReportsFromRawSQLFull(reportIds);
    reportDtoList.forEach((dto) => {
      dto.hasData = this.convertToBooleanValue(dto);
    });
    if (reportDtoList?.length == 0) {
      throw new NotFoundException(`Report not found`);
    }
    const reportDto = reportDtoList[0];
    return await this.convertToReportDTO(reportDto);
  }

  private convertToBooleanValue(reportDto: ReadDataExportReportDto) {
    if (typeof reportDto.hasData == 'string') {
      return !!Number.parseInt(reportDto.hasData as string);
    }
    return !!reportDto.hasData;
  }

  async getFilterMetadata(
    organizationId: string,
    filterOption: FilterSortByOptions,
    paginationOptions: PaginationOptions,
  ) {
    const { total_count } = await this.getReportsCountFromRawSQL(
      organizationId,
      filterOption,
    );
    const { reportIds } = await this.getReportIdsFromRawSQL(
      organizationId,
      filterOption,
      paginationOptions,
    );
    const values = await this.getReportsFromRawSQL(reportIds);
    values.forEach((dto) => {
      dto.hasData = this.convertToBooleanValue(dto);
    });
    return new PaginatedResultArray<ReadDataExportReportDto>(
      values,
      Number.parseInt(total_count),
    );
  }

  async convertToReportDTO(
    reportDto: ReadDataExportReportSQLDto,
  ): Promise<ReadDataExportReportSQLDto> {
    let downloadUrl: string | undefined = undefined;
    if (reportDto?.s3Key && reportDto?.s3Bucket) {
      // Sign s3 file and return download url
      downloadUrl = await this.s3Service.getSignedUrl(
        reportDto?.s3Bucket,
        reportDto?.s3Key,
      );
    }
    if (reportDto?.channels.length > 0) {
      reportDto.channels = reportDto.channels.map((c) =>
        c in NewToOldChannelRecord ? NewToOldChannelRecord[c] : c,
      );
    } else {
      reportDto.channels = undefined;
    }
    return {
      ...reportDto,
      downloadUrl,
      s3Bucket: undefined,
      s3Key: undefined,
    };
  }

  private async createFilterQueryBuilder(
    organizationId: string,
    options?: DataExportFilterRequestDto,
  ) {
    const parameters =
      options != undefined ? this.createParameterSelect(options) : {};
    const whereFilter: FindOptionsWhere<AnalyticsDataExportQuery> = {
      organizationId,
      ...parameters,
    };
    const reports = await this.dataExportReportRepository.find({
      where: { status: Not(Status.DELETED), dataExportQuery: whereFilter },
      relations: ReportSelectRelations,
    });
    return this.createDataExportFilterValueFromEntities(reports);
  }

  private createDataExportFilterValueFromEntities(
    reports: AnalyticsDataExportReport[],
  ): DataExportFilterValue {
    const reportFilterValues: DataExportFilterValue[] =
      this.classMapper.mapArray(
        reports,
        AnalyticsDataExportReport,
        DataExportFilterValue,
      );
    return {
      exportType: onlyUniqueFilterValues<ReportType>(
        reportFilterValues.map((r) => r.exportType).flat(),
      ),
      createdOn: onlyUniqueFilterValues<string>(
        reportFilterValues.map((r) => r.createdOn).flat(),
      ),
      createdBy: onlyUniqueFilterValues<IdAndName>(
        reportFilterValues.map((r) => r.createdBy).flat(),
      ),
      status: onlyUniqueFilterValues<Status>(
        reportFilterValues.map((r) => r.status).flat(),
      ),
      channel: onlyUniqueFilterValues<string>(
        reportFilterValues.map((r) => r.channel).flat(),
      ),
      workspace: onlyUniqueFilterValues<IdAndName>(
        reportFilterValues.map((r) => r.workspace).flat(),
      ),
      adAccount: onlyUniqueFilterValues<IdAndName>(
        reportFilterValues.map((r) => (r.adAccount ? r.adAccount : [])).flat(),
      ),
      market: onlyUniqueFilterValues<IdAndName>(
        reportFilterValues.map((r) => (r.market ? r.market : [])).flat(),
      ),
      brand: onlyUniqueFilterValues<IdAndName>(
        reportFilterValues.map((r) => (r.brand ? r.brand : [])).flat(),
      ),
    };
  }

  private createParameterSelect(
    options: DataExportFilterRequestDto,
  ): FindOptionsWhere<AnalyticsDataExportQuery> {
    if (options.reportStartDate && options.reportEndDate) {
      return {
        parameters: Raw(
          (columnAlias) =>
            `JSON_UNQUOTE(JSON_EXTRACT(${columnAlias}, '$.startTime')) <= :startTime and JSON_UNQUOTE(JSON_EXTRACT(${columnAlias}, '$.endTime')) >= :endTime`,
          {
            startTime: options.reportStartDate,
            endTime: options.reportEndDate,
          },
        ),
      };
    }

    if (options.reportStartDate) {
      return {
        parameters: Raw(
          (columnAlias) =>
            `JSON_UNQUOTE(JSON_EXTRACT(${columnAlias}, '$.startTime')) <= :startTime`,
          { startTime: options.reportStartDate },
        ),
      };
    }
    if (options.reportEndDate) {
      return {
        parameters: Raw(
          (columnAlias) =>
            `JSON_UNQUOTE(JSON_EXTRACT(${columnAlias}, '$.endTime')) >= :endTime`,
          { endTime: options.reportEndDate },
        ),
      };
    }
    return {};
  }

  private async getReportsCountFromRawSQL(
    organizationId: string,
    filterSortByOptions?: FilterSortByOptions,
  ) {
    const { filter } = filterSortByOptions ?? {
      filter: undefined,
      sort: undefined,
    };
    let sql = `
      SELECT count(filtered_report_ids.report_id) as total_count
      FROM (
        SELECT DISTINCT reports.report_id
        FROM analytics_data_export_report reports
        INNER JOIN analytics_data_export_query queries ON queries.report_query_id = reports.report_query_id
        INNER JOIN person users ON queries.person_id = users.id
        WHERE queries.organization_id = ? AND reports.status != 'DELETED' 
      `;

    // Add WHERE conditions
    const whereClauses: [string, any][] = buildWhereStatement(filter);
    const parameters: any[] = [
      organizationId,
      ...(whereClauses.length > 0 ? whereClauses.map((w) => w[1]) : []),
    ];
    if (whereClauses.length > 0) {
      sql += `AND ` + whereClauses.map((w) => w[0]).join(' AND ');
    }
    sql += `) as filtered_report_ids;`;

    return (
      await this.dataExportReportRepository.query(sql, parameters)
    )[0] as { total_count: string };
  }

  private async getReportIdsFromRawSQL(
    organizationId: string,
    filterSortByOptions?: FilterSortByOptions,
    paginationOption?: PaginationOptions,
  ) {
    const { sort } = filterSortByOptions ?? {
      filter: undefined,
      sort: undefined,
    };
    let sql = `
      SELECT json_arrayagg(filtered_report_ids.report_id) as reportIds
      FROM (
        SELECT DISTINCT reports.report_id,
               reports.report_name,
               queries.report_type,
               TIMESTAMP(reports.date_created),
               users.display_name,
               JSON_EXTRACT(report_results, '$.records'),
               queries.state
        FROM analytics_data_export_report reports
        INNER JOIN analytics_data_export_query queries ON queries.report_query_id = reports.report_query_id
        INNER JOIN person users ON queries.person_id = users.id
        WHERE queries.organization_id = ? AND reports.status != 'DELETED'
      `;

    // Add WHERE conditions
    let parameters: any[] = [organizationId];
    const filter = filterSortByOptions?.filter;
    const whereClauses: [string, any][] = buildWhereStatement(filter);
    if (whereClauses.length > 0) {
      parameters = [
        organizationId,
        ...(whereClauses.length > 0 ? whereClauses.map((w) => w[1]) : []),
      ];
      sql += ` AND ` + whereClauses.map((w) => w[0]).join(' AND ');
    }
    sql += ` group by reports.report_id, reports.report_name, queries.report_type, TIMESTAMP(reports.date_created), users.display_name, JSON_EXTRACT(report_results, '$.records'), queries.state `;
    // Add ORDER BY conditions
    if (filterSortByOptions?.sort && filterSortByOptions.sort?.length > 0) {
      const orderByClauses = buildOrderByStatement(sort);
      sql += ` ORDER BY ` + orderByClauses.join(', ');
    } else {
      sql += ` ORDER BY TIMESTAMP(reports.date_created) DESC `;
    }

    // Add LIMIT and OFFSET for pagination
    if (paginationOption?.perPage) {
      sql += ` LIMIT ?`;
      parameters.push(paginationOption.perPage);
    }

    if (paginationOption?.offset) {
      sql += ` OFFSET ?`;
      parameters.push(paginationOption.offset);
    }

    sql += `) as filtered_report_ids;`;

    return (
      await this.dataExportReportRepository.query(sql, parameters)
    )[0] as {
      reportIds: string[];
    };
  }

  private buildStatusRequestForReportSelect(reportTableAlias: string) {
    const archivedStatus: Status = Status.ARCHIVED;
    const currentReportDataDiffCommand = `DATEDIFF(CURRENT_DATE(), ${reportTableAlias}.date_created)`;
    const statusFieldSQL = `${reportTableAlias}.status`;
    return `if(${currentReportDataDiffCommand} < ${MAX_ACTIVE_DAYS}, ${statusFieldSQL}, '${archivedStatus}')`;
  }

  private async getReportsFromRawSQL(reportIds: string[]) {
    const reportAlias = 'reports';
    return (await this.dataExportReportRepository.query(
      `
  SELECT reports.report_id                                              AS reportId,
       reports.report_name                                              AS reportName,
       queries.report_type                                              AS reportType,
       ${this.buildStatusRequestForReportSelect(reportAlias)}   AS status,
       JSON_OBJECT('id', queries.person_id, 'name', users.display_name) AS createdBy,
       TIMESTAMP(reports.date_created)                                  AS dateCreated,
       JSON_UNQUOTE(JSON_EXTRACT(queries.parameters, '$.startDate'))    AS startDate,
       JSON_UNQUOTE(JSON_EXTRACT(queries.parameters, '$.endDate'))      AS endDate,
       JSON_EXTRACT(report_results, '$.records')                        AS recordCount,
       IF(JSON_EXTRACT(report_results, '$.records') != 0, true, false)  AS hasData,
       reports.expiration_time                                          AS expiration,
       reports.failure_reason                                           AS failureReason
FROM analytics_data_export_query queries
         JOIN analytics_data_export_report reports ON reports.report_query_id = queries.report_query_id
         JOIN person users ON queries.person_id = users.id
WHERE reports.report_id in (?) and reports.status != 'DELETED'
GROUP BY reports.report_id
ORDER BY FIELD(reports.report_id, ?)
`,
      [reportIds, reportIds],
    )) as ReadDataExportReportDto[];
  }

  private buildJoinReportToQueryTable(reportAlias: string, queryAlias: string) {
    const queryTableDefinition = `analytics_data_export_query ${queryAlias}`;
    const joinComparatorStatement = `${queryAlias}.report_query_id = ${reportAlias}.report_query_id`;
    return `join ${queryTableDefinition} on ${joinComparatorStatement}`;
  }

  private buildOrganizationReportStatusQuery() {
    const reportAlias = 'status_report';
    const reportTable = `analytics_data_export_report ${reportAlias}`;
    const queryStatus = 'status_query';
    const selectStatus: string =
      this.buildStatusRequestForReportSelect(reportAlias);
    const selectOrg = `${queryStatus}.organization_id`;
    const organizationStatusSQLSelect = `${selectOrg}, ${selectStatus} as report_status`;
    const organizationStatusSQLGroupBy = `organization_id, report_status`;
    return `
      select 
            ${organizationStatusSQLSelect}
      from ${reportTable}
         ${this.buildJoinReportToQueryTable(reportAlias, queryStatus)}
      where 
        ${reportAlias}.status != 'DELETED'
      group by ${organizationStatusSQLGroupBy}`;
  }

  private buildStatusFilterValueSubQuery(): string {
    const statusSubTableAlias = 'status_filter_value';
    const organizationIdSelect = `${statusSubTableAlias}.organization_id`;
    const statusValuesSelect = `json_arrayagg(${statusSubTableAlias}.report_status) as status`;
    const statusFilerValueSQLSelect = `${organizationIdSelect},${statusValuesSelect}`;
    const orgReportStatusQuery = this.buildOrganizationReportStatusQuery();
    const statusFilerValueSQLGroupBy = `group by ${statusSubTableAlias}.organization_id`;
    return `select ${statusFilerValueSQLSelect} from (${orgReportStatusQuery}) as ${statusSubTableAlias} ${statusFilerValueSQLGroupBy}`;
  }

  // TODO come back to this
  private async runFilterValueSQLQuery(
    organizationId: string,
    filterValueOption?: DataExportFilterRequestDto,
  ) {
    let sql = `
select report_type_value.reportType as                                exportType,
       JSON_ARRAYAGG(TIMESTAMP(report.date_created))                  createdOn,
       created_by_value.createBy                                      createdBy,
       status_value.status                                            status
from analytics_data_export_report report
         join analytics_data_export_query query on query.report_query_id = report.report_query_id
         join vidmob.person p on query.person_id = p.id
         left join (select created_by_value.organization_id,
                           JSON_ARRAYAGG(created_by_value.person) createBy
                    from (SELECT adeq.organization_id,
                                 JSON_OBJECT('id', CAST(user.id as char), 'name', user.display_name) AS person
                          FROM person user
                                   INNER JOIN analytics_data_export_query adeq on user.id = adeq.person_id
                          GROUP BY adeq.organization_id, user.id) as created_by_value
                    group by created_by_value.organization_id) created_by_value
                   on created_by_value.organization_id = query.organization_id
         inner join (${this.buildStatusFilterValueSubQuery()}) status_value on status_value.organization_id = query.organization_id
         inner join (select report_type_value.organization_id,
                            json_arrayagg(report_type_value.report_type) reportType
                     from (select query1.organization_id,
                                  query1.report_type
                           from analytics_data_export_report report1
                                    join analytics_data_export_query query1
                                         on query1.report_query_id = report1.report_query_id
                           group by organization_id, query1.report_type) as report_type_value
                     group by report_type_value.organization_id) report_type_value
                    on report_type_value.organization_id = query.organization_id
         WHERE query.organization_id = ? and report.status != 'DELETED'
    `;
    const parameters = [organizationId];
    if (filterValueOption?.reportEndDate) {
      parameters.push(filterValueOption?.reportEndDate);
      sql +=
        " and where JSON_UNQUOTE(JSON_EXTRACT(queries.parameters, '$.endDate')) <= ?";
    }
    if (filterValueOption?.reportStartDate) {
      parameters.push(filterValueOption?.reportStartDate);
      sql +=
        " and where JSON_UNQUOTE(JSON_EXTRACT(queries.parameters, '$.startDate')) >= ?";
    }
    sql += 'group by query.organization_id;';
    return (
      await this.dataExportReportRepository.query(sql, parameters)
    )[0] as DataExportFilterValue;
  }

  private async getReportsFromRawSQLFull(
    reportIds: string[],
  ): Promise<ReadDataExportReportSQLDto[]> {
    return (await this.dataExportReportRepository.query(
      `
    SELECT reports.report_id                                            AS reportId,
       reports.report_name                                              AS reportName,
       queries.report_type                                              AS reportType,
       ${this.buildStatusRequestForReportSelect('reports')}   AS status,
       JSON_OBJECT('id', queries.person_id, 'name', users.display_name) AS createdBy,
       TIMESTAMP(reports.date_created)                                  AS dateCreated,
       JSON_UNQUOTE(JSON_EXTRACT(queries.parameters, '$.startDate'))    AS startDate,
       JSON_UNQUOTE(JSON_EXTRACT(queries.parameters, '$.endDate'))      AS endDate,
       workspace_map_1.workspaces                                       AS workspaces,
       JSON_EXTRACT(parameters, '$.channels')                           AS channels,
       market_map_1.markets                                             AS markets,
       brand_map_1.brands                                               AS brands,
       ad_account_map_1.ad_accounts                                     AS adAccounts,
       JSON_UNQUOTE(JSON_EXTRACT(report_results, '$.s3Bucket'))         AS s3Bucket,
       JSON_UNQUOTE(JSON_EXTRACT(report_results, '$.s3Key'))            AS s3Key,
       JSON_EXTRACT(report_results, '$.records')                        AS recordCount,
       IF(JSON_EXTRACT(report_results, '$.records') != 0, true, false)  AS hasData,
       reports.expiration_time                                          AS expiration,
       reports.failure_reason                                           AS failureReason
FROM analytics_data_export_query queries
         JOIN analytics_data_export_report reports ON reports.report_query_id = queries.report_query_id
         JOIN person users ON queries.person_id = users.id
         LEFT JOIN (SELECT workspace_map.report_query_id,
                           JSON_ARRAYAGG(JSON_OBJECT('id', workspace.id, 'name', workspace.name)) AS workspaces
                    FROM analytics_data_export_query_workspace_map workspace_map
                             INNER JOIN partner workspace ON workspace_map.partner_id = workspace.id
                    GROUP BY workspace_map.report_query_id) AS workspace_map_1
                   ON workspace_map_1.report_query_id = queries.report_query_id
         LEFT JOIN (SELECT ad_account_map.report_query_id,
                           platform_ad_account.platform,
                           JSON_ARRAYAGG(JSON_OBJECT('id', platform_ad_account.platform_account_id,
                                                     'name',
                                                     platform_ad_account.platform_account_name)) AS ad_accounts
                    FROM analytics_data_export_query_ad_account_map ad_account_map
                             INNER JOIN platform_ad_account
                                        ON ad_account_map.platform_ad_account_id = platform_ad_account.id
                    GROUP BY ad_account_map.report_query_id,
                             platform_ad_account.platform) as ad_account_map_1
                   ON ad_account_map_1.report_query_id = queries.report_query_id
         LEFT JOIN (SELECT brand_map.report_query_id,
                           JSON_ARRAYAGG(JSON_OBJECT('id', brand.id, 'name', brand.name)) AS brands
                    FROM analytics_data_export_query_brand_map brand_map
                             INNER JOIN brand ON brand.id = brand_map.brand_id
                    GROUP BY brand_map.report_query_id) AS brand_map_1
                   ON brand_map_1.report_query_id = queries.report_query_id
         LEFT JOIN (SELECT market_map.report_query_id,
                           JSON_ARRAYAGG(JSON_OBJECT('id', market_map.iso_code, 'name', market.name)) as markets
                    FROM analytics_data_export_query_country_map market_map
                             INNER JOIN country market ON market.iso_code = market_map.iso_code
                    GROUP BY market_map.report_query_id) AS market_map_1
                   ON market_map_1.report_query_id = queries.report_query_id
WHERE reports.report_id in (?) and reports.status != 'DELETED'
GROUP BY reports.report_id
ORDER BY FIELD(reports.report_id, ?)
`,
      [reportIds, reportIds],
    )) as ReadDataExportReportDto[];
  }
}
