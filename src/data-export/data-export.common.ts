import {
  AnalyticsDataExportReport,
  Status,
} from './entities/analytics-data-export-report.entity';
import { ReportStatusRecord } from './data-export.type';
import { isInstance } from 'class-validator';
import {
  FilterOption,
  SortByOption,
  SortByParameters,
} from './dto/read-data-export-filter-query.dto';
import {
  FindOptionsWhere,
  FindOptionsWhereProperty,
} from 'typeorm/find-options/FindOptionsWhere';
import { In, Like, Raw } from 'typeorm';
import { AnalyticsDataExportQuery } from './entities/analytics-data-export-query.entity';
import { FindOptionsOrder } from 'typeorm/find-options/FindOptionsOrder';
import { WorkspaceAnalyticsDataExportQuery } from './entities/workspace-analytics-data-export-query.entity';
import { AdAccountAnalyticsDataExportQuery } from './entities/ad-account-analytics-data-export-query.entity';
import { BrandAnalyticsDataExportQuery } from './entities/brand-analytics-data-export-query.entity';
import { MarketAnalyticsDataExportQuery } from './entities/market-analytics-data-export-query.entity';
import { PlatformAdAccount } from '../ad-accounts/entities/ad-account.entity';
import { FindOptionsRelations } from 'typeorm/find-options/FindOptionsRelations';

type FilterableObjects =
  | AnalyticsDataExportReport
  | AnalyticsDataExportQuery
  | WorkspaceAnalyticsDataExportQuery
  | AdAccountAnalyticsDataExportQuery
  | BrandAnalyticsDataExportQuery
  | MarketAnalyticsDataExportQuery
  | PlatformAdAccount;

type Comparator = '<' | '>' | '<=' | '>=' | '=';

export const MAX_ACTIVE_DAYS = 30;

type ReportFindWhereMap<T extends FilterableObjects> = Partial<
  Record<keyof FilterOption, (option: FilterOption) => FindOptionsWhere<T>>
>;

export const mapCorrectChannel = (channel: string) =>
  channel in NewToOldChannelRecord
    ? NewToOldChannelRecord[channel].toUpperCase()
    : channel.toUpperCase();

export const ReportSelectRelations: FindOptionsRelations<AnalyticsDataExportReport> =
  {
    dataExportQuery: {
      person: true,
      queryToBrands: {
        brand: true,
      },
      queryToWorkspaces: {
        workspace: true,
      },
      queryToAdAccounts: {
        adAccount: true,
      },
      queryToMarkets: {
        market: true,
      },
    },
  };

const ReportSortByColumnsSQLValue: Record<SortByParameters, string> = {
  [SortByParameters.reportName]: 'reports.report_name',
  [SortByParameters.reportType]: 'queries.report_type',
  [SortByParameters.createdOn]: 'reports.date_created',
  [SortByParameters.createdBy]: 'users.display_name',
  [SortByParameters.numberOfRows]: "JSON_EXTRACT(report_results, '$.records')",
  [SortByParameters.state]: 'reports.state',
};
const convertDateFormatToSQLFormat = (dateString: string): string => {
  const months: { [key: string]: string } = {
    January: '01',
    February: '02',
    March: '03',
    April: '04',
    May: '05',
    June: '06',
    July: '07',
    August: '08',
    September: '09',
    October: '10',
    November: '11',
    December: '12',
  };

  // Split the input string by spaces and commas
  const [monthName, dayString, year] = dateString.replace(',', '').split(' ');

  const month = months[monthName];
  const day = dayString.padStart(2, '0');

  return `${year}-${month}-${day}`;
};

const statusParameterWithArchivedStatus = `If(DATEDIFF(CURRENT_DATE(), reports.date_created) < ${MAX_ACTIVE_DAYS}, reports.status, 'ARCHIVED')`;
const ReportFindWhereSQLStatement: Record<
  keyof FilterOption,
  (filter: FilterOption) => [string, any]
> = {
  search: (filter) => [
    `reports.report_name LIKE concat('%', ? ,'%')`,
    filter.search,
  ],
  reportIds: (filter) => [`reports.report_id IN (?)`, filter?.reportIds],
  reportNames: (filter) => [`reports.report_name IN (?)`, filter?.reportNames],
  reportTypes: (filter) => [`queries.report_type IN (?)`, filter?.reportTypes],
  reportStates: (filter) => [`reports.state IN (?)`, filter?.reportStates],
  reportStatuses: (filter) => [
    `${statusParameterWithArchivedStatus} IN (?)`,
    filter?.reportStatuses,
  ],
  createdOnDates: (filter) => [
    `DATE(reports.date_created) IN (?)`,
    filter?.createdOnDates.map((d) => convertDateFormatToSQLFormat(d)),
  ],
  createdByIds: (filter) => [`queries.person_id IN (?)`, filter?.createdByIds],
  startDate: (filter) => [
    `JSON_UNQUOTE(JSON_EXTRACT(queries.parameters, $.startTime)) >= ?`,
    filter?.startDate,
  ],
  endDate: (filter) => [
    `JSON_UNQUOTE(JSON_EXTRACT(queries.parameters, $.endTime)) <= ?`,
    filter?.endDate,
  ],
};

const getWhereStatementFunction = (key: string) => {
  if (!(key in ReportFindWhereSQLStatement)) {
    throw new Error(`Report parameter ${key} is not filterable`);
  }
  return ReportFindWhereSQLStatement[key as keyof FilterOption];
};

export const buildWhereStatement = (filter?: FilterOption): [string, any][] => {
  if (!filter) return [];
  return Object.keys(filter)
    .filter(
      (key) =>
        filter[key as keyof FilterOption] !== undefined &&
        (Array.isArray(filter[key as keyof FilterOption])
          ? filter[key as keyof FilterOption]?.length > 0
          : true),
    )
    .map((key) => getWhereStatementFunction(key)(filter));
};

export const buildOrderByStatement = (sort: SortByOption[]): string[] => {
  return sort.map((sortOption) => {
    if (!(sortOption.sortOrder in ReportSortByColumnsSQLValue)) {
      throw new Error(`Can not sort on ${sortOption.sortOrder}`);
    }
    const column = ReportSortByColumnsSQLValue[sortOption.sortOrder];
    if (sortOption.sortOrder == SortByParameters.createdBy) {
      `TIMESTAMP(${column}) ${sortOption.orderBy}`;
    }
    return `${column} ${sortOption.orderBy}`;
  });
};

const InConditionFilter = <T, K extends keyof T>(
  key: keyof T,
  value: T[K][],
): FindOptionsWhere<T> => {
  return <FindOptionsWhere<T>>{
    [key]: <FindOptionsWhereProperty<T>>In<T[K]>(value),
  };
};

const likeConditionFilter = <T>(
  key: keyof T,
  value: string,
): FindOptionsWhere<T> => {
  return <FindOptionsWhere<T>>{
    [key]: Like(`%${value}%`),
  };
};

const JsonDateConditionalFilter = <T>(
  objectKey: keyof T,
  key: string,
  comparator: Comparator,
  value: any,
) =>
  ({
    [objectKey]: Raw(
      (columnAlias) =>
        `JSON_UNQUOTE(JSON_EXTRACT(${columnAlias}, '$.${key}')) ${comparator} :${key}`,
      { [key]: value },
    ),
  } as FindOptionsWhere<T>);

export const ReportFindWhereRecord: ReportFindWhereMap<AnalyticsDataExportReport> =
  {
    search: (o) =>
      likeConditionFilter<AnalyticsDataExportReport>('reportName', o.search),
    reportIds: (o) =>
      InConditionFilter<
        AnalyticsDataExportReport,
        keyof AnalyticsDataExportReport
      >('reportId', o.reportIds),
    reportNames: (o) =>
      InConditionFilter<
        AnalyticsDataExportReport,
        keyof AnalyticsDataExportReport
      >('reportName', o.reportNames),
    createdOnDates: (o) =>
      InConditionFilter<
        AnalyticsDataExportReport,
        keyof AnalyticsDataExportReport
      >('dateCreated', o.createdOnDates),
    reportStatuses: (o) =>
      InConditionFilter<
        AnalyticsDataExportReport,
        keyof AnalyticsDataExportReport
      >('status', o.reportStatuses),
  };

export const QueryFindWhereRecord: ReportFindWhereMap<AnalyticsDataExportQuery> =
  {
    reportTypes: (o) =>
      InConditionFilter<
        AnalyticsDataExportQuery,
        keyof AnalyticsDataExportQuery
      >('reportType', o.reportTypes),
    createdByIds: (o) =>
      InConditionFilter<
        AnalyticsDataExportQuery,
        keyof AnalyticsDataExportQuery
      >('personId', o.createdByIds),
    startDate: (o) =>
      JsonDateConditionalFilter<AnalyticsDataExportQuery>(
        'parameters',
        'startTime',
        '>=',
        o.startDate,
      ),
    endDate: (o) =>
      JsonDateConditionalFilter<AnalyticsDataExportQuery>(
        'parameters',
        'startTime',
        '<=',
        o.endDate,
      ),
  };

export const aggregateWhereCondition = <T extends FilterableObjects>(
  reportFilterMap: ReportFindWhereMap<T>,
  initialWhere?: FindOptionsWhere<T>,
  option?: FilterOption,
): FindOptionsWhere<T> => {
  if (!option) return initialWhere ? initialWhere : {};
  return Object.keys(option).reduce(
    (a, r) => {
      if (!(r in reportFilterMap)) return a;
      const findOption = reportFilterMap[r](option);
      return {
        ...a,
        ...findOption,
      };
    },
    initialWhere ? initialWhere : {},
  );
};

const SortByFunctionMap: Record<
  SortByParameters,
  (
    orderOption: FindOptionsOrder<AnalyticsDataExportReport>,
    sortByOption: SortByOption,
  ) => FindOptionsOrder<AnalyticsDataExportReport>
> = {
  reportName: (orderOption, sortByOption) => {
    return {
      ...orderOption,
      ...{
        reportName: sortByOption.orderBy,
      },
    };
  },
  reportType: (orderOption, sortByOption) => {
    return {
      ...orderOption,
      ...{
        dataExportQuery: {
          reportType: sortByOption.orderBy,
        },
      },
    };
  },
  createdOn: (orderOption, sortByOption) => {
    return {
      ...orderOption,
      ...{
        dateCreated: sortByOption.orderBy,
      },
    };
  },
  createdBy: (orderOption, sortByOption) => {
    return {
      ...orderOption,
      ...{
        dataExportQuery: {
          person: {
            displayName: sortByOption.orderBy,
          },
        },
      },
    };
  },
  numberOfRows: (orderOption, sortByOption) => {
    return {
      ...orderOption,
      ...{
        reportResults: {
          records: sortByOption.orderBy,
        },
      },
    };
  },
  state: (orderOption, sortByOption) => {
    return {
      ...orderOption,
      ...{
        dataExportQuery: {
          state: sortByOption.orderBy,
        },
      },
    };
  },
};

export const sortByForGetReportMetadata = (
  sortOptions?: SortByOption[],
): FindOptionsOrder<AnalyticsDataExportReport> => {
  if (!sortOptions) {
    return {
      dateCreated: 'desc',
    };
  }
  return sortOptions.reduce((a, r) => {
    const parameter = r.sortOrder as SortByParameters;
    const mappingFunction = SortByFunctionMap[parameter];
    return mappingFunction(a, r);
  }, {} as FindOptionsOrder<AnalyticsDataExportReport>);
};

export const NewToOldChannelRecord = {
  meta: 'FACEBOOK',
  x: 'TWITTER',
};

export const onlyUniqueFilterValues = <T>(arr: T[]): T[] => {
  const uniqueObjects: T[] = [];
  const seenIds = new Set<any>();
  for (const obj of arr) {
    if (obj) {
      if (
        (typeof obj === 'string' || typeof obj === 'number') &&
        !seenIds.has(obj)
      ) {
        seenIds.add(obj);
        uniqueObjects.push(obj);
      } else if (isInstance(obj, Date)) {
        const stringDate: string = (<Date>obj).toISOString();
        seenIds.add(stringDate);
        uniqueObjects.push(stringDate as T);
      } else if (
        typeof obj == 'object' &&
        'id' in obj &&
        !seenIds.has(obj.id)
      ) {
        seenIds.add(obj.id as string);
        uniqueObjects.push(obj);
      }
    }
  }
  return uniqueObjects;
};

/**
 * Maps Report Status to lists of AnalyticsDataExportReport.
 * Note that the key is the Status Enum for AnalyticsDataExportReport
 * and the value is a list of AnalyticsDataExportReport
 *
 * @param reports
 */
export const buildReportStatusRecord = (
  reports: AnalyticsDataExportReport[],
): ReportStatusRecord => {
  return reports.reduce((acc, item) => {
    if (!acc[item.status]) {
      acc[Status[item.status]] = [];
    }
    acc[item.status].push(item);
    return acc;
  }, {} as ReportStatusRecord);
};
