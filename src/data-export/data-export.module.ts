import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataExportService } from './data-export.service';
import { DataExportController } from './data-export.controller';
import { AnalyticsDataExportQuery } from './entities/analytics-data-export-query.entity';
import { AnalyticsDataExportReport } from './entities/analytics-data-export-report.entity';
import { DataExportProfile } from './profile/data-export.profile';
import { DatabricksApiModule } from '../databricks-api/databricks-api.module';
import { DataExportUpdateService } from './data-export-update/data-export-update.service';
import { S3Module, SqsModule } from '@vidmob/vidmob-nestjs-common';
import { WorkspaceAnalyticsDataExportQuery } from './entities/workspace-analytics-data-export-query.entity';
import { MarketAnalyticsDataExportQuery } from './entities/market-analytics-data-export-query.entity';
import { BrandAnalyticsDataExportQuery } from './entities/brand-analytics-data-export-query.entity';
import { AdAccountAnalyticsDataExportQuery } from './entities/ad-account-analytics-data-export-query.entity';
import { DataConnectorService } from './data-connector/data-connector.service';
import { DataConnectorController } from './data-connector/data-connector.controller';
import { DataExportConnector } from './entities/data-export-connector.entity';
import { Organization } from '../organizations/entities/organization.entity';
import { OrganizationsModule } from 'src/organizations/organizations.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AnalyticsDataExportQuery,
      Organization,
      DataExportConnector,
      AnalyticsDataExportReport,
      WorkspaceAnalyticsDataExportQuery,
      MarketAnalyticsDataExportQuery,
      BrandAnalyticsDataExportQuery,
      AdAccountAnalyticsDataExportQuery,
    ]),
    S3Module,
    SqsModule,
    DatabricksApiModule,
    OrganizationsModule,
  ],
  controllers: [DataExportController, DataConnectorController],
  providers: [
    DataExportService,
    DataExportProfile,
    DataExportUpdateService,
    DataConnectorService,
  ],
})
export class DataExportModule {}
