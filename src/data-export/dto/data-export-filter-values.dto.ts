import { AutoMap } from '@automapper/classes';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { ReportType } from '../entities/analytics-data-export-query.entity';
import { IdAndName } from './read-data-export-report.dto';
import { Type } from 'class-transformer';
import { Status } from '../entities/analytics-data-export-report.entity';

export class DataExportFilterQueryValue {
  @AutoMap()
  exportType: string;
  @AutoMap()
  createdOn: string;
  @AutoMap()
  createdBy: number;
  @AutoMap()
  createdByName: string;
  @AutoMap()
  status: string;
  @AutoMap()
  workspaceId: number;
  @AutoMap()
  workspaceName: string;
  @AutoMap()
  platformAccountId: string | null;
  @AutoMap()
  platformAccountName: string | null;
  @AutoMap()
  channel: string;
  @AutoMap()
  marketId: string | null;
  @AutoMap()
  marketName: string | null;
  @AutoMap()
  brandId: string | null;
  @AutoMap()
  brandName: string | null;
}

export class DataExportFilterQueryValueDTO {
  @AutoMap()
  @IsEnum(ReportType)
  exportType: ReportType;

  @AutoMap()
  @IsDateString()
  createdOn: string;

  @AutoMap()
  @IsObject()
  @Type(() => IdAndName)
  createdBy: IdAndName;

  @AutoMap()
  @IsEnum(Status)
  status: Status;

  @AutoMap()
  @IsString()
  channel: string;

  @AutoMap()
  @IsObject()
  @Type(() => IdAndName)
  workspace: IdAndName;

  @AutoMap()
  @IsObject()
  @Type(() => IdAndName)
  adAccount?: IdAndName;

  @AutoMap()
  @IsObject()
  @Type(() => IdAndName)
  market?: IdAndName;

  @AutoMap()
  @IsObject()
  @Type(() => IdAndName)
  brand?: IdAndName;
}

export class DataExportFilterValue {
  @AutoMap()
  @IsArray()
  @IsEnum([ReportType])
  exportType: ReportType[];

  @AutoMap()
  @IsArray()
  @Type(() => String)
  createdOn: string[];

  @AutoMap()
  @IsArray()
  @Type(() => IdAndName)
  createdBy: IdAndName[];

  @AutoMap()
  @IsArray()
  @IsEnum([Status])
  status: Status[];

  @AutoMap()
  @IsString()
  channel?: string[];

  @AutoMap()
  @IsArray()
  @Type(() => IdAndName)
  workspace?: IdAndName[];

  @AutoMap()
  @IsArray()
  @IsOptional()
  @Type(() => IdAndName)
  adAccount?: IdAndName[];

  @AutoMap()
  @IsArray()
  @IsOptional()
  @Type(() => IdAndName)
  market?: IdAndName[];

  @AutoMap()
  @IsArray()
  @IsOptional()
  @Type(() => IdAndName)
  brand?: IdAndName[];
}
