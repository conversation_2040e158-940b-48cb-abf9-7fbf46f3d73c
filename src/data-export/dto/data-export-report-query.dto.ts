import { AutoMap } from '@automapper/classes';
import { IsE<PERSON>, IsN<PERSON>ber, IsString, IsUUID } from 'class-validator';
import { Status } from '../entities/analytics-data-export-report.entity';
import {
  ReportType,
  State,
} from '../entities/analytics-data-export-query.entity';

export class FirstOrderDataExportQueryResults {
  @AutoMap()
  @IsUUID()
  reportId: string;

  @AutoMap()
  @IsString()
  reportName: string;

  @AutoMap()
  @IsEnum(ReportType)
  reportType: ReportType;

  @AutoMap()
  @IsEnum(State)
  reportState: State;

  @AutoMap()
  @IsEnum(Status)
  reportStatus: Status;

  @AutoMap()
  @IsNumber()
  createdById: number;

  @AutoMap()
  @IsString()
  createdByName: string;
}
