import {
  DataExportConfigurationTestRequestType,
  DataExportConfigurationType,
  DataExportConnectionTestType,
} from '../data-export.type';
import { AutoMap } from '@automapper/classes';
import { IsEnum, IsObject, IsOptional, IsString } from 'class-validator';
import { DataExportConnectorDataService } from '../entities/data-export-connector.entity';
import { Type } from 'class-transformer';
import { CreateDataExportConnectorConfigurationDto } from './create-data-export-connector.dto';
import { ConnectionStatus } from '../data-connector/data-connector.types';

export class DataExportConnectorConfigurationDto
  implements DataExportConfigurationType
{
  @AutoMap()
  @IsString()
  bucket: string;

  @AutoMap()
  @IsString()
  path: string;

  @AutoMap()
  @IsString()
  roleArn: string;

  @AutoMap()
  @IsEnum(() => DataExportConnectorDataService)
  service: DataExportConnectorDataService;
}

export class DataConnectorTestRequestDto
  implements DataExportConfigurationTestRequestType
{
  @AutoMap()
  @IsString()
  connectorName: string;

  @AutoMap()
  @IsObject()
  @Type(() => CreateDataExportConnectorConfigurationDto)
  s3Config: DataExportConnectorConfigurationDto;
}

export class DataExportConnectionTestResult
  implements DataExportConnectionTestType
{
  @AutoMap()
  @IsString()
  connectorName: string;

  @AutoMap()
  @IsString()
  @IsOptional()
  connectionStatus?: ConnectionStatus;

  @AutoMap()
  @IsString()
  @IsOptional()
  failureMessage?: string;
}
