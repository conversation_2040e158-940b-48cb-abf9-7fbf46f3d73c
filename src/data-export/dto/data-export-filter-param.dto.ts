import { ReportType } from '../entities/analytics-data-export-query.entity';
import { Status } from '../entities/analytics-data-export-report.entity';
import { AutoMap } from '@automapper/classes';
import { IsArray, IsDateString, IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class DataExportFilterParamDto {
  @AutoMap()
  @IsEnum(ReportType)
  @IsOptional()
  exportType?: ReportType;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  createdOn?: string;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  createdBy?: string | string[];

  @AutoMap()
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @AutoMap()
  @IsEnum(Status)
  @IsOptional()
  status?: Status;

  @AutoMap()
  @IsArray({ each: true })
  @Type(() => Number)
  @IsOptional()
  workspaces?: number[];

  @AutoMap()
  @IsArray({ each: true })
  @Type(() => String)
  @IsOptional()
  channels?: string[];

  @AutoMap()
  @IsArray({ each: true })
  @Type(() => String)
  @IsOptional()
  brands?: string[];

  @AutoMap()
  @IsArray({ each: true })
  @Type(() => Number)
  @IsOptional()
  markets?: number[];

  @AutoMap()
  @IsArray({ each: true })
  @Type(() => Number)
  @IsOptional()
  adAccounts?: number[];
}
export type DataExportFilterParameters = keyof DataExportFilterParamDto;
export type DataExportFilterValues =
  DataExportFilterParamDto[keyof DataExportFilterParamDto];
