import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { Status } from '../entities/analytics-data-export-report.entity';
import { ReportType } from '../entities/analytics-data-export-query.entity';
import { AutoMap } from '@automapper/classes';
import { Type } from 'class-transformer';

export class IdAndName {
  @AutoMap()
  @IsString()
  id: string;

  @AutoMap()
  @IsString()
  name: string;
}

export class ReadDataExportReportDto {
  @AutoMap()
  @IsUUID()
  @IsNotEmpty()
  reportId: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  reportName: string;

  @AutoMap()
  @IsEnum(ReportType)
  @IsNotEmpty()
  reportType: ReportType;

  @AutoMap()
  @IsEnum(Status)
  @IsNotEmpty()
  status: Status;

  @AutoMap()
  @IsString()
  createdBy: IdAndName;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  dateCreated: string;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  startDate: string;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  endDate: string;

  @AutoMap()
  @IsArray()
  @Type(() => IdAndName)
  workspaces: IdAndName[];

  @AutoMap()
  @IsArray()
  @IsString({ each: true })
  channels: string[];

  @AutoMap()
  @IsArray()
  @Type(() => IdAndName)
  @IsOptional()
  markets?: IdAndName[];

  @AutoMap()
  @IsArray()
  @Type(() => IdAndName)
  @IsOptional()
  brands?: IdAndName[];

  @AutoMap()
  @IsArray()
  @Type(() => IdAndName)
  @IsOptional()
  adAccounts?: IdAndName[];

  @AutoMap()
  @IsString()
  @IsOptional()
  downloadUrl?: string;

  @AutoMap()
  @IsString()
  @IsOptional()
  recordCount?: number;

  @AutoMap()
  @IsOptional()
  @IsBoolean()
  hasData?: boolean;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  expiration?: string;

  @AutoMap()
  @IsString()
  @IsOptional()
  failureReason?: string;
}

export class ReadDataExportReportSQLDto extends ReadDataExportReportDto {
  @AutoMap()
  @IsString()
  @IsOptional()
  s3Bucket?: string;

  @AutoMap()
  @IsString()
  @IsOptional()
  s3Key?: string;
}
