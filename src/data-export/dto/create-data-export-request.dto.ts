import { AutoMap } from '@automapper/classes';
import {
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { ReportType } from '../entities/analytics-data-export-query.entity';

export class CreateDataExportRequestDto {
  @AutoMap()
  @IsEnum(ReportType)
  reportType: ReportType;

  @AutoMap()
  @IsString()
  reportName: string;

  @AutoMap()
  @IsString()
  startDate: string;

  @AutoMap()
  @IsString()
  endDate: string;

  @AutoMap()
  @IsArray()
  @IsNumber({}, { each: true })
  workspaces: number[];

  @AutoMap()
  @IsArray()
  @IsString({ each: true })
  channels: string[];

  @AutoMap()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  brands?: string[];

  @AutoMap()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  markets?: string[];

  @AutoMap()
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  adAccounts?: string[];
}
