import { AutoMap } from '@automapper/classes';
import { IsEnum, IsObject, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import {
  DataExportConfigurationType,
  SuccessfulDataConnectionResponse,
} from '../data-export.type';
import {
  DataExportConnectorDataService,
  DataExportConnectorStatus,
} from '../entities/data-export-connector.entity';

export class CreateDataExportConnectorConfigurationDto
  implements DataExportConfigurationType
{
  @AutoMap()
  @IsString()
  bucket: string;

  @AutoMap()
  @IsString()
  path: string;

  @AutoMap()
  @IsString()
  roleArn: string;

  @AutoMap()
  @IsEnum(() => DataExportConnectorDataService)
  service: DataExportConnectorDataService;
}

export class CreateDataExportConnectorDto {
  @AutoMap()
  @IsString()
  connectorName: string;

  @AutoMap()
  @IsObject()
  @Type(() => CreateDataExportConnectorConfigurationDto)
  s3Config: CreateDataExportConnectorConfigurationDto;
}

export class SuccessfulCreateDataExportConnectorResponseDto
  implements SuccessfulDataConnectionResponse
{
  connectorId: string;
  connectorName: string;
  dateCreated: string;
  message: 'Data connector created successfully';
  status: DataExportConnectorStatus;
}
