import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { AutoMap } from '@automapper/classes';
import {
  ReportType,
  State,
} from '../entities/analytics-data-export-query.entity';
import { Status } from '../entities/analytics-data-export-report.entity';
import { Type } from 'class-transformer';

export enum SortByParameters {
  reportName = 'reportName',
  reportType = 'reportType',
  createdOn = 'createdOn',
  createdBy = 'createdBy',
  numberOfRows = 'numberOfRows',
  state = 'state',
}

export enum OrderByOption {
  Ascending = 'ASC',
  Descending = 'DESC',
}

export class SortByOption {
  @AutoMap()
  @IsEnum(SortByOption)
  sortOrder: SortByParameters;

  @AutoMap()
  @IsEnum(SortByOption)
  orderBy: OrderByOption;
}

export class FilterOption {
  @AutoMap()
  @IsString()
  @IsOptional()
  search?: string;

  @AutoMap()
  @IsArray()
  @Type(() => String)
  @IsOptional()
  reportIds?: string[];

  @AutoMap()
  @IsArray()
  @Type(() => String)
  @IsOptional()
  reportNames?: string[];

  @AutoMap()
  @IsArray()
  @IsEnum(ReportType)
  @IsOptional()
  reportTypes?: ReportType[];

  @AutoMap()
  @IsArray()
  @IsEnum(() => State)
  @IsOptional()
  reportStates?: State[];

  @AutoMap()
  @IsArray()
  @IsEnum(Status)
  @IsOptional()
  reportStatuses?: Status[];

  @AutoMap()
  @IsArray()
  @Type(() => String)
  @IsOptional()
  createdOnDates?: string[];

  @AutoMap()
  @IsArray()
  @Type(() => Number)
  @IsOptional()
  createdByIds?: number[];

  @AutoMap()
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  endDate?: string;

  // @AutoMap()
  // @IsArray({ each: true })
  // @Type(() => Number)
  // @IsOptional()
  // workspaces?: number[];
  //
  // @AutoMap()
  // @IsArray({ each: true })
  // @Type(() => String)
  // @IsOptional()
  // channels?: string[];
  //
  // @AutoMap()
  // @IsArray({ each: true })
  // @Type(() => String)
  // @IsOptional()
  // brands?: string[];
  //
  // @AutoMap()
  // @IsArray({ each: true })
  // @Type(() => String)
  // @IsOptional()
  // markets?: string[];
  //
  // @AutoMap()
  // @IsArray({ each: true })
  // @Type(() => String)
  // @IsOptional()
  // adAccounts?: string[];
}

export class ReadDataExportFilterQueryDto {
  /**
   * Sort Order string
   * @example "ASC"
   */
  @IsOptional()
  @IsBoolean()
  sortByName?: boolean;
}

export class FilterSortByOptions {
  @AutoMap()
  @IsObject()
  @Type(() => FilterOption)
  filter: FilterOption;

  @AutoMap()
  @IsOptional()
  @IsArray()
  @Type(() => SortByOption)
  sort?: SortByOption[];
}
