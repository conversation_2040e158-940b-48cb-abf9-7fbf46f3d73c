import { Mapper } from '@automapper/core';
import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { DataExportProfile } from './data-export.profile';
import {
  AnalyticsDataExportQuery,
  ReportType,
  State,
} from '../entities/analytics-data-export-query.entity';
import {
  AnalyticsDataExportReport,
  Status,
} from '../entities/analytics-data-export-report.entity';
import { CreateDataExportRequestDto } from '../dto/create-data-export-request.dto';
import { CreateDataExportResponseDto } from '../dto/create-data-export-response.dto';
import { formatDateToLocalDateString } from '../../common/utils/helper';
import { User } from '../../entities/user.entity';
import { Workspace } from '../../workspaces/entities/workspace.entity';
import { ReadDataExportReportDto } from '../dto/read-data-export-report.dto';
import {
  DataExportFilterQueryValue,
  DataExportFilterQueryValueDTO,
} from '../dto/data-export-filter-values.dto';

describe('DataExportProfile', () => {
  let mapper: Mapper;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [DataExportProfile],
    }).compile();

    const dataExportProfile: DataExportProfile =
      module.get<DataExportProfile>(DataExportProfile);
    mapper = module.get<Mapper>(getMapperToken());
    dataExportProfile.profile(mapper);
  });

  it('should map CreateDataExportRequestDto to AnalyticsDataExportQuery', () => {
    const createDataExportRequestDto: CreateDataExportRequestDto = {
      reportType: ReportType.CREATIVE_SCORING,
      reportName: 'WPP Report',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      workspaces: [1],
      channels: ['meta'],
    };
    const dataExportQuery = mapper.map(
      createDataExportRequestDto,
      CreateDataExportRequestDto,
      AnalyticsDataExportQuery,
    );
    expect(dataExportQuery).toBeDefined();
    expect(dataExportQuery.reportQueryName).toBe(
      createDataExportRequestDto.reportName,
    );
    expect(dataExportQuery.parameters.startDate).toBe(
      createDataExportRequestDto.startDate,
    );
    expect(dataExportQuery.parameters.endDate).toBe(
      createDataExportRequestDto.endDate,
    );
    expect(dataExportQuery.parameters.workspaces).toBe(
      createDataExportRequestDto.workspaces,
    );
    expect(dataExportQuery.parameters.channels).toBe(
      createDataExportRequestDto.channels,
    );
  });

  it('should map AnalyticsDataExportReport to CreateDataExportResponseDto', () => {
    const dataExportReport: AnalyticsDataExportReport = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      status: Status.QUEUED,
      dateCreated: new Date(),
    };
    const createDataExportResponseDto = mapper.map(
      dataExportReport,
      AnalyticsDataExportReport,
      CreateDataExportResponseDto,
    );
    expect(createDataExportResponseDto).toBeDefined();
    expect(createDataExportResponseDto.reportId).toBe(
      dataExportReport.reportId,
    );
    expect(createDataExportResponseDto.status).toBe(dataExportReport.status);
    expect(createDataExportResponseDto.dateCreated).toBe(
      formatDateToLocalDateString(dataExportReport.dateCreated.toISOString()),
    );
  });
  it('should map AnalyticsDataExportQuery to AnalyticsDataExportReport', () => {
    const datExportQuery: AnalyticsDataExportQuery = {
      reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportQueryName: 'test',
      organizationId: '27a7e882-43de-4bfa-8f53-3b62875b8432',
      reportType: ReportType.CREATIVE_SCORING,
      state: State.ACTIVE,
      personId: 2,
      person: {
        id: 2,
        username: 'test-username',
        displayName: 'Test Username',
      } as User,
      parameters: {
        startDate: '2024-07-01',
        endDate: '2024-07-07',
        workspaces: [1],
        channels: ['meta'],
      },
      dataExports: [],
      queryToWorkspaces: [],
      queryToBrands: [],
      queryToAdAccounts: [],
      queryToMarkets: [],
    };
    const dataExportReport = mapper.map(
      datExportQuery,
      AnalyticsDataExportQuery,
      AnalyticsDataExportReport,
    );
    expect(dataExportReport).toBeDefined();
    expect(dataExportReport.reportQueryId).toBe(datExportQuery.reportQueryId);
  });
  it('should map AnalyticsDataExportReport to ReadDataExportReportDto', () => {
    const dataExportQuery: AnalyticsDataExportQuery = {
      person: {
        id: 1,
        username: 'test-username',
        displayName: 'Test Username',
      } as User,
      queryToWorkspaces: [
        {
          workspaceId: 1,
          reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
          query: {} as AnalyticsDataExportQuery,
          workspace: {
            id: 1,
            name: 'test-workspace',
          } as Workspace,
        },
      ],
      queryToBrands: [],
      queryToAdAccounts: [],
      queryToMarkets: [],
      dataExports: [],
      organizationId: 'test-organization',
      personId: 0,
      reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportQueryName: 'test',
      reportType: 'CREATIVE_SCORING',
      state: 'ACTIVE',
      parameters: {
        startDate: '2024-01-01',
        endDate: '2024-07-01',
        workspaces: [1],
        channels: ['meta'],
      },
    };
    const dataExportReport: AnalyticsDataExportReport = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      status: Status.QUEUED,
      dateCreated: new Date('Jul 24, 2024'),
      dataExportQuery,
    };
    const expectedDto: ReadDataExportReportDto = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: ReportType.CREATIVE_SCORING,
      status: Status.QUEUED,
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      endDate: '2024-07-01',
      workspaces: [{ id: '1', name: 'test-workspace' }],
      channels: ['meta'],
      expiration: undefined,
      failureReason: undefined,
      markets: [],
      recordCount: undefined,
      adAccounts: [],
      brands: [],
    };
    const readDataExportReportDto = mapper.map(
      dataExportReport,
      AnalyticsDataExportReport,
      ReadDataExportReportDto,
    );
    expect(readDataExportReportDto).toBeDefined();
    expect(readDataExportReportDto).toEqual(expectedDto);
  });
  it('should map AnalyticsDataExportReport to CompletedReadDataExportReportDto with no record', () => {
    const dataExportQuery: AnalyticsDataExportQuery = {
      queryToWorkspaces: [
        {
          workspaceId: 1,
          reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
          query: {} as AnalyticsDataExportQuery,
          workspace: {
            id: 1,
            name: 'test-workspace',
          } as Workspace,
        },
      ],
      queryToBrands: [],
      queryToAdAccounts: [],
      queryToMarkets: [],
      person: {
        id: 1,
        username: 'test-username',
        displayName: 'Test Username',
      } as User,
      dataExports: [],
      organizationId: 'test-organization',
      personId: 0,
      reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportQueryName: 'test',
      reportType: 'CREATIVE_SCORING',
      state: 'ACTIVE',
      parameters: {
        startDate: '2024-01-01',
        endDate: '2024-07-01',
        workspaces: [1],
        channels: ['meta'],
      },
    };
    const dataExportReport: AnalyticsDataExportReport = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      status: Status.COMPLETED,
      dateCreated: new Date('Jul 24, 2024'),
      reportResults: {
        s3Bucket: 'test',
        s3Key: 'report/uri',
      },
      expirationTime: new Date('2024-01-01'),
      dataExportQuery,
    };
    const expectedDto: ReadDataExportReportDto = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: ReportType.CREATIVE_SCORING,
      status: Status.COMPLETED,
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      expiration: formatDateToLocalDateString(
        new Date('2024-01-01').toISOString(),
      ),
      workspaces: [{ id: '1', name: 'test-workspace' }],
      channels: ['meta'],
      markets: [],
      brands: [],
      adAccounts: [],
      hasData: false,
    };
    const readDataExportReportDto = mapper.map(
      dataExportReport,
      AnalyticsDataExportReport,
      ReadDataExportReportDto,
    );
    expect(readDataExportReportDto).toBeDefined();
    expect(readDataExportReportDto).toEqual(expectedDto);
  });
  it('should map AnalyticsDataExportReport to CompletedReadDataExportReportDto with record', () => {
    const dataExportQuery: AnalyticsDataExportQuery = {
      queryToWorkspaces: [
        {
          workspaceId: 1,
          reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
          query: {} as AnalyticsDataExportQuery,
          workspace: {
            id: 1,
            name: 'test-workspace',
          } as Workspace,
        },
      ],
      queryToBrands: [],
      queryToAdAccounts: [],
      queryToMarkets: [],
      person: {
        id: 1,
        username: 'test-username',
        displayName: 'Test Username',
      } as User,
      dataExports: [],
      organizationId: 'test-organization',
      personId: 0,
      reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportQueryName: 'test',
      reportType: 'CREATIVE_SCORING',
      state: 'ACTIVE',
      parameters: {
        startDate: '2024-01-01',
        endDate: '2024-07-01',
        workspaces: [1],
        channels: ['meta'],
      },
    };
    const dataExportReport: AnalyticsDataExportReport = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      status: Status.COMPLETED,
      dateCreated: new Date('Jul 24, 2024'),
      reportResults: {
        records: 3,
        s3Bucket: 'test',
        s3Key: 'report/uri',
      },
      expirationTime: new Date('2024-01-01'),
      dataExportQuery,
    };
    const expectedDto: ReadDataExportReportDto = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: ReportType.CREATIVE_SCORING,
      status: Status.COMPLETED,
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      expiration: formatDateToLocalDateString(
        new Date('2024-01-01').toISOString(),
      ),
      workspaces: [{ id: '1', name: 'test-workspace' }],
      channels: ['meta'],
      recordCount: 3,
      markets: [],
      brands: [],
      adAccounts: [],
      hasData: true,
    };
    const readDataExportReportDto = mapper.map(
      dataExportReport,
      AnalyticsDataExportReport,
      ReadDataExportReportDto,
    );
    expect(readDataExportReportDto).toBeDefined();
    expect(readDataExportReportDto).toEqual(expectedDto);
  });
  it('should map AnalyticsDataExportReport to FailedReadDataExportReportDto', () => {
    const dataExportQuery: AnalyticsDataExportQuery = {
      queryToWorkspaces: [
        {
          workspaceId: 1,
          reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
          query: {} as AnalyticsDataExportQuery,
          workspace: {
            id: 1,
            name: 'test-workspace',
          } as Workspace,
        },
      ],
      queryToBrands: [],
      queryToAdAccounts: [],
      queryToMarkets: [],
      dataExports: [],
      organizationId: 'test-organization',
      personId: 0,
      person: {
        id: 1,
        username: 'test-username',
        displayName: 'Test Username',
      } as User,
      reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportQueryName: 'test',
      reportType: 'CREATIVE_SCORING',
      state: 'ACTIVE',
      parameters: {
        startDate: '2024-01-01',
        endDate: '2024-07-01',
        workspaces: [1],
        channels: ['meta'],
      },
    };
    const dataExportReport: AnalyticsDataExportReport = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportQueryId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      status: Status.FAILED,
      dateCreated: new Date('Jul 24, 2024'),
      reportResults: {
        failureReason: 'test-failure-reason',
      },
      dataExportQuery,
    };
    const expectedDto: ReadDataExportReportDto = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: ReportType.CREATIVE_SCORING,
      status: Status.FAILED,
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      failureReason: 'test-failure-reason',
      workspaces: [{ id: '1', name: 'test-workspace' }],
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      channels: ['meta'],
      adAccounts: [],
      brands: [],
      expiration: undefined,
      markets: [],
      recordCount: undefined,
    };
    const readDataExportReportDto = mapper.map(
      dataExportReport,
      AnalyticsDataExportReport,
      ReadDataExportReportDto,
    );
    expect(readDataExportReportDto).toBeDefined();
    expect(readDataExportReportDto).toEqual(expectedDto);
  });
  it('should map DataExportFilterQueryValue to DataExportFilterQueryValueDTO', () => {
    const input: DataExportFilterQueryValue = {
      exportType: ReportType.CREATIVE_SCORING.toString(),
      channel: 'test-channel',
      createdOn: '2024-01-01',
      status: Status.PROCESSING.toString(),
      workspaceId: 1,
      workspaceName: 'name_1',
      createdBy: 2,
      createdByName: `name_2`,
      platformAccountId: '123',
      platformAccountName: `name_123`,
      marketId: '3',
      marketName: `name_3`,
      brandId: '4',
      brandName: `name_4`,
    };
    const expected: DataExportFilterQueryValueDTO = {
      exportType: ReportType.CREATIVE_SCORING,
      channel: 'test-channel',
      createdOn: '2024-01-01',
      status: Status.PROCESSING,
      workspace: { id: '1', name: 'name_1' },
      createdBy: { id: '2', name: `name_2` },
      adAccount: { id: '123', name: `name_123` },
      market: { id: '3', name: 'name_3' },
      brand: { id: '4', name: 'name_4' },
    };
    const dto = mapper.map(
      input,
      DataExportFilterQueryValue,
      DataExportFilterQueryValueDTO,
    );
    expect(dto).toBeInstanceOf(DataExportFilterQueryValueDTO);
    expect(dto).toEqual(expected);
  });
});
