import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingConfiguration,
  preCondition,
} from '@automapper/core';
import { Mapping } from '@automapper/core/lib/types';
import {
  DataExportFilterQueryValue,
  DataExportFilterQueryValueDTO,
} from '../../dto/data-export-filter-values.dto';
import { ReportType } from '../../entities/analytics-data-export-query.entity';
import { Status } from '../../entities/analytics-data-export-report.entity';
import { NewToOldChannelRecord } from '../../data-export.common';

const mapBrandToObject: MappingConfiguration<
  DataExportFilterQueryValue,
  DataExportFilterQueryValueDTO
> = forMember<DataExportFilterQueryValue, DataExportFilterQueryValueDTO>(
  (d) => d.brand,
  preCondition((s) => !!s.brandId && !!s.brandName, undefined),
  mapFrom((s) => ({
    id: s.brandId,
    name: s.brandName,
  })),
);

const mapMarketToObject: MappingConfiguration<
  DataExportFilterQueryValue,
  DataExportFilterQueryValueDTO
> = forMember<DataExportFilterQueryValue, DataExportFilterQueryValueDTO>(
  (d) => d.market,
  preCondition((s) => !!s.marketId && !!s.marketName, undefined),
  mapFrom((s) => ({
    id: s.marketId,
    name: s.marketName,
  })),
);

const mapAdAccountToObject: MappingConfiguration<
  DataExportFilterQueryValue,
  DataExportFilterQueryValueDTO
> = forMember<DataExportFilterQueryValue, DataExportFilterQueryValueDTO>(
  (d) => d.adAccount,
  preCondition(
    (s) => !!s.platformAccountId && !!s.platformAccountName,
    undefined,
  ),
  mapFrom((s) => ({
    id: s.platformAccountId,
    name: s.platformAccountName,
  })),
);

const mapChannelsToObject: MappingConfiguration<
  DataExportFilterQueryValue,
  DataExportFilterQueryValueDTO
> = forMember<DataExportFilterQueryValue, DataExportFilterQueryValueDTO>(
  (d) => d.channel,
  preCondition((s) => !!s.channel && !!s.channel, undefined),
  mapFrom((s) =>
    s.channel in NewToOldChannelRecord
      ? NewToOldChannelRecord[s.channel]
      : s.channel,
  ),
);

const mapWorkspaceToObject: MappingConfiguration<
  DataExportFilterQueryValue,
  DataExportFilterQueryValueDTO
> = forMember<DataExportFilterQueryValue, DataExportFilterQueryValueDTO>(
  (d) => d.workspace,
  preCondition((s) => !!s.workspaceId && !!s.workspaceName, undefined),
  mapFrom((s) => ({
    id: s.workspaceId.toString(),
    name: s.workspaceName,
  })),
);

const mapStatusToReportStatus: MappingConfiguration<
  DataExportFilterQueryValue,
  DataExportFilterQueryValueDTO
> = forMember(
  (d) => d.status,
  preCondition((s) => s.status in Status, undefined),
  mapFrom((s) => Status[s.status]),
);

const mapCreateByToObject: MappingConfiguration<
  DataExportFilterQueryValue,
  DataExportFilterQueryValueDTO
> = forMember<DataExportFilterQueryValue, DataExportFilterQueryValueDTO>(
  (d) => d.createdBy,
  preCondition((s) => !!s.createdByName && !!s.createdBy, undefined),
  mapFrom((s) => ({
    id: s.createdBy.toString(),
    name: s.createdByName,
  })),
);

const mapExportTypeToReportType: MappingConfiguration<
  DataExportFilterQueryValue,
  DataExportFilterQueryValueDTO
> = forMember(
  (d) => d.exportType,
  preCondition((s) => s.exportType in ReportType, undefined),
  mapFrom((s) => ReportType[s.exportType]),
);

export const MapDataExportFilterQueryToDto = (
  mapper: Mapper,
): Mapping<DataExportFilterQueryValue, DataExportFilterQueryValueDTO> =>
  createMap<DataExportFilterQueryValue, DataExportFilterQueryValueDTO>(
    mapper,
    DataExportFilterQueryValue,
    DataExportFilterQueryValueDTO,
    mapExportTypeToReportType,
    mapCreateByToObject,
    mapStatusToReportStatus,
    mapWorkspaceToObject,
    mapAdAccountToObject,
    mapBrandToObject,
    mapMarketToObject,
    mapChannelsToObject,
  );
