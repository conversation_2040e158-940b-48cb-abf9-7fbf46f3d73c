import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingConfiguration,
  preCondition,
} from '@automapper/core';
import { Mapping } from '@automapper/core/lib/types';
import { DataExportFilterValue } from '../../dto/data-export-filter-values.dto';
import { AnalyticsDataExportReport } from '../../entities/analytics-data-export-report.entity';
import { IdAndName } from '../../dto/read-data-export-report.dto';
import { ReportType } from '../../entities/analytics-data-export-query.entity';
import { formatDateToLocalDateString } from '../../../common/utils/helper';
import { mapCorrectChannel } from '../../data-export.common';

const MapReportType: MappingConfiguration<
  AnalyticsDataExportReport,
  DataExportFilterValue
> = forMember<AnalyticsDataExportReport, DataExportFilterValue>(
  (d) => d.exportType,
  mapFrom((s) => ReportType[s.dataExportQuery.reportType]),
);

const MapCreatedOn: MappingConfiguration<
  AnalyticsDataExportReport,
  DataExportFilterValue
> = forMember<AnalyticsDataExportReport, DataExportFilterValue>(
  (d) => d.createdOn,
  mapFrom((s) => formatDateToLocalDateString(s.dateCreated.toISOString())),
);

const MapCreateByToIdAndName: MappingConfiguration<
  AnalyticsDataExportReport,
  DataExportFilterValue
> = forMember<AnalyticsDataExportReport, DataExportFilterValue>(
  (d) => d.createdBy,
  mapFrom(
    (s) =>
      ({
        id: s.dataExportQuery.person.id.toString(),
        name: s.dataExportQuery.person.displayName,
      } as IdAndName),
  ),
);

const MapStatus: MappingConfiguration<
  AnalyticsDataExportReport,
  DataExportFilterValue
> = forMember<AnalyticsDataExportReport, DataExportFilterValue>(
  (d) => d.status,
  mapFrom((s) => s.status),
);

const MapChannel: MappingConfiguration<
  AnalyticsDataExportReport,
  DataExportFilterValue
> = forMember<AnalyticsDataExportReport, DataExportFilterValue>(
  (d) => d.channel,
  mapFrom((s) =>
    s.dataExportQuery.parameters.channels.map((c) => mapCorrectChannel(c)),
  ),
);

const MapWorkspaceToIdAndName: MappingConfiguration<
  AnalyticsDataExportReport,
  DataExportFilterValue
> = forMember<AnalyticsDataExportReport, DataExportFilterValue>(
  (d) => d.workspace,
  preCondition((s) => !!s.dataExportQuery.parameters.workspaces, undefined),
  mapFrom((s) =>
    s.dataExportQuery.queryToWorkspaces.map(
      (w) =>
        ({
          id: w.workspace.id.toString(),
          name: w.workspace.name,
        } as IdAndName),
    ),
  ),
);

const MapAdAccountToIdAndName: MappingConfiguration<
  AnalyticsDataExportReport,
  DataExportFilterValue
> = forMember<AnalyticsDataExportReport, DataExportFilterValue>(
  (d) => d.adAccount,
  preCondition((s) => !!s.dataExportQuery.parameters.adAccounts, undefined),
  mapFrom((s) =>
    s.dataExportQuery.queryToAdAccounts.map(
      (a) =>
        ({
          id: a.adAccount.platformAccountId,
          name: a.adAccount.platformAccountName,
        } as IdAndName),
    ),
  ),
);

const MapMarketToIdAndName: MappingConfiguration<
  AnalyticsDataExportReport,
  DataExportFilterValue
> = forMember<AnalyticsDataExportReport, DataExportFilterValue>(
  (d) => d.market,
  preCondition((s) => !!s.dataExportQuery.parameters.markets, undefined),
  mapFrom((s) =>
    s.dataExportQuery.queryToMarkets.map(
      (a) =>
        ({
          id: a.market.isoCode,
          name: a.market.name,
        } as IdAndName),
    ),
  ),
);

const MapBrandToIdAndName: MappingConfiguration<
  AnalyticsDataExportReport,
  DataExportFilterValue
> = forMember<AnalyticsDataExportReport, DataExportFilterValue>(
  (d) => d.brand,
  preCondition((s) => !!s.dataExportQuery.parameters.brands, undefined),
  mapFrom((s) =>
    s.dataExportQuery.queryToBrands.map(
      (a) =>
        ({
          id: a.brand.id,
          name: a.brand.name,
        } as IdAndName),
    ),
  ),
);

export const MapReportToFilterDto = (
  mapper: Mapper,
): Mapping<AnalyticsDataExportReport, DataExportFilterValue> =>
  createMap(
    mapper,
    AnalyticsDataExportReport,
    DataExportFilterValue,
    MapReportType,
    MapCreatedOn,
    MapCreateByToIdAndName,
    MapStatus,
    MapChannel,
    MapWorkspaceToIdAndName,
    MapAdAccountToIdAndName,
    MapMarketToIdAndName,
    MapBrandToIdAndName,
  );
