import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingConfiguration,
} from '@automapper/core';
import { CreateDataExportRequestDto } from '../../dto/create-data-export-request.dto';
import {
  AnalyticsDataExportQuery,
  Parameters,
} from '../../entities/analytics-data-export-query.entity';
import { Mapping } from '@automapper/core/lib/types';

/**
 * Maps Parameters from CreateDataExportRequestDto to AnalyticsDataExportQuery object
 *
 */
const MapParametersToAnalyticsDataExportQuery: MappingConfiguration<
  CreateDataExportRequestDto,
  AnalyticsDataExportQuery
> = forMember(
  (destination) => destination.parameters,
  mapFrom(
    (source) =>
      ({
        startDate: source.startDate,
        endDate: source.endDate,
        workspaces: source.workspaces,
        channels: source.channels,
        brands: source.brands,
        markets: source.markets,
        adAccounts: source.adAccounts,
      } as unknown as Parameters),
  ),
);

/**
 * Maps Query Name from CreateDataExportRequestDto to AnalyticsDataExportQuery object
 *
 */
const MapQueryNameToAnalyticsDataExportQuery: MappingConfiguration<
  CreateDataExportRequestDto,
  AnalyticsDataExportQuery
> = forMember(
  (destination) => destination.reportQueryName,
  mapFrom((source) => source.reportName),
);

/**
 * Maps CreateDataExportRequestDto to an AnalyticsDataExportQuery used in
 * VidMob's RDS Database
 * @param mapper
 * @constructor
 */
export const MapCreateDataExportRequestDtoToAnalyticsDataExportQuery: (
  mapper: Mapper,
) => Mapping<CreateDataExportRequestDto, AnalyticsDataExportQuery> = (
  mapper: Mapper,
) =>
  createMap(
    mapper,
    CreateDataExportRequestDto,
    AnalyticsDataExportQuery,
    MapQueryNameToAnalyticsDataExportQuery,
    MapParametersToAnalyticsDataExportQuery,
  );
