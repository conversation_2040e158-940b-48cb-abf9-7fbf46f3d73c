import { Mapping } from '@automapper/core/lib/types';
import {
  CreateDataExportConnectorConfigurationDto,
  CreateDataExportConnectorDto,
} from '../../dto/create-data-export-connector.dto';
import {
  DataExportConfiguration,
  DataExportConnector,
  DataExportConnectorDataService,
  DataExportConnectorStatus,
} from '../../entities/data-export-connector.entity';
import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingConfiguration,
  mapWithArguments,
  nullSubstitution,
  preCondition,
  undefinedSubstitution,
} from '@automapper/core';

const MapState: MappingConfiguration<
  CreateDataExportConnectorDto,
  DataExportConnector
> = forMember<CreateDataExportConnectorDto, DataExportConnector>(
  (d) => d.status,
  mapFrom(() => DataExportConnectorStatus.ACTIVE),
);

const MapOrganizationId: MappingConfiguration<
  CreateDataExportConnectorDto,
  DataExportConnector
> = forMember<CreateDataExportConnectorDto, DataExportConnector>(
  (d) => d.organizationId,
  mapWithArguments((source, { organizationId }) => {
    return organizationId;
  }),
);
const MapCreator: MappingConfiguration<
  CreateDataExportConnectorDto,
  DataExportConnector
> = forMember<CreateDataExportConnectorDto, DataExportConnector>(
  (d) => d.createdBy,
  mapWithArguments((source, { userId }) => {
    return userId;
  }),
);

export const MapConnectorDTOToConnector: (
  mapper: Mapper,
) => Mapping<CreateDataExportConnectorDto, DataExportConnector> = (mapper) =>
  createMap<CreateDataExportConnectorDto, DataExportConnector>(
    mapper,
    CreateDataExportConnectorDto,
    DataExportConnector,
    MapState,
    MapOrganizationId,
    MapCreator,
  );

export const MapConnectorDTOToConnectorConfigs: (
  mapper: Mapper,
) => Mapping<
  CreateDataExportConnectorConfigurationDto,
  DataExportConfiguration
> = (mapper) =>
  createMap<CreateDataExportConnectorConfigurationDto, DataExportConfiguration>(
    mapper,
    CreateDataExportConnectorConfigurationDto,
    DataExportConfiguration,
  );
