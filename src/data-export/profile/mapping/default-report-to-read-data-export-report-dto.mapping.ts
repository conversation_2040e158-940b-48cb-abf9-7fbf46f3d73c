import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingConfiguration,
  preCondition,
} from '@automapper/core';
import {
  AnalyticsDataExportReport,
  Status,
} from '../../entities/analytics-data-export-report.entity';
import {
  IdAndName,
  ReadDataExportReportDto,
} from '../../dto/read-data-export-report.dto';
import { Mapping } from '@automapper/core/lib/types';
import { ReportType } from '../../entities/analytics-data-export-query.entity';
import { formatDateToLocalDateString } from '../../../common/utils/helper';

/**
 * Chooses the value for the hasData flag
 * @param source
 */
const chooseHasDataFlag = (source: AnalyticsDataExportReport): boolean =>
  !!source.reportResults?.records;

/**
 * Check if the DTO should include hasData flag
 * @param source
 */
const includeHasDataFlag = (source: AnalyticsDataExportReport): boolean => {
  return source.status == Status.COMPLETED || !!source.reportResults?.records;
};

const MapHasDataFlagToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember(
  (d) => d.hasData,
  preCondition((s) => includeHasDataFlag(s), undefined),
  mapFrom((s) => chooseHasDataFlag(s)),
);

/**
 * Maps report id associated to the report to the DTO
 *
 */
const MapReportIdToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember(
  (d) => d.reportId,
  mapFrom((s) => s.reportId),
);

/**
 * Maps report name associated to the report to the DTO
 *
 */
const MapReportNameToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember(
  (d) => d.reportName,
  mapFrom((s) => s.reportName),
);

/**
 * Maps report type to the DTO
 *
 */
const MapReportTypeToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.reportType,
  preCondition((s) => s.dataExportQuery.reportType in ReportType, ''),
  mapFrom((s) => ReportType[s.dataExportQuery.reportType]),
);

/**
 * Maps report status associated to the report to the DTO
 *
 */
const MapReportStatusToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.status,
  preCondition((s) => s.status in Status),
  mapFrom((s) => Status[s.status]),
);

/**
 * Maps date create associated to the report to the DTO
 *
 */
const MapDateCreatedISOStringToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.dateCreated,
  mapFrom((s) => formatDateToLocalDateString(s.dateCreated.toISOString())),
);

/**
 * Maps start date associated to the report to the DTO
 *
 */
const MapStartDateToISOStringToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.startDate,
  mapFrom((s) => s.dataExportQuery.parameters.startDate),
);

/**
 * Maps end date associated to the report to the DTO
 *
 */
const MapEndDateToISOStringToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.endDate,
  mapFrom((s) => s.dataExportQuery.parameters.endDate),
);

/**
 * Maps workspaces associated to the report to the DTO
 *
 */
const MapWorkspacesToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.workspaces,
  preCondition(
    (s) =>
      s.dataExportQuery.queryToWorkspaces.map((w) => w.workspace).length > 0,
    [],
  ),
  mapFrom((s) =>
    s.dataExportQuery.queryToWorkspaces.map(
      (w) =>
        ({
          id: w.workspace.id.toString(),
          name: w.workspace.name,
        } as IdAndName),
    ),
  ),
);

/**
 * Maps brands associated to the report to the DTO
 *
 */
const MapBrandsToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.brands,
  preCondition(
    (s) => s.dataExportQuery.queryToBrands.map((b) => b.brand).length > 0,
    [],
  ),
  mapFrom((s) =>
    s.dataExportQuery.queryToBrands.map(
      (b) => ({ id: b.brand.id, name: b.brand.name } as IdAndName),
    ),
  ),
);

/**
 * Maps Person associated to the report to the DTO
 *
 */
const MapPersonToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.createdBy,
  preCondition((s) => !!s.dataExportQuery.person, undefined),
  mapFrom((s) => ({
    id: s.dataExportQuery.person.id.toString(),
    name: s.dataExportQuery.person.displayName,
  })),
);

/**
 * Maps channels associated to the report to the DTO
 *
 */
const MapChannelsToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.channels,
  mapFrom((s) => s.dataExportQuery.parameters.channels),
);

/**
 * Maps markets associated to the report to the DTO
 *
 */
const MapMarketsToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.markets,
  preCondition(
    (s) => s.dataExportQuery.queryToMarkets.map((m) => m.market).length > 0,
    [],
  ),
  mapFrom((s) =>
    s.dataExportQuery.queryToMarkets.map(
      (m) =>
        ({
          id: m.isoCode,
          name: m.market.name,
        } as IdAndName),
    ),
  ),
);

/**
 * Maps ad accounts associated to the report to the DTO
 *
 */
const MapAdAccountsToReadReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember<AnalyticsDataExportReport, ReadDataExportReportDto>(
  (d) => d.adAccounts,
  preCondition(
    (s) =>
      s.dataExportQuery.queryToAdAccounts.map((a) => a.adAccount).length > 0,
    [],
  ),
  mapFrom((s) =>
    s.dataExportQuery.queryToAdAccounts?.map(
      (a) =>
        ({
          id: a.adAccount.platformAccountId,
          name: a.adAccount.platformAccountName,
        } as IdAndName),
    ),
  ),
);

/**
 * Maps Expiration date of the report to its toISOString to the DTO
 */
const MapExpirationISOStringToCompletedReadDataExportReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember(
  (d) => d.expiration,
  preCondition((s) => !!s.expirationTime, undefined),
  mapFrom((s) => formatDateToLocalDateString(s.expirationTime.toISOString())),
);

/**
 * Maps failure response from AnalyticsDataExportReport to ReadDataExportReportDto
 *
 */
const MapRecordCountReadDataExportReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember(
  (d) => d.recordCount,
  preCondition((s) => !!s.reportResults?.records, undefined),
  mapFrom((s) => s.reportResults?.records),
);

/**
 * Maps failure response from AnalyticsDataExportReport to ReadDataExportReportDto
 *
 */
const MapFailureReasonToFailedReadDataExportReportDto: MappingConfiguration<
  AnalyticsDataExportReport,
  ReadDataExportReportDto
> = forMember(
  (d) => d.failureReason,
  preCondition((s) => !!s.reportResults?.failureReason, undefined),
  mapFrom((s) => s.reportResults?.failureReason),
);

/**
 * Maps an AnalyticsDataExportReport Object to a ReadDataExportReportDto
 * @param mapper
 * @constructor
 */
export const MapDataExportReportToReadDataExportReportDto: (
  mapper: Mapper,
) => Mapping<AnalyticsDataExportReport, ReadDataExportReportDto> = (
  mapper: Mapper,
) =>
  createMap<AnalyticsDataExportReport, ReadDataExportReportDto>(
    mapper,
    AnalyticsDataExportReport,
    ReadDataExportReportDto,
    MapReportIdToReadReportDto,
    MapReportNameToReadReportDto,
    MapReportTypeToReadReportDto,
    MapReportStatusToReadReportDto,
    MapDateCreatedISOStringToReadReportDto,
    MapStartDateToISOStringToReadReportDto,
    MapBrandsToReadReportDto,
    MapEndDateToISOStringToReadReportDto,
    MapWorkspacesToReadReportDto,
    MapChannelsToReadReportDto,
    MapMarketsToReadReportDto,
    MapAdAccountsToReadReportDto,
    MapPersonToReadReportDto,
    MapRecordCountReadDataExportReportDto,
    MapExpirationISOStringToCompletedReadDataExportReportDto,
    MapFailureReasonToFailedReadDataExportReportDto,
    MapHasDataFlagToReadReportDto,
  );
