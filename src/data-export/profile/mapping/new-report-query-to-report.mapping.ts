import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingConfiguration,
} from '@automapper/core';
import { AnalyticsDataExportQuery } from '../../entities/analytics-data-export-query.entity';
import {
  AnalyticsDataExportReport,
  Status,
} from '../../entities/analytics-data-export-report.entity';
import { Mapping } from '@automapper/core/lib/types';

/**
 * Maps a new report entity to its created date
 *
 */
const MapNewReportToCreateDate: MappingConfiguration<
  AnalyticsDataExportQuery,
  AnalyticsDataExportReport
> = forMember(
  (destination) => destination.dateCreated,
  mapFrom(() => new Date()),
);

/**
 * Maps a new report entity to a QUEUE status
 *
 */
const MapNewReportToQueueStatus: MappingConfiguration<
  AnalyticsDataExportQuery,
  AnalyticsDataExportReport
> = forMember(
  (destination) => destination.status,
  mapFrom(() => Status.QUEUED),
);

/**
 * Maps a new report query to a queued report entity in the
 * Vidmob RDS database
 *
 * @param mapper
 * @constructor
 */
export const MapNewReportQueryToReport: (
  mapper: Mapper,
) => Mapping<AnalyticsDataExportQuery, AnalyticsDataExportReport> = (
  mapper: Mapper,
) =>
  createMap(
    mapper,
    AnalyticsDataExportQuery,
    AnalyticsDataExportReport,
    MapNewReportToQueueStatus,
    MapNewReportToCreateDate,
  );
