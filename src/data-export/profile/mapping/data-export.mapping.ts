import { Mapping } from '@automapper/core/lib/types';
import { Mapper } from '@automapper/core';
import { MapCreateDataExportRequestDtoToAnalyticsDataExportQuery } from './create-data-export-dto-to-report-query.mapping';
import { MapDataExportReportToReadDataExportReportDto } from './default-report-to-read-data-export-report-dto.mapping';
import { MapNewReportQueryToReport } from './new-report-query-to-report.mapping';
import { MapNewReportToCreateResponseDTO } from './new-report-to-create-response-dto.mapping';
import { MapDataExportFilterQueryToDto } from './data-export-filter-query-to-dto.mapping';
import { MapReportToFilterDto } from './read-report-to-filter-value-dto.mapping';
import {
  MapConnectorDTOToConnector,
  MapConnectorDTOToConnectorConfigs,
} from './create-data-export-connector-dto-to-data-export-connector.mapping';

/**
 * All mappings that will be used in DataExportProfile.
 * Whenever a new mapping is made, add the mapping function to this list.
 * No need to touch the DataExportProfile class, since its iterate the mappings
 * from here.
 */
export const Mappings: Array<(mapper: Mapper) => Mapping> = [
  MapCreateDataExportRequestDtoToAnalyticsDataExportQuery,
  MapDataExportReportToReadDataExportReportDto,
  MapNewReportQueryToReport,
  MapNewReportToCreateResponseDTO,
  MapDataExportFilterQueryToDto,
  MapReportToFilterDto,
  MapConnectorDTOToConnector,
  MapConnectorDTOToConnectorConfigs,
];
