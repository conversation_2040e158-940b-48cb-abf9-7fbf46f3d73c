import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingConfiguration,
} from '@automapper/core';
import { Mapping } from '@automapper/core/lib/types';
import { AnalyticsDataExportReport } from '../../entities/analytics-data-export-report.entity';
import { CreateDataExportResponseDto } from '../../dto/create-data-export-response.dto';
import { formatDateToLocalDateString } from '../../../common/utils/helper';

/**
 * Maps response message to CreateDataExportResponseDto
 *
 */
const MapSuccessfulResponseMessageToResponse: MappingConfiguration<
  AnalyticsDataExportReport,
  CreateDataExportResponseDto
> = forMember(
  (destination) => destination.message,
  mapFrom(() => 'Data export requested successfully'),
);

/**
 * Maps DataCreated to its ISOString from
 * AnalyticsDataExportReport to CreateDataExportResponseDto
 *
 */
const MapReportDataCreatedToItsISOString: MappingConfiguration<
  AnalyticsDataExportReport,
  CreateDataExportResponseDto
> = forMember(
  (destination) => destination.dateCreated,
  mapFrom((source) =>
    formatDateToLocalDateString(source.dateCreated.toISOString()),
  ),
);

/**
 * Maps a new report from AnalyticsDataExportReport to a
 * CreateDataExportResponseDto
 * @param mapper
 * @constructor
 */
export const MapNewReportToCreateResponseDTO: (
  mapper: Mapper,
) => Mapping<AnalyticsDataExportReport, CreateDataExportResponseDto> = (
  mapper: Mapper,
) =>
  createMap(
    mapper,
    AnalyticsDataExportReport,
    CreateDataExportResponseDto,
    MapReportDataCreatedToItsISOString,
    MapSuccessfulResponseMessageToResponse,
  );
