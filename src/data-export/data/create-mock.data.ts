import {
  AnalyticsDataExportReport,
  Status,
} from '../entities/analytics-data-export-report.entity';
import {
  AnalyticsDataExportQuery,
  Parameters,
  ReportType,
  State,
} from '../entities/analytics-data-export-query.entity';
import { randomUUID } from 'crypto';
import {
  IdAndName,
  ReadDataExportReportDto,
} from '../dto/read-data-export-report.dto';
import { formatDateToLocalDateString } from '../../common/utils/helper';
import { Workspace } from '../../workspaces/entities/workspace.entity';
import { PlatformAdAccount } from '../../ad-accounts/entities/ad-account.entity';
import { Market } from '../../markets/entities/market.entity';
import { User } from '../../entities/user.entity';
import { Brand } from '../../brands/entities/brand.entity';
import { DataExportFilterQueryValue } from '../dto/data-export-filter-values.dto';

const buildAdAccountFilterInfo = (): Partial<DataExportFilterQueryValue> => {
  const platformAccountId = randomUUID() as string;
  return {
    platformAccountId,
    platformAccountName: `name_${platformAccountId}`,
  };
};

const buildMarketFilterInfo = (): Partial<DataExportFilterQueryValue> => {
  const id = randomUUID() as string;
  return {
    marketId: id,
    marketName: `name_${id}`,
  };
};

const buildBrandFilterInfo = (): Partial<DataExportFilterQueryValue> => {
  const id = randomUUID() as string;
  return {
    brandId: id,
    brandName: `name_${id}`,
  };
};

const buildCreateByFilterInfo = (): Partial<DataExportFilterQueryValue> => {
  const id = Math.floor(Math.random() * 10) + 1;
  return {
    createdBy: id,
    createdByName: `name_${id}`,
  };
};

const buildWorkspaceFilterInfo = (): Partial<DataExportFilterQueryValue> => {
  const id = Math.floor(Math.random() * 10) + 1;
  return {
    workspaceId: id,
    workspaceName: `name_${id}`,
  };
};

export const buildMockFilterValue = (
  includeMarket?: boolean,
  includeAdAccounts?: boolean,
  includeBrand?: boolean,
): DataExportFilterQueryValue =>
  <DataExportFilterQueryValue>{
    exportType: ReportType.CREATIVE_SCORING.toString(),
    channel: 'test-channel',
    createdOn: '2024-01-01',
    status: Status.PROCESSING.toString(),
    ...buildWorkspaceFilterInfo(),
    ...buildCreateByFilterInfo(),
    ...(includeMarket ? buildMarketFilterInfo() : {}),
    ...(includeAdAccounts ? buildAdAccountFilterInfo() : {}),
    ...(includeBrand ? buildBrandFilterInfo() : {}),
  };

const buildMockReport = (
  reportQueryId: string,
  status: Status,
  dataExportQuery: AnalyticsDataExportQuery,
) => {
  const reportId = randomUUID() as string;
  const reportName = `test-${reportId}`;
  const date = new Date();
  return {
    reportId,
    reportQueryId,
    reportName,
    status,
    dateCreated: date,
    dataExportQuery,
    expirationTime: status == Status.COMPLETED ? date : undefined,
    reportUrl: status == Status.COMPLETED ? 's3://test' : undefined,
    failureReason: status == Status.FAILED ? 'test-failure-reason' : undefined,
  } as AnalyticsDataExportReport;
};

const buildMockParameter = (
  hasBrands = false,
  hasMarkets = false,
  hasAdAccounts = false,
): Parameters => {
  return {
    startDate: '2024-01-01',
    endDate: '2024-07-01',
    workspaces: [Math.floor(Math.random() * 10) + 1],
    channels: ['meta'],
    brands: hasBrands ? ['coke'] : undefined,
    markets: hasMarkets ? ['USD'] : undefined,
    adAccounts: hasAdAccounts ? ['1'] : undefined,
  };
};

const buildMockQuery = (
  reportQueryId: string,
  reportQueryName: string,
  reportState: State = State.ACTIVE,
  reportType: ReportType,
  parameters: Parameters,
): AnalyticsDataExportQuery => {
  const id = Math.floor(Math.random() * 10) + 1;
  return {
    queryToAdAccounts: parameters.adAccounts
      ? parameters.adAccounts.map((a) => ({
          reportQueryId,
          platformAdAccountId: id,
          adAccount: {
            id: id,
            platformAccountId: `test-platform-account-id-${a}`,
            platformAccountName: `test-platform-account-name-${a}`,
          } as unknown as PlatformAdAccount,
          query: {} as unknown as AnalyticsDataExportQuery,
        }))
      : [],
    queryToMarkets: parameters.markets
      ? parameters.markets.map((m) => ({
          reportQueryId,
          isoCode: m,
          market: {
            isoCode: 'USD',
            name: 'United States',
          } as Market,
          query: {} as unknown as AnalyticsDataExportQuery,
        }))
      : [],
    queryToWorkspaces: parameters.workspaces
      ? parameters.workspaces.map((w) => ({
          reportQueryId,
          workspaceId: w,
          query: {} as unknown as AnalyticsDataExportQuery,
          workspace: { id: w, name: `test-workspace-${w}` } as Workspace,
        }))
      : [],
    queryToBrands: parameters.brands
      ? parameters.brands.map((b) => ({
          reportQueryId,
          brandId: b,
          brand: { id: b, name: `test-workspace-${b}` } as Brand,
          query: {} as unknown as AnalyticsDataExportQuery,
        }))
      : [],
    reportQueryId,
    reportQueryName,
    organizationId: randomUUID(),
    reportType,
    parameters,
    state: reportState,
    personId: 1,
    person: {
      id: 1,
      username: 'test-username',
      displayName: 'test-user',
    } as User,
    dataExports: [],
  };
};

export const createMockAnalyticsDataExportReport = (
  reportStatus: Status,
  reportState: State = State.ACTIVE,
  reportType: ReportType,
  hasBrands = false,
  hasMarkets = false,
  hasAdAccounts = false,
): AnalyticsDataExportReport => {
  const parameters: Parameters = buildMockParameter(
    hasBrands,
    hasMarkets,
    hasAdAccounts,
  );
  const queryId = randomUUID();
  const queryName = `query-name-${queryId}`;
  const query: AnalyticsDataExportQuery = buildMockQuery(
    queryId,
    queryName,
    reportState,
    reportType,
    parameters,
  );
  return buildMockReport(queryId, reportStatus, query);
};

export const createMockReadDataExportReportDto = (
  report: AnalyticsDataExportReport,
): ReadDataExportReportDto => {
  const partialDto: Partial<ReadDataExportReportDto> = {
    reportId: report.reportId,
    reportName: report.reportName,
    reportType: ReportType[report.dataExportQuery.reportType],
    status: Status[report.status],
    createdBy: {
      id: report.dataExportQuery.person.id.toString(),
      name: report.dataExportQuery.person.displayName,
    },
    dateCreated: formatDateToLocalDateString(report.dateCreated.toISOString()),
    startDate: report.dataExportQuery.parameters.startDate,
    endDate: report.dataExportQuery.parameters.endDate,
    workspaces: report.dataExportQuery.queryToWorkspaces.map(
      (w) =>
        ({
          id: w.workspace.id.toString(),
          name: w.workspace.name,
        } as IdAndName),
    ),
    channels: report.dataExportQuery.parameters.channels,
    brands: report.dataExportQuery.queryToBrands.map((b) => b.brand)
      ? report.dataExportQuery.queryToBrands.map(
          (b) => ({ id: b.brand.id, name: b.brand.name } as IdAndName),
        )
      : undefined,
    markets: report.dataExportQuery.queryToMarkets.map((m) => m.market)
      ? report.dataExportQuery.queryToMarkets
          .map((m) => m.market)
          .map((m) => ({ id: m.isoCode, name: m.name } as IdAndName))
      : undefined,
    adAccounts: report.dataExportQuery.queryToAdAccounts.map((a) => a.adAccount)
      ? report.dataExportQuery.queryToAdAccounts
          .map((a) => a.adAccount)
          .map((a) => ({
            id: a.platformAccountId,
            name: a.platformAccountName,
          }))
      : undefined,
  };
  return {
    ...partialDto,
    expiration: report.expirationTime
      ? formatDateToLocalDateString(report.expirationTime.toISOString())
      : undefined,
    failureReason: report.reportResults?.failureReason,
    recordCount: report.reportResults?.records,
    hasData:
      report.status == Status.COMPLETED
        ? !!report.reportResults?.records
        : undefined,
  } as ReadDataExportReportDto;
};
