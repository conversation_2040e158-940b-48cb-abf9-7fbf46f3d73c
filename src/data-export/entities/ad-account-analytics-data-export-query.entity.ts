import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { PlatformAdAccount } from '../../ad-accounts/entities/ad-account.entity';
import { AnalyticsDataExportQuery } from './analytics-data-export-query.entity';

@Entity('analytics_data_export_query_ad_account_map')
export class AdAccountAnalyticsDataExportQuery {
  @AutoMap()
  @PrimaryColumn({ name: 'report_query_id', type: 'uuid' })
  reportQueryId: string;

  @AutoMap()
  @PrimaryColumn({ name: 'platform_ad_account_id', type: 'bigint' })
  platformAdAccountId: number;

  @ManyToOne(() => AnalyticsDataExportQuery, (query) => query.queryToAdAccounts)
  @JoinColumn({ name: 'report_query_id' })
  query: AnalyticsDataExportQuery;

  @ManyToOne(() => PlatformAdAccount)
  @JoinColumn({ name: 'platform_ad_account_id' })
  adAccount: PlatformAdAccount;
}
