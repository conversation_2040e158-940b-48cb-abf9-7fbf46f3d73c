import { AutoMap } from '@automapper/classes';
import { IsDateString, IsEnum, IsString } from 'class-validator';
import {
  Column,
  <PERSON><PERSON><PERSON>,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Organization } from '../../organizations/entities/organization.entity';
import {
  DataExportConfigurationType,
  isDataExportConfigurationType,
} from '../data-export.type';
import { ValueTransformer } from 'typeorm/decorator/options/ValueTransformer';
import { User } from '../../entities/user.entity';
import { Type } from 'class-transformer';

/**
 * Transforms JSON data from mySQL table to a DataExportConfiguration object
 * @param value
 */
const DataConnectorConfigurationTransformer = (
  value: any,
): DataExportConfiguration => {
  if (!isDataExportConfigurationType(value)) {
    throw new Error(
      `Error when transforming value to Data Export Configuration: Data is not isDataExportConfigurationType. ${JSON.stringify(
        value,
      )}`,
    );
  }
  return <DataExportConfiguration>value;
};

export enum DataExportConnectorStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum DataExportConnectorDataService {
  S3 = 'S3',
}

export class DataExportConfiguration implements DataExportConfigurationType {
  @AutoMap()
  @IsEnum(DataExportConnectorDataService)
  service: DataExportConnectorDataService;

  @AutoMap()
  @IsString()
  roleArn: string;

  @AutoMap()
  @IsString()
  bucket: string;

  @AutoMap()
  @IsString()
  path: string;
}

/**
 * Objects that specifies where Data Export Reports will be uploaded.
 * Contains configuration to S3 buckets associated to either client's S3
 * bucket or Vidmob's bucket.
 */
@Entity('data_export_connector')
export class DataExportConnector {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid', {
    name: 'connector_id',
  })
  id: string;

  @AutoMap()
  @Column({
    type: 'char',
    name: 'organization_id',
    length: 36,
  })
  organizationId: string;

  @AutoMap()
  @Column({
    type: 'varchar',
    name: 'connector_name',
    length: 255,
  })
  connectorName: string;

  @AutoMap()
  @Column({
    type: 'json',
    name: 'connector_config',
    transformer: {
      to: DataConnectorConfigurationTransformer,
      from: DataConnectorConfigurationTransformer,
    },
  })
  @Type(() => DataExportConfiguration)
  s3Config: DataExportConfiguration;

  @AutoMap()
  @Column({ name: 'status', type: 'enum', enum: DataExportConnectorStatus })
  status: DataExportConnectorStatus;

  @AutoMap()
  @Column({ name: 'created_by', type: 'bigint' })
  createdBy: number;

  @AutoMap()
  @Column({
    name: 'date_created',
    type: 'datetime',
  })
  dateCreated: string;

  @AutoMap()
  @Column({
    name: 'last_updated',
    type: 'datetime',
  })
  lastUpdated: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by', referencedColumnName: 'id' })
  creator: User;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id', referencedColumnName: 'id' })
  organization: Organization;
}
