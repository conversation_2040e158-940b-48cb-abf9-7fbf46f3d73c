import { AutoMap } from '@automapper/classes';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AnalyticsDataExportReport } from './analytics-data-export-report.entity';
import { User } from '../../entities/user.entity';
import { WorkspaceAnalyticsDataExportQuery } from './workspace-analytics-data-export-query.entity';
import { AdAccountAnalyticsDataExportQuery } from './ad-account-analytics-data-export-query.entity';
import { MarketAnalyticsDataExportQuery } from './market-analytics-data-export-query.entity';
import { BrandAnalyticsDataExportQuery } from './brand-analytics-data-export-query.entity';

export enum ReportType {
  CREATIVE_SCORING = 'CREATIVE_SCORING',
  CREATIVE_ELEMENTS = 'CREATIVE_ELEMENTS',
  CREATIVE_ANALYTICS = 'CREATIVE_ANALYTICS',
}

export enum State {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export class Parameters {
  @AutoMap()
  startDate: string;
  @AutoMap()
  endDate: string;
  @AutoMap()
  workspaces: number[];
  @AutoMap()
  channels: string[];
  @AutoMap()
  brands?: string[];
  @AutoMap()
  markets?: string[];
  @AutoMap()
  adAccounts?: string[];
}

@Entity('analytics_data_export_query')
export class AnalyticsDataExportQuery {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid', { name: 'report_query_id' })
  reportQueryId: string;

  @AutoMap()
  @Column({ name: 'report_query_name', length: 255 })
  reportQueryName: string;

  @AutoMap()
  @Column({ name: 'organization_id', type: 'varchar', length: 36 })
  organizationId: string;

  @AutoMap()
  @Column({ name: 'report_type', length: 20 })
  reportType: string;

  @AutoMap()
  @Column({ type: 'json' })
  parameters: Parameters;

  @AutoMap()
  @Column({ length: 20 })
  state: string;

  @AutoMap()
  @Column({ name: 'person_id', type: 'bigint' })
  personId: number;

  @OneToMany(
    () => AnalyticsDataExportReport,
    (analyticsDataExportReport) => analyticsDataExportReport.dataExportQuery,
  )
  @JoinColumn({ name: 'report_query_id' })
  public dataExports: AnalyticsDataExportReport[];

  @OneToOne(() => User, (user) => user.id)
  @JoinColumn({ name: 'person_id' })
  person: User;

  @OneToMany(
    () => WorkspaceAnalyticsDataExportQuery,
    (workspaceMap) => workspaceMap.query,
  )
  @JoinColumn({ name: 'report_query_id' })
  queryToWorkspaces: WorkspaceAnalyticsDataExportQuery[];

  @OneToMany(
    () => MarketAnalyticsDataExportQuery,
    (marketMap) => marketMap.query,
  )
  @JoinColumn({ name: 'report_query_id' })
  queryToMarkets: MarketAnalyticsDataExportQuery[];

  @OneToMany(
    () => AdAccountAnalyticsDataExportQuery,
    (adAccountMap) => adAccountMap.query,
  )
  @JoinColumn({ name: 'report_query_id' })
  queryToAdAccounts: AdAccountAnalyticsDataExportQuery[];

  @OneToMany(() => BrandAnalyticsDataExportQuery, (brandMap) => brandMap.query)
  queryToBrands: BrandAnalyticsDataExportQuery[];
}
