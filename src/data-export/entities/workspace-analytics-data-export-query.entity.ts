import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { AnalyticsDataExportQuery } from './analytics-data-export-query.entity';
import { Workspace } from '../../workspaces/entities/workspace.entity';

@Entity('analytics_data_export_query_workspace_map')
export class WorkspaceAnalyticsDataExportQuery {
  @AutoMap()
  @PrimaryColumn({ name: 'report_query_id', type: 'uuid' })
  reportQueryId: string;

  @AutoMap()
  @PrimaryColumn({ name: 'partner_id' })
  workspaceId: number;

  @ManyToOne(() => AnalyticsDataExportQuery, (query) => query.queryToWorkspaces)
  @JoinColumn({ name: 'report_query_id' })
  query: AnalyticsDataExportQuery;

  @ManyToOne(() => Workspace)
  @JoinColumn({ name: 'partner_id' })
  workspace: Workspace;
}
