import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Market } from '../../markets/entities/market.entity';
import { AnalyticsDataExportQuery } from './analytics-data-export-query.entity';

@Entity('analytics_data_export_query_country_map')
export class MarketAnalyticsDataExportQuery {
  @AutoMap()
  @PrimaryColumn({ name: 'report_query_id', type: 'uuid' })
  reportQueryId: string;

  @AutoMap()
  @PrimaryColumn({ name: 'iso_code', type: 'char' })
  isoCode: string;

  @ManyToOne(() => AnalyticsDataExportQuery, (query) => query.queryToMarkets)
  @JoinColumn({ name: 'report_query_id' })
  query: AnalyticsDataExportQuery;

  @ManyToOne(() => Market)
  @JoinColumn({ name: 'iso_code' })
  market: Market;
}
