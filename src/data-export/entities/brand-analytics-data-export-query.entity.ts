import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Brand } from '../../brands/entities/brand.entity';
import { AnalyticsDataExportQuery } from './analytics-data-export-query.entity';

@Entity('analytics_data_export_query_brand_map')
export class BrandAnalyticsDataExportQuery {
  @AutoMap()
  @PrimaryColumn({ name: 'report_query_id', type: 'uuid' })
  reportQueryId: string;

  @AutoMap()
  @PrimaryColumn({ name: 'brand_id', type: 'uuid' })
  brandId: string;

  @ManyToOne(() => AnalyticsDataExportQuery, (query) => query.queryToBrands)
  @JoinColumn({ name: 'report_query_id' })
  query: AnalyticsDataExportQuery;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brand_id' })
  brand: Brand;
}
