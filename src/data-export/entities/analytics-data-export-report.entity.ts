import { AutoMap } from '@automapper/classes';
import {
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AnalyticsDataExportQuery } from './analytics-data-export-query.entity';

export enum Status {
  QUEUED = 'QUEUED',
  STARTED = 'STARTED',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  DELETED = 'DELETED',
  ARCHIVED = 'ARCHIVED',
}

export interface ReportResults {
  jobRunId?: number;
  s3Bucket?: string;
  s3Key?: string;
  records?: number;
  failureReason?: string;
}

@Entity('analytics_data_export_report')
export class AnalyticsDataExportReport {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid', { name: 'report_id' })
  reportId: string;

  @AutoMap()
  @Column({ name: 'report_name', length: 255 })
  reportName: string;

  @AutoMap()
  @Column({ name: 'report_query_id', type: 'varchar', length: 36 })
  reportQueryId: string;

  @AutoMap()
  @Column({ length: 20 })
  status: Status;

  @AutoMap()
  @Column({ name: 'report_results', type: 'json' })
  reportResults?: ReportResults;

  @AutoMap()
  @Column({
    name: 'date_created',
    type: 'datetime',
  })
  dateCreated: Date;

  @AutoMap()
  @Column({
    name: 'expiration_time',
    type: 'datetime',
  })
  expirationTime?: Date;

  @ManyToOne(() => AnalyticsDataExportQuery)
  @JoinColumn({ name: 'report_query_id' })
  dataExportQuery?: AnalyticsDataExportQuery;
}
