import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CreateDataExportRequestDto } from './dto/create-data-export-request.dto';
import { DataExportService } from './data-export.service';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { ReadDataExportReportDto } from './dto/read-data-export-report.dto';
import { DataExportFilterRequestDto } from './dto/data-export-filter-request.dto';
import { DataExportFilterValue } from './dto/data-export-filter-values.dto';
import { Status } from './entities/analytics-data-export-report.entity';
import { FilterSortByOptions } from './dto/read-data-export-filter-query.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';

@ApiTags('Data Export')
@Controller('organization/:organizationId/export')
export class DataExportController {
  constructor(private readonly dataExportService: DataExportService) {}

  @Get('filters')
  @VmApiOkResponse({ type: DataExportFilterValue })
  async getFilters(
    @Param('organizationId') organizationId: string,
    @Query() filterParams?: DataExportFilterRequestDto,
  ) {
    return this.dataExportService.getFilterValues(organizationId, filterParams);
  }

  @Post('report/filter')
  @VmApiOkPaginatedArrayResponse({
    type: ReadDataExportReportDto,
    isArray: true,
  })
  async getFilteredReports(
    @Param('organizationId') organizationId: string,
    @Body() filterSortByOptions: FilterSortByOptions,
    @GetPagination() options?: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadDataExportReportDto>> {
    return this.dataExportService.getFilterMetadata(
      organizationId,
      filterSortByOptions,
      options,
    );
  }

  @Get()
  @VmApiOkPaginatedArrayResponse({
    type: ReadDataExportReportDto,
    isArray: true,
  })
  async listDataExports(
    @Param('organizationId') organizationId: string,
    @GetPagination() options?: PaginationOptions,
  ) {
    return await this.dataExportService.getAllDataExportReportMetadata(
      organizationId,
      options,
    );
  }

  @Get('report/:reportId')
  @VmApiOkResponse({
    type: ReadDataExportReportDto,
  })
  async getDataExport(
    @Param('organizationId') organizationId: string,
    @Param('reportId') reportId: string,
  ) {
    return await this.dataExportService.getDataExportReport(
      organizationId,
      reportId,
    );
  }

  @Delete('report/:reportId')
  @VmApiOkResponse({
    type: ReadDataExportReportDto,
  })
  async deleteDataExport(
    @Param('organizationId') organizationId: string,
    @Param('reportId') reportId: string,
  ) {
    return await this.dataExportService.updateStatus(
      organizationId,
      reportId,
      Status.DELETED,
    );
  }

  @Post('user/:userId')
  async createDataExport(
    @Param('organizationId') organizationId: string,
    @Param('userId') userId: number,
    @Body()
    createDataExportRequestDto: CreateDataExportRequestDto,
  ) {
    return this.dataExportService.createDataExport(
      organizationId,
      userId,
      createDataExportRequestDto,
    );
  }
}
