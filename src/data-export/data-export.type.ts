import {
  AnalyticsDataExportReport,
  ReportResults,
  Status,
} from './entities/analytics-data-export-report.entity';
import {
  DataExportConfiguration,
  DataExportConnectorDataService,
  DataExportConnectorStatus,
} from './entities/data-export-connector.entity';
import { ConnectionStatus } from './data-connector/data-connector.types';

export type ReportStatusRecord = {
  [status in Status]: AnalyticsDataExportReport[];
};
export type ReportStatusRecordEntry = [Status, AnalyticsDataExportReport[]];

export interface DataExportTypeConfig {
  enabled: boolean;
  jobId: number;
}

export type DataExportTypesConfig = { [type: string]: DataExportTypeConfig };

export interface DataExportReportUpdate {
  reportId: string;
  status: Status;
  reportResults: ReportResults;
}

/**
 * Checks if object is of type DataExportConfigurationType
 * @param data
 */
export const isDataExportConfigurationType = (
  data: any,
): data is DataExportConfigurationType => {
  const hasService =
    'service' in data && data['service'] in DataExportConnectorDataService;
  const hasBucket = 'bucket' in data && data['bucket'].length > 0;
  const hasPath = 'path' in data && data['path'].length > 0;
  return hasService && hasPath && hasBucket;
};

export type DataExportConfigurationType = {
  service: DataExportConnectorDataService;
  roleArn: string;
  bucket: string;
  path: string;
};

export type DataExportConfigurationTestRequestType = {
  connectorName: string;
  s3Config: DataExportConfigurationType;
};

export type DataExportConnectionTestType = {
  connectorName: string;
  connectionStatus?: ConnectionStatus;
  failureMessage?: string;
};

export type SuccessfulDataConnectionResponse = {
  connectorId: string;
  connectorName: string;
  status: DataExportConnectorStatus;
  dateCreated: string;
  message: 'Data connector created successfully';
};
