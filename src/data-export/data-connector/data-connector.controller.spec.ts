import { Test, TestingModule } from '@nestjs/testing';
import { DataConnectorController } from './data-connector.controller';
import { DataConnectorService } from './data-connector.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataExportConnector } from '../entities/data-export-connector.entity';
import { Repository } from 'typeorm';
import { Organization } from '../../organizations/entities/organization.entity';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';

describe('DataConnectorController', () => {
  let controller: DataConnectorController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DataConnectorController],
      providers: [
        DataConnectorService,
        {
          provide: getRepositoryToken(DataExportConnector),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(Organization),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    controller = module.get<DataConnectorController>(DataConnectorController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
