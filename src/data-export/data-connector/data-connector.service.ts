import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataExportConnector } from '../entities/data-export-connector.entity';
import { Repository } from 'typeorm';
import { Organization } from '../../organizations/entities/organization.entity';
import { CreateDataExportConnectorDto } from '../dto/create-data-export-connector.dto';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import {
  DataConnectorTestRequestDto,
  DataExportConnectionTestResult,
} from '../dto/data-connector-test.dto';

@Injectable()
export class DataConnectorService {
  private readonly logger: Logger = new Logger(DataConnectorService.name);
  constructor(
    @InjectRepository(DataExportConnector)
    private readonly dataConnectorRepository: Repository<DataExportConnector>,
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
    @InjectMapper()
    private readonly mapper: Mapper,
  ) {}

  /**
   * TODO: Implement this method
   * Fetches all DataExportConnector by organization.
   * Throws an exception when organization does not exist.
   * @param organizationId
   * @returns Array<DataExportConnector>
   * @throws OrganiziatonDoesNotExist
   */
  async getAllByOrg(organizationId: string): Promise<DataExportConnector[]> {
    return;
  }

  /**
   * TODO: Implement this method
   * Test Vidmob connection to client's S3 bucket.
   * @param input
   */
  async testClientConnection(
    input: DataConnectorTestRequestDto,
  ): Promise<DataExportConnectionTestResult> {
    return;
  }

  /**
   * Create a data export connector for a client.
   * Throws an exception when organization does not exist.
   * @param userId
   * @param organizationId
   * @param data
   * @returns Array<DataExportConnector>
   * @throws OrganiziatonDoesNotExist
   */
  async create(
    userId: number,
    organizationId: string,
    data: CreateDataExportConnectorDto,
  ): Promise<DataExportConnector> {
    const organization: Organization | null =
      await this.organizationRepository.findOneBy({ id: organizationId });
    if (!organization) {
      throw new NotFoundException(
        `Trying to retrieve Data Export Connectors for an organization that does not exist ${organizationId}`,
      );
    }
    const connector: DataExportConnector = this.mapper.map(
      data,
      CreateDataExportConnectorDto,
      DataExportConnector,
      { extraArgs: () => ({ organizationId, userId }) },
    );
    return await this.dataConnectorRepository.save(connector);
  }
}
