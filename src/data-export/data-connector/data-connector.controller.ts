import {
  Body,
  Controller,
  Logger,
  NotFoundException,
  Param,
  Post,
} from '@nestjs/common';
import { DataConnectorService } from './data-connector.service';
import { ErrorResponse, VmApiOkResponse } from '@vidmob/vidmob-nestjs-common';
import {
  CreateDataExportConnectorDto,
  SuccessfulCreateDataExportConnectorResponseDto,
} from '../dto/create-data-export-connector.dto';
import { DataExportConnectorStatus } from '../entities/data-export-connector.entity';
import { formatDateToLocalDateString } from '../../common/utils/helper';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Data Export Connector')
@Controller('organization/:organizationId/data-connector')
export class DataConnectorController {
  private readonly logger: Logger = new Logger(DataConnectorController.name);
  constructor(private readonly dataConnectorService: DataConnectorService) {}

  @Post('/:userId')
  @VmApiOkResponse({ type: SuccessfulCreateDataExportConnectorResponseDto })
  async createNewDataConnectorForOrganization(
    @Param('userId') userId: number,
    @Param('organizationId') organizationId: string,
    @Body() input: CreateDataExportConnectorDto,
  ): Promise<SuccessfulCreateDataExportConnectorResponseDto | ErrorResponse> {
    try {
      const connector = await this.dataConnectorService.create(
        userId,
        organizationId,
        input,
      );
      const connectorId: string = connector.id;
      const connectorName: string = connector.connectorName;
      const status: DataExportConnectorStatus = connector.status;
      const dateCreated: string = formatDateToLocalDateString(
        connector.dateCreated,
      );

      return {
        connectorId,
        connectorName,
        status,
        dateCreated,
        message: 'Data connector created successfully',
      };
    } catch (e) {
      this.logger.error(
        `Error occurred when making Data Connector ${JSON.stringify(e)}`,
      );
      if (e instanceof NotFoundException) {
        throw e;
      }
      return new ErrorResponse({
        httpStatusCode: 500,
        traceId: `${userId}_${organizationId}`,
        error: e,
      });
    }
  }
}
