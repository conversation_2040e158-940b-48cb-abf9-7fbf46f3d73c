import { Test, TestingModule } from '@nestjs/testing';
import { DataConnectorService } from './data-connector.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DataExportConnector } from '../entities/data-export-connector.entity';
import { Organization } from '../../organizations/entities/organization.entity';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';

describe('DataConnectorService', () => {
  let service: DataConnectorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataConnectorService,
        {
          provide: getRepositoryToken(DataExportConnector),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(Organization),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    service = module.get<DataConnectorService>(DataConnectorService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
