import { Test, TestingModule } from '@nestjs/testing';
import { RoleService } from './role.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Role } from 'src/entities/role.entity';
import { In, Repository } from 'typeorm';

const TEST_ROLE_ID = 1;

const workspaceRole: Role = {
  id: TEST_ROLE_ID,
  name: 'test role',
  type: 'business_entity',
  identifier: 'TEST_ROLE',
  description: 'just a test role',
};

describe('RoleService', () => {
  let service: RoleService;
  let roleRepository: Repository<Role>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleService,
        {
          provide: getRepositoryToken(Role),
          useValue: {
            findOneBy: jest.fn(),
            findBy: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<RoleService>(RoleService);
    roleRepository = module.get<Repository<Role>>(getRepositoryToken(Role));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findRoles', () => {
    afterEach(() => jest.restoreAllMocks());

    it('should return a promise of roles', () => {
      const roleIds = [1, 2, 3];

      const findByMock = jest
        .spyOn(roleRepository, 'findBy')
        .mockResolvedValueOnce([new Role(), new Role()]);
      service.findRoles(roleIds);

      expect(findByMock).toBeCalledWith({ id: In(roleIds) });
    });
  });

  describe('isRoleValidWorkspaceRole', () => {
    afterEach(() => jest.restoreAllMocks());

    it('it should return true when it finds the role', async () => {
      jest
        .spyOn(roleRepository, 'findOneBy')
        .mockResolvedValueOnce(workspaceRole);

      await expect(
        service.isRoleValidWorkspaceRole(TEST_ROLE_ID),
      ).resolves.toBe(true);
    });

    it('it should return false when it doesnt find the role', async () => {
      jest.spyOn(roleRepository, 'findOneBy').mockResolvedValueOnce(null);

      await expect(service.isRoleValidWorkspaceRole(1234)).resolves.toBe(false);
    });
  });

  describe('findWorkspaceRoleById', () => {
    afterEach(() => jest.restoreAllMocks());

    it('should return the workspace role', async () => {
      jest
        .spyOn(roleRepository, 'findOneBy')
        .mockResolvedValueOnce(workspaceRole);

      await expect(service.findWorkspaceRoleById(TEST_ROLE_ID)).resolves.toBe(
        workspaceRole,
      );
    });
  });

  describe('isRoleIdWorkspaceStandardRole', () => {
    it('should return true if the role id is a workspace standard', async () => {
      jest.spyOn(roleRepository, 'findOneBy').mockResolvedValueOnce(new Role());

      await expect(
        service.isRoleIdWorkspaceStandardRole(TEST_ROLE_ID),
      ).resolves.toBeTruthy();
    });

    it('should return false if the role id is not a workspace standard', async () => {
      jest.spyOn(roleRepository, 'findOneBy').mockResolvedValueOnce(null);

      await expect(
        service.isRoleIdWorkspaceStandardRole(TEST_ROLE_ID),
      ).resolves.toBeFalsy();
    });
  });
});
