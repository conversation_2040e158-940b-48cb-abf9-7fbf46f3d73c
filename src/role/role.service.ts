import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  OrganizationUserRoles,
  RoleType,
  WORKSPACE_USER_ROLE,
} from 'src/common/constants/constants';
import { Role } from 'src/entities/role.entity';
import { In, Repository } from 'typeorm';

@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  findRoles(roleIds: number[]): Promise<Role[]> {
    return this.roleRepository.findBy({
      id: In(roleIds),
    });
  }

  async isRoleIdWorkspaceStandardRole(roleId: number): Promise<boolean> {
    const role = await this.roleRepository.findOneBy({
      id: roleId,
      type: RoleType.WORKSPACE_ROLE_TYPE,
      identifier: WORKSPACE_USER_ROLE.STANDARD,
    });

    return role !== null;
  }

  async isRoleValidWorkspaceRole(roleId: number) {
    const role = await this.findWorkspaceRoleById(roleId);

    return role !== null;
  }

  async findOrganizationRoleByIdentifier(identifier: OrganizationUserRoles) {
    return await this.findRoleByIdentifierAndType(
      identifier,
      RoleType.ORGANIZATION_ROLE_TYPE,
    );
  }

  async findWorkspaceRoleById(roleId: number): Promise<Role> {
    return await this.roleRepository.findOneBy({
      id: roleId,
      type: RoleType.WORKSPACE_ROLE_TYPE,
    });
  }

  async findRoleByIdentifierAndType(
    identifier: OrganizationUserRoles,
    roleType: RoleType,
  ) {
    return await this.roleRepository.findOneBy({
      identifier,
      type: roleType,
    });
  }
}
