import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import {
  API_DESCRIPTION,
  API_TITLE,
  API_VERSION,
} from './common/constants/api.constants';
import { INestApplication } from '@nestjs/common';
import {
  ErrorResponse,
  PaginatedSuccessResponse,
  SuccessResponse,
} from '@vidmob/vidmob-nestjs-common';

export const getSwaggerDoc = (app: INestApplication) => {
  const config = new DocumentBuilder()
    .addBearerAuth()
    .setTitle(API_TITLE)
    .setDescription(API_DESCRIPTION)
    .setVersion(API_VERSION)
    .build();
  return SwaggerModule.createDocument(app, config, {
    extraModels: [SuccessResponse, PaginatedSuccessResponse, ErrorResponse],
    operationIdFactory: (_controllerKey: string, methodKey: string) =>
      methodKey,
  });
};
