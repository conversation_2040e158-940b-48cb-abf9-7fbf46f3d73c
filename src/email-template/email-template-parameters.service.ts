import { Injectable } from '@nestjs/common';
import { TemplateMessageDto } from '@vidmob/vidmob-soa-notification-service-sdk';

// this interface will be moved to nestJs common library
interface IEmailTemplateToOptions {
  to: string;
  toName?: string;
  cc?: string;
  bcc?: string;
}

// this interface will be moved to nestJs common library
interface IEmailTemplate {
  mandrillTemplateIdentifier: string;
  subjectLine: string;
  preheader?: string;
}

// this enum will be moved to nestJs common library
enum COMPONENTS_PROPERTY {
  title = 'TITLE',
  body = 'BODY',
  button = 'BUTTON',
  buttonUrl = 'BUTTON_URL',
  mediaUrl = 'MEDIA_URL',
  tableType = 'TABLE_TYPE',
  footnote = 'FOOTNOTE',
  callToAction = 'CALL_TO_ACTION',
  callToActionUrl = 'CALL_TO_ACTION_URL',
}

// this interface will be moved to nestJs common library
interface IEmailTemplateComponent {
  identifier: string;
  title?: string;
  body?: string;
  button?: string;
  buttonUrl?: string;
  mediaUrl?: string;
  tableType?: string;
  footnote?: string;
  callToAction?: string;
  callToActionUrl?: string;
}

@Injectable()
// This whole service will be moved to NestJs common library to provide common functionality for emails
export class EmailTemplateParametersService {
  constructor() {}

  // Builds the whole template at once, useful when you're delivering only one email
  buildTemplate(
    template: IEmailTemplate,
    components: IEmailTemplateComponent[],
    id: string,
    deliverOptions: IEmailTemplateToOptions,
    parameters: Object,
  ) {
    let templateObj = this.buildEmptyTemplate();

    templateObj = this.buildTemplateId(template, templateObj);
    templateObj = this.buildSubject(template, templateObj, parameters);
    templateObj = this.buildGlobalVars(
      template,
      components,
      templateObj,
      parameters,
    );
    templateObj = this.buildRecipient(deliverOptions, templateObj);
    templateObj = this.buildId(id, templateObj);

    return templateObj;
  }

  // Initializes an empty template message
  buildEmptyTemplate(): TemplateMessageDto {
    return {
      templateId: null,
      id: null,
      subject: null,
      to: null,
    };
  }

  // Builds partially the template Id only, useful when building dynamically multiple differents templates and use the same template base
  buildTemplateId(
    template: IEmailTemplate,
    templateObj: TemplateMessageDto,
  ): TemplateMessageDto {
    const templateId = template.mandrillTemplateIdentifier;

    return {
      ...templateObj,
      templateId,
    };
  }

  // Builds partially the subject only, useful when building dynamically multiple differents templates and use the same template base
  buildSubject(
    template: IEmailTemplate,
    templateObj: TemplateMessageDto,
    parameters: Object,
  ): TemplateMessageDto {
    const subject = this.mergeStringWithVariables(
      template.subjectLine,
      parameters,
    );

    return {
      ...templateObj,
      subject,
    };
  }

  // Builds partially the global vars components, useful when building dynamically multiple differents templates and use the same template base
  buildGlobalVars(
    template: IEmailTemplate,
    components: IEmailTemplateComponent[],
    templateObj: TemplateMessageDto,
    parameters: Object,
  ): TemplateMessageDto {
    const globalVars = this.buildGlobalComponents(components, parameters);

    if (template.preheader.length > 0) {
      globalVars['MC_PREVIEW_TEXT'] = this.mergeStringWithVariables(
        template.preheader,
        parameters,
      );
    }

    return {
      ...templateObj,
      globalVars,
    };
  }

  // Builds partially the recipient of the email, useful when building dynamically multiple differents templates and use the same template base
  buildRecipient(
    deliverOptions: IEmailTemplateToOptions,
    templateObj: TemplateMessageDto,
  ): TemplateMessageDto {
    const to = this.buildDeliverTo(deliverOptions);

    return {
      ...templateObj,
      to,
    };
  }

  // Builds partially the template message id, useful when building dynamically multiple differents templates and use the same template base
  buildId(id: string, templateObj: TemplateMessageDto): TemplateMessageDto {
    return {
      ...templateObj,
      id,
    };
  }

  // This will do lazy template literals interpolation
  // @text: "<p><b>${client.firstName} ${client.lastName}</b>"
  // @parameters: { client: { firstName: "John", lastName: "Doe" }, ... }
  mergeStringWithVariables(text: string, parameters: Object): string {
    const lazyTemplateLiteralsFn = new Function(
      'parameters',
      [
        'const tagged = ( ' + Object.keys(parameters).join(', ') + ' ) =>',
        '`' + text + '`',
        'return tagged(...Object.values(parameters))',
      ].join('\n'),
    );

    return lazyTemplateLiteralsFn(parameters);
  }

  private buildGlobalComponents(
    components: IEmailTemplateComponent[],
    parameters: Object,
  ) {
    let globalVarsComponents = {};

    components.forEach((component) => {
      let content = {};

      content = Object.keys(COMPONENTS_PROPERTY).reduce((acc, property) => {
        const componentProperty = component[property];
        if (this.isPropertyNotEmpty(componentProperty)) {
          acc[COMPONENTS_PROPERTY[property]] = this.mergeStringWithVariables(
            componentProperty,
            parameters,
          );
        }

        return acc;
      }, {});

      globalVarsComponents[component.identifier] = content;
    });

    return {
      COMPONENTS: globalVarsComponents,
    };
  }

  private isPropertyNotEmpty(text: string | null | undefined) {
    return text?.length > 0;
  }

  private buildDeliverTo(deliverOptions: IEmailTemplateToOptions) {
    let toOptions = [];
    if (deliverOptions.to && deliverOptions.toName)
      toOptions.push({ email: deliverOptions.to, name: deliverOptions.toName });

    if (deliverOptions.to && !deliverOptions.toName)
      toOptions.push({ type: 'to', email: deliverOptions.to });

    if (deliverOptions.cc)
      toOptions.push({ type: 'cc', email: deliverOptions.cc });
    if (deliverOptions.bcc)
      toOptions.push({ type: 'bcc', email: deliverOptions.bcc });

    return toOptions;
  }
}
