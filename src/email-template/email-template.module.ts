import { Module } from '@nestjs/common';
import { EmailTemplateService } from './email-template.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmailTemplate } from './entities/email-template.entity';
import { EmailTemplateComponent } from './entities/email-template-component.entity';
import { EmailTemplateParametersService } from './email-template-parameters.service';
import { InvitationLinkModule } from 'src/invitation-link/invitation-link.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([EmailTemplate, EmailTemplateComponent]),
    InvitationLinkModule,
  ],
  exports: [EmailTemplateService, EmailTemplateParametersService],
  providers: [EmailTemplateService, EmailTemplateParametersService],
})
export class EmailTemplateModule {}
