import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EmailTemplate } from './email-template.entity';

@Entity({ name: 'email_template_component' })
export class EmailTemplateComponent {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ name: 'identifier', type: 'varchar', length: 255, nullable: true })
  identifier: string;

  @Column({ name: 'version', type: 'bigint', nullable: false })
  version: number;

  @Column({ name: 'email_template_id', type: 'bigint', nullable: false })
  @ManyToOne(() => EmailTemplate, (emailTemplate) => emailTemplate.id)
  @JoinColumn({ name: 'email_template_id' })
  emailTemplateId: number;

  @Column({
    name: 'supports_white_label',
    type: 'tinyint',
    default: 0,
    nullable: false,
  })
  supportsWhiteLabel: number;

  @Column({ name: 'media_url', type: 'longtext', nullable: true })
  mediaUrl: string;

  @Column({
    name: 'requires_user',
    type: 'tinyint',
    default: 0,
    nullable: false,
  })
  requiresUser: number;

  @Column({
    name: 'requires_date',
    type: 'tinyint',
    default: 0,
    nullable: false,
  })
  requiresDate: number;

  @Column({ name: 'call_to_action', type: 'longtext', nullable: true })
  callToAction: string;

  @Column({ name: 'call_to_action_url', type: 'longtext', nullable: true })
  callToActionUrl: string;

  @Column({ name: 'title', type: 'longtext', nullable: true })
  title: string;

  @Column({ name: 'body', type: 'longtext', nullable: true })
  body: string;

  @Column({ name: 'button', type: 'longtext', nullable: true })
  button: string;

  @Column({ name: 'button_url', type: 'longtext', nullable: true })
  buttonUrl: string;

  @Column({ name: 'footnote', type: 'longtext', nullable: true })
  footnote: string;

  @Column({ name: 'table_type', type: 'varchar', length: 24, nullable: true })
  tableType: string;
}
