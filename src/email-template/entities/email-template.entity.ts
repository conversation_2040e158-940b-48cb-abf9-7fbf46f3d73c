import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { EmailTemplateComponent } from './email-template-component.entity';

@Entity({ name: 'email_template' })
export class EmailTemplate {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ name: 'version', type: 'bigint', nullable: false })
  version: number;

  @Column({ name: 'identifier', type: 'varchar', length: 255, nullable: false })
  identifier: string;

  @Column({
    name: 'mandrill_template_identifier',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  mandrillTemplateIdentifier: string;

  @Column({ name: 'subject_line', type: 'longtext', nullable: false })
  subjectLine: string;

  @Column({ name: 'preheader', type: 'longtext', nullable: true })
  preheader: string;

  @Column({
    name: 'header_type',
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  headerType: string;

  @Column({ name: 'show_social', type: 'tinyint', default: 0, nullable: false })
  showSocial: number;

  @OneToMany(
    () => EmailTemplateComponent,
    (emailTemplateComponent) => emailTemplateComponent.emailTemplateId,
  )
  emailTemplateComponents: EmailTemplateComponent[];
}
