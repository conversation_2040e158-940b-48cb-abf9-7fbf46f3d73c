import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EmailTemplate } from './entities/email-template.entity';
import { Repository } from 'typeorm';
import { DefaultService as NotificationServiceSdk } from '@vidmob/vidmob-soa-notification-service-sdk';

import { EmailTemplateParametersService } from './email-template-parameters.service';
import { OrganizationInvite } from 'src/organization-invite/entities/organization-invite.entity';
import {
  EMAIL_IDENTIFIERS,
  INVITATION_TYPE,
  SHARE_CHANNEL,
} from 'src/common/constants/constants';
import { User } from 'src/entities/user.entity';
import { InvitationLinkService } from 'src/invitation-link/invitation-link.service';
import { CreateOrganizationInviteWithIdDto } from 'src/organization-invite/dto/create-organization-invite-with-id.dto';
import { removeClickableUrlsFromString } from 'src/common/utils/helper';

@Injectable()
export class EmailTemplateService {
  constructor(
    @InjectRepository(EmailTemplate)
    private emailTemplateRepository: Repository<EmailTemplate>,
    private readonly emailTemplateParametersService: EmailTemplateParametersService,
    private readonly notificationServiceSdk: NotificationServiceSdk,
    private readonly invitationLinkService: InvitationLinkService,
  ) {}

  // fetch a template and its components from db
  async getTemplateAndComponents(templateIdentifier: string) {
    return this.emailTemplateRepository
      .createQueryBuilder('emailTemplate')
      .leftJoinAndSelect(
        'emailTemplate.emailTemplateComponents',
        'emailTemplateComponents',
      )
      .where('emailTemplate.identifier = :templateIdentifier', {
        templateIdentifier,
      })
      .getOne();
  }

  async dispatchOrganizationInvites(
    organizationInvites: CreateOrganizationInviteWithIdDto[],
    inviterUser: User,
    organizationName: string,
  ) {
    const emailTemplate = await this.getTemplateAndComponents(
      EMAIL_IDENTIFIERS.ORGANIZATION_INVITE,
    );

    const baseParameters = this.sanitizeEmailParameters(inviterUser, organizationName);

    await Promise.all(
      organizationInvites.map(async (organizationInvite) => {
        const inviteUrl = await this.getOrganizationInviteUrl(
          organizationInvite,
          organizationName,
        );

        const parameters = {
          ...baseParameters,
          joinTeamUrl: inviteUrl,
        };

        const organizationInvitePayload =
          this.emailTemplateParametersService.buildTemplate(
            emailTemplate,
            emailTemplate.emailTemplateComponents,
            organizationInvite.id,
            { to: organizationInvite.email },
            parameters,
          );

        await this.notificationServiceSdk.enqueueMessageAsPromise(
          organizationInvitePayload,
        );
      }),
    );
  }

  async getOrganizationInviteUrl(
    organizationInvite: CreateOrganizationInviteWithIdDto,
    organizationName: string,
  ) {
    const title = 'Organization Invite';
    const description = `Click to join ${organizationName}`;
    const branchIoUrl = await this.invitationLinkService.createUrl(
      {
        organizationId: organizationInvite.organizationId,
        code: organizationInvite.inviteCode,
      },
      INVITATION_TYPE.ORGANIZATION_INVITE,
      SHARE_CHANNEL.OTHER,
      { title, description },
    );

    return branchIoUrl + `?validationCode=${organizationInvite.validationCode}`;
  }

  private sanitizeEmailParameters(
    inviterUser: User,
    organizationName: string,
  ) {
    const firstName = removeClickableUrlsFromString(inviterUser.firstName);
    const lastName = removeClickableUrlsFromString(inviterUser.lastName);
    const companyName = removeClickableUrlsFromString(organizationName);

    return {
      client: {
        firstName,
        lastName
      },
      companyName
    }
  }
}
