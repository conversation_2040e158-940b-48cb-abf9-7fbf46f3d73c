import { Test, TestingModule } from '@nestjs/testing';
import { EmailTemplateService } from './email-template.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EmailTemplate } from './entities/email-template.entity';
import { EmailTemplateParametersService } from './email-template-parameters.service';
import { DefaultService as NotificationServiceSdk } from '@vidmob/vidmob-soa-notification-service-sdk';
import { User } from 'src/entities/user.entity';
import { OrganizationInvite } from 'src/organization-invite/entities/organization-invite.entity';
import { Repository } from 'typeorm';
import { InvitationLinkService } from 'src/invitation-link/invitation-link.service';

const emailTemplateComponents = [
  {
    identifier: 'FIRST',
    title: 'Join your company!',
    body: "<p><b>${client.firstName} ${client.lastName}</b> has invited you to join <b>${companyName}</b>. That means you're about to have some fun.</p>",
    button: null,
    buttonUrl: null,
    mediaUrl: null,
    tableType: null,
    footnote: null,
    callToAction: null,
    callToActionUrl: null,
  },
  {
    identifier: 'SECOND',
    title: null,
    body: null,
    button: 'ACCEPT INVITATION',
    buttonUrl: '${joinTeamUrl}',
    mediaUrl: 'someS3Link.com',
    tableType: 'TEAMMATE_TABLE',
    footnote: 'Some footnote as test',
    callToAction: 'ACCEPT INVITATION',
    callToActionUrl: '${joinTeamUrl}',
  },
];

const emailTemplate = {
  mandrillTemplateIdentifier: 'User Invite',
  subjectLine: "You're Invited: Join ${companyName}.",
  preheader: "Let's start making some magic.",
  emailTemplateComponents,
};

const createQueryBuilder: any = {
  leftJoinAndSelect: () => createQueryBuilder,
  where: () => createQueryBuilder,
  getOne: () => jest.fn(),
};

describe('EmailTemplateService', () => {
  let service: EmailTemplateService;
  let notificationServiceSdk: NotificationServiceSdk;
  let emailTemplateRepository: Repository<EmailTemplate>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailTemplateService,
        EmailTemplateParametersService,
        {
          provide: getRepositoryToken(EmailTemplate),
          useValue: {
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: NotificationServiceSdk,
          useValue: {
            enqueueMessageAsPromise: jest.fn(),
          },
        },
        {
          provide: InvitationLinkService,
          useValue: {
            createUrl: jest
              .fn()
              .mockImplementation(() => 'https://bt.vidmob.io/'),
          },
        },
      ],
    }).compile();

    service = module.get<EmailTemplateService>(EmailTemplateService);
    notificationServiceSdk = module.get<NotificationServiceSdk>(
      NotificationServiceSdk,
    );
    emailTemplateRepository = module.get<Repository<EmailTemplate>>(
      getRepositoryToken(EmailTemplate),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('dispatchOrganizationInvites', () => {
    beforeEach(() => {
      jest
        .spyOn(emailTemplateRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      jest
        .spyOn(createQueryBuilder, 'getOne')
        .mockResolvedValueOnce(emailTemplate);
    });

    const organizationInvite = {
      id: 'yyyyy',
      email: '<EMAIL>',
    } as OrganizationInvite;

    const inviter = {
      firstName: 'John',
      lastName: 'Doe',
    } as User;

    it('should dispatch the email template to notification service', async () => {
      const spySendMessageToSqsAsPromise = jest.spyOn(
        notificationServiceSdk,
        'enqueueMessageAsPromise',
      );
      await service.dispatchOrganizationInvites(
        [organizationInvite],
        inviter,
        'Test Org!',
      );

      expect(spySendMessageToSqsAsPromise).toBeCalledTimes(1);
    });
  });
});
