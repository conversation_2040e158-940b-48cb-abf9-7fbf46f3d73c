import { Test, TestingModule } from '@nestjs/testing';
import { EmailTemplateParametersService } from './email-template-parameters.service';

describe('EmailTemplateParametersService', () => {
  let service: EmailTemplateParametersService;

  const emailTemplate = {
    mandrillTemplateIdentifier: 'User Invite',
    subjectLine: 'Test subject line with ${subjectTest}',
    preheader: "Let's start making some magic.",
  };

  const emailTemplateComponents = [
    {
      identifier: 'FIRST',
      title: 'Join your company!',
      body: "<p><b>${client.firstName} ${client.lastName}</b> has invited you to join <b>${companyName}</b>. That means you're about to have some fun.</p>",
    },
    {
      identifier: 'SECOND',
      button: 'ACCEPT INVITATION',
      buttonUrl: '${joinTeamUrl}',
      mediaUrl: 'someS3Link.com',
      tableType: 'TEAMMATE_TABLE',
      footnote: 'Some footnote as test',
      callToAction: 'ACCEPT INVITATION',
      callToActionUrl: '${joinTeamUrl}',
    },
  ];

  const emptyTemplateMessage = {
    templateId: null,
    id: null,
    subject: null,
    to: null,
  };

  const emailParameters = {
    subjectTest: 'lazy load interpolation',
    client: {
      firstName: 'John',
      lastName: 'Doe',
    },
    companyName: 'Test Org',
    joinTeamUrl: 'https://acs-dev.vidmob.com',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [EmailTemplateParametersService],
    }).compile();

    service = module.get<EmailTemplateParametersService>(
      EmailTemplateParametersService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('buildTemplate', () => {
    it('should build the whole template message at once', () => {
      expect(
        service.buildTemplate(
          emailTemplate,
          emailTemplateComponents,
          'xxxx',
          { to: '<EMAIL>' },
          emailParameters,
        ),
      ).toStrictEqual({
        templateId: 'User Invite',
        id: 'xxxx',
        subject: 'Test subject line with lazy load interpolation',
        to: [{ email: '<EMAIL>', type: 'to' }],
        globalVars: {
          COMPONENTS: {
            FIRST: {
              BODY: "<p><b>John Doe</b> has invited you to join <b>Test Org</b>. That means you're about to have some fun.</p>",
              TITLE: 'Join your company!',
            },
            SECOND: {
              BUTTON: 'ACCEPT INVITATION',
              BUTTON_URL: 'https://acs-dev.vidmob.com',
              CALL_TO_ACTION: 'ACCEPT INVITATION',
              CALL_TO_ACTION_URL: 'https://acs-dev.vidmob.com',
              FOOTNOTE: 'Some footnote as test',
              MEDIA_URL: 'someS3Link.com',
              TABLE_TYPE: 'TEAMMATE_TABLE',
            },
          },
          MC_PREVIEW_TEXT: "Let's start making some magic.",
        },
      });
    });
  });

  describe('buildEmptyTemplate', () => {
    it('returns am empty template message dto', () => {
      expect(service.buildEmptyTemplate()).toStrictEqual(emptyTemplateMessage);
    });
  });

  describe('buildTemplateId', () => {
    it('should add template id to template message', () => {
      expect(
        service.buildTemplateId(emailTemplate, emptyTemplateMessage),
      ).toStrictEqual({
        templateId: 'User Invite',
        id: null,
        subject: null,
        to: null,
      });
    });
  });

  describe('buildSubject', () => {
    it('should add the subject to template message', () => {
      expect(
        service.buildSubject(
          emailTemplate,
          emptyTemplateMessage,
          emailParameters,
        ),
      ).toStrictEqual({
        templateId: null,
        id: null,
        subject: 'Test subject line with lazy load interpolation',
        to: null,
      });
    });
  });

  describe('buildGlobalVars', () => {
    it('should build the globar vars components for the template', () => {
      expect(
        service.buildGlobalVars(
          emailTemplate,
          emailTemplateComponents,
          emptyTemplateMessage,
          emailParameters,
        ),
      ).toStrictEqual({
        templateId: null,
        id: null,
        subject: null,
        to: null,
        globalVars: {
          COMPONENTS: {
            FIRST: {
              BODY: "<p><b>John Doe</b> has invited you to join <b>Test Org</b>. That means you're about to have some fun.</p>",
              TITLE: 'Join your company!',
            },
            SECOND: {
              BUTTON: 'ACCEPT INVITATION',
              BUTTON_URL: 'https://acs-dev.vidmob.com',
              CALL_TO_ACTION: 'ACCEPT INVITATION',
              CALL_TO_ACTION_URL: 'https://acs-dev.vidmob.com',
              FOOTNOTE: 'Some footnote as test',
              MEDIA_URL: 'someS3Link.com',
              TABLE_TYPE: 'TEAMMATE_TABLE',
            },
          },
          MC_PREVIEW_TEXT: "Let's start making some magic.",
        },
      });
    });
  });

  describe('buildRecipient', () => {
    it('should add the email of recipient to template message', () => {
      expect(
        service.buildRecipient({ to: '<EMAIL>' }, emptyTemplateMessage),
      ).toStrictEqual({
        templateId: null,
        id: null,
        subject: null,
        to: [{ email: '<EMAIL>', type: 'to' }],
      });
    });

    it('should add the email of recipient with name to template message', () => {
      expect(
        service.buildRecipient(
          { to: '<EMAIL>', toName: 'Test' },
          emptyTemplateMessage,
        ),
      ).toStrictEqual({
        templateId: null,
        id: null,
        subject: null,
        to: [{ email: '<EMAIL>', name: 'Test' }],
      });
    });

    it('should add the email of recipient with name and cc to template message', () => {
      expect(
        service.buildRecipient(
          { to: '<EMAIL>', toName: 'Test', cc: '<EMAIL>' },
          emptyTemplateMessage,
        ),
      ).toStrictEqual({
        templateId: null,
        id: null,
        subject: null,
        to: [
          { email: '<EMAIL>', name: 'Test' },
          { email: '<EMAIL>', type: 'cc' },
        ],
      });
    });

    it('should add the email of recipient with name and cc and bcc to template message', () => {
      expect(
        service.buildRecipient(
          {
            to: '<EMAIL>',
            toName: 'Test',
            cc: '<EMAIL>',
            bcc: '<EMAIL>',
          },
          emptyTemplateMessage,
        ),
      ).toStrictEqual({
        templateId: null,
        id: null,
        subject: null,
        to: [
          { email: '<EMAIL>', name: 'Test' },
          { email: '<EMAIL>', type: 'cc' },
          { email: '<EMAIL>', type: 'bcc' },
        ],
      });
    });
  });

  describe('buildId', () => {
    it('should add the id to template message', () => {
      expect(service.buildId('xxxx', emptyTemplateMessage)).toStrictEqual({
        templateId: null,
        id: 'xxxx',
        subject: null,
        to: null,
      });
    });
  });

  describe('mergeStringWithVariables', () => {
    it('should do lazy template literals interpolation', () => {
      const text = '<p><b>${client.firstName} ${client.lastName}</b>';
      const parameters = {
        client: { firstName: 'John', lastName: 'Doe' },
        companyName: 'Test Org',
      };

      expect(service.mergeStringWithVariables(text, parameters)).toBe(
        '<p><b>John Doe</b>',
      );
    });
  });
});
