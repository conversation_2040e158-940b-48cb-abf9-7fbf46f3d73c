import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>al,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>ested,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateWorkspaceDto } from '../../workspaces/dto/create-workspace.dto';
import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { DEFAULT_SESSION_IDLE_TIMEOUT_MIN } from '../utils/constants';

export class CreateOrganizationDto {
  /**
   * Name of the Organization
   * @example "VidMob"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  name: string;

  /**
   * Status of the Organization
   * @example "ENABLED"
   */
  @AutoMap()
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  status?: string;

  /**
   * ACS session idle timeout in minutes
   * @example 60
   */
  @AutoMap()
  @ApiProperty({
    required: false,
    description: 'ACS session idle timeout in minutes',
    default: DEFAULT_SESSION_IDLE_TIMEOUT_MIN,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  sessionIdleTimeoutMin?: number;

  /**
   * List of workspaces connected to the Organization
   */
  @AutoMap(() => CreateWorkspaceDto)
  @ApiProperty({
    required: false,
  })
  @ValidateNested({ each: true })
  @Type(() => CreateWorkspaceDto)
  workspaces?: CreateWorkspaceDto[];
}
