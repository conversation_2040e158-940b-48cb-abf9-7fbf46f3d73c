import {
  IsBoolean,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
} from 'class-validator';

export class ValidateOrganizationWorkspacesRequestDto {
  /**
   * The workspace ids to validate user access to ad accounts within them
   * @example [84748, 94882]
   */
  @IsNotEmpty()
  @IsNumber({}, { each: true })
  workspaceIds: number[];
}

export class ValidateOrganizationAdAccountsRequestDto {
  /**
   * (Optional) The workspace ids to validate user access to ad accounts within them
   * @example [84748, 94882]
   */
  @IsOptional()
  @IsNumber({}, { each: true })
  workspaceIds?: number[];

  /**
   * The ad account ids to validate within the organization
   * @example ['****************', 's9sq99e80io39i82']
   */
  @IsNotEmpty()
  @IsString({ each: true })
  adAccountIds: string[];
}

export class ValidateOrganizationEntitiesResponseDto {
  @IsNotEmpty()
  @IsBoolean()
  success: boolean;

  @IsNotEmpty()
  @IsString()
  message: string;
}
