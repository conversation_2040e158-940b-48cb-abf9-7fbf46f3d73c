import { CreateOrganizationDto } from './create-organization.dto';
import { ApiExtraModels, PartialType } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min } from 'class-validator';
import { AutoMap } from '@automapper/classes';

@ApiExtraModels(CreateOrganizationDto)
export class UpdateOrganizationDto extends PartialType(CreateOrganizationDto) {
  /**
   * Status of the Organization
   */
  @AutoMap()
  @IsOptional()
  @IsString()
  status: string;

  /**
   * ACS session timeout in minutes
   */
  @AutoMap()
  @IsOptional()
  @IsNumber()
  @Min(1)
  sessionTimeoutMin?: number;
}
