import { AutoMap } from '@automapper/classes';
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { FeatureWhitelist } from '../../workspaces/entities/feature-whitelist.entity';
import { Organization } from './organization.entity';

@Entity('feature_whitelist_organization')
export class OrganizationFeatureWhitelist {
  @AutoMap()
  @Column({ name: 'feature_whitelist_id', primary: true })
  featureWhitelistId: number;

  @AutoMap()
  @Column({ name: 'organization_id', primary: true })
  organizationId: string;

  @AutoMap()
  @Column({ name: 'date_created' })
  dateCreated: Date;

  @ManyToOne(() => FeatureWhitelist, (f) => f.id)
  @JoinColumn({
    name: 'feature_whitelist_id',
    referencedColumnName: 'id',
    foreignKeyConstraintName: 'feature_whitelist_organization_ibfk_1',
  })
  featureWhitelists: FeatureWhitelist;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;
}
