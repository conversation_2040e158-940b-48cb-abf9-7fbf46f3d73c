import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Workspace } from '../../workspaces/entities/workspace.entity';
import { UserOrganizationMap } from '../organization-user/entities/user-organization-map.entity';
import { OrganizationUserRole } from '../organization-user/entities/organization-user-role.entity';
import { OrganizationFeatureWhitelist } from './organization-feature-whitelist.entity';
import { DEFAULT_SESSION_TIMEOUT_MIN } from '../utils/constants';

@Entity()
export class Organization {
  /**
   * System assigned Id of the Organization
   */
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Name of the Organization
   */
  @AutoMap()
  @Column({ length: 255 })
  name: string;

  /**
   * Status of the Organization
   */
  @AutoMap()
  @Column({ length: 50 })
  status: string;

  /**
   * Creation date of the Org. This is of the format YYYY-MM-DD
   */
  @AutoMap()
  @CreateDateColumn({ name: 'date_created' })
  dateCreated: Date;

  /**
   * Updated date of the Org
   */
  @AutoMap()
  @UpdateDateColumn({ name: 'last_updated' })
  lastUpdated: Date;

  /**
   * Is Personal Organization
   */
  @AutoMap()
  @Column({ name: 'is_personal' })
  isPersonal: boolean;

  /**
   * ACS session timeout in minutes
   */
  @AutoMap()
  @Column({ name: 'session_timeout_min', default: DEFAULT_SESSION_TIMEOUT_MIN })
  sessionTimeoutMin: number;

  /**
   * List of workspaces connected to the Organization
   */
  @AutoMap()
  @OneToMany(() => Workspace, (workspace) => workspace.organization)
  workspaces: Workspace[];

  @OneToMany(
    () => OrganizationUserRole,
    (organizationUserRole) => organizationUserRole.organization,
  )
  organizationUserRoles?: OrganizationUserRole[];

  @OneToMany(
    () => UserOrganizationMap,
    (userOrganizationMap) => userOrganizationMap.organization,
  )
  userOrganizationMaps?: UserOrganizationMap[];

  @OneToMany(
    () => OrganizationFeatureWhitelist,
    (OrganizationFeatureWhitelist) => OrganizationFeatureWhitelist.organization,
  )
  organizationFeatureWhitelists?: OrganizationFeatureWhitelist[];
}
