import { forwardRef, Module } from '@nestjs/common';
import { OrganizationsService } from './organizations.service';
import { OrganizationsController } from './organizations.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Organization } from './entities/organization.entity';
import { Workspace } from '../workspaces/entities/workspace.entity';
import { OrganizationProfile } from './mapper/organization.profile';
import { SplitAndTrimPipe } from './utils/split-and-trim-pipe';
import { OrganizationPlatformAdAccountMap } from '../ad-accounts/entities/organization-platform-ad-account-map.entity';
import { PlatformAdAccountToWorkspace } from '../ad-accounts/entities/ad-account-workspace-map.entity';
import { PlatformAdAccount } from '../ad-accounts/entities/ad-account.entity';
import { Brand } from '../brands/entities/brand.entity';
import { Brand as OrgBrand } from '../organizations/brand/entities/brand.entity';
import { Market } from '../markets/entities/market.entity';
import { WorkspaceMarket } from '../workspaces/entities/workspace-market.entity';
import { WorkspaceBrand } from '../workspaces/entities/workspace-brand.entity';
import { AccountType } from '../workspaces/entities/account-type.entity';
import { FeatureAccountType } from '../workspaces/entities/feature-account-type.entity';
import { FeatureWorkspace } from '../workspaces/entities/feature-workspace.entity';
import { WorkspaceIndustry } from '../workspaces/entities/partner-industry.entity';
import { OrganizationUserModule } from './organization-user/organization-user.module';
import { OrganizationAdAccountUserPermissions } from '../ad-accounts/entities/organization-ad-account-user-permissions.entity';
import { WorkspaceManager } from '../workspaces/entities/workspace-manager.entity';
import { WorkspaceUser } from '../workspaces/entities/workspace-user.entity';
import { BrandController } from './brand/controllers/brand.controller';
import { BrandIdentifierController } from './brand/brand-identifier/controllers/brand-identifier.controller';
import { BrandService as OrgBrandService } from './brand/services/brand.service';
import { WorkspaceBrandMap } from '../workspaces/brand/entities/workspace-brand-map.entity';
import { BrandIdentifier } from './brand/brand-identifier/entities/brand-identifier.entity';
import { BrandIdentifierService } from './brand/brand-identifier/services/brand-identifier.service';
import { BrandIdentifierProfile } from './brand/brand-identifier/mappers/brand-identifier.profile';
import { BrandProfile } from './brand/mappers/brand.profile';
import { PlatformAdAccountBrandMap } from '../ad-accounts/entities/platform-ad-account-brand-map.entity';
import { PlatformAdAccountMarketMap } from '../ad-accounts/entities/platform-ad-account-market-map.entity';
import { ProductAccountType } from '../workspaces/entities/product-account-type.entity';
import { IndustryModule } from '../industry/industry.module';
import { OrganizationFeatureWhitelist } from './entities/organization-feature-whitelist.entity';
import { PlatformAdAccountIndustryMap } from '../ad-accounts/entities/platform-ad-account-industry-map.entity';
import { PlatformAdAccountSequentialFailure } from '../ad-accounts/entities/platform-ad-account-sequential-failure.entity';
import { PlatformAdAccountImportStatus } from '../ad-accounts/entities/platform-ad-account-import-status.entity';
import { FeatureWhitelist } from '../workspaces/entities/feature-whitelist.entity';
import { WorkspacesModule } from '../workspaces/workspaces.module';
import { AdAccountsModule } from 'src/ad-accounts/ad-accounts.module';
import { BrandModule } from 'src/brands/brand.module';
import { MarketModule } from 'src/markets/market.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Organization,
      OrganizationFeatureWhitelist,
      Workspace,
      WorkspaceUser,
      WorkspaceMarket,
      WorkspaceBrand,
      AccountType,
      WorkspaceIndustry,
      WorkspaceManager,
      FeatureWhitelist,
      FeatureAccountType,
      FeatureWorkspace,
      OrganizationPlatformAdAccountMap,
      OrganizationAdAccountUserPermissions,
      PlatformAdAccountToWorkspace,
      PlatformAdAccountImportStatus,
      PlatformAdAccount,
      Brand,
      Market,
      OrgBrand,
      WorkspaceBrandMap,
      BrandIdentifier,
      PlatformAdAccountBrandMap,
      PlatformAdAccountMarketMap,
      PlatformAdAccountIndustryMap,
      ProductAccountType,
      PlatformAdAccountSequentialFailure,
    ]),
    OrganizationUserModule,
    IndustryModule,
    BrandModule,
    MarketModule,
    forwardRef(() => AdAccountsModule),
    forwardRef(() => WorkspacesModule),
  ],
  controllers: [
    OrganizationsController,
    BrandController,
    BrandIdentifierController,
  ],
  providers: [
    OrganizationsService,
    OrganizationProfile,
    SplitAndTrimPipe,
    OrgBrandService,
    BrandProfile,
    BrandIdentifierService,
    BrandIdentifierProfile,
  ],
  exports: [OrganizationsService],
})
export class OrganizationsModule {}
