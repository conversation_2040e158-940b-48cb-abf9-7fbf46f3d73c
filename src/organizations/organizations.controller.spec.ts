import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationsController } from './organizations.controller';
import { OrganizationsService } from './organizations.service';
import { WorkspaceService } from '../workspaces/workspaces.service';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { Organization } from './entities/organization.entity';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { SearchParamsDto } from 'src/workspaces/dto/search-params.dto';
import { Permission, Platform } from '../common/constants/constants';
import { AdAccountsService } from '../ad-accounts/ad-accounts.service';
import { BadRequestException } from '@nestjs/common';
import { OrganizationAdAccountsService } from '../ad-accounts/organization-ad-accounts.service';
import {
  CreateAdAccountWorkspaceMapDto,
  WorkspaceAdAccountConnectionStatus,
} from '../ad-accounts/dto/create-ad-account-workspace-map.dto';
import { BrandService } from 'src/brands/brand.service';
import { Brand } from 'src/brands/entities/brand.entity';
import { CreateBulkAdAccountBrandMapDto } from 'src/ad-accounts/dto/create-bulk-ad-account-brand-map.dto';
import { CreateBulkAdAccountMarketMapDto } from 'src/ad-accounts/dto/create-bulk-ad-account-market-map.dto';
import { ReadAdAccountMapDto } from 'src/ad-accounts/dto/read-ad-account-map.dto';
import { ReadAdAccountBrandsDto } from 'src/ad-accounts/dto/read-ad-account-brands.dto';
import { ReadAdAccountMarketsDto } from 'src/ad-accounts/dto/read-ad-accounts-markets.dto';
import { IndustryService } from '../industry/industry.service';
import { OrganizationFeatureWhitelist } from './entities/organization-feature-whitelist.entity';
import { PlatformAdAccountIndustryMap } from '../ad-accounts/entities/platform-ad-account-industry-map.entity';
import { CreateIndustryAdAccountsRequestDto } from '../ad-accounts/dto/create-industry-ad-accounts-request.dto';
import { IndustryDTO } from '../industry/dto/industry.dto';

const TEST_ORG_ID = '4ae86248-e475-11ed-afee-122fed7a5c7f';
const TEST_WORKSPACE_ID = 9876;
const TEST_USER_ID = 1234;
const TEST_ACCOUNT_ID = 'test_account_123';
const TEST_PLATFORM = 'facebook';

describe('OrganizationsController', () => {
  let controller: OrganizationsController;
  let organizationsService: OrganizationsService;
  let workspaceService: WorkspaceService;
  let adAccountService: AdAccountsService;
  let brandService: BrandService;
  let organizationAdAccountsService: OrganizationAdAccountsService;
  let industryService: IndustryService;
  const PLATFORM_AD_ACCOUNT_INDUSTRY_MAPPINGS: PlatformAdAccountIndustryMap[] =
    [
      {
        platformAdAccountId: 1,
        platformAdAccount: {
          platformAccountId: '123',
        },
      } as unknown as PlatformAdAccountIndustryMap,
      {
        platformAdAccountId: 2,
        platformAdAccount: {
          platformAccountId: '234',
        },
      } as unknown as PlatformAdAccountIndustryMap,
    ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrganizationsController],
      providers: [
        OrganizationsService,
        {
          provide: BrandService,
          useFactory: () => ({
            isAllBrandsAssociatedWithOrganizationId: jest
              .fn()
              .mockReturnValue(true),
          }),
        },
        {
          provide: WorkspaceService,
          useFactory: () => ({
            getWorkspacesByOrganizationIdAndSearch: jest.fn(),
            linkWorkspaceToAdAccounts: jest.fn(),
            connectPlatformAdAccountsToAWorkspace: jest.fn(),
            disconnectPlatformAdAccountsToAWorkspace: jest.fn(),
            arePlatformAdAccountsAbleToMapToAnOrganizationWorkspace: jest.fn(),
            verifyWorkspacesBelongToAnOrganization: jest.fn(),
            getAllLightweight: jest.fn(),
            getWorkspaceEntity: jest.fn(),
          }),
        },
        {
          provide: getRepositoryToken(Organization),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(OrganizationFeatureWhitelist),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
        {
          provide: AdAccountsService,
          useFactory: () => ({
            getConnectedAdAccountsForAnOrganization: jest.fn(),
          }),
        },
        {
          provide: OrganizationAdAccountsService,
          useFactory: () => ({
            mapAdAccountToOrganization: jest.fn(),
            getUserAccountsWithPermissionsInOrganization: jest.fn(),
            getWorkspaceAdAccountsListForHealthDashBoard: jest.fn(),
            getOrganizationAdAccountsListForHealthDashBoard: jest.fn(),
            saveBulkMapBetweenAdAccountsAndBrands: jest.fn(),
            saveBulkMapBetweenAdAccountsAndMarkets: jest.fn(),
            getOrganizationAdAccountBrands: jest.fn(),
            getOrganizationAdAccountMarkets: jest.fn(),
            getPlatformAdAccountsIndustryGroups: jest.fn(),
            getPlatformAdAccountsIndustriesByIndustryGroup: jest.fn(),
            getPlatformAdAccountsSubIndustriesByIndustry: jest.fn(),
            getPlatformAdAccountsIndustryMappings: jest.fn(),
            filterNewAccountsToBeAddedToPlatformAdAccountIndustry: jest.fn(),
            assignIndustryGroupToPlatformAdAccounts: jest.fn(),
            updatePlatformAdAccountIndustryData: jest.fn(),
            removeIndustryEntityFromPlatformAdAccounts: jest.fn(),
          }),
        },
        {
          provide: getRepositoryToken(Brand),
          useClass: Repository,
        },
        {
          provide: IndustryService,
          useFactory: () => ({
            getIndustries: jest.fn(),
            getIndustryGroups: jest.fn(),
            getSubIndustries: jest.fn(),
          }),
        },
      ],
    }).compile();

    controller = module.get<OrganizationsController>(OrganizationsController);
    organizationsService =
      module.get<OrganizationsService>(OrganizationsService);
    workspaceService = module.get<WorkspaceService>(WorkspaceService);
    adAccountService = module.get<AdAccountsService>(AdAccountsService);
    organizationAdAccountsService = module.get<OrganizationAdAccountsService>(
      OrganizationAdAccountsService,
    );
    brandService = module.get<BrandService>(BrandService);
    industryService = module.get<IndustryService>(IndustryService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findWorkspacesByOrganization', () => {
    it('should call workspaceService.getWorkspacesByOrganizationIdAndSearch with correct parameters', async () => {
      // Mock the workspaceService.getWorkspacesByOrganizationIdAndSearch method
      const getWorkspacesByOrganizationIdAndSearchSpy = jest.spyOn(
        workspaceService,
        'getWorkspacesByOrganizationIdAndSearch',
      );
      const expectedPaginationOptions: PaginationOptions = {}; // Replace with your expected pagination options
      const expectedId = TEST_ORG_ID;
      const expectedSearchParams = new SearchParamsDto(
        'yourSearch',
        'fra,bra,usa',
      );

      // Invoke the function
      await controller.findWorkspacesByOrganization(
        expectedId,
        expectedPaginationOptions,
        expectedSearchParams,
      );

      // Verify that the workspaceService.getWorkspacesByOrganizationIdAndSearch method was called with the correct arguments
      expect(getWorkspacesByOrganizationIdAndSearchSpy).toHaveBeenCalledWith(
        expectedId,
        expectedPaginationOptions,
        expectedSearchParams,
      );
    });

    it('should return the expected workspaces', async () => {
      // Mock the workspaceService.getWorkspacesByOrganizationIdAndSearch method
      const expectedWorkspaces: any = [
        {
          id: 12345,
          name: 'John Biz 1',
          isPrimary: null,
          organizationId: TEST_ORG_ID,
          totalUsers: 1,
          markets: ['usa', 'bra'],
          users: [
            {
              id: 9876,
              username: '<EMAIL>',
            },
          ],
        },
        {
          id: 54321,
          name: 'John 2',
          isPrimary: null,
          organizationId: TEST_ORG_ID,
          totalUsers: 1,
          markets: ['fra'],
          users: [
            {
              id: 8765,
              username: '<EMAIL>',
            },
          ],
        },
      ];
      jest
        .spyOn(workspaceService, 'getWorkspacesByOrganizationIdAndSearch')
        .mockResolvedValue(expectedWorkspaces);

      const expectedPaginationOptions: PaginationOptions = {}; // Replace with your expected pagination options
      const expectedId = 'yourId';
      const expectedSearchParams = new SearchParamsDto(
        'yourSearch',
        'col,bra,usa',
      );

      // Invoke the function and verify the result
      const result = await controller.findWorkspacesByOrganization(
        expectedId,
        expectedPaginationOptions,
        expectedSearchParams,
      );
      expect(result).toEqual(expectedWorkspaces);
    });
  });

  describe('configureOrganizationWorkspaceAdAccounts', () => {
    const mockCreateDto: CreateAdAccountWorkspaceMapDto = {
      platformAccountList: [
        {
          platformAccountId: '*********',
          platform: Platform.PLATFORM_FACEBOOK,
        },
      ],
      status: WorkspaceAdAccountConnectionStatus.CONNECTED,
    };
    it('should call workspaceService.mapAdAccountsToAWorkspace if status is CONNECTED', async () => {
      adAccountService.getPlatformAdAccountIdFromPlatformAndPlatformId = jest
        .fn()
        .mockResolvedValueOnce(12345);
      jest
        .spyOn(
          workspaceService,
          'arePlatformAdAccountsAbleToMapToAnOrganizationWorkspace',
        )
        .mockImplementation(jest.fn());
      jest
        .spyOn(workspaceService, 'connectPlatformAdAccountsToAWorkspace')
        .mockResolvedValueOnce(
          'Platform Ad Accounts successfully connected to workspace 1. Number of mappings created: 1',
        );
      const results =
        await controller.configureOrganizationWorkspaceAdAccountConnections(
          54321,
          mockCreateDto,
        );
      expect(results).toEqual(
        'Platform Ad Accounts successfully connected to workspace 1. Number of mappings created: 1',
      );
    });

    it('should call workspaceService.deletePlatformAdAccountFromWorkspace if status is DISCONNECTED', async () => {
      adAccountService.getPlatformAdAccountIdFromPlatformAndPlatformId = jest
        .fn()
        .mockResolvedValueOnce(12345);
      mockCreateDto.status = WorkspaceAdAccountConnectionStatus.DISCONNECTED;
      jest
        .spyOn(workspaceService, 'disconnectPlatformAdAccountsToAWorkspace')
        .mockResolvedValueOnce(
          'Platform Ad Accounts successfully disconnected from workspace 1. Number of mappings deleted: 1',
        );
      const results =
        await controller.configureOrganizationWorkspaceAdAccountConnections(
          54321,
          mockCreateDto,
        );
      expect(results).toEqual(
        'Platform Ad Accounts successfully disconnected from workspace 1. Number of mappings deleted: 1',
      );
    });

    it('should throw exception if dto does not contain a list of ad accounts', async () => {
      mockCreateDto.platformAccountList = [];
      controller
        .configureOrganizationWorkspaceAdAccountConnections(
          54321,
          mockCreateDto,
        )
        .catch((error) => expect(error).toBeInstanceOf(BadRequestException));
    });
  });

  describe('mapAdAccountToOrganization', () => {
    it('should call organizationAdAccountsService.mapAdAccountToOrganization with correct parameters', async () => {
      const mapAdAccountToOrganizationSpy = jest.spyOn(
        organizationAdAccountsService,
        'mapAdAccountToOrganization',
      );
      const createOrganizationPlatformAdAccountDto = {
        userId: TEST_USER_ID,
        platformAccountId: TEST_ACCOUNT_ID,
        permission: Permission.ALLOW,
      };
      await controller.mapAdAccountToOrganization(
        TEST_ORG_ID,
        createOrganizationPlatformAdAccountDto,
      );
      expect(mapAdAccountToOrganizationSpy).toHaveBeenCalledWith(
        TEST_ORG_ID,
        createOrganizationPlatformAdAccountDto,
      );
    });
  });

  describe('verifyWorkspacesByIds', () => {
    it('should call workspaceService.verifyWorkspacesByIds with correct parameters', async () => {
      const verifyWorkspacesByIdsSpy = jest
        .spyOn(workspaceService, 'verifyWorkspacesBelongToAnOrganization')
        .mockResolvedValue([123, 456]);
      const dto = { workspaceIds: [123, 456] };
      const expected = await controller.verifyWorkspacesByIds('test_id', dto);
      expect(verifyWorkspacesByIdsSpy).toHaveBeenCalledWith(
        'test_id',
        [123, 456],
      );
      expect(expected.map((map) => map.id)).toEqual([123, 456]);
    });
    it('should throw an error if workspaceService.verifyWorkspacesByIds throws an error', async () => {
      jest
        .spyOn(workspaceService, 'verifyWorkspacesBelongToAnOrganization')
        .mockRejectedValue(new BadRequestException('test'));
      const dto = { workspaceIds: [123, 456] };
      await expect(
        controller.verifyWorkspacesByIds('test_id', dto),
      ).rejects.toThrow(BadRequestException);
    });
    it('should throw an error if the list of workspaces from user is empty', async () => {
      const dto = { workspaceIds: [] };
      await expect(
        controller.verifyWorkspacesByIds('test_id', dto),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('getUserAccountPermissionsInOrganization', () => {
    it('should call organizationAdAccountsService.getUserAccountPermissionsInOrganization with correct parameters', async () => {
      const getUserAccountPermissionsInOrganizationSpy = jest.spyOn(
        organizationAdAccountsService,
        'getUserAccountsWithPermissionsInOrganization',
      );
      await controller.getUserAccountPermissionsInOrganization(
        TEST_ORG_ID,
        TEST_PLATFORM,
        TEST_USER_ID,
      );
      expect(getUserAccountPermissionsInOrganizationSpy).toHaveBeenCalledWith(
        TEST_ORG_ID,
        TEST_PLATFORM,
        TEST_USER_ID,
      );
    });
  });

  describe('test getWorkspaceAdAccountsListForHealthDashBoard method', () => {
    it('should call organizationAdAccountService.getWorkspaceAdAccountsListForHealthDashBoard with correct parameters', async () => {
      const getWorkspaceAdAccountsListForHealthDashBoardSpy = jest.spyOn(
        organizationAdAccountsService,
        'getWorkspaceAdAccountsListForHealthDashBoard',
      );
      const paginationOptions: PaginationOptions = {};
      const searchParamsDto: SearchParamsDto = new SearchParamsDto();
      await controller.getWorkspaceAdAccountsListForHealthDashBoard(
        TEST_ORG_ID,
        TEST_WORKSPACE_ID,
        TEST_USER_ID,
        paginationOptions,
        searchParamsDto,
      );
      expect(
        getWorkspaceAdAccountsListForHealthDashBoardSpy,
      ).toHaveBeenCalledWith(
        TEST_ORG_ID,
        TEST_WORKSPACE_ID,
        TEST_USER_ID,
        paginationOptions,
        searchParamsDto,
      );
    });
  });

  describe('test getOrganizationAdAccountsListForHealthDashBoard method', () => {
    it('should call organizationAdAccountService.getOrganizationAdAccountsListForHealthDashBoard with correct parameters', async () => {
      const getOrganizationAdAccountsListForHealthDashBoardSpy = jest.spyOn(
        organizationAdAccountsService,
        'getOrganizationAdAccountsListForHealthDashBoard',
      );
      const paginationOptions: PaginationOptions = {};
      const searchParamsDto: SearchParamsDto = new SearchParamsDto();
      await controller.getOrganizationAdAccountsListForHealthDashBoard(
        TEST_ORG_ID,
        TEST_USER_ID,
        paginationOptions,
        searchParamsDto,
      );
      expect(
        getOrganizationAdAccountsListForHealthDashBoardSpy,
      ).toHaveBeenCalledWith(
        TEST_ORG_ID,
        TEST_USER_ID,
        paginationOptions,
        searchParamsDto,
      );
    });
  });

  describe('createBulkMapBetweenAdAccountAndBrands', () => {
    afterEach(() => jest.clearAllMocks());
    it('returns a message indicating that 3 maps were created', async () => {
      const dto: CreateBulkAdAccountBrandMapDto = {
        accounts: ['act_123'],
        selected_brands: ['123', '456'],
        unselected_brands: null,
      };
      const response: ReadAdAccountMapDto = {
        message:
          'Selected brands successfully added/removed from to/from 2 ad account(s).',
      };

      jest
        .spyOn(
          organizationAdAccountsService,
          'saveBulkMapBetweenAdAccountsAndBrands',
        )
        .mockResolvedValueOnce(response);

      await expect(
        controller.createBulkMapBetweenAdAccountAndBrands(
          TEST_ORG_ID,
          TEST_WORKSPACE_ID,
          dto,
        ),
      ).resolves.toEqual(response);
    });
  });

  describe('createBulkMapBetweenAdAccountAndMarkets', () => {
    afterEach(() => jest.clearAllMocks());
    it('returns a message indicating that 3 maps were created', async () => {
      const dto: CreateBulkAdAccountMarketMapDto = {
        accounts: ['act_123'],
        selected_markets: ['usa', 'bra'],
        unselected_markets: null,
      };

      const response: ReadAdAccountMapDto = {
        message:
          'Selected markets successfully added/removed from to/from 1 ad account(s).',
      };

      jest
        .spyOn(
          organizationAdAccountsService,
          'saveBulkMapBetweenAdAccountsAndMarkets',
        )
        .mockResolvedValueOnce(response);

      await expect(
        controller.createBulkMapBetweenAdAccountAndMarkets(
          TEST_ORG_ID,
          TEST_WORKSPACE_ID,
          dto,
        ),
      ).resolves.toEqual(response);
    });
  });

  describe('getOrganizationAdAccountBrands', () => {
    afterEach(() => jest.clearAllMocks());

    it('returns a message with an array of brands associated to the ad account', async () => {
      const response: ReadAdAccountBrandsDto = {
        brands: [
          {
            id: 'xxxxx-xxxx-xxxx',
            name: 'brand 1',
          },
          {
            id: 'yyyy-yyyyy-yyy',
            name: 'brand 2',
          },
        ],
      } as ReadAdAccountBrandsDto;

      jest
        .spyOn(organizationAdAccountsService, 'getOrganizationAdAccountBrands')
        .mockResolvedValueOnce(response);

      await expect(
        controller.getOrganizationAdAccountBrands(TEST_ACCOUNT_ID),
      ).resolves.toEqual(response);
    });
  });

  describe('getOrganizationAdAccountMarkets', () => {
    afterEach(() => jest.clearAllMocks());

    it('returns a message with an array of markets associated to the ad account', async () => {
      const response = {
        markets: [
          {
            isoCode: 'usa',
            name: 'United States',
          },
          {
            isoCode: 'bra',
            name: 'Brazil',
          },
        ],
      } as ReadAdAccountMarketsDto;

      jest
        .spyOn(organizationAdAccountsService, 'getOrganizationAdAccountMarkets')
        .mockResolvedValueOnce(response);

      await expect(
        controller.getOrganizationAdAccountMarkets(TEST_ACCOUNT_ID),
      ).resolves.toEqual(response);
    });
  });

  describe('getAllLightWeightWorkspaces', () => {
    afterEach(() => jest.clearAllMocks());

    it('should return a list of workspace with only id and name', async () => {
      const lightWeightWorkspaces = [
        {
          id: 1,
          name: 'Test 1',
        },
        {
          id: 2,
          name: 'Test 2',
        },
        {
          id: 3,
          name: 'Test 3',
        },
      ];

      jest
        .spyOn(workspaceService, 'getAllLightweight')
        .mockResolvedValueOnce(lightWeightWorkspaces);

      await expect(
        controller.getAllLightWeightWorkspaces(TEST_ORG_ID),
      ).resolves.toEqual(lightWeightWorkspaces);
    });
  });

  describe('validateAdAccountIndustryAssociation method', () => {
    afterEach(() => jest.clearAllMocks());

    it('should throw if both selected and unselected industry entities are provided', () => {
      expect(() =>
        controller.validateAdAccountIndustryAssociation(
          [],
          1,
          [2],
          IndustryService.INDUSTRY_GROUP_ENTITY,
          [1, 2],
        ),
      ).toThrow(
        new BadRequestException(
          `Assigning and un-assigning ${IndustryService.INDUSTRY_GROUP_ENTITY} ids from ad accounts is not allowed in same request.`,
        ),
      );
    });

    it('should throw if neither selected nor unselected industry entities are provided', () => {
      expect(() =>
        controller.validateAdAccountIndustryAssociation(
          [],
          0,
          [],
          IndustryService.INDUSTRY_GROUP_ENTITY,
          [],
        ),
      ).toThrow(
        new BadRequestException(
          `${IndustryService.INDUSTRY_GROUP_ENTITY} Id must be provided to map ${IndustryService.INDUSTRY_GROUP_ENTITY} ids to ad accounts`,
        ),
      );
    });

    it('should throw if selected industry entity is not in entityIds', () => {
      expect(() =>
        controller.validateAdAccountIndustryAssociation(
          [],
          1,
          [],
          IndustryService.INDUSTRY_GROUP_ENTITY,
          [2, 3],
        ),
      ).toThrow(
        new BadRequestException(
          `No ${IndustryService.INDUSTRY_GROUP_ENTITY} found with id: 1.`,
        ),
      );
    });

    it('should pass if selected industry entity has no other industry associations', () => {
      const platformAdAccountIndustryMaps: [string, number][] = [['123', 1]];

      expect(() =>
        controller.validateAdAccountIndustryAssociation(
          platformAdAccountIndustryMaps,
          1,
          [],
          IndustryService.INDUSTRY_GROUP_ENTITY,
          [1],
        ),
      ).not.toThrow();
    });

    it('should pass if unselected industry entity has associated ad accounts', () => {
      const platformAdAccountIndustryMaps: [string, number][] = [['123', 1]];

      expect(() =>
        controller.validateAdAccountIndustryAssociation(
          platformAdAccountIndustryMaps,
          0,
          [1],
          IndustryService.INDUSTRY_GROUP_ENTITY,
          [1],
        ),
      ).not.toThrow();
    });
  });

  describe('mapIndustryGroupToBulkAdAccounts method', () => {
    const platformAdAccountIndustryMap: [string, number][] = [['123', 1]];
    beforeEach(() => {
      jest
        .spyOn(
          organizationAdAccountsService,
          'getPlatformAdAccountsIndustryGroups',
        )
        .mockResolvedValueOnce(platformAdAccountIndustryMap);
      jest
        .spyOn(industryService, 'getIndustryGroups')
        .mockResolvedValueOnce([{ id: 1 } as unknown as IndustryDTO]);
      jest
        .spyOn(
          organizationAdAccountsService,
          'filterNewAccountsToBeAddedToPlatformAdAccountIndustry',
        )
        .mockReturnValueOnce(['1', '2', '3']);
    });
    afterEach(() => jest.clearAllMocks());

    it('should pass if unselected industry group has associated ad accounts', async () => {
      await expect(
        controller.mapIndustryGroupToBulkAdAccounts('1234', TEST_USER_ID, {
          accountIds: [],
        }),
      ).rejects.toThrow(
        new BadRequestException(
          'List of platform ad account ids must be provided',
        ),
      );
    });

    it('should assign industry group to ad accounts if selectedIndustryGroupId is provided', async () => {
      const dto = {
        accountIds: ['1', '2', '3'],
        selectedIndustryGroupId: 1,
      } as unknown as CreateIndustryAdAccountsRequestDto;
      const result = await controller.mapIndustryGroupToBulkAdAccounts(
        '12345',
        TEST_USER_ID,
        dto,
      );

      expect(
        organizationAdAccountsService.assignIndustryGroupToPlatformAdAccounts,
      ).toHaveBeenCalledWith(
        TEST_USER_ID,
        ['1', '2', '3'],
        dto.selectedIndustryGroupId,
      );
      expect(result).toEqual({
        message: `Industry group - ${dto.selectedIndustryGroupId} assigned to 3 ad accounts successfully`,
      });
    });

    it('should remove industry group from ad accounts if unselectedIndustryGroupId is provided', async () => {
      const dto = {
        accountIds: ['1', '2', '3'],
        unselectedIndustryGroupIds: [1],
      } as unknown as CreateIndustryAdAccountsRequestDto;

      const result = await controller.mapIndustryGroupToBulkAdAccounts(
        '12345',
        TEST_USER_ID,
        dto,
      );

      expect(
        organizationAdAccountsService.removeIndustryEntityFromPlatformAdAccounts,
      ).toHaveBeenCalledWith(
        1234,
        ['1', '2', '3'],
        dto.unselectedIndustryGroupIds,
      );
      expect(result).toEqual({
        message: `All the mappings with industry group id - ${dto.unselectedIndustryGroupIds} are removed from the selected ad accounts successfully`,
      });
    });
  });

  describe('validateAccountsAssociationWithParentEntity method', () => {
    it('should not do anything when account industry mapping exists.', async () => {
      jest
        .spyOn(
          organizationAdAccountsService,
          'getPlatformAdAccountsIndustryMappings',
        )
        .mockResolvedValueOnce(PLATFORM_AD_ACCOUNT_INDUSTRY_MAPPINGS);
      await expect(
        controller.validateAccountsAssociationWithParentEntity(
          ['123', '234'],
          2,
          IndustryService.INDUSTRY_ENTITY,
        ),
      ).resolves.not.toThrow();
    });

    it('should throw error when account industry mapping missing in database.', async () => {
      jest
        .spyOn(
          organizationAdAccountsService,
          'getPlatformAdAccountsIndustryMappings',
        )
        .mockResolvedValueOnce(PLATFORM_AD_ACCOUNT_INDUSTRY_MAPPINGS);
      await expect(
        controller.validateAccountsAssociationWithParentEntity(
          ['123', '234', '345'],
          2,
          IndustryService.INDUSTRY_ENTITY,
        ),
      ).rejects.toThrow(
        new BadRequestException(
          `Ad account(s): 345 provided in the request are not associated with the ${IndustryService.INDUSTRY_ENTITY} - 2.`,
        ),
      );
    });
  });

  describe('mapIndustryToBulkAdAccounts method', () => {
    const INDUSTRY_GROUP_ID = 1;
    beforeEach(() => {
      jest
        .spyOn(
          organizationAdAccountsService,
          'getPlatformAdAccountsIndustriesByIndustryGroup',
        )
        .mockResolvedValueOnce([['123', 2]]);
      jest
        .spyOn(industryService, 'getIndustries')
        .mockResolvedValueOnce([{ id: 2 } as unknown as IndustryDTO]);
      jest
        .spyOn(
          organizationAdAccountsService,
          'filterNewAccountsToBeAddedToPlatformAdAccountIndustry',
        )
        .mockReturnValueOnce(['123', '234']);
      jest
        .spyOn(
          organizationAdAccountsService,
          'getPlatformAdAccountsIndustryMappings',
        )
        .mockResolvedValueOnce(PLATFORM_AD_ACCOUNT_INDUSTRY_MAPPINGS);
    });
    afterEach(() => jest.clearAllMocks());

    it('should pass if unselected industry has associated ad accounts', async () => {
      await expect(
        controller.mapIndustryToBulkAdAccounts(
          '12345',
          TEST_USER_ID,
          INDUSTRY_GROUP_ID,
          {
            accountIds: [],
          },
        ),
      ).rejects.toThrow(
        new BadRequestException(
          'List of platform ad account ids must be provided',
        ),
      );
    });

    it('should assign industry to ad accounts if selectedGroupId is provided', async () => {
      const dto = {
        accountIds: ['123', '234'],
        selectedIndustryId: 2,
      } as unknown as CreateIndustryAdAccountsRequestDto;
      const result = await controller.mapIndustryToBulkAdAccounts(
        '12345',
        TEST_USER_ID,
        INDUSTRY_GROUP_ID,
        dto,
      );

      expect(
        organizationAdAccountsService.updatePlatformAdAccountIndustryData,
      ).toHaveBeenCalledWith(
        TEST_USER_ID,
        ['123', '234'],
        dto.selectedIndustryId,
      );
      expect(result).toEqual({
        message: `Industry - ${dto.selectedIndustryId} assigned to 2 ad accounts successfully`,
      });
    });

    it('should remove industry from ad accounts if unselectedIndustryId is provided', async () => {
      const dto = {
        accountIds: ['123', '234'],
        unselectedIndustryIds: [2],
      } as unknown as CreateIndustryAdAccountsRequestDto;

      const result = await controller.mapIndustryToBulkAdAccounts(
        '12345',
        TEST_USER_ID,
        INDUSTRY_GROUP_ID,
        dto,
      );

      expect(
        organizationAdAccountsService.updatePlatformAdAccountIndustryData,
      ).toHaveBeenCalledWith(1234, ['123', '234'], INDUSTRY_GROUP_ID);
      expect(result).toEqual({
        message: `All the mappings with industry id - ${dto.unselectedIndustryIds} are removed from the selected ad accounts successfully`,
      });
    });
  });

  describe('mapSubIndustryToBulkAdAccounts method', () => {
    const INDUSTRY_ID = 2;
    beforeEach(() => {
      jest
        .spyOn(
          organizationAdAccountsService,
          'getPlatformAdAccountsSubIndustriesByIndustry',
        )
        .mockResolvedValueOnce([['123', 3]]);
      jest
        .spyOn(industryService, 'getSubIndustries')
        .mockResolvedValueOnce([{ id: 3 } as unknown as IndustryDTO]);
      jest
        .spyOn(
          organizationAdAccountsService,
          'filterNewAccountsToBeAddedToPlatformAdAccountIndustry',
        )
        .mockReturnValueOnce(['123', '234']);
      jest
        .spyOn(
          organizationAdAccountsService,
          'getPlatformAdAccountsIndustryMappings',
        )
        .mockResolvedValueOnce(PLATFORM_AD_ACCOUNT_INDUSTRY_MAPPINGS);
    });
    afterEach(() => jest.clearAllMocks());

    it('should pass if unselected sub-industry has associated ad accounts', async () => {
      await expect(
        controller.mapSubIndustryToBulkAdAccounts(
          '12345',
          TEST_USER_ID,
          INDUSTRY_ID,
          {
            accountIds: [],
          },
        ),
      ).rejects.toThrow(
        new BadRequestException(
          'List of platform ad account ids must be provided',
        ),
      );
    });

    it('should assign sub-industry to ad accounts if selectedGroupId is provided', async () => {
      const dto = {
        accountIds: ['123', '234'],
        selectedSubIndustryId: 3,
      } as unknown as CreateIndustryAdAccountsRequestDto;
      const result = await controller.mapSubIndustryToBulkAdAccounts(
        '12345',
        TEST_USER_ID,
        INDUSTRY_ID,
        dto,
      );

      expect(
        organizationAdAccountsService.updatePlatformAdAccountIndustryData,
      ).toHaveBeenCalledWith(
        TEST_USER_ID,
        ['123', '234'],
        dto.selectedSubIndustryId,
      );
      expect(result).toEqual({
        message: `Sub-industry - ${dto.selectedSubIndustryId} assigned to 2 ad accounts successfully`,
      });
    });

    it('should remove sub-industry from ad accounts if unselectedSubIndustryId is provided', async () => {
      const dto = {
        accountIds: ['123', '234'],
        unselectedSubIndustryIds: [3],
      } as unknown as CreateIndustryAdAccountsRequestDto;

      const result = await controller.mapSubIndustryToBulkAdAccounts(
        '12345',
        TEST_USER_ID,
        INDUSTRY_ID,
        dto,
      );

      expect(
        organizationAdAccountsService.updatePlatformAdAccountIndustryData,
      ).toHaveBeenCalledWith(1234, ['123', '234'], INDUSTRY_ID);
      expect(result).toEqual({
        message: `All the mappings with sub-industry id - ${dto.unselectedSubIndustryIds} are removed from the selected ad accounts successfully`,
      });
    });
  });
});
