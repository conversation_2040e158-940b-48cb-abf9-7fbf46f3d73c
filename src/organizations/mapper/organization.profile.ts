import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import {
  createMap,
  forMember,
  ignore,
  mapFrom,
  Mapper,
} from '@automapper/core';
import { CreateOrganizationDto } from '../dto/create-organization.dto';
import { Organization } from '../entities/organization.entity';
import { UpdateOrganizationDto } from '../dto/update-organization.dto';
import { ReadOrganizationDto } from '../dto/read-organization.dto';
import { ReadWorkspaceDto } from '../../workspaces/dto/read-workspace.dto';
import { DeleteOrganizationDto } from '../dto/delete-organization.dto';
import { PlatformMedia } from '../../media/entities/platform-media.entity';
import { ReadPlatformMediaDto } from '../../media/dto/read-platform-media.dto';
import { FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED } from '../utils/constants';

/**
 * Defines the mapping rule between DTOs and entities under Organization
 */
@Injectable()
export class OrganizationProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  /**
   * Profile to create mapping between DTOs and Entities.
   * Updates the mapper with a collection of maps where
   * each map defines the relationship and flow between a DTO and an Entity class.
   */
  override get profile() {
    return (mapper) => {
      /*
       For creating a workspace, this map links CreateOrganizationDto to Organization entity.
       Source : CreateOrganizationDto ; Destination: Organization
       */
      createMap(
        mapper,
        CreateOrganizationDto,
        Organization,
        //  Rules to ignore changes to the destination id ie. Organization.id
        forMember((destination) => destination.id, ignore()),
      );
      /*
       For updating a workspace, this map links UpdateOrganizationDto to Organization entity.
       Source : UpdateOrganizationDto ; Destination: Organization
       */
      createMap(mapper, UpdateOrganizationDto, Organization);
      /*
       For reading a workspace, this map links  Organization to ReadOrganizationDto entity.
       Source : Organization ; Destination: ReadOrganizationDto
       */
      createMap(
        mapper,
        Organization,
        ReadOrganizationDto,
        forMember(
          (destination) => destination.workspaces,
          mapFrom((source) =>
            source.workspaces?.map((workspace) =>
              this.mapper.map(workspace, ReadWorkspaceDto),
            ),
          ),
        ),
        forMember(
          (destination) => destination.spendEnabled,
          mapFrom((source) => {
            if (!source.organizationFeatureWhitelists) {
              return false;
            }

            const spendEnabledFeatureWhitelist =
              source.organizationFeatureWhitelists.find(
                (orgFeatWhitelist) =>
                  orgFeatWhitelist.featureWhitelists.identifier ===
                  FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED,
              );

            return spendEnabledFeatureWhitelist ? true : false;
          }),
        ),
      );

      // For deleting a workspace, this map links Organization to DeleteOrganizationDto entity.
      createMap(mapper, Organization, DeleteOrganizationDto);

      createMap(
        mapper,
        PlatformMedia,
        ReadPlatformMediaDto,
        forMember(
          (destination) => destination.mediaId,
          mapFrom((source) => source.mediaId),
        ),
        forMember(
          (destination) => destination.platformAccountId,
          mapFrom((source) => source.platformAccountId),
        ),
        forMember(
          (destination) => destination.platformMediaId,
          mapFrom((source) => source.platformMediaId),
        ),
      );
    };
  }
}
