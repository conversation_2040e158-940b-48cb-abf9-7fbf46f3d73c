import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { Organization } from './entities/organization.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { ReadOrganizationDto } from './dto/read-organization.dto';
import { DeleteOrganizationDto } from './dto/delete-organization.dto';
import {
  ENABLED,
  PermissionSubResource,
  PermissionType,
  Status,
} from 'src/common/constants/constants';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { OrganizationFeatureWhitelist } from './entities/organization-feature-whitelist.entity';

@Injectable()
export class OrganizationsService {
  constructor(
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
    @InjectRepository(OrganizationFeatureWhitelist)
    private readonly organizationFeatureWhitelistRepo: Repository<OrganizationFeatureWhitelist>,
    // Mapper automatically maps DTOs to/from Entities
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  async create(createOrganizationDto: CreateOrganizationDto) {
    // Create Entity object using the Mapper using the mapping defined in org profile.
    const organizationEntity = this.classMapper.map(
      createOrganizationDto,
      CreateOrganizationDto,
      Organization,
    );

    // When empty, add status as 'ENABLED'
    organizationEntity.status = organizationEntity.status ?? ENABLED;

    return this.classMapper.mapAsync(
      await this.organizationRepository.save(organizationEntity),
      Organization,
      ReadOrganizationDto,
    );
  }

  /**
   * find all organizations
   *
   * @returns
   */
  async findAll(
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadOrganizationDto>> {
    // Find all organizations and get the count
    const [result, total] = await this.organizationRepository.findAndCount({
      skip: paginationOptions.offset,
      take: paginationOptions.perPage,
    });

    const organizations: ReadOrganizationDto[] = this.classMapper.mapArray(
      result,
      Organization,
      ReadOrganizationDto,
    );

    return new PaginatedResultArray(organizations, total);
  }

  /**
   * Find one organization by ID
   *
   * @param id
   * @returns
   */
  async findOne(id: string): Promise<ReadOrganizationDto> {
    const organization = await this.getOrganizationEntity(id);

    return this.classMapper.mapAsync(
      organization,
      Organization,
      ReadOrganizationDto,
    );
  }

  /**
   * Update an organization
   *
   * @param id
   * @param updateOrganizationDto
   *
   * @returns
   */
  async update(
    id: string,
    updateOrganizationDto: UpdateOrganizationDto,
  ): Promise<ReadOrganizationDto> {
    const { name, status } = updateOrganizationDto;
    const organization = await this.findOne(id);

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    organization.name = name ?? organization.name;
    organization.status = status ?? organization.status;

    await this.organizationRepository.save(organization);

    return this.classMapper.map(
      organization,
      Organization,
      ReadOrganizationDto,
    );
  }

  /**
   * Delete an organization
   *
   * @param id
   * @returns
   */
  async remove(id: string) {
    const organizationToBeDeleted = await this.findOne(id);

    if (!organizationToBeDeleted) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    } else {
      await this.organizationRepository.delete(id);
    }

    return this.classMapper.map(
      organizationToBeDeleted,
      Organization,
      DeleteOrganizationDto,
    );
  }

  /**
   * Get an organization entity by ID. throws NotFoundException if not found.
   *
   * @param id
   * @returns organization entity
   */
  private async getOrganizationEntity(id: string): Promise<Organization> {
    const organization = await this.organizationRepository
      .createQueryBuilder('organization')
      .leftJoinAndSelect(
        'organization.organizationFeatureWhitelists',
        'organizationFeatureWhitelists',
      )
      .leftJoinAndSelect(
        'organizationFeatureWhitelists.featureWhitelists',
        'featureWhitelists',
      )
      .where('organization.id = :organizationId', {
        organizationId: id,
      })
      .getOne();

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    return organization;
  }

  async doesOrganizationExist(id: string): Promise<boolean> {
    return await this.organizationRepository.exist({ where: { id } });
  }

  async findOrgIdsForUserWithUpdateAllWorkspacePermission(
    personId: number,
  ): Promise<string[]> {
    const sql = `
      SELECT organization_id
        FROM organization_person_role opr
        INNER JOIN organization o ON o.id = opr.organization_id
        WHERE opr.person_id = ?
        AND o.status = '${Status.ENABLED}'
        AND opr.role_id IN (
          SELECT id 
            FROM role 
            WHERE id IN (
                SELECT role_id 
                FROM role_permission 
                WHERE permission_id IN (
                    SELECT id 
                    FROM permission 
                    WHERE subresource = '${PermissionSubResource.WORKSPACE_ALL}' AND type = '${PermissionType.UPDATE}'
                )
            )
        );
    `;

    return (await this.organizationRepository.query(sql, [personId]))?.map(
      (row) => row.organization_id,
    );
  }

  /**
   * Check if a user is authorized in an organization
   * @param organizationId organization id
   * @param userId user id
   */
  async getUserAuthorizedRoleInOrganization(
    organizationId: string,
    userId: number,
  ): Promise<string> {
    const result = await this.organizationRepository
      .createQueryBuilder('organization')
      .innerJoinAndSelect('organization.organizationUserRoles', 'orgUserRole')
      .innerJoinAndSelect('orgUserRole.role', 'role')
      .innerJoin('organization.userOrganizationMaps', 'userOrgMap')
      .where('organization.id = :organizationId', { organizationId })
      .andWhere('orgUserRole.user.id = :userId', { userId })
      .andWhere('userOrgMap.user.id = :userId', { userId })
      .getMany();
    if (result.length > 1 || result[0]?.organizationUserRoles.length > 1) {
      throw new BadRequestException(
        `User ${userId} has multiple roles in organization ${organizationId}`,
      );
    } else if (result.length === 1) {
      return result[0].organizationUserRoles[0].role.identifier;
    } else {
      return null;
    }
  }

  async getAvailableOrganizationWhitelistFeatures(
    organizationId: string,
    features?: string[],
  ): Promise<Record<string, boolean>> {
    const orgWhitelistFeatures =
      await this.organizationFeatureWhitelistRepo.find({
        where: { organizationId },
        relations: { featureWhitelists: true },
      });
    const featureIdentifiers = orgWhitelistFeatures
      .map((o) => o.featureWhitelists)
      .map((fw) => fw.identifier);
    if (!!features) {
      return features.reduce((agg, curr) => {
        if (!(curr in agg)) {
          return { ...agg, [curr]: featureIdentifiers.includes(curr) };
        }
      }, {});
    }
    return featureIdentifiers.reduce((agg, curr) => {
      return { ...agg, [curr]: true };
    }, {});
  }
}
