import {
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  BadRequestException,
  ParseUUIDPipe,
  ParseArrayPipe,
  ParseIntPipe,
  forwardRef,
  Inject,
} from '@nestjs/common';
import { OrganizationsService } from './organizations.service';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { ApiExtraModels, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { ORGANIZATION_API_TAG_NAME } from '../common/constants/api.constants';
import { WorkspaceService } from '../workspaces/workspaces.service';
import { ReadOrganizationDto } from './dto/read-organization.dto';
import { DeleteOrganizationDto } from './dto/delete-organization.dto';
import {
  GetPagination,
  PaginationOptions,
  VmApiCreatedResponse,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadWorkspaceDto } from '../workspaces/dto/read-workspace.dto';
import { UUID } from 'typeorm/driver/mongodb/bson.typings';
import { SearchParamsDto } from 'src/workspaces/dto/search-params.dto';
import { CreateOrganizationPlatformAdAccountDto } from '../ad-accounts/dto/create-organization-platform-ad-account.dto';
import { AdAccountsService } from '../ad-accounts/ad-accounts.service';
import { ReadPlatformAdAccountWithAdditionalInfoDto } from '../ad-accounts/dto/read-platform-ad-account-with-additional-info.dto';
import { ReadWorkspacesFromAdAccountDto } from '../ad-accounts/dto/read-workspaces-from-ad-account.dto';
import { CreateVerifyOrganizationWorkspaceDto } from './dto/create-verify-organization-workspace.dto';
import { ReadVerifyOrganizationWorkspaceDto } from './dto/read-verify-organization-workspace.dto';
import { OrganizationAdAccountsService } from '../ad-accounts/organization-ad-accounts.service';
import { ReadPlatformAdAccountPermissionsDto } from '../ad-accounts/dto/read-platform-ad-account-permissions.dto';
import { VmApiOkUnPaginatedArrayResponse } from '@vidmob/vidmob-nestjs-common/dist/docs/vmresponse.decorator';
import {
  CreateAdAccountWorkspaceMapDto,
  PlatformAccount,
} from '../ad-accounts/dto/create-ad-account-workspace-map.dto';
import { ReadHealthDashboardAdAccountDto } from '../ad-accounts/dto/read-health-dashboard-ad-account.dto';
import { AccountSearchParamsDto } from '../ad-accounts/dto/ad-account-search-params.dto';
import {
  ValidateOrganizationAdAccountsRequestDto,
  ValidateOrganizationEntitiesResponseDto,
  ValidateOrganizationWorkspacesRequestDto,
} from './dto/validate-organization-entities.dto';
import { CreateBulkAdAccountBrandMapDto } from 'src/ad-accounts/dto/create-bulk-ad-account-brand-map.dto';
import { BrandService } from 'src/brands/brand.service';
import { CreateBulkAdAccountMarketMapDto } from 'src/ad-accounts/dto/create-bulk-ad-account-market-map.dto';
import { ReadAdAccountMapDto } from 'src/ad-accounts/dto/read-ad-account-map.dto';
import { ReadAdAccountBrandsDto } from 'src/ad-accounts/dto/read-ad-account-brands.dto';
import { ReadAdAccountMarketsDto } from 'src/ad-accounts/dto/read-ad-accounts-markets.dto';
import { ReadLightWeightWorkspaceDto } from './dto/read-light-weight-workspace.dto';
import { ReadAdAccountWorkspaceMarketsDto } from '../ad-accounts/dto/read-ad-account-workspace-markets.dto';
import { ReadBasicMarketDto } from '../markets/dto/read-basic-market.dto';
import { GetFilteredBrandsDto } from '../ad-accounts/dto/get-filtered-brands.dto';
import { Brand } from '../brands/entities/brand.entity';
import { IndustryService } from '../industry/industry.service';
import { IndustryDTO } from '../industry/dto/industry.dto';
import { ReadOrganizationWhitelistFeatureRecordDto } from './dto/read-organization-whitelist-feature-record.dto';
import { CreateIndustryAdAccountsRequestDto } from '../ad-accounts/dto/create-industry-ad-accounts-request.dto';
import { AdAccountHealthSearchParamsDto } from 'src/ad-accounts/dto/ad-account-health-search-params.dto';

@ApiTags(ORGANIZATION_API_TAG_NAME)
@ApiExtraModels(ReadOrganizationDto, DeleteOrganizationDto)
@Controller('organization')
/**
 * Organization in VidMob represents a single customer or conglomerate
 */
export class OrganizationsController {
  constructor(
    private readonly organizationsService: OrganizationsService,
    @Inject(forwardRef(() => WorkspaceService))
    private readonly workspaceService: WorkspaceService,
    @Inject(forwardRef(() => AdAccountsService))
    private readonly adAccountService: AdAccountsService,
    @Inject(forwardRef(() => OrganizationAdAccountsService))
    private readonly organizationAdAccountService: OrganizationAdAccountsService,
    private readonly brandService: BrandService,
    private readonly industryService: IndustryService,
  ) {}

  /**
   * This endpoint creates an Organization.
   * @param createOrganizationDto
   */
  @VmApiCreatedResponse({
    type: ReadOrganizationDto,
    description: 'The organization has been successfully created.',
  })
  @Post()
  create(@Body() createOrganizationDto: CreateOrganizationDto) {
    return this.organizationsService.create(createOrganizationDto);
  }

  /**
   * This endpoint returns all organizations
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadOrganizationDto,
  })
  @Get()
  findAll(
    @GetPagination() paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadOrganizationDto>> {
    return this.organizationsService.findAll(paginationOptions);
  }

  /**
   * This endpoint retrieves an organization by its id
   * @param id System assigned id of the organization
   */
  @VmApiOkResponse({
    type: ReadOrganizationDto,
    description: 'Read one organization.',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'System assigned id of the Organization',
  })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const result = await this.organizationsService.findOne(id);

    if (!result) {
      throw new NotFoundException('No organization found for id ' + id);
    }

    return result;
  }

  /**
   * Lightweight endpoint to load all workspaces within an organization.
   * The purpose of this endpoint is to provide a fast query to retrieve all workspaces, without much querying.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @returns - all organization workspace: name and id
   */
  @ApiParam({
    name: 'id',
    description:
      'The organization ID represents the unique identifier of the organization to which all workspaces belong.',
  })
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadLightWeightWorkspaceDto,
  })
  @Get(':id/workspace/all')
  async getAllLightWeightWorkspaces(@Param('id') organizationId: string) {
    return await this.workspaceService.getAllLightweight(organizationId);
  }

  /**
   * This endpoint returns all workspaces for an organization.
   * Supports filtering the list of workspaces whose names contain the optional search value.
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadWorkspaceDto,
  })
  @ApiParam({
    name: 'id',
    type: UUID,
    description: 'System assigned id of the Organization',
  })
  @ApiQuery({
    name: 'searchParams',
    type: String,
    description:
      'Filter workspaces by partial match on name or market associated to the workspace',
    required: false,
  })
  @Get(':id/workspace')
  async findWorkspacesByOrganization(
    @Param('id') id: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() searchParams: SearchParamsDto,
  ) {
    return await this.workspaceService.getWorkspacesByOrganizationIdAndSearch(
      id,
      paginationOptions,
      searchParams,
    );
  }

  /**
   * This endpoint returns all organization workspaces associated with a specified user.
   * Supports filtering the list of workspaces whose names contain the optional search value.
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadWorkspaceDto,
  })
  @ApiParam({
    name: 'id',
    type: UUID,
    description: 'System assigned id of the Organization',
  })
  @ApiParam({
    name: 'userId',
    type: Number,
    description: 'System assigned id of the VidMob user',
  })
  @ApiQuery({
    name: 'searchParams',
    type: String,
    description:
      'Filter workspaces by partial match on name or market associated to the workspace',
    required: false,
  })
  @Get(':id/user/:userId/workspace')
  async findWorkspacesByOrganizationAndUser(
    @Param('id') id: string,
    @Param('userId') userId: number,
    @Query() getWorkspaceSearchParams: SearchParamsDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return await this.workspaceService.getWorkspacesByOrganizationIdAndUserIdAndSearch(
      id,
      userId,
      getWorkspaceSearchParams,
      paginationOptions,
    );
  }

  /**
   * This endpoint updates an Organization by its id
   * @param id System assigned id of the Organization
   * @param updateOrganizationDto Object of the organization to update.
   */
  @VmApiOkResponse({
    type: ReadOrganizationDto,
    description: 'Updates organization by id.',
  })
  @ApiParam({
    name: 'id',
    description: 'System assigned id of the Organization',
  })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
  ) {
    return this.organizationsService.update(id, updateOrganizationDto);
  }

  /**
   * This endpoint deletes an Organization by its id
   * @param id System assigned id of the Organization
   * The documentation skips default error response examples by passing `true` below.
   */
  @VmApiCreatedResponse({
    type: DeleteOrganizationDto,
    description: 'Deletes organization by id.',
  })
  @ApiParam({
    name: 'id',
    description: 'System assigned id of the Organization',
  })
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.organizationsService.remove(id);
  }

  /**
   * This endpoint updates the permissions for a platform ad account in an organization
   * @return platform ad account response with updated permissions
   * @param id
   * @param createOrganizationPlatformAdAccountDto
   */
  @VmApiCreatedResponse({
    description: 'Platform ad account with permissions.',
  })
  @ApiParam({ name: 'organizationId', description: 'Organization Id' })
  @Post(':organizationId/ad-accounts')
  async mapAdAccountToOrganization(
    @Param('organizationId') id: string,
    @Body()
    createOrganizationPlatformAdAccountDto: CreateOrganizationPlatformAdAccountDto,
  ): Promise<string> {
    return this.organizationAdAccountService.mapAdAccountToOrganization(
      id,
      createOrganizationPlatformAdAccountDto,
    );
  }

  /**
   * This endpoint configures a list of ad accounts for an organizations' workspace
   * If the status is CONNECTED, the ad accounts will be mapped to the workspace
   * If the status is DISCONNECTED, the ad accounts will be removed from the workspace
   * @param workspaceId
   * @param dto
   */
  @VmApiCreatedResponse({
    type: String,
    description:
      'Tells the user if a list of ad accounts were connected or disconnected from the workspace.',
  })
  @ApiParam({ name: 'workspaceId', description: 'Workspace Id' })
  @Post('workspace/:workspaceId/platform-account')
  async configureOrganizationWorkspaceAdAccountConnections(
    @Param('workspaceId') workspaceId: number,
    @Body() dto: CreateAdAccountWorkspaceMapDto,
  ) {
    if (dto.platformAccountList.length === 0)
      throw new BadRequestException(
        'List of platform ad accounts must be provided',
      );
    const workspace = await this.workspaceService.getWorkspaceEntity(
      workspaceId,
    );
    const platformAdAccountIds: number[] =
      await this.getPlatformAdAccountIdsFromListOfPlatformAccounts(
        dto.platformAccountList,
      );
    await this.workspaceService.arePlatformAdAccountsAbleToMapToAnOrganizationWorkspace(
      workspace,
      platformAdAccountIds,
    );
    if (dto.status === 'CONNECTED') {
      return await this.workspaceService.connectPlatformAdAccountsToAWorkspace(
        workspace,
        platformAdAccountIds,
      );
    } else if (dto.status === 'DISCONNECTED') {
      return await this.workspaceService.disconnectPlatformAdAccountsToAWorkspace(
        workspace,
        platformAdAccountIds,
      );
    } else {
      throw new BadRequestException('Invalid status provided');
    }
  }

  /**
   * This endpoint LIST ad accounts for organizations
   * @return platform ad account response with updated permissions
   * @param organizationId Organization Id
   * @param paginationOptions Pagination Options
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadPlatformAdAccountWithAdditionalInfoDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization.',
  })
  @ApiQuery({
    name: 'workspaceId',
    type: Number,
    description: 'workspace id',
    required: false,
  })
  @Get(':organizationId/ad-accounts')
  async getConnectedAdAccountsForOrganization(
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() searchParams: AccountSearchParamsDto,
    @Query('workspaceId') workspaceId?: number,
  ) {
    return await this.adAccountService.getConnectedAdAccountsForAnOrganization(
      organizationId,
      workspaceId,
      paginationOptions,
      searchParams,
    );
  }

  @VmApiOkPaginatedArrayResponse({
    type: ReadWorkspacesFromAdAccountDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'System assigned id of the Organization',
  })
  @ApiParam({
    name: 'adAccountId',
    type: UUID,
    description: 'System assigned id of the Ad Account',
  })
  @Get(':organizationId/ad-account/:adAccountId/workspaces')
  async getWorkspacesForAdAccount(
    @Param('organizationId') organizationId: string,
    @Param('adAccountId') adAccountId: string,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return this.workspaceService.grabAllWorkspacesFromAnAdAccountForAnOrganization(
      organizationId,
      adAccountId,
      paginationOptions,
    );
  }

  /**
   * This point return verified workspaces by ids for an organization.
   * If any of the workspaces do not belong to the organization, an error is thrown.
   * @param organizationId
   * @param dto
   */
  @VmApiOkResponse({
    type: Array,
    description: 'Returns verified workspaces by ids for an organization.',
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @Post(':organizationId/workspace/by-ids')
  async verifyWorkspacesByIds(
    @Param('organizationId', ParseUUIDPipe) organizationId: string,
    @Body() dto: CreateVerifyOrganizationWorkspaceDto,
  ): Promise<ReadVerifyOrganizationWorkspaceDto[]> {
    const workspaceIds = dto.workspaceIds;
    if (workspaceIds.length <= 0) {
      throw new BadRequestException(
        'WorkspaceIds is required. List of workspaces is empty.',
      );
    }
    const verifiedWorkspaceIds =
      await this.workspaceService.verifyWorkspacesBelongToAnOrganization(
        organizationId,
        workspaceIds,
      );
    return verifiedWorkspaceIds.map((id) => {
      return { id };
    });
  }

  /**
   * Get user account permissions for platform in an organization.
   * @param organizationId organization id
   * @param platform platform
   * @param userId user id
   * @returns ReadPlatformAdAccountPermissionsDtoList user's list of ad accounts with permissions in an organization
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadPlatformAdAccountPermissionsDto,
    description: `Returns user's list of ad accounts with permissions for platform in an organization.`,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'platform',
    description: 'Platform to list accounts for',
  })
  @ApiParam({
    name: 'userId',
    description: 'Logged in user Id to fetch accounts list',
  })
  @Get(':organizationId/:platform/user/:userId/ad-account/permissions')
  async getUserAccountPermissionsInOrganization(
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
    @Param('userId') userId: number,
  ): Promise<ReadPlatformAdAccountPermissionsDto[]> {
    return this.organizationAdAccountService.getUserAccountsWithPermissionsInOrganization(
      organizationId,
      platform,
      userId,
    );
  }

  private async getPlatformAdAccountIdsFromListOfPlatformAccounts(
    platformAccountList: PlatformAccount[],
  ): Promise<number[]> {
    const platformAdAccountIds: Set<number> = new Set();
    for (const platformAccount of platformAccountList) {
      platformAdAccountIds.add(
        await this.adAccountService.getPlatformAdAccountIdFromPlatformAndPlatformId(
          platformAccount.platformAccountId,
          platformAccount.platform,
        ),
      );
    }
    return Array.from(platformAdAccountIds);
  }

  /**
   * List ad accounts from an organization workspace to display in dashboard
   * @param organizationId organization id
   * @param workspaceId workspace id
   * @param userId user id
   * @param paginationOptions pagination options
   * @param searchParams search params
   * @returns ReadHealthDashboardAdAccountDto[] list of ad accounts in an organization workspace
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadHealthDashboardAdAccountDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'Workspace Id',
  })
  @ApiParam({
    name: 'userId',
    description: 'Logged in user Id to fetch accounts list',
  })
  @Get(':organizationId/workspace/:workspaceId/user/:userId/ad-account-health')
  async getWorkspaceAdAccountsListForHealthDashBoard(
    @Param('organizationId') organizationId: string,
    @Param('workspaceId') workspaceId: number,
    @Param('userId') userId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() searchParams: AdAccountHealthSearchParamsDto,
  ): Promise<PaginatedResultArray<ReadHealthDashboardAdAccountDto>> {
    return this.organizationAdAccountService.getWorkspaceAdAccountsListForHealthDashBoard(
      organizationId,
      workspaceId,
      userId,
      paginationOptions,
      searchParams,
    );
  }

  /**
   * Bulk inserts the mapping generated from the brands and accounts passed in the body
   * @param organizationId organization id
   * @param workspaceId workspace id
   * @param dto
   * @returns ReadAdAccountMapDto indicating how much maps were inserted on the database
   */
  @VmApiOkResponse({
    type: ReadAdAccountMapDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'Workspace Id',
  })
  @Post(':organizationId/workspace/:workspaceId/ad-accounts/brands')
  async createBulkMapBetweenAdAccountAndBrands(
    @Param('organizationId') organizationId: string,
    @Param('workspaceId') workspaceId: number,
    @Body() dto: CreateBulkAdAccountBrandMapDto,
  ): Promise<ReadAdAccountMapDto> {
    if (dto.accounts.length === 0)
      throw new BadRequestException(
        'List of platform ad accounts must be provided',
      );
    const brandsToCheck = [];

    if (dto.selected_brands?.length > 0)
      brandsToCheck.push(...dto.selected_brands);
    if (dto.unselected_brands?.length > 0)
      brandsToCheck.push(...dto.unselected_brands);

    if (brandsToCheck.length === 0)
      throw new BadRequestException('List of brands must be provided');

    if (
      !this.brandService.isAllBrandsAssociatedWithOrganizationId(
        brandsToCheck,
        organizationId,
      )
    ) {
      throw new BadRequestException(
        'Unable to map the brands to the ad account(s).',
      );
    }

    return await this.organizationAdAccountService.saveBulkMapBetweenAdAccountsAndBrands(
      organizationId,
      workspaceId,
      dto,
    );
  }

  /**
   * Bulk inserts the mapping generated from the markets and accounts passed in the body
   * @param organizationId organization id
   * @param workspaceId workspace id
   * @param dto
   * @returns ReadAdAccountMapDto indicating how much maps were inserted on the database
   */
  @VmApiOkResponse({
    type: ReadAdAccountMapDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'Workspace Id',
  })
  @Post(':organizationId/workspace/:workspaceId/ad-accounts/markets')
  async createBulkMapBetweenAdAccountAndMarkets(
    @Param('organizationId') organizationId: string,
    @Param('workspaceId') workspaceId: number,
    @Body() dto: CreateBulkAdAccountMarketMapDto,
  ): Promise<ReadAdAccountMapDto> {
    if (dto.accounts.length === 0)
      throw new BadRequestException(
        'List of platform ad accounts must be provided',
      );

    if (dto.selected_markets?.length > 0 || dto.unselected_markets?.length > 0)
      return await this.organizationAdAccountService.saveBulkMapBetweenAdAccountsAndMarkets(
        organizationId,
        workspaceId,
        dto,
      );

    throw new BadRequestException('List of markets must be provided');
  }

  /**
   * List brands for an ad account
   * @param organizationId organization id
   * @param userId user id
   * @param paginationOptions pagination options
   * @param searchParams search params
   * @returns ReadAdAccountBrandsDto list of brands an ad account has
   */
  @VmApiOkResponse({
    type: ReadAdAccountBrandsDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'Workspace Id',
  })
  @ApiParam({
    name: 'adAccountId',
    type: String,
  })
  @Get(':organizationId/workspace/:workspaceId/ad-account/:adAccountId/brands')
  async getOrganizationAdAccountBrands(
    @Param('adAccountId') adAccountId: string,
  ) {
    return await this.organizationAdAccountService.getOrganizationAdAccountBrands(
      adAccountId,
    );
  }

  /**
   * List markets for an ad account
   * @param organizationId organization id
   * @param userId user id
   * @param paginationOptions pagination options
   * @param searchParams search params
   * @returns ReadAdAccountMarketsDto list of markets an ad account has
   */
  @VmApiOkResponse({
    type: ReadAdAccountMarketsDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'Workspace Id',
  })
  @ApiParam({
    name: 'adAccountId',
    type: String,
  })
  @Get(':organizationId/workspace/:workspaceId/ad-account/:adAccountId/markets')
  async getOrganizationAdAccountMarkets(
    @Param('adAccountId') adAccountId: string,
  ) {
    return await this.organizationAdAccountService.getOrganizationAdAccountMarkets(
      adAccountId,
    );
  }

  @VmApiOkPaginatedArrayResponse({
    type: ReadBasicMarketDto,
    description: 'List of Ad Account Markets',
  })
  @Post('/markets')
  async getAdAccountMarketsByWorkspaces(
    @Body() readAdAccountWorkspaceMarketsDto: ReadAdAccountWorkspaceMarketsDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const workspaceIds = readAdAccountWorkspaceMarketsDto.workspaceIds;

    return await this.organizationAdAccountService.getAdAccountMarketsByWorkspaceIds(
      workspaceIds,
      paginationOptions,
    );
  }

  /**
   * List ad accounts from an user organization to display in dashboard
   * @param organizationId organization id
   * @param userId user id
   * @param paginationOptions pagination options
   * @param searchParams search params
   * @returns ReadHealthDashboardAdAccountDto[] list of ad accounts in an organization workspace
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadHealthDashboardAdAccountDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'userId',
    description: 'Logged in user Id to fetch accounts list',
  })
  @Get(':organizationId/user/:userId/ad-account-health')
  async getOrganizationAdAccountsListForHealthDashBoard(
    @Param('organizationId') organizationId: string,
    @Param('userId') userId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() searchParams: AdAccountHealthSearchParamsDto,
  ): Promise<PaginatedResultArray<ReadHealthDashboardAdAccountDto>> {
    return this.organizationAdAccountService.getOrganizationAdAccountsListForHealthDashBoard(
      organizationId,
      userId,
      paginationOptions,
      searchParams,
    );
  }

  /**
   * Validate if all ad account ids in request belong the specified organization
   * @param organizationId organization id
   * @param userId user id
   * @param requestDto { adAccountIds: string[]}
   * @returns boolean - whether all ad accounts belong to the organization
   */
  @VmApiOkResponse({
    type: ValidateOrganizationEntitiesResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'userId',
    type: Number,
  })
  @Post(':organizationId/user/:userId/ad-accounts/validate')
  async validateAdAccountsWithinOrganization(
    @Param('organizationId') organizationId: string,
    @Param('userId') userId: number,
    @Body() requestDto: ValidateOrganizationAdAccountsRequestDto,
  ): Promise<ValidateOrganizationEntitiesResponseDto> {
    if (requestDto?.workspaceIds?.length > 0) {
      return this.adAccountService.validateUserAccountsWithinOrganizationWorkspaces(
        organizationId,
        userId,
        requestDto.adAccountIds,
        requestDto.workspaceIds,
      );
    }
    return this.adAccountService.validateUserAccountsWithinOrganization(
      organizationId,
      userId,
      requestDto.adAccountIds,
    );
  }

  /**
   * Validate if all ad workspace ids in request belong the specified organization and user has access to them
   * @param organizationId organization id
   * @param userId user's id
   * @param requestDto { workspaceIds: number[]}
   * @returns boolean - whether all ad workspaces belong to the organization and user has access to them
   */
  @VmApiOkResponse({
    type: ValidateOrganizationEntitiesResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @Post(':organizationId/user/:userId/workspaces/validate')
  async validateWorkspacesWithinOrganization(
    @Param('organizationId') organizationId: string,
    @Param('userId') userId: number,
    @Body() requestDto: ValidateOrganizationWorkspacesRequestDto,
  ): Promise<ValidateOrganizationEntitiesResponseDto> {
    return this.workspaceService.validateUserWorkspacesWithinOrganization(
      organizationId,
      userId,
      requestDto.workspaceIds,
    );
  }

  @ApiParam({
    name: 'organizationId',
    type: String,
    description: 'Organization ID',
  })
  @VmApiOkUnPaginatedArrayResponse({
    type: Brand,
  })
  @Post(':organizationId/brands/filter')
  async getFilteredBrands(
    @Param('organizationId') organizationId: string,
    @Body() filterDto: GetFilteredBrandsDto,
  ): Promise<Brand[]> {
    return this.brandService.getFilteredBrands(
      organizationId,
      filterDto.adAccountIds,
    );
  }

  @ApiParam({
    name: 'organizationId',
    type: String,
    description: 'Organization ID',
  })
  @VmApiOkUnPaginatedArrayResponse({
    type: IndustryDTO,
  })
  @Get(':organizationId/industry-groups')
  async getIndustryGroups(
    @Param('organizationId') organizationId: string,
  ): Promise<IndustryDTO[]> {
    return this.industryService.getIndustryGroups();
  }

  @ApiParam({
    name: 'organizationId',
    type: String,
    description: 'Organization ID',
  })
  @VmApiOkUnPaginatedArrayResponse({
    type: IndustryDTO,
  })
  @Get(':organizationId/industry-groups/:industryGroupId/industries')
  async getIndustries(
    @Param('organizationId') organizationId: string,
    @Param('industryGroupId') industryGroupId: number,
  ): Promise<IndustryDTO[]> {
    return this.industryService.getIndustries(industryGroupId);
  }

  @ApiParam({
    name: 'organizationId',
    type: String,
    description: 'Organization ID',
  })
  @VmApiOkUnPaginatedArrayResponse({
    type: IndustryDTO,
  })
  @Get(':organizationId/industries/:industryId/sub_industries')
  async getSubIndustries(
    @Param('organizationId') organizationId: string,
    @Param('industryId') industryId: number,
  ): Promise<IndustryDTO[]> {
    return this.industryService.getSubIndustries(industryId);
  }

  @ApiParam({
    name: 'organizationId',
    type: String,
    description: 'Organization ID',
  })
  @ApiQuery({
    name: 'features',
    type: Array<string>,
    description: 'Features',
  })
  @VmApiOkResponse({
    type: ReadOrganizationWhitelistFeatureRecordDto,
  })
  @Get(':organizationId/feature-whitelist-record')
  async getOrganizationWhitelistFeatureEndpoint(
    @Param('organizationId') organizationId: string,
    @Query('features', new ParseArrayPipe()) features?: string[],
  ): Promise<ReadOrganizationWhitelistFeatureRecordDto> {
    const record: Record<string, boolean> =
      await this.organizationsService.getAvailableOrganizationWhitelistFeatures(
        organizationId,
        features ?? undefined,
      );
    return {
      organizationId,
      featureRecord: record,
    };
  }

  validateAdAccountIndustryAssociation(
    platformAdAccountIndustryMaps: [string, number][],
    selectedIndustryEntity: number,
    unselectedIndustryEntities: number[],
    entityName: string,
    entityIds: number[],
  ) {
    if (selectedIndustryEntity && unselectedIndustryEntities?.length > 0) {
      throw new BadRequestException(
        `Assigning and un-assigning ${entityName} ids from ad accounts is not allowed in same request.`,
      );
    } else if (
      !selectedIndustryEntity &&
      !(unselectedIndustryEntities?.length > 0)
    ) {
      throw new BadRequestException(
        `${entityName} Id must be provided to map ${entityName} ids to ad accounts`,
      );
    }
    if (selectedIndustryEntity && !entityIds.includes(selectedIndustryEntity)) {
      throw new BadRequestException(
        `No ${entityName} found with id: ${selectedIndustryEntity}.`,
      );
    }
    if (unselectedIndustryEntities?.length > 0) {
      unselectedIndustryEntities.forEach((unselectedIndustryEntity) => {
        if (!entityIds.includes(unselectedIndustryEntity)) {
          throw new BadRequestException(
            `No ${entityName} found with id: ${unselectedIndustryEntity}.`,
          );
        }
      });
    }
  }

  /**
   * Validate if accounts list is empty or not and return unique values
   * @param accountIds List of platform account ids
   * @returns List of unique platform account ids
   */
  private static validateAdAccountsAndReturnUniqueValues(
    accountIds: string[],
  ): string[] {
    if (accountIds.length === 0) {
      throw new BadRequestException(
        'List of platform ad account ids must be provided',
      );
    }
    return [...new Set(accountIds)];
  }

  /**
   * Validate if accounts are associated with the parent entity i,e. industry group or industry
   * @param accountIds List of platform account ids
   * @param industryId Industry group or industry id
   * @param entityName Name of the entity i.e. industry group or industry
   */
  async validateAccountsAssociationWithParentEntity(
    accountIds: string[],
    industryId: number,
    entityName: string,
  ): Promise<void> {
    const existingAccountIndustryMappings =
      await this.organizationAdAccountService.getPlatformAdAccountsIndustryMappings(
        accountIds,
        industryId,
      );
    const existingAccountIds = existingAccountIndustryMappings.map(
      (item) => item.platformAdAccount.platformAccountId,
    );
    const distinctAccountIds = [...new Set(existingAccountIds)];
    if (distinctAccountIds.length !== accountIds.length) {
      const missingAccountIds = accountIds.filter(
        (acc) => !distinctAccountIds.includes(acc),
      );
      throw new BadRequestException(
        `Ad account(s): ${missingAccountIds} provided in the request are not associated with the ${entityName} - ${industryId}.`,
      );
    }
  }

  /**
   * Assigns industry group to a list of ad accounts
   * @param organizationId organization id
   * @param userId user id
   * @param dto
   * @returns ReadAdAccountMapDto indicating how much maps were inserted on the database
   */
  @VmApiOkResponse({
    type: ReadAdAccountMapDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'userId',
    type: Number,
  })
  @Post(':organizationId/user/:userId/ad-accounts/industry-group')
  async mapIndustryGroupToBulkAdAccounts(
    @Param('organizationId') organizationId: string,
    @Param('userId', ParseIntPipe) userId: number,
    @Body() dto: CreateIndustryAdAccountsRequestDto,
  ): Promise<ReadAdAccountMapDto> {
    const accountIds =
      OrganizationsController.validateAdAccountsAndReturnUniqueValues(
        dto.accountIds,
      );
    const platformAdAccountIndustryMap =
      await this.organizationAdAccountService.getPlatformAdAccountsIndustryGroups(
        accountIds,
      );
    const industryGroups = await this.industryService.getIndustryGroups();
    const industryGroupIds = industryGroups.map((group) => group.id);
    this.validateAdAccountIndustryAssociation(
      platformAdAccountIndustryMap,
      dto.selectedIndustryGroupId,
      dto.unselectedIndustryGroupIds,
      IndustryService.INDUSTRY_GROUP_ENTITY,
      industryGroupIds,
    );
    if (dto.selectedIndustryGroupId) {
      const currentlyMappedAccountIds = platformAdAccountIndustryMap.map(
        (accountIndustry) => accountIndustry[0],
      );
      await this.organizationAdAccountService.updatePlatformAdAccountIndustryData(
        userId,
        currentlyMappedAccountIds,
        dto.selectedIndustryGroupId,
      );
      const newAccountsToBeMapped =
        this.organizationAdAccountService.filterNewAccountsToBeAddedToPlatformAdAccountIndustry(
          platformAdAccountIndustryMap,
          accountIds,
        );
      await this.organizationAdAccountService.assignIndustryGroupToPlatformAdAccounts(
        userId,
        newAccountsToBeMapped,
        dto.selectedIndustryGroupId,
      );
      return {
        message: `Industry group - ${dto.selectedIndustryGroupId} assigned to ${accountIds.length} ad accounts successfully`,
      };
    } else if (dto.unselectedIndustryGroupIds) {
      await this.organizationAdAccountService.removeIndustryEntityFromPlatformAdAccounts(
        userId,
        accountIds,
        dto.unselectedIndustryGroupIds,
      );
      return {
        message: `All the mappings with industry group id - ${dto.unselectedIndustryGroupIds} are removed from the selected ad accounts successfully`,
      };
    }
  }

  /**
   * Assigns industry to a list of ad accounts
   * @param organizationId organization id
   * @param userId user id
   * @param industryGroupId industry group id
   * @param dto
   * @returns ReadAdAccountMapDto indicating how much maps were inserted on the database
   */
  @VmApiOkResponse({
    type: ReadAdAccountMapDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'userId',
    type: Number,
  })
  @ApiParam({
    name: 'industryGroupId',
    type: Number,
  })
  @Post(
    ':organizationId/user/:userId/ad-accounts/industry-group/:industryGroupId/industry',
  )
  async mapIndustryToBulkAdAccounts(
    @Param('organizationId') organizationId: string,
    @Param('userId', ParseIntPipe) userId: number,
    @Param('industryGroupId', ParseIntPipe) industryGroupId: number,
    @Body() dto: CreateIndustryAdAccountsRequestDto,
  ): Promise<ReadAdAccountMapDto> {
    const accountIds =
      OrganizationsController.validateAdAccountsAndReturnUniqueValues(
        dto.accountIds,
      );
    await this.validateAccountsAssociationWithParentEntity(
      accountIds,
      industryGroupId,
      IndustryService.INDUSTRY_GROUP_ENTITY,
    );
    const currentAccountIndustryMappings =
      await this.organizationAdAccountService.getPlatformAdAccountsIndustriesByIndustryGroup(
        accountIds,
        industryGroupId,
      );
    const industries = await this.industryService.getIndustries(
      industryGroupId,
    );
    const industryIds = industries.map((industry) => industry.id);
    this.validateAdAccountIndustryAssociation(
      currentAccountIndustryMappings,
      dto.selectedIndustryId,
      dto.unselectedIndustryIds,
      IndustryService.INDUSTRY_ENTITY,
      industryIds,
    );
    if (dto.selectedIndustryId) {
      await this.organizationAdAccountService.updatePlatformAdAccountIndustryData(
        userId,
        accountIds,
        dto.selectedIndustryId,
      );
      return {
        message: `Industry - ${dto.selectedIndustryId} assigned to ${accountIds.length} ad accounts successfully`,
      };
    } else if (dto.unselectedIndustryIds) {
      await this.organizationAdAccountService.updatePlatformAdAccountIndustryData(
        userId,
        accountIds,
        industryGroupId,
      );
      return {
        message: `All the mappings with industry id - ${dto.unselectedIndustryIds} are removed from the selected ad accounts successfully`,
      };
    }
  }

  /**
   * Assigns sub-industry to a list of ad accounts
   * @param organizationId organization id
   * @param userId user id
   * @param industryId industry id
   * @param dto
   * @returns ReadAdAccountMapDto indicating how much maps were inserted on the database
   */
  @VmApiOkResponse({
    type: ReadAdAccountMapDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
  })
  @ApiParam({
    name: 'userId',
    type: Number,
  })
  @ApiParam({
    name: 'industryId',
    type: Number,
  })
  @Post(
    ':organizationId/user/:userId/ad-accounts/industry/:industryId/sub-industry',
  )
  async mapSubIndustryToBulkAdAccounts(
    @Param('organizationId') organizationId: string,
    @Param('userId', ParseIntPipe) userId: number,
    @Param('industryId', ParseIntPipe) industryId: number,
    @Body() dto: CreateIndustryAdAccountsRequestDto,
  ): Promise<ReadAdAccountMapDto> {
    const accountIds =
      OrganizationsController.validateAdAccountsAndReturnUniqueValues(
        dto.accountIds,
      );
    await this.validateAccountsAssociationWithParentEntity(
      accountIds,
      industryId,
      IndustryService.INDUSTRY_ENTITY,
    );
    const currentAccountSubIndustryMappings =
      await this.organizationAdAccountService.getPlatformAdAccountsSubIndustriesByIndustry(
        accountIds,
        industryId,
      );
    const subIndustries = await this.industryService.getSubIndustries(
      industryId,
    );
    const subIndustryIds = subIndustries.map((subIndustry) => subIndustry.id);
    this.validateAdAccountIndustryAssociation(
      currentAccountSubIndustryMappings,
      dto.selectedSubIndustryId,
      dto.unselectedSubIndustryIds,
      IndustryService.SUB_INDUSTRY_ENTITY,
      subIndustryIds,
    );
    if (dto.selectedSubIndustryId) {
      await this.organizationAdAccountService.updatePlatformAdAccountIndustryData(
        userId,
        accountIds,
        dto.selectedSubIndustryId,
      );
      return {
        message: `Sub-industry - ${dto.selectedSubIndustryId} assigned to ${accountIds.length} ad accounts successfully`,
      };
    } else if (dto.unselectedSubIndustryIds) {
      await this.organizationAdAccountService.updatePlatformAdAccountIndustryData(
        userId,
        accountIds,
        industryId,
      );
      return {
        message: `All the mappings with sub-industry id - ${dto.unselectedSubIndustryIds} are removed from the selected ad accounts successfully`,
      };
    }
  }
}
