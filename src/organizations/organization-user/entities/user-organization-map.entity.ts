import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { Organization } from '../../entities/organization.entity';
import { User } from '../../../entities/user.entity';

@Entity('person_organization_map')
export class UserOrganizationMap {
  @PrimaryColumn({ type: 'varchar', name: 'organization_id' })
  organizationId: string;

  @PrimaryColumn({ type: 'bigint', name: 'person_id' })
  userId: number;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'person_id' })
  user: User;
}
