import { AutoMap } from '@automapper/classes';
import {
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
} from 'typeorm';
import { Organization } from '../../entities/organization.entity';
import { User } from '../../../entities/user.entity';
import { Role } from '../../../entities/role.entity';

@Entity('organization_person_role')
export class OrganizationUserRole {
  @PrimaryColumn({ type: 'varchar', name: 'organization_id' })
  organizationId: string;

  @PrimaryColumn({ type: 'bigint', name: 'person_id' })
  userId: number;

  @PrimaryColumn({ type: 'bigint', name: 'role_id' })
  roleId: number;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'person_id' })
  user: User;

  @ManyToOne(() => Role)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @AutoMap()
  @CreateDateColumn({ name: 'date_created' })
  dateCreated: Date;
}
