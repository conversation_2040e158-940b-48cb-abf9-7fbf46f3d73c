import {
  Controller,
  Get,
  Body,
  Param,
  Delete,
  ParseIntPipe,
  Put,
  Query,
  NotFoundException,
  Post,
} from '@nestjs/common';
import { OrganizationUserService } from './organization-user.service';
import { UpdateOrganizationUserDto } from './dto/update-organization-user.dto';
import { ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { OrganizationUserResponseDto } from './dto/organization-user-response.dto';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
  VmApiOkUnPaginatedArrayResponse,
} from '@vidmob/vidmob-nestjs-common';
import { UUID } from 'typeorm/driver/mongodb/bson.typings';
import { ReadOrganizationUserWorkspaceDto } from './dto/read-organization-user-workspace.dto';
import { ReadLightWeightUserDto } from './dto/read-light-weight-user.dto';
import { OrganizationUserQueryService } from './organization-user-query.service';
import { ORGANIZATION_USER_FILTER_BY } from '../utils/constants';
import { ReadOrganizationUserDataDto } from './dto/read-organization-user-data.dto';
import { OrganizationWorkspaceUserRequestDto } from './dto/organization-workspace-user-request.dto';

@ApiTags('organization-user')
@Controller('organization')
export class OrganizationUserController {
  constructor(
    private readonly organizationUserService: OrganizationUserService,
    private readonly organizationUserQueryService: OrganizationUserQueryService,
  ) {}

  /**
   * Returns a list of organizations associated with a given user ID,
   * along with a count of workspaces the user is associated with in each organization.
   * @param userId The user's ID.
   * @param search Search string on the organization name or id.
   * @param paginationOptions Pagination options.
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadOrganizationUserWorkspaceDto,
  })
  @ApiQuery({
    name: 'search',
    type: 'string',
    description: 'Search string on the organization name or id',
  })
  @Get('user/:userId')
  findUserAssociatedOrganizationsAndWorkspaceCount(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('search') search: string | undefined,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return this.organizationUserService.findOrganizationsByUserWithWorkspaceCount(
      userId,
      search,
      paginationOptions,
    );
  }

  /**
   * Lists organizations based on the user's role (either org admin or org standard),
   * filtering to include only those with the brand governance feature enabled in
   * at least one of their workspaces.
   *
   * @param userId The user's ID.
   * @param search Search string on the organization name or id.
   * @param paginationOptions Pagination options.
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadOrganizationUserWorkspaceDto,
  })
  @ApiQuery({
    name: 'search',
    type: 'string',
    description: 'Search string on the organization name or id',
  })
  @Get('user/:userId/organization/brand-governance')
  listOrgsWithBrandGovernanceWorkspaces(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('search') search: string | undefined,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return this.organizationUserService.listOrgsWithBrandGovernanceWorkspaces(
      userId,
      search,
      paginationOptions,
    );
  }

  /**
   * Lightweight endpoint to load all users within an organization.
   * The purpose of this endpoint is to provide a fast query to retrieve all users, without much querying.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all users belong.
   * @returns - all organization users: name, id and email
   */
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadLightWeightUserDto,
  })
  @Get(':organizationId/user/all')
  async getAllUsers(@Param('organizationId') organizationId: string) {
    return await this.organizationUserService.getAllLightWeight(organizationId);
  }

  /**
   * Return all users data across the organization, filtered by VIDMOB_ONLY or NO_VIDMOB
   * @param organizationId org id
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadOrganizationUserDataDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiQuery({
    name: 'filterBy',
    type: 'string',
    description:
      'filter the results whether for vidmob employees or no vidmob employees. Example: VIDMOB_ONLY or NO_VIDMOB',
  })
  @Get(':organizationId/user-workspace')
  findAllUserWorkspace(
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('organizationId') organizationId: string,
    @Query('filterBy') filterBy?: ORGANIZATION_USER_FILTER_BY,
  ) {
    return this.organizationUserService.findAllUserWorkspacesByOrganization(
      organizationId,
      filterBy,
      paginationOptions,
    );
  }

  /**
   * This endpoint returns the information about the user in the organization.
   * @param organizationId Id of the Organization
   * @param userId Id of the user
   */
  @VmApiOkResponse({
    type: OrganizationUserResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiParam({
    name: 'userId',
    type: Number,
    description: 'Id of the user',
  })
  @Get(':organizationId/user/:userId')
  async getUser(
    @Param('organizationId') organizationId: string,
    @Param('userId') userId: number,
  ) {
    const organization =
      await this.organizationUserQueryService.findOrganization(organizationId);

    if (!organization) throw new NotFoundException('Organization not found');

    const user = await this.organizationUserQueryService.findUser(userId);

    if (!user) throw new NotFoundException('User not found');

    return await this.organizationUserService.getUserInOrganization(
      organization,
      user,
    );
  }

  /**
   * Return all users for the organization.
   * @param organizationId org id
   */
  @VmApiOkPaginatedArrayResponse({
    type: OrganizationUserResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiQuery({
    name: 'search',
    type: 'string',
    description: 'Search string on the username',
  })
  @ApiQuery({
    name: 'filterBy',
    type: 'string',
    description:
      'filter the results whether for vidmob employees or no vidmob employees. Example: VIDMOB_ONLY or NO_VIDMOB',
  })
  @Get(':organizationId/user')
  findAll(
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('organizationId') organizationId: string,
    @Query('search') search?: string,
    @Query('filterBy') filterBy?: ORGANIZATION_USER_FILTER_BY,
  ) {
    return this.organizationUserService.findAll(
      organizationId,
      search,
      filterBy,
      paginationOptions,
    );
  }

  /**
   * Update roles of a user on an organization.
   */
  @VmApiOkResponse({
    type: OrganizationUserResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiParam({
    name: 'id',
    type: 'number',
    description: 'User ID to update on the organization',
  })
  @Put(':organizationId/user/:id')
  update(
    @Param('organizationId') organizationId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrganizationUserDto: UpdateOrganizationUserDto,
  ) {
    return this.organizationUserService.update(
      organizationId,
      id,
      updateOrganizationUserDto,
    );
  }

  /**
   * Remove a user from an organization.
   */
  @VmApiOkResponse({
    type: OrganizationUserResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiParam({
    name: 'userId',
    type: 'number',
    description: 'User ID to update on the organization',
  })
  @Delete(':organizationId/user/:userId')
  async remove(
    @Param('organizationId') organizationId: string,
    @Param('userId', ParseIntPipe) userId: number,
  ) {
    const organization =
      await this.organizationUserQueryService.findOrganization(organizationId);

    if (!organization) throw new NotFoundException('Organization not found');

    const user = await this.organizationUserQueryService.findUser(userId);

    if (!user) throw new NotFoundException('User not found');

    await this.organizationUserService.validateUserExistsInOrganizationOrThrowError(
      organization,
      user,
    );

    return this.organizationUserService.remove(organization, user);
  }

  @Post(':organizationId/user')
  async getUsersByWorkspaceIds(
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Body() body: OrganizationWorkspaceUserRequestDto,
  ) {
    return this.organizationUserService.getUsersByWorkspaceIds(
      organizationId,
      body.workspaceIds,
      paginationOptions,
    );
  }
}
