import { AutoMap } from '@automapper/classes';

export class ReadOrganizationUserWorkspaceDto {
  @AutoMap()
  id: string;

  @AutoMap()
  name: string;

  @AutoMap()
  associatedWorkspaces: number | string;

  @AutoMap()
  redirectWorkspaceId: number | string;

  @AutoMap()
  importsEnabled: boolean;

  /**
   * ACS session idle timeout in minutes
   */
  @AutoMap()
  sessionIdleTimeoutMin: number;
}
