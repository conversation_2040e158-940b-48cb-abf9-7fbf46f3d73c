import { AutoMap } from '@automapper/classes';

/**
 * Represents the raw result structure for an organization-user query.
 */
export class ReadOrganizationUserWorkspaceEntity {
  /**
   * Dummy field for AutoMapper compatibility.
   * This field is mapped from 'organization_id' in the AutoMapper configuration.
   */
  id: string;

  /**
   * The unique identifier for the organization.
   */
  @AutoMap()
  organization_id: string;

  /**
   * Dummy field for AutoMapper compatibility.
   * This field is mapped from 'organization_name' in the AutoMapper configuration.
   */
  name: string;

  /**
   * The name of the organization.
   */
  @AutoMap()
  organization_name: string;

  /**
   * The count of workspaces associated with the organization,
   * determined based on the user's role within the organization.
   */
  @AutoMap()
  associatedWorkspaces: string;

  /**
   * The id of the default workspace
   * to which the admin user should be redirected upon selecting this organization.
   */
  @AutoMap()
  redirectWorkspaceId: string;

  /**
   * The id of the default workspace
   * to which the standard user should be redirected upon selecting this organization.
   */
  @AutoMap()
  standardRedirectWorkspaceId?: string;

  @AutoMap()
  user_role: string;

  /**
   * Dummy field for AutoMapper compatibility.
   * This field is mapped from 'feature.identifier' in the AutoMapper configuration.
   */
  importsEnabled: boolean;

  @AutoMap()
  imports_enabled: string;

  /**
   * Dummy field for AutoMapper compatibility.
   * This field is mapped from 'session_idle_timeout_min' in the AutoMapper configuration.
   */
  sessionIdleTimeoutMin: number;

  /**
   * ACS session idle timeout in minutes
   */
  @AutoMap()
  session_idle_timeout_min: number;
}
