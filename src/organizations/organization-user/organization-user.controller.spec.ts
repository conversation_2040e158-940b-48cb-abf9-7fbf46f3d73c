import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationUserController } from './organization-user.controller';
import { OrganizationUserService } from './organization-user.service';
import { OrganizationUserQueryService } from './organization-user-query.service';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Organization } from '../entities/organization.entity';
import { User } from 'src/entities/user.entity';

describe('OrganizationUserController', () => {
  let controller: OrganizationUserController;
  let organizationUserService: OrganizationUserService;
  let organizationUserQueryService: OrganizationUserQueryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrganizationUserController],
      providers: [
        {
          provide: OrganizationUserService,
          useValue: {
            validateUserExistsInOrganizationOrThrowError: jest.fn(),
            remove: jest.fn(),
          },
        },
        {
          provide: OrganizationUserQueryService,
          useValue: {
            findOrganization: jest.fn(),
            findUser: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<OrganizationUserController>(
      OrganizationUserController,
    );

    organizationUserService = module.get<OrganizationUserService>(
      OrganizationUserService,
    );
    organizationUserQueryService = module.get<OrganizationUserQueryService>(
      OrganizationUserQueryService,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('remove', () => {
    const organization = new Organization();
    const user = new User();
    const response = {
      message: 'User removed from organization',
    };

    it('should throw error if organization cannot be found', async () => {
      jest
        .spyOn(organizationUserQueryService, 'findOrganization')
        .mockResolvedValue(null);

      await expect(controller.remove('xxx', 1234)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw error if user cannot be found', async () => {
      jest
        .spyOn(organizationUserQueryService, 'findOrganization')
        .mockResolvedValue(organization);

      jest
        .spyOn(organizationUserQueryService, 'findUser')
        .mockResolvedValue(null);

      await expect(controller.remove('xxx', 1234)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should thrown error if user is not in organization', async () => {
      jest
        .spyOn(organizationUserQueryService, 'findOrganization')
        .mockResolvedValue(organization);

      jest
        .spyOn(organizationUserQueryService, 'findUser')
        .mockResolvedValue(user);

      jest
        .spyOn(
          organizationUserService,
          'validateUserExistsInOrganizationOrThrowError',
        )
        .mockRejectedValue(
          new BadRequestException('User is not in the organization'),
        );

      await expect(controller.remove('xxx', 1234)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should remove user from organization', async () => {
      jest
        .spyOn(organizationUserQueryService, 'findOrganization')
        .mockResolvedValue(organization);

      jest
        .spyOn(organizationUserQueryService, 'findUser')
        .mockResolvedValue(user);

      jest
        .spyOn(
          organizationUserService,
          'validateUserExistsInOrganizationOrThrowError',
        )
        .mockResolvedValue();

      jest.spyOn(organizationUserService, 'remove').mockResolvedValue(response);

      await expect(controller.remove('xxx', 1234)).resolves.toBe(response);
    });
  });
});
