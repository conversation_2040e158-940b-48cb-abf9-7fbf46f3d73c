import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationUserService } from './organization-user.service';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { OrganizationUserQueryService } from './organization-user-query.service';
import { RoleService } from 'src/role/role.service';
import { WorkspaceUserService } from 'src/workspace-user/workspace-user.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UserOrganizationMap } from './entities/user-organization-map.entity';
import { OrganizationUserRole } from './entities/organization-user-role.entity';
import { QueryRunner, Repository } from 'typeorm';
import { Workspace } from 'src/workspaces/entities/workspace.entity';
import { Organization } from '../entities/organization.entity';
import { User } from 'src/entities/user.entity';
import { BadRequestException } from '@nestjs/common';
import { LoginEventService } from 'src/login-event/login-event.service';
import {
  adminRole,
  loginEvents,
  organization,
  organizationUserRoles,
  standardRole,
  user1,
  user1LoginEvent,
  user2,
  user2LoginEvent,
  user3,
  userOrganizationMaps,
  workspace1,
  workspace2,
  workspace3,
  workspace4,
  workspaceManagers,
  workspaceUsers,
} from './mock/organization-user-mock';
import { ORGANIZATION_USER_FILTER_BY } from '../utils/constants';
import { OrganizationUserProfile } from './mappers/organization-user.profile';

const createQueryBuilder: any = {
  delete: () => createQueryBuilder,
  from: () => createQueryBuilder,
  where: () => createQueryBuilder,
  execute: () => createQueryBuilder,
};

const mockedQueryRunner: Partial<QueryRunner> = {
  connect: jest.fn(),
  startTransaction: jest.fn(),
  commitTransaction: jest.fn(),
  rollbackTransaction: jest.fn(),
  release: jest.fn(),
};

describe('OrganizationUserService', () => {
  let service: OrganizationUserService;
  let workspaceUserService: WorkspaceUserService;
  let organizationUserQueryService: OrganizationUserQueryService;
  let loginEventService: LoginEventService;
  let userOrganizationMapRepository: Repository<UserOrganizationMap>;
  let organizationUserRoleRepository: Repository<OrganizationUserRole>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [
        OrganizationUserService,
        OrganizationUserProfile,
        {
          provide: OrganizationUserQueryService,
          useValue: {
            findAllOrganizationWorkspaces: jest.fn(),
            findUserOrganizationMap: jest.fn(),
            findUserOrganizationMaps: jest.fn(),
            findOrganizationUserRoles: jest.fn(),
          },
        },
        {
          provide: RoleService,
          useValue: {},
        },
        {
          provide: WorkspaceUserService,
          useValue: {
            deleteAllWorkspaceUser: jest.fn(),
            deleteAllWorkspaceManager: jest.fn(),
            findAllWorkspaceUsersByOrgIdAndUserIds: jest.fn(),
            findAllWorkspaceManagersByOrgIdAndUserIds: jest.fn(),
          },
        },
        {
          provide: LoginEventService,
          useValue: {
            findLatestSuccessfulLoginForUsers: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserOrganizationMap),
          useValue: {
            queryRunner: mockedQueryRunner,
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(OrganizationUserRole),
          useValue: {
            queryRunner: mockedQueryRunner,
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    service = module.get<OrganizationUserService>(OrganizationUserService);
    workspaceUserService =
      module.get<WorkspaceUserService>(WorkspaceUserService);
    organizationUserQueryService = module.get<OrganizationUserQueryService>(
      OrganizationUserQueryService,
    );
    loginEventService = module.get<LoginEventService>(LoginEventService);

    userOrganizationMapRepository = module.get(
      getRepositoryToken(UserOrganizationMap),
    );
    organizationUserRoleRepository = module.get(
      getRepositoryToken(OrganizationUserRole),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAllUserWorkspacesByOrganization', () => {
    beforeEach(() => {
      jest.resetAllMocks();

      jest
        .spyOn(organizationUserQueryService, 'findUserOrganizationMaps')
        .mockResolvedValue([userOrganizationMaps, 2]);

      jest
        .spyOn(workspaceUserService, 'findAllWorkspaceUsersByOrgIdAndUserIds')
        .mockResolvedValue(workspaceUsers);

      jest
        .spyOn(
          workspaceUserService,
          'findAllWorkspaceManagersByOrgIdAndUserIds',
        )
        .mockResolvedValue(workspaceManagers);

      jest
        .spyOn(organizationUserQueryService, 'findOrganizationUserRoles')
        .mockResolvedValue(organizationUserRoles);

      jest
        .spyOn(loginEventService, 'findLatestSuccessfulLoginForUsers')
        .mockResolvedValue(loginEvents);
    });

    it('should retrieve all organization users with its roles and the workspaces they are in', async () => {
      await expect(
        service.findAllUserWorkspacesByOrganization(
          organization.id,
          ORGANIZATION_USER_FILTER_BY.NO_VIDMOB,
          {},
        ),
      ).resolves.toEqual({
        items: [
          {
            ...user1,
            workspaces: [workspace1, workspace2, workspace3],
            roles: [adminRole],
            lastLoginDate: user1LoginEvent.dateCreated,
          },
          {
            ...user2,
            workspaces: [workspace1, workspace4],
            roles: [standardRole],
            lastLoginDate: user2LoginEvent.dateCreated,
          },
          {
            ...user3,
            workspaces: [],
            roles: [standardRole],
            lastLoginDate: null,
          },
        ],
        totalCount: 2,
        queryId: undefined,
      });
    });
  });

  describe('validateUserExistsInOrganizationOrThrowError', () => {
    afterEach(() => jest.restoreAllMocks());

    it('should not thrown an error when user organization map can be found', async () => {
      jest
        .spyOn(organizationUserQueryService, 'findUserOrganizationMap')
        .mockResolvedValue(new UserOrganizationMap());

      await expect(
        service.validateUserExistsInOrganizationOrThrowError(
          new Organization(),
          new User(),
        ),
      ).resolves.not.toThrow();
    });

    it('should thrown an error when user organization map cannot be found', async () => {
      jest
        .spyOn(organizationUserQueryService, 'findUserOrganizationMap')
        .mockResolvedValue(null);

      await expect(
        service.validateUserExistsInOrganizationOrThrowError(
          new Organization(),
          new User(),
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('remove', () => {
    let executeSpy: jest.SpyInstance;
    let commitTransactionSpy: jest.SpyInstance;
    let rollbackTransactionSpy: jest.SpyInstance;
    let releaseSpy: jest.SpyInstance;

    const workspaces = [{ id: 123 }, { id: 456 }] as Workspace[];
    const organization = new Organization();
    const user = new User();

    const response = {
      message: 'User removed from organization',
    };
    beforeEach(() => {
      jest
        .spyOn(organizationUserQueryService, 'findAllOrganizationWorkspaces')
        .mockResolvedValue(workspaces);

      jest
        .spyOn(userOrganizationMapRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      jest
        .spyOn(organizationUserRoleRepository, 'createQueryBuilder')
        .mockImplementationOnce(() => createQueryBuilder);

      executeSpy = jest.spyOn(createQueryBuilder, 'execute');
      commitTransactionSpy = jest.spyOn(mockedQueryRunner, 'commitTransaction');
      rollbackTransactionSpy = jest.spyOn(
        mockedQueryRunner,
        'rollbackTransaction',
      );
      releaseSpy = jest.spyOn(mockedQueryRunner, 'release');
    });

    afterEach(() => jest.restoreAllMocks());

    it('should remove the user from the organization and workspaces', async () => {
      await expect(service.remove(organization, user)).resolves.toStrictEqual(
        response,
      );
      expect(executeSpy).toBeCalledTimes(2);
      expect(commitTransactionSpy).toBeCalledTimes(1);
      expect(releaseSpy).toBeCalledTimes(1);
      expect(rollbackTransactionSpy).not.toBeCalled();
    });
  });
});
