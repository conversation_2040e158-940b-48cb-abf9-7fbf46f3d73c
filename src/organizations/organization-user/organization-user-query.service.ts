import { Injectable, NotFoundException } from '@nestjs/common';
import { OrganizationUserRole } from './entities/organization-user-role.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { UserOrganizationMap } from './entities/user-organization-map.entity';
import {
  Brackets,
  In,
  QueryR<PERSON><PERSON>,
  Raw,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { Organization } from '../entities/organization.entity';
import { User } from 'src/entities/user.entity';
import { Role } from 'src/entities/role.entity';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { ReadOrganizationUserWorkspaceDto } from './dto/read-organization-user-workspace.dto';
import {
  OrganizationUserRoles,
  PermissionResource,
  PermissionSubResource,
  PermissionType,
  RoleType,
} from '../../common/constants/constants';
import { Workspace } from '../../workspaces/entities/workspace.entity';
import {
  ACCOUNT_TYPE_PUBLIC_SCOPE,
  FEATURE_INTEGRATION_ACCOUNT_IMPORTS_ENABLED,
  ORGANIZATION_USER_FILTER_BY,
  SELF_MANAGED_IDENTIFIER,
  featureBrandGovernanceIdentifier,
} from '../utils/constants';

@Injectable()
export class OrganizationUserQueryService {
  constructor(
    @InjectRepository(OrganizationUserRole)
    private readonly organizationUserRoleRepository: Repository<OrganizationUserRole>,
    @InjectRepository(Workspace)
    private readonly workspaceRepository: Repository<Workspace>,
    @InjectRepository(UserOrganizationMap)
    private readonly userOrganizationMapRepository: Repository<UserOrganizationMap>,
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  async getAllUserOrganizationLightWeight(organizationId: string) {
    return await this.userRepository
      .createQueryBuilder('user')
      .innerJoin(
        UserOrganizationMap,
        'userOrganizationMap',
        'userOrganizationMap.user = user.id',
      )
      .where('userOrganizationMap.organization_id = :organizationId', {
        organizationId,
      })
      .andWhere('user.enabled = true')
      .andWhere('user.accountLocked = false')
      .andWhere('user.accountExpired = false')
      .getMany();
  }

  async getAllUsersByWorkspaceIds(
    organizationId: string,
    workspaceIds: number[],
    paginationOptions?: PaginationOptions,
  ) {
    return await this.userRepository
      .createQueryBuilder('user')
      .select(['user.id', 'user.displayName', 'user.photo'])
      .innerJoin(
        UserOrganizationMap,
        'userOrganizationMap',
        'userOrganizationMap.user = user.id',
      )
      .innerJoin(
        Workspace,
        'workspace',
        'workspace.organizationId = userOrganizationMap.organizationId',
      )
      .where('userOrganizationMap.organization_id = :organizationId', {
        organizationId,
      })
      .andWhere('user.enabled = true')
      .andWhere('user.accountLocked = false')
      .andWhere('user.accountExpired = false')
      .andWhere('workspace.id IN (:...workspaceIds)', { workspaceIds })
      .orderBy('user.displayName', 'ASC')
      .take(paginationOptions?.perPage)
      .skip(paginationOptions?.offset)
      .getManyAndCount();
  }

  findUserOrganizationMaps(
    organizationId: string,
    search: string | undefined,
    filterUserBy: ORGANIZATION_USER_FILTER_BY | undefined,
    paginationOptions: PaginationOptions,
  ): Promise<[UserOrganizationMap[], number]> {
    const queryBase = this.userOrganizationMapRepository
      .createQueryBuilder('userOrganizationMap')
      .innerJoinAndSelect('userOrganizationMap.organization', 'organization')
      .innerJoinAndSelect('userOrganizationMap.user', 'user')
      .where('organization.id = :organizationId', { organizationId })
      .andWhere('user.enabled = true')
      .andWhere('user.accountLocked = false')
      .andWhere('user.accountExpired = false');

    if (search) {
      queryBase.andWhere(
        '(user.displayName LIKE :search OR user.username LIKE :search)',
        {
          search: `%${search}%`,
        },
      );
    }

    if (filterUserBy === ORGANIZATION_USER_FILTER_BY.VIDMOB_ONLY) {
      queryBase.andWhere('user.email LIKE :vidmobUser', {
        vidmobUser: `%@vidmob.com`,
      });
    }

    if (filterUserBy === ORGANIZATION_USER_FILTER_BY.NO_VIDMOB) {
      queryBase.andWhere('user.email NOT LIKE :vidmobUser', {
        vidmobUser: `%@vidmob.com`,
      });
    }

    return queryBase
      .orderBy({
        'user.displayName': 'ASC',
        'user.id': 'ASC',
      })
      .limit(paginationOptions.perPage)
      .offset(paginationOptions.offset)
      .getManyAndCount();
  }

  findOrganizationUserByEmails(
    organizationId: string,
    emails: string[],
  ): Promise<User[]> {
    return this.userRepository
      .createQueryBuilder('user')
      .innerJoin(
        UserOrganizationMap,
        'userOrganizationMap',
        'userOrganizationMap.user = user.id',
      )
      .where('userOrganizationMap.organization_id = :organizationId', {
        organizationId,
      })
      .andWhere('user.email IN (:...emails)', { emails })
      .getMany();
  }

  // Inactive emails are either account emails that are expired or locked
  async findInactivePeopleByEmail(emails: string[]): Promise<string[]> {
    const inactivePeople = await this.userRepository.find({
      where: [
        { email: In(emails), accountExpired: true },
        { email: In(emails), accountLocked: true },
      ],
    });

    return inactivePeople.map((inactivePerson) => inactivePerson.email);
  }

  findOrganizationUserRoles(
    organizationId: string,
  ): Promise<OrganizationUserRole[]> {
    return this.organizationUserRoleRepository.find({
      relations: {
        organization: true,
        user: true,
        role: true,
      },
      where: {
        organization: Raw((alias) => `${alias} = :organizationId`, {
          organizationId,
        }),
      },
    });
  }

  findOrganizationUserRolesForUser(
    organization: Organization,
    user: User,
  ): Promise<OrganizationUserRole[]> {
    return this.organizationUserRoleRepository.find({
      relations: {
        organization: true,
        user: true,
        role: true,
      },
      where: {
        organization: Raw((alias) => `${alias} = :organizationId`, {
          organizationId: organization.id,
        }),
        user: Raw((alias) => `${alias} = :userId`, {
          userId: user.id,
        }),
      },
    });
  }

  async findUserOrganizationMap(
    organization: Organization,
    user: User,
  ): Promise<UserOrganizationMap> {
    return await this.userOrganizationMapRepository.findOneBy({
      organization: Raw((alias) => `${alias} = :organizationId`, {
        organizationId: organization.id,
      }),
      user: Raw((alias) => `${alias} = :userId`, {
        userId: user.id,
      }),
    });
  }

  // TODO: move this to RoleService (this service is already built in develop branch)
  async areRolesValidOrganizationRoles(roleIds: number[]) {
    const roles = await this.roleRepository.findBy({
      id: In(roleIds),
      type: RoleType.ORGANIZATION_ROLE_TYPE,
    });

    return roles.length == roleIds.length;
  }

  async findAllOrganizationWorkspaces(
    organizationId: string,
  ): Promise<Workspace[]> {
    return await this.workspaceRepository.findBy({
      organizationId,
    });
  }

  findOrganization(organizationId: string): Promise<Organization> {
    return this.organizationRepository.findOneBy({
      id: organizationId,
    });
  }

  findUser(userId: number): Promise<User> {
    return this.userRepository.findOneBy({
      id: userId,
    });
  }

  findUserByEmail(userEmail: string): Promise<User> {
    return this.userRepository.findOneBy({
      email: userEmail,
    });
  }

  async createUserToOrganizationMapIfNotExists(
    user: User,
    organization: Organization,
  ): Promise<void> {
    const org = await this.userOrganizationMapRepository.findOne({
      relations: {
        organization: true,
        user: true,
      },
      where: {
        organization: Raw((alias) => `${alias} = :organizationId`, {
          organizationId: organization.id,
        }),
        user: Raw((alias) => `${alias} = :userId`, {
          userId: user.id,
        }),
      },
    });
    if (!org) {
      await this.createUserOrganizationMap(user, organization);
    }
  }

  async createUserOrganizationMapTransactional(
    user: User,
    organization: Organization,
    queryRunner: QueryRunner,
  ) {
    const userOrg = new UserOrganizationMap();
    userOrg.organization = organization;
    userOrg.user = user;

    await this.userOrganizationMapRepository
      .createQueryBuilder('userOrganizationMap', queryRunner)
      .insert()
      .into(UserOrganizationMap)
      .values(userOrg)
      .orIgnore()
      .execute();
  }

  async createUserOrganizationMap(user: User, organization: Organization) {
    const userOrg = new UserOrganizationMap();
    userOrg.organization = organization;
    userOrg.user = user;
    return this.userOrganizationMapRepository.save(userOrg);
  }

  async createOrganizationUserRoleTransactional(
    user: User,
    organization: Organization,
    role: Role,
    queryRunner: QueryRunner,
  ) {
    const userRole = new OrganizationUserRole();
    userRole.organization = organization;
    userRole.user = user;
    userRole.role = role;
    userRole.dateCreated = new Date(Date.now());

    await this.organizationUserRoleRepository
      .createQueryBuilder('organizationUserRole', queryRunner)
      .insert()
      .into(OrganizationUserRole)
      .values(userRole)
      .orIgnore()
      .execute();
  }

  async createOrganizationUserRoles(
    user: User,
    organization: Organization,
    roles: Role[],
  ): Promise<void> {
    const userRoles = roles.map((role) => {
      const userRole = new OrganizationUserRole();
      userRole.organization = organization;
      userRole.user = user;
      userRole.role = role;
      return userRole;
    });
    await this.organizationUserRoleRepository.save(userRoles);
  }

  async deleteOrganizationUserRoles(
    user: User,
    organization: Organization,
    roles: Role[],
  ): Promise<void> {
    const userRoles = roles.map((role) => {
      const userRole = new OrganizationUserRole();
      userRole.organization = organization;
      userRole.user = user;
      userRole.role = role;
      return userRole;
    });
    await this.organizationUserRoleRepository.remove(userRoles);
  }

  async findOrganizationsWithAssociatedWorkspaces(
    userId: number,
    search: string | undefined,
    paginationOptions: PaginationOptions,
  ): Promise<[ReadOrganizationUserWorkspaceDto[], number]> {
    await this.validateUserExists(userId);

    const queryBuilder = this.createBaseQueryBuilder(userId);

    this.applySearchFilter(queryBuilder, search);

    const rawOrganizationResult = await queryBuilder.getRawMany();

    const organizationDetailsQueryBuilder: ReadOrganizationUserWorkspaceDto[] =
      await this.applyRoleBasedFilter(rawOrganizationResult, userId);

    this.applyPaginationAndOrdering(queryBuilder, paginationOptions);

    const organizationList = this.applyObjectPaginationAndOrdering(
      organizationDetailsQueryBuilder,
      paginationOptions,
    );

    return [organizationList, organizationDetailsQueryBuilder.length];
  }

  async listOrgsWithBrandGovernanceWorkspaces(
    userId: number,
    search: string | undefined,
    paginationOptions: PaginationOptions,
  ): Promise<[ReadOrganizationUserWorkspaceDto[], number]> {
    await this.validateUserExists(userId);

    const queryBuilder = this.createBaseQueryBuilder(userId);

    queryBuilder
      .leftJoin('workspace.accountType', 'accountType')
      .leftJoin('workspace.featureWorkspaces', 'featureWorkspace')
      .leftJoin('featureWorkspace.featureWhiteList', 'featureWhiteList')
      .leftJoin('accountType.featureAccountType', 'featureAccountType')
      .leftJoin(
        'featureAccountType.featureWhiteList',
        'featureWhiteListAccountType',
      )
      .where('organizationUserRole.person_id = :userId', { userId });

    this.applyBrandGovernanceFilter(queryBuilder);

    queryBuilder.groupBy('organization.id').orderBy('organization.name', 'ASC');

    this.applySearchFilter(queryBuilder, search);

    const rawOrganizationResult = await queryBuilder.getRawMany();

    const organizationDetailsQueryBuilder: ReadOrganizationUserWorkspaceDto[] =
      await this.applyRoleBasedFilterFromBrandGovernanceWorkspaces(
        rawOrganizationResult,
        userId,
      );

    this.applyPaginationAndOrdering(queryBuilder, paginationOptions);

    const organizationList = this.applyObjectPaginationAndOrdering(
      organizationDetailsQueryBuilder,
      paginationOptions,
    );

    return [organizationList, organizationDetailsQueryBuilder.length];
  }

  async areWorkspacesBelongsToOrganization(
    organizationId: string,
    workspaceIds: number[],
  ) {
    const workspacesCount = await this.workspaceRepository.countBy({
      id: In(workspaceIds),
      organizationId,
    });

    return workspacesCount === workspaceIds.length;
  }

  async isUserOrganizationAdmin(
    organizationId: string,
    userId: number,
  ): Promise<boolean> {
    const organizationAdminUserRole =
      await this.organizationUserRoleRepository.findOneBy({
        organizationId,
        userId,
        role: {
          identifier: OrganizationUserRoles.ORG_ADMIN,
          type: RoleType.ORGANIZATION_ROLE_TYPE,
        },
      });

    return organizationAdminUserRole !== null;
  }

  private createBaseQueryBuilder(userId: number) {
    // Base query builder, only with necessary joins
    const queryBuilder = this.organizationRepository
      .createQueryBuilder('organization')
      .leftJoinAndSelect(
        'organization.organizationUserRoles',
        'organizationUserRole',
        'organizationUserRole.person_id = :userId',
        { userId },
      )
      .leftJoinAndSelect('organizationUserRole.role', 'role')
      .select([
        'organization.id AS organization_id',
        'role.identifier AS user_role',
      ])
      .innerJoin('organization.workspaces', 'workspace')
      .where('organizationUserRole.person_id = :userId', { userId })
      .groupBy('organization.id')
      .orderBy('organization.name', 'ASC');

    return queryBuilder;
  }

  /* If mapped roleIdentifiers are not ORG_STANDARD or ORG_ADMIN, then the user has no permissions
   and the organization should not be returned */
  private async applyRoleBasedFilter(organizations: any[], userId: number) {
    const standardOrgIds = [];
    const adminOrgIds = [];

    // Separate the organization IDs based on the user role
    organizations.forEach((organization) => {
      if (organization.user_role === OrganizationUserRoles.ORG_STANDARD) {
        standardOrgIds.push(organization.organization_id);
      } else if (organization.user_role === OrganizationUserRoles.ORG_ADMIN) {
        adminOrgIds.push(organization.organization_id);
      }
    });

    const standardOrgDetails =
      standardOrgIds.length > 0
        ? await this.fetchStandardOrganizationDetails(userId, standardOrgIds)
        : [];

    const adminOrgDetails =
      adminOrgIds.length > 0
        ? await this.fetchAdminOrganizationDetails(adminOrgIds)
        : [];

    // Merge the two sets of results
    return [...standardOrgDetails, ...adminOrgDetails];
  }

  private applySearchFilter(queryBuilder: any, search?: string) {
    if (search && search.trim().length > 0) {
      queryBuilder.andWhere(
        '(organization.name LIKE :nameSearch OR organization.id = :idSearch)',
        { nameSearch: `%${search}%`, idSearch: search },
      );
    }
  }

  private async fetchStandardOrganizationDetails(
    userId: number,
    standardOrganizationIds: string[],
  ): Promise<any> {
    const organizationDetailsQueryBuilder = await this.workspaceRepository
      .createQueryBuilder('workspace')
      .leftJoinAndSelect('workspace.organization', 'organization')

      .leftJoin('workspace.workspaceUsers', 'workspaceUser')
      .leftJoin('workspace.workspaceManagers', 'workspaceManager')
      .where(
        `((workspaceUser.userId = :userId AND workspaceUser.active = 1) OR workspaceManager.managerId = :userId)`,
        { userId },
      )
      .andWhere('workspace.organizationId IN (:...standardOrganizationIds)', {
        standardOrganizationIds,
      })
      .select('workspace.organizationId', 'organization_id')
      .addSelect('organization.name', 'organization_name')
      .addSelect('organization.sessionTimeoutMin', 'session_timeout_min')
      .addSelect('COUNT(DISTINCT workspace.id)', 'associatedWorkspaces')
      .addSelect((subQuery) => {
        return subQuery
          .select(
            'case when featureWhitelist.identifier is null then FALSE else TRUE end',
          )
          .from('OrganizationFeatureWhitelist', 'organizationFeatureWhitelists')
          .leftJoin(
            'organizationFeatureWhitelists.featureWhitelists',
            'featureWhitelist',
          )
          .where('featureWhitelist.identifier = :importsEnabled', {
            importsEnabled: FEATURE_INTEGRATION_ACCOUNT_IMPORTS_ENABLED,
          })
          .andWhere(
            'organizationFeatureWhitelists.organizationId = workspace.organizationId',
          )
          .limit(1);
      }, 'imports_enabled')
      .addSelect((subQuery) => {
        return subQuery
          .select('workspaceSub.id', 'firstWorkspaceId')
          .from('Workspace', 'workspaceSub')
          .leftJoin('workspaceSub.workspaceUsers', 'workspaceUserSub')
          .leftJoin('workspaceSub.workspaceManagers', 'workspaceManagerSub')
          .where(
            `((workspaceUserSub.userId = :userId AND workspaceUserSub.active = 1) OR workspaceManagerSub.managerId = :userId) AND workspaceSub.organizationId = workspace.organizationId`,
            { userId },
          )
          .orderBy('workspaceSub.name', 'ASC')
          .limit(1);
      }, 'redirectWorkspaceId')
      .groupBy('workspace.organizationId')
      .getRawMany();

    return organizationDetailsQueryBuilder;
  }

  private async fetchAdminOrganizationDetails(
    adminOrgIds: string[],
  ): Promise<any> {
    const organizationDetailsQueryBuilder = this.workspaceRepository
      .createQueryBuilder('workspace')
      .leftJoinAndSelect('workspace.organization', 'organization')

      .andWhere(`workspace.organizationId IN (:...adminOrgIds)`, {
        adminOrgIds,
      })
      .select('workspace.organizationId', 'organization_id')
      .addSelect('organization.name', 'organization_name')
      .addSelect('organization.sessionTimeoutMin', 'session_timeout_min')
      .addSelect('COUNT(DISTINCT workspace.id)', 'associatedWorkspaces')

      .addSelect((subQuery) => {
        return subQuery
          .select(
            'case when featureWhitelist.identifier is null then FALSE else TRUE end',
          )
          .from('OrganizationFeatureWhitelist', 'organizationFeatureWhitelists')
          .leftJoin(
            'organizationFeatureWhitelists.featureWhitelists',
            'featureWhitelist',
          )
          .where('featureWhitelist.identifier = :importsEnabled', {
            importsEnabled: FEATURE_INTEGRATION_ACCOUNT_IMPORTS_ENABLED,
          })
          .andWhere(
            'organizationFeatureWhitelists.organizationId = workspace.organizationId',
          )
          .limit(1);
      }, 'imports_enabled')
      .addSelect((subQuery) => {
        return subQuery
          .select('workspaceSub.id', 'firstWorkspaceId')
          .from('Workspace', 'workspaceSub')
          .where('workspaceSub.organizationId = workspace.organizationId')
          .orderBy('workspaceSub.name', 'ASC')
          .limit(1);
      }, 'redirectWorkspaceId')
      .groupBy('workspace.organizationId')
      .getRawMany();

    return organizationDetailsQueryBuilder;
  }

  /* If mapped roleIdentifiers are not ORG_STANDARD or ORG_ADMIN, then the user has no permissions
   and the organization should not be returned */
  private async applyRoleBasedFilterFromBrandGovernanceWorkspaces(
    organizations: any[],
    userId: number,
  ) {
    const standardOrgIds = [];
    const adminOrgIds = [];

    // Separate the organization IDs based on the user role
    organizations.forEach((organization) => {
      if (organization.user_role === OrganizationUserRoles.ORG_STANDARD) {
        standardOrgIds.push(organization.organization_id);
      } else if (organization.user_role === OrganizationUserRoles.ORG_ADMIN) {
        adminOrgIds.push(organization.organization_id);
      }
    });

    const standardOrgDetails =
      standardOrgIds.length > 0
        ? await this.listStandardOrgsWithBrandGovernanceWorkspaces(
            userId,
            standardOrgIds,
          )
        : [];

    const adminOrgDetails =
      adminOrgIds.length > 0
        ? await this.listAdminOrgsWithBrandGovernanceWorkspaces(adminOrgIds)
        : [];

    // Merge the two sets of results
    return [...standardOrgDetails, ...adminOrgDetails];
  }

  async listStandardOrgsWithBrandGovernanceWorkspaces(
    userId: number,
    organizationIds: string[],
  ): Promise<any> {
    const queryBuilder =
      this.workspaceRepository.createQueryBuilder('workspace');
    this.setupBaseQuery(queryBuilder, userId, organizationIds);
    this.applyBrandGovernanceFilter(queryBuilder);

    return await this.executeFinalQuery(queryBuilder, userId, organizationIds);
  }

  private setupBaseQuery(
    queryBuilder: SelectQueryBuilder<Workspace>,
    userId: number,
    organizationIds: string[],
  ) {
    queryBuilder
      .leftJoin(
        'workspace.organization',
        'organization',
        'organization.id IN (:organizationIds)',
        { organizationIds },
      )
      .leftJoin(
        'workspace.workspaceUsers',
        'workspaceUser',
        'workspaceUser.userId = :userId AND workspaceUser.active = true',
        { userId },
      )
      .leftJoin(
        'workspace.workspaceManagers',
        'workspaceManager',
        'workspaceManager.managerId = :userId',
        { userId },
      )
      .leftJoin('workspace.accountType', 'accountType')
      .leftJoin('workspace.featureWorkspaces', 'featureWorkspace')
      .leftJoin('featureWorkspace.featureWhiteList', 'featureWhiteList')
      .leftJoin('accountType.featureAccountType', 'featureAccountType')
      .leftJoin(
        'featureAccountType.featureWhiteList',
        'featureWhiteListAccountType',
      )
      .where('workspace.personal = false')
      .andWhere('workspace.organizationId IN (:...organizationIds)', {
        organizationIds,
      });
  }

  private applyBrandGovernanceFilter<T>(queryBuilder: SelectQueryBuilder<T>) {
    queryBuilder.andWhere(
      new Brackets((qb) => {
        qb.where(
          new Brackets((qbInner) => {
            qbInner
              .where(
                'accountType.scope = :scope AND accountType.name = :name',
                {
                  scope: ACCOUNT_TYPE_PUBLIC_SCOPE,
                  name: SELF_MANAGED_IDENTIFIER,
                },
              )
              .andWhere('featureWhiteList.identifier = :featureIdentifier', {
                featureIdentifier: featureBrandGovernanceIdentifier,
              });
          }),
        ).orWhere(
          new Brackets((qbInner) => {
            qbInner
              .where(
                '(accountType.scope != :scope OR accountType.name != :name)',
                {
                  scope: ACCOUNT_TYPE_PUBLIC_SCOPE,
                  name: SELF_MANAGED_IDENTIFIER,
                },
              )
              .andWhere(
                'featureWhiteListAccountType.identifier = :featureIdentifier',
                {
                  featureIdentifier: featureBrandGovernanceIdentifier,
                },
              );
          }),
        );
      }),
    );
  }

  private async executeFinalQuery(
    queryBuilder: SelectQueryBuilder<Workspace>,
    userId: number,
    organizationIds: string[],
  ): Promise<any> {
    return queryBuilder
      .select('organization.id AS organization_id')
      .addSelect('organization.name AS organization_name')
      .addSelect('organization.sessionTimeoutMin AS session_timeout_min')
      .addSelect((subQuery) => {
        return subQuery
          .select('COUNT(DISTINCT workspaceSub.id)', 'associatedWorkspaces')
          .from('Workspace', 'workspaceSub')
          .leftJoin(
            'workspaceSub.workspaceUsers',
            'workspaceUserSub',
            'workspaceUserSub.userId = :userId AND workspaceUserSub.active = true',
            { userId },
          )
          .leftJoin(
            'workspaceSub.workspaceManagers',
            'workspaceManagerSub',
            'workspaceManagerSub.managerId = :userId',
            { userId },
          )
          .leftJoin('workspaceSub.accountType', 'accountTypeSub')
          .leftJoin('workspaceSub.featureWorkspaces', 'featureWorkspaceSub')
          .leftJoin(
            'featureWorkspaceSub.featureWhiteList',
            'featureWhiteListSub',
          )
          .leftJoin(
            'accountTypeSub.featureAccountType',
            'featureAccountTypeSub',
          )
          .leftJoin(
            'featureAccountTypeSub.featureWhiteList',
            'featureWhiteListAccountTypeSub',
          )
          .where('workspaceSub.organizationId = organization.id')
          .andWhere('workspaceSub.personal = false')
          .andWhere(
            new Brackets((qb) => {
              qb.where(
                'workspaceUserSub.userId IS NOT NULL OR workspaceManagerSub.managerId IS NOT NULL',
              ).andWhere(
                new Brackets((qbInner) => {
                  qbInner
                    .where(
                      'accountTypeSub.scope = :scope AND accountTypeSub.name = :name AND featureWhiteListSub.identifier = :featureIdentifier',
                      {
                        scope: ACCOUNT_TYPE_PUBLIC_SCOPE,
                        name: SELF_MANAGED_IDENTIFIER,
                        featureIdentifier: featureBrandGovernanceIdentifier,
                      },
                    )
                    .orWhere(
                      'featureWhiteListAccountTypeSub.identifier = :featureIdentifier AND (accountTypeSub.scope != :scope OR accountTypeSub.name != :name)',
                      {
                        featureIdentifier: featureBrandGovernanceIdentifier,
                        scope: ACCOUNT_TYPE_PUBLIC_SCOPE,
                        name: SELF_MANAGED_IDENTIFIER,
                      },
                    );
                }),
              );
            }),
          );
      }, 'associatedWorkspaces')
      .having('associatedWorkspaces > 0') // Ensures we only get organizations with valid workspaces
      .addSelect((subQuery) => {
        return subQuery
          .select('workspace.id', 'firstWorkspaceId')
          .from('Workspace', 'workspace')
          .leftJoin('workspace.featureWorkspaces', 'featureWorkspace')
          .leftJoin('featureWorkspace.featureWhiteList', 'featureWhiteList')
          .leftJoin('workspace.accountType', 'accountType')
          .leftJoin('accountType.featureAccountType', 'featureAccountType')
          .leftJoin(
            'featureAccountType.featureWhiteList',
            'featureWhiteListAccountType',
          )
          .where('workspace.organizationId = organization.id')
          .andWhere(
            new Brackets((qb) => {
              qb.where('featureWhiteList.identifier = :featureIdentifier', {
                featureIdentifier: featureBrandGovernanceIdentifier,
              }).orWhere(
                'featureWhiteListAccountType.identifier = :featureIdentifier',
                { featureIdentifier: featureBrandGovernanceIdentifier },
              );
            }),
          )
          .orderBy('workspace.name', 'ASC')
          .limit(1);
      }, 'redirectWorkspaceId')
      .where('organization.id IN (:...organizationIds)', { organizationIds })
      .groupBy('organization.id')
      .getRawMany();
  }

  async listAdminOrgsWithBrandGovernanceWorkspaces(
    adminOrgIds: string[],
  ): Promise<any> {
    const queryBuilder =
      this.workspaceRepository.createQueryBuilder('workspace');
    this.setupAdminBaseQuery(queryBuilder, adminOrgIds);
    this.applyAdminFeatureFilters(
      queryBuilder,
      featureBrandGovernanceIdentifier,
    );

    return await this.executeAdminFinalQuery(queryBuilder);
  }

  private setupAdminBaseQuery(
    queryBuilder: SelectQueryBuilder<Workspace>,
    adminOrgIds: string[],
  ) {
    queryBuilder
      .leftJoin(
        'workspace.organization',
        'organization',
        'organization.id IN (:adminOrgIds)',
        { adminOrgIds },
      )
      .leftJoin('workspace.accountType', 'accountType')
      .leftJoin('workspace.featureWorkspaces', 'featureWorkspace')
      .leftJoin('featureWorkspace.featureWhiteList', 'featureWhiteList')
      .leftJoin('accountType.featureAccountType', 'featureAccountType')
      .leftJoin(
        'featureAccountType.featureWhiteList',
        'featureWhiteListAccountType',
      )
      .where('workspace.personal = false')
      .andWhere('workspace.organizationId IN (:...adminOrgIds)', {
        adminOrgIds,
      });
  }

  private applyAdminFeatureFilters(
    queryBuilder: SelectQueryBuilder<Workspace>,
    featureIdentifier: string,
  ) {
    queryBuilder.andWhere(
      new Brackets((qb) => {
        qb.where(
          new Brackets((qbInner) => {
            qbInner
              .where(
                'accountType.scope = :scope AND accountType.name = :name',
                {
                  scope: ACCOUNT_TYPE_PUBLIC_SCOPE,
                  name: SELF_MANAGED_IDENTIFIER,
                },
              )
              .andWhere('featureWhiteList.identifier = :featureIdentifier', {
                featureIdentifier,
              });
          }),
        ).orWhere(
          new Brackets((qbInner) => {
            qbInner
              .where(
                '(accountType.scope != :scope OR accountType.name != :name)',
                {
                  scope: ACCOUNT_TYPE_PUBLIC_SCOPE,
                  name: SELF_MANAGED_IDENTIFIER,
                },
              )
              .andWhere(
                'featureWhiteListAccountType.identifier = :featureIdentifier',
                {
                  featureIdentifier,
                },
              );
          }),
        );
      }),
    );
  }

  private async executeAdminFinalQuery(
    queryBuilder: SelectQueryBuilder<Workspace>,
  ): Promise<any> {
    return queryBuilder
      .select([
        'organization.id AS organization_id',
        'organization.name AS organization_name',
        'organization.sessionTimeoutMin AS session_timeout_min',
        'COUNT(DISTINCT workspace.id) AS associatedWorkspaces',
      ])
      .addSelect((subQuery) => {
        return subQuery
          .select('workspace.id', 'firstWorkspaceId')
          .from('Workspace', 'workspace')
          .leftJoin('workspace.featureWorkspaces', 'featureWorkspace')
          .leftJoin('featureWorkspace.featureWhiteList', 'featureWhiteList')
          .leftJoin('workspace.accountType', 'accountType')
          .leftJoin('accountType.featureAccountType', 'featureAccountType')
          .leftJoin(
            'featureAccountType.featureWhiteList',
            'featureWhiteListAccountType',
          )
          .where('workspace.organizationId = organization.id')
          .andWhere('workspace.personal = false')
          .andWhere(
            new Brackets((qb) => {
              qb.where('featureWhiteList.identifier = :featureIdentifier', {
                featureIdentifier: featureBrandGovernanceIdentifier,
              }).orWhere(
                'featureWhiteListAccountType.identifier = :featureIdentifier',
                { featureIdentifier: featureBrandGovernanceIdentifier },
              );
            }),
          )
          .orderBy('workspace.name', 'ASC')
          .limit(1);
      }, 'redirectWorkspaceId')
      .groupBy('organization.id')
      .getRawMany();
  }

  private async validateUserExists(userId: number) {
    const user = await this.userRepository.findOneBy({ id: userId });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} does not exist`);
    }
  }

  private applyPaginationAndOrdering(
    queryBuilder: any,
    paginationOptions: PaginationOptions,
  ) {
    queryBuilder
      .orderBy({ 'organization.name': 'ASC' })
      .limit(paginationOptions.perPage)
      .offset(paginationOptions.offset);
  }

  private applyObjectPaginationAndOrdering(
    organizationData,
    paginationOptions,
  ) {
    // Sort the data
    const sortedData = organizationData.sort((a, b) =>
      a.organization_name.localeCompare(b.organization_name),
    );

    // Apply pagination
    const startIndex = paginationOptions.offset;
    const endIndex = startIndex + paginationOptions.perPage;
    const paginatedData = sortedData.slice(startIndex, endIndex);

    return paginatedData;
  }

  async getOrgIDsWithReadWorkspaceAllForPerson(
    personId: number,
  ): Promise<string[]> {
    const sql = `
      SELECT org.id
         FROM permission pe
                JOIN role_permission rp ON rp.permission_id = pe.id
                JOIN organization_person_role opr ON opr.role_id = rp.role_id
                JOIN organization org ON org.id = opr.organization_id
                JOIN partner pa ON pa.organization_id = org.id
         WHERE pe.resource = '${PermissionResource.ORGANIZATION}'
           AND pe.subresource = '${PermissionSubResource.WORKSPACE_ALL}'
           AND pe.type = '${PermissionType.READ}'
           AND opr.person_id = ? group by org.id`;
    return (
      (await this.organizationRepository.query(sql, [personId]))?.map(
        (row) => row.id,
      ) ?? []
    );
  }

  async getOrganizationIdsForPerson(userId: number): Promise<string[]> {
    const queryBuilder = this.organizationRepository
      .createQueryBuilder('organization')
      .select('organization.id AS id')
      .innerJoin(
        'organization.organizationUserRoles',
        'organizationUserRole',
        'organizationUserRole.person_id = :userId',
        { userId },
      );

    const response = await queryBuilder.getRawMany();
    return response.map((row) => row.id);
  }
}
