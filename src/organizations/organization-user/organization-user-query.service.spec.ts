import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrganizationUserQueryService } from './organization-user-query.service';
import { OrganizationUserRole } from './entities/organization-user-role.entity';
import { UserOrganizationMap } from './entities/user-organization-map.entity';
import { Organization } from '../entities/organization.entity';
import { User } from 'src/entities/user.entity';
import { Role } from 'src/entities/role.entity';
import { Workspace } from '../../workspaces/entities/workspace.entity';
import { NotFoundException } from '@nestjs/common';

describe('OrganizationUserQueryService', () => {
  let service: OrganizationUserQueryService;
  let mockUserOrganizationMapRepository: Repository<UserOrganizationMap>;
  let mockOrganizationUserRoleRepository: Repository<OrganizationUserRole>;
  let mockUserRepository: Repository<User>;
  let mockWorkspaceRepository: Repository<Workspace>;

  const mockRepository = () => ({
    find: jest.fn(),
    findBy: jest.fn(),
    findOneBy: jest.fn(),
    countBy: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      innerJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
    })),
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationUserQueryService,
        {
          provide: getRepositoryToken(UserOrganizationMap),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(OrganizationUserRole),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(Organization),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(Workspace),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(User),
          useFactory: mockRepository,
        },
        {
          provide: getRepositoryToken(Role),
          useFactory: mockRepository,
        },
      ],
    }).compile();

    service = module.get<OrganizationUserQueryService>(
      OrganizationUserQueryService,
    );
    mockUserOrganizationMapRepository = module.get(
      getRepositoryToken(UserOrganizationMap),
    );
    mockOrganizationUserRoleRepository = module.get(
      getRepositoryToken(OrganizationUserRole),
    );
    mockUserRepository = module.get(getRepositoryToken(User));
    mockWorkspaceRepository = module.get(getRepositoryToken(Workspace));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAllUserOrganizationLightWeight', () => {
    it('should return a list of users', async () => {
      const users = [new User(), new User()];

      jest.spyOn(mockUserRepository, 'createQueryBuilder').mockReturnValue({
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(users),
      } as any);

      const results = await service.getAllUserOrganizationLightWeight('xxx');
      expect(results).toEqual(users);
      expect(
        mockUserRepository.createQueryBuilder().getMany,
      ).toHaveBeenCalled();
    });
  });

  describe('findUserOrganizationMaps', () => {
    it('should return a list of UserOrganizationMaps and a count', async () => {
      const orgMaps: UserOrganizationMap[] = [];
      const orgMapCount = 1;

      jest
        .spyOn(mockUserOrganizationMapRepository, 'createQueryBuilder')
        .mockReturnValue({
          innerJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          orderBy: jest.fn().mockReturnThis(),
          limit: jest.fn().mockReturnThis(),
          offset: jest.fn().mockReturnThis(),
          getManyAndCount: jest.fn().mockResolvedValue([orgMaps, orgMapCount]),
        } as any);

      const paginationOptions = { perPage: 20, offset: 0 };
      const results = await service.findUserOrganizationMaps(
        'org-id',
        undefined,
        undefined,
        paginationOptions,
      );

      expect(results).toEqual([orgMaps, orgMapCount]);
      expect(
        mockUserOrganizationMapRepository.createQueryBuilder().getManyAndCount,
      ).toHaveBeenCalled();
    });
  });

  describe('findUserOrganizationMaps', () => {
    it('should return a empty array when no user organization maps are found', async () => {
      const orgMaps: UserOrganizationMap[] = [];
      const orgMapCount = 0;

      jest
        .spyOn(mockUserOrganizationMapRepository, 'createQueryBuilder')
        .mockReturnValue({
          innerJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          orderBy: jest.fn().mockReturnThis(),
          limit: jest.fn().mockReturnThis(),
          offset: jest.fn().mockReturnThis(),
          getManyAndCount: jest.fn().mockResolvedValue([orgMaps, orgMapCount]),
        } as any);

      const paginationOptions = { perPage: 20, offset: 0 };
      const results = await service.findUserOrganizationMaps(
        'org-id',
        undefined,
        undefined,
        paginationOptions,
      );

      expect(results).toEqual([orgMaps, orgMapCount]);
      expect(
        mockUserOrganizationMapRepository.createQueryBuilder().getManyAndCount,
      ).toHaveBeenCalled();
    });
  });

  describe('findOrganizationsWithAssociatedWorkspaces', () => {
    it('should throw NotFoundException for a non-existing user', async () => {
      const nonExistingUserId = 999;

      jest.spyOn(mockUserRepository, 'findOneBy').mockResolvedValue(null);

      const paginationOptions = { perPage: 20, offset: 0 };

      await expect(
        service.findOrganizationsWithAssociatedWorkspaces(
          nonExistingUserId,
          undefined,
          paginationOptions,
        ),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('findAllOrganizationWorkspaces', () => {
    const workspaces = [new Workspace(), new Workspace()];

    it('should return all the organization workspaces', async () => {
      jest
        .spyOn(mockWorkspaceRepository, 'findBy')
        .mockResolvedValue(workspaces);

      await expect(service.findAllOrganizationWorkspaces('xxx')).resolves.toBe(
        workspaces,
      );
    });
  });

  describe('areWorkspacesBelongsToOrganization', () => {
    it('should return false if the workspace does not belongs to the org', async () => {
      jest.spyOn(mockWorkspaceRepository, 'countBy').mockResolvedValue(0);

      await expect(
        service.areWorkspacesBelongsToOrganization('xxx', [123]),
      ).resolves.toBe(false);
    });

    it('should return true if the workspace belongs to the org', async () => {
      jest.spyOn(mockWorkspaceRepository, 'countBy').mockResolvedValue(1);

      await expect(
        service.areWorkspacesBelongsToOrganization('xxx', [123]),
      ).resolves.toBe(true);
    });
  });

  describe('isUserOrganizationAdmin', () => {
    it('should return true if user is an org admin inside organization', async () => {
      jest
        .spyOn(mockOrganizationUserRoleRepository, 'findOneBy')
        .mockResolvedValueOnce(new OrganizationUserRole());

      await expect(
        service.isUserOrganizationAdmin('xxx', 1),
      ).resolves.toBeTruthy();
    });

    it('should return false if user is not an org admin inside organization', async () => {
      jest
        .spyOn(mockOrganizationUserRoleRepository, 'findOneBy')
        .mockResolvedValueOnce(null);

      await expect(
        service.isUserOrganizationAdmin('xxx', 1),
      ).resolves.toBeFalsy();
    });
  });
});
