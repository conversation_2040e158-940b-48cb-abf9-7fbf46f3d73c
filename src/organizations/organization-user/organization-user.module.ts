import { Module } from '@nestjs/common';
import { OrganizationUserService } from './organization-user.service';
import { OrganizationUserController } from './organization-user.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Organization } from '../entities/organization.entity';
import { UserOrganizationMap } from './entities/user-organization-map.entity';
import { User } from 'src/entities/user.entity';
import { Role } from 'src/entities/role.entity';
import { OrganizationUserRole } from './entities/organization-user-role.entity';
import { OrganizationUserProfile } from './mappers/organization-user.profile';
import { OrganizationUserQueryService } from './organization-user-query.service';
import { Workspace } from '../../workspaces/entities/workspace.entity';
import { RoleModule } from 'src/role/role.module';
import { WorkspaceUserModule } from 'src/workspace-user/workspace-user.module';
import { LoginEventModule } from 'src/login-event/login-event.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Organization,
      User,
      Role,
      Workspace,
      UserOrganizationMap,
      OrganizationUserRole,
    ]),
    RoleModule,
    WorkspaceUserModule,
    LoginEventModule,
  ],
  exports: [OrganizationUserQueryService, OrganizationUserService],
  controllers: [OrganizationUserController],
  providers: [
    OrganizationUserProfile,
    OrganizationUserService,
    OrganizationUserQueryService,
  ],
})
export class OrganizationUserModule {}
