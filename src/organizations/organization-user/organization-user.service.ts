import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { UpdateOrganizationUserDto } from './dto/update-organization-user.dto';
import { OrganizationUserRole } from './entities/organization-user-role.entity';
import { UserOrganizationMap } from './entities/user-organization-map.entity';
import { User } from 'src/entities/user.entity';
import { Role } from 'src/entities/role.entity';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { OrganizationUserResponseDto } from './dto/organization-user-response.dto';
import { OrganizationUserRoleResponseDto } from './dto/organization-user-role-response.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { Organization } from '../entities/organization.entity';
import { UpdateOrganizationUserRoleDto } from './dto/update-organization-user-role.dto';
import { OrganizationUserQueryService } from './organization-user-query.service';
import { ReadOrganizationUserWorkspaceDto } from './dto/read-organization-user-workspace.dto';
import { ReadOrganizationUserWorkspaceEntity } from './dto/read-organization-user-workspace-entity.dto';
import { ReadLightWeightUserDto } from './dto/read-light-weight-user.dto';
import { RoleService } from 'src/role/role.service';
import { InjectRepository } from '@nestjs/typeorm';
import { DeleteResult, QueryRunner, Repository } from 'typeorm';
import { startTransaction } from 'src/common/utils/helper';
import { WorkspaceUserService } from 'src/workspace-user/workspace-user.service';
import { SuccessfulMessageResponseDto } from 'src/organization-invite/dto/successful-message-response.dto';
import { ORGANIZATION_USER_FILTER_BY } from '../utils/constants';
import { LoginEventService } from 'src/login-event/login-event.service';
import { LoginEvent } from 'src/login-event/entities/login-event.entity';
import { ReadOrganizationUserDataDto } from './dto/read-organization-user-data.dto';
import { Workspace } from 'src/workspaces/entities/workspace.entity';
import { ReadWorkspaceDto } from './dto/read-workspace.dto';

@Injectable()
export class OrganizationUserService {
  constructor(
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly organizationUserQueryService: OrganizationUserQueryService,
    private readonly roleService: RoleService,
    private readonly workspaceUserService: WorkspaceUserService,
    private readonly loginEventService: LoginEventService,
    @InjectRepository(UserOrganizationMap)
    private readonly userOrganizationMapRepository: Repository<UserOrganizationMap>,
    @InjectRepository(OrganizationUserRole)
    private readonly organizationUserRoleRepository: Repository<OrganizationUserRole>,
  ) {}

  async getAllLightWeight(organizationId: string) {
    const users =
      await this.organizationUserQueryService.getAllUserOrganizationLightWeight(
        organizationId,
      );

    return this.classMapper.mapArray(users, User, ReadLightWeightUserDto);
  }

  async getUserInOrganization(
    organization: Organization,
    user: User,
  ): Promise<OrganizationUserResponseDto> {
    const userOrganizationRolesEntities =
      await this.organizationUserRoleRepository.find({
        relations: {
          role: true,
        },
        where: {
          organizationId: organization.id,
          userId: user.id,
          user: {
            enabled: true,
            accountExpired: false,
            accountLocked: false,
          },
        },
      });
    const userOrganizationRoles = userOrganizationRolesEntities.map(
      (userOrganizationRole) => userOrganizationRole.role,
    );

    const userRoles = this.classMapper.mapArray(
      userOrganizationRoles,
      Role,
      OrganizationUserRoleResponseDto,
    );

    const organizationUserResponseDto = this.classMapper.map(
      user,
      User,
      OrganizationUserResponseDto,
    );

    organizationUserResponseDto.roles = userRoles;

    return organizationUserResponseDto;
  }

  /**
   * Returns a paginated response containing the users within an organization, the last login date on the platform,
   * the roles in the org these users have and also the workspaces (from both workspace user and workspace manager table) within this org these users are in.
   * @param organizationId
   * @param filterUserBy
   * @param paginationOptions
   * @returns
   */
  async findAllUserWorkspacesByOrganization(
    organizationId: string,
    filterUserBy: ORGANIZATION_USER_FILTER_BY | undefined,
    paginationOptions: PaginationOptions,
  ) {
    const [userMaps, total] =
      await this.organizationUserQueryService.findUserOrganizationMaps(
        organizationId,
        undefined,
        filterUserBy,
        paginationOptions,
      );

    const userIds = userMaps.map((userMap) => userMap.userId);

    const workspacesMap = await this.findAllUsersWorkspaces(
      organizationId,
      userIds,
    );

    const userRoleMap = await this.findOrganizationUserRoleMap(organizationId);

    const userLoginEventsMap = await this.findUsersLatestLoginDate(userIds);

    const users = userMaps.map((userMap) => userMap.user);

    const usersDto = this.classMapper.mapArray(
      users,
      User,
      ReadOrganizationUserDataDto,
    );

    usersDto.forEach((user) => {
      const roles = userRoleMap.get(user.id);
      const loginEvent = userLoginEventsMap.get(user.id);
      const userWorkspaceMap = workspacesMap.get(user.id) ?? new Map();

      user.workspaces = Array.from(userWorkspaceMap.values()) ?? [];

      if (roles) {
        user.roles = roles;
      }

      user.lastLoginDate = loginEvent?.dateCreated ?? null;
    });

    return new PaginatedResultArray(usersDto, total);
  }

  async findAll(
    organizationId: string,
    search: string | undefined,
    filterUserBy: ORGANIZATION_USER_FILTER_BY | undefined,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<OrganizationUserResponseDto>> {
    // This function must pull from two sources: (1) the user-organization map and
    // (2) the user-role map. The search/pagination operates on the user-organization map,
    // which drives the response. Then the roles can be added to the response.
    const [userMaps, total] =
      await this.organizationUserQueryService.findUserOrganizationMaps(
        organizationId,
        search,
        filterUserBy,
        paginationOptions,
      );

    const userRoleMap = await this.findOrganizationUserRoleMap(organizationId);

    const users = this.userMapEntitiesToDtos(userMaps);
    const userIds = users.map((user) => user.id);

    const userLoginEventsMap = await this.findUsersLatestLoginDate(userIds);

    // add the roles and login event to the users
    users.forEach((user) => {
      const roles = userRoleMap.get(user.id);
      const loginEvent = userLoginEventsMap.get(user.id);

      if (roles) {
        user.roles = roles;
      }

      user.lastLoginDate = loginEvent?.dateCreated ?? null;
    });

    return new PaginatedResultArray(users, total);
  }

  async getUsersByWorkspaceIds(
    organizationId: string,
    workspaceIds: number[],
    paginationOptions?: PaginationOptions,
  ) {
    const [result, total] =
      await this.organizationUserQueryService.getAllUsersByWorkspaceIds(
        organizationId,
        workspaceIds,
        paginationOptions,
      );

    return new PaginatedResultArray(result, total);
  }

  addUserToRoleMap(
    map: Map<number, OrganizationUserRoleResponseDto[]>,
    userRole: OrganizationUserRole,
  ): Map<number, OrganizationUserRoleResponseDto[]> {
    const role = this.classMapper.map(
      userRole.role,
      Role,
      OrganizationUserRoleResponseDto,
    );
    const roles = map.get(userRole.user.id);
    if (!roles) {
      map.set(userRole.user.id, [role]);
      return map;
    }
    roles.push(role);
    return map;
  }

  userMapEntitiesToDtos(
    userMaps: UserOrganizationMap[],
  ): OrganizationUserResponseDto[] {
    const userEntities = userMaps.map((userMap) => userMap.user);
    return this.classMapper.mapArray(
      userEntities,
      User,
      OrganizationUserResponseDto,
    );
  }

  async update(
    organizationId: string,
    userId: number,
    updateOrganizationUserDto: UpdateOrganizationUserDto,
  ): Promise<OrganizationUserResponseDto> {
    const roleIds = updateOrganizationUserDto.roles.map((role) => role.id);

    // Since it's a hotfix I'll add this here for now, but I do think common and necessary validations
    // should be explicity called on the controller itself before calling the service that will do it's thing.
    await this.validateRolesAreOrganizationRolesOrThrowError(roleIds);

    const organization =
      await this.organizationUserQueryService.findOrganization(organizationId);
    const user = await this.organizationUserQueryService.findUser(userId);
    const roles = await this.roleService.findRoles(
      updateOrganizationUserDto.roles.map((role) => role.id),
    );

    if (!organization || !user) {
      throw new NotFoundException('Organization or User not found');
    }
    this.validateRolesExistOrThrowError(updateOrganizationUserDto.roles, roles);
    // Since it's a hotfix I'll add this here for now, but I do think common and necessary validations
    // should be explicity called on the controller itself before calling the service that will do it's thing.
    await this.validateUserExistsInOrganizationOrThrowError(organization, user);

    await this.organizationUserQueryService.createUserToOrganizationMapIfNotExists(
      user,
      organization,
    );

    await this.syncOrganizationUserRoles(user, organization, roles);

    const userResponse = this.classMapper.map(
      user,
      User,
      OrganizationUserResponseDto,
    );

    const rolesResponse = this.classMapper.mapArray(
      roles,
      Role,
      OrganizationUserRoleResponseDto,
    );

    userResponse.roles = rolesResponse;

    return userResponse;
  }

  validateRolesExistOrThrowError(
    requestRoles: UpdateOrganizationUserRoleDto[],
    foundRoles: Role[],
  ) {
    const requestRoleIds = requestRoles.map((role) => role.id);
    const foundRoleIds = foundRoles.map((role) => role.id);
    const missingRoles = requestRoleIds.filter(
      (roleId) => !foundRoleIds.includes(roleId),
    );
    if (missingRoles.length > 0) {
      throw new NotFoundException('Roles not found');
    }
  }

  async validateUserExistsInOrganizationOrThrowError(
    organization: Organization,
    user: User,
  ) {
    const organizationUserMap =
      await this.organizationUserQueryService.findUserOrganizationMap(
        organization,
        user,
      );
    if (organizationUserMap === null) {
      throw new BadRequestException('User is not in the organization');
    }
  }

  async validateRolesAreOrganizationRolesOrThrowError(roleIds: number[]) {
    const areRolesValid =
      await this.organizationUserQueryService.areRolesValidOrganizationRoles(
        roleIds,
      );
    if (!areRolesValid) {
      throw new NotFoundException('Roles not found');
    }
  }

  async syncOrganizationUserRoles(
    user: User,
    organization: Organization,
    roles: Role[],
  ): Promise<void> {
    const existingUserRoles =
      await this.organizationUserQueryService.findOrganizationUserRolesForUser(
        organization,
        user,
      );
    const existingRoles = existingUserRoles.map((userRole) => userRole.role);

    const missingRolesToCreate = this.getMissingRolesToCreate(
      existingRoles,
      roles,
    );
    if (missingRolesToCreate.length > 0) {
      await this.organizationUserQueryService.createOrganizationUserRoles(
        user,
        organization,
        missingRolesToCreate,
      );
    }

    const extraRolesToCreate = this.getExtraRolesToDelete(existingRoles, roles);
    if (extraRolesToCreate.length > 0) {
      await this.organizationUserQueryService.deleteOrganizationUserRoles(
        user,
        organization,
        extraRolesToCreate,
      );
    }
  }

  getMissingRolesToCreate(existingRoles: Role[], roles: Role[]): Role[] {
    const existingRoleIds = existingRoles.map((role) => role.id);
    const roleIds = roles.map((role) => role.id);
    const missingRoleIds = roleIds.filter(
      (roleId) => !existingRoleIds.includes(roleId),
    );
    const missingRoles = roles.filter((role) =>
      missingRoleIds.includes(role.id),
    );
    return missingRoles;
  }

  getExtraRolesToDelete(existingRoles: Role[], roles: Role[]): Role[] {
    const existingRoleIds = existingRoles.map((role) => role.id);
    const roleIds = roles.map((role) => role.id);
    const extraRoleIds = existingRoleIds.filter(
      (roleId) => !roleIds.includes(roleId),
    );
    const extraRoles = existingRoles.filter((role) =>
      extraRoleIds.includes(role.id),
    );
    return extraRoles;
  }

  /**
   * It will delete within a transaction the user from the organizatio, which means:
   * 1 - remove every user organization maps;
   * 2 - remove every organization user roles;
   * 3 - remove user from every partner_person;
   * 4 - remove user from every partner_manager;
   * @param organization
   * @param user
   * @returns Promise<OrganizationUserResponseDto>
   */
  async remove(
    organization: Organization,
    user: User,
  ): Promise<SuccessfulMessageResponseDto> {
    const workspaces =
      await this.organizationUserQueryService.findAllOrganizationWorkspaces(
        organization.id,
      );

    const queryRunner = await startTransaction(
      this.userOrganizationMapRepository,
    );

    try {
      // remove every user organization maps;
      await this.deleteAllUserOrganizationMaps(user, organization, queryRunner);
      // remove every organization user roles;
      await this.deleteAllOrganizationUserRoles(
        organization,
        user,
        queryRunner,
      );

      const workspaceIds = workspaces.map((workspace) => workspace.id);

      // remove user from partner_person;
      await this.workspaceUserService.deleteAllWorkspaceUser(
        workspaceIds,
        user.id,
        queryRunner,
      );

      // remove user from partner_manager;
      await this.workspaceUserService.deleteAllWorkspaceManager(
        workspaceIds,
        user.id,
        queryRunner,
      );

      await queryRunner.commitTransaction();
      await queryRunner.release();

      return {
        message: 'User removed from organization',
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      await queryRunner.release();
      throw error;
    }
  }

  async findOrganizationsByUserWithWorkspaceCount(
    userId: number,
    search: string | undefined,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadOrganizationUserWorkspaceDto>> {
    const [orgs, total] =
      await this.organizationUserQueryService.findOrganizationsWithAssociatedWorkspaces(
        userId,
        search,
        paginationOptions,
      );

    return new PaginatedResultArray(
      this.classMapper.mapArray(
        orgs,
        ReadOrganizationUserWorkspaceEntity,
        ReadOrganizationUserWorkspaceDto,
      ),
      total,
    );
  }

  async listOrgsWithBrandGovernanceWorkspaces(
    userId: number,
    search: string | undefined,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadOrganizationUserWorkspaceDto>> {
    const [orgs, total] =
      await this.organizationUserQueryService.listOrgsWithBrandGovernanceWorkspaces(
        userId,
        search,
        paginationOptions,
      );

    return new PaginatedResultArray(
      this.classMapper.mapArray(
        orgs,
        ReadOrganizationUserWorkspaceEntity,
        ReadOrganizationUserWorkspaceDto,
      ),
      total,
    );
  }

  /**
   * It returns all the workspaces the users are in.
   * Loads the workspaces data from the WorkspaceUser (that are active) and WorkspaceManager table.
   * Returns a Map containing the map for a userId and the workspaces list.
   * @param organizationId
   * @param userIds
   * @returns
   */
  private async findAllUsersWorkspaces(
    organizationId: string,
    userIds: number[],
  ): Promise<Map<number, Map<number, ReadWorkspaceDto>>> {
    const workspaceUsers =
      await this.workspaceUserService.findAllWorkspaceUsersByOrgIdAndUserIds(
        organizationId,
        userIds,
      );

    const workspaceManagers =
      await this.workspaceUserService.findAllWorkspaceManagersByOrgIdAndUserIds(
        organizationId,
        userIds,
      );

    const workspaceMap = new Map<number, Map<number, ReadWorkspaceDto>>();

    workspaceUsers.forEach(({ userId, workspace }) => {
      const userWorkspaceMap = this.mapUserWorkspaceToWorkspaceMap(
        workspaceMap,
        workspace,
        userId,
      );
      workspaceMap.set(userId, userWorkspaceMap);
    });

    workspaceManagers.forEach(({ managerId, workspace }) => {
      const userWorkspaceMap = this.mapUserWorkspaceToWorkspaceMap(
        workspaceMap,
        workspace,
        managerId,
      );
      workspaceMap.set(managerId, userWorkspaceMap);
    });

    return workspaceMap;
  }

  private mapUserWorkspaceToWorkspaceMap(
    workspaceMap: Map<number, Map<number, ReadWorkspaceDto>>,
    workspace: Workspace,
    userId: number,
  ): Map<number, ReadWorkspaceDto> {
    const userWorkspaceMap =
      workspaceMap.get(userId) ?? new Map<number, ReadWorkspaceDto>();

    const readWorkspaceDto = this.classMapper.map(
      workspace,
      Workspace,
      ReadWorkspaceDto,
    );

    userWorkspaceMap.set(workspace.id, readWorkspaceDto);

    return userWorkspaceMap;
  }

  /**
   * It returns a map containing the map for a user id and organization role
   * @param organizationId
   * @returns
   */
  private async findOrganizationUserRoleMap(
    organizationId: string,
  ): Promise<Map<number, OrganizationUserRoleResponseDto[]>> {
    const userRoles =
      await this.organizationUserQueryService.findOrganizationUserRoles(
        organizationId,
      );

    return userRoles.reduce(
      (map, userRole) => this.addUserToRoleMap(map, userRole),
      new Map<number, OrganizationUserRoleResponseDto[]>(),
    );
  }

  /**
   * It returns a map containing the map for a user id and the latest login event date
   * @param userIds
   * @returns
   */
  private async findUsersLatestLoginDate(
    userIds: number[],
  ): Promise<Map<number, LoginEvent>> {
    const userLoginEvents =
      await this.loginEventService.findLatestSuccessfulLoginForUsers(userIds);

    return userLoginEvents.reduce((map, loginEvent: LoginEvent) => {
      map.set(loginEvent.userId, loginEvent);

      return map;
    }, new Map<number, LoginEvent>());
  }

  /**
   * It will delete all user organization maps within a transaction
   * @param {User} user
   * @param {Organization} organization
   * @param {QueryRunner} queryRunner
   * @returns {Promise<DeleteResult>}
   */
  private async deleteAllUserOrganizationMaps(
    user: User,
    organization: Organization,
    queryRunner: QueryRunner,
  ): Promise<DeleteResult> {
    return await this.userOrganizationMapRepository
      .createQueryBuilder('userOrganizationMap', queryRunner)
      .delete()
      .from(UserOrganizationMap)
      .where({
        organizationId: organization.id,
        userId: user.id,
      })
      .execute();
  }

  /**
   * It will delete all organization user roles within a transaction
   * @param {Organization} organization
   * @param {User} user
   * @param {QueryRunner} queryRunner
   * @returns {Promise<DeleteResult>}
   */
  private async deleteAllOrganizationUserRoles(
    organization: Organization,
    user: User,
    queryRunner: QueryRunner,
  ): Promise<DeleteResult> {
    return await this.organizationUserRoleRepository
      .createQueryBuilder('organizationUserRole', queryRunner)
      .delete()
      .from(OrganizationUserRole)
      .where({
        organizationId: organization.id,
        userId: user.id,
      })
      .execute();
  }

  /**
   * Get organization user map by organizationId and userId
   * @param organizationId organization id
   * @param userId user id
   */
  async getOrganizationUser(
    organizationId: string,
    userId: number,
  ): Promise<UserOrganizationMap> {
    return await this.userOrganizationMapRepository.findOne({
      where: {
        organizationId,
        userId,
      },
    });
  }
}
