import { Test, TestingModule } from '@nestjs/testing';
import { BrandService } from './brand.service';
import { Repository } from 'typeorm';
import { Brand } from '../entities/brand.entity';
import { WorkspaceBrandMap } from '../../../workspaces/brand/entities/workspace-brand-map.entity';
import { User } from '../../../entities/user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BrandProfile } from '../mappers/brand.profile';
import { ReadBrandDto } from '../dto/read-brand.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { CreateBrandResultDto } from '../dto/create-brand-result.dto';
import { UpdateBrandDto } from '../dto/update-brand.dto';

describe('BrandService', () => {
  let brandService: BrandService;
  let brandRepo: Repository<Brand>;
  let workspaceBrandMapRepo: Repository<WorkspaceBrandMap>;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      providers: [
        BrandService,
        BrandProfile,
        {
          provide: getRepositoryToken(Brand),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(WorkspaceBrandMap),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    brandService = moduleRef.get<BrandService>(BrandService);
    brandRepo = moduleRef.get<Repository<Brand>>(getRepositoryToken(Brand));
    workspaceBrandMapRepo = moduleRef.get<Repository<WorkspaceBrandMap>>(
      getRepositoryToken(WorkspaceBrandMap),
    );
  });

  it('should be defined', () => {
    expect(brandService).toBeDefined();
  });

  describe('getBrands', () => {
    it('should find all brands', async () => {
      const person = new User();
      person.id = 1;
      person.username = '<EMAIL>';
      person.firstName = 'Foo';
      person.lastName = 'Bar';

      const brand1 = new Brand();
      brand1.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      brand1.name = 'Coke';
      brand1.organizationId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';
      brand1.dateCreated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand1.lastUpdated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand1.updatedByPerson = person;

      const brand2 = new Brand();
      brand2.id = '011fd28b-24fd-4e62-a29b-19525c449046';
      brand2.name = 'Pepsi';
      brand2.description = 'Description added for Pepsi';
      brand2.organizationId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';
      brand2.dateCreated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand2.lastUpdated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand2.updatedByPerson = person;

      const mockResult = [brand1, brand2];

      const mockTotal = 2;
      const readBrandDto1 = new ReadBrandDto();
      readBrandDto1.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      readBrandDto1.name = 'Coke';
      readBrandDto1.organizationId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';
      readBrandDto1.dateCreated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );
      readBrandDto1.lastUpdated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );

      const readBrandDto2 = new ReadBrandDto();
      readBrandDto2.id = '011fd28b-24fd-4e62-a29b-19525c449046';
      readBrandDto2.name = 'Pepsi';
      readBrandDto2.description = 'Description added for Pepsi';
      readBrandDto2.organizationId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';
      readBrandDto2.dateCreated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );
      readBrandDto2.lastUpdated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );

      const mockConvertedResult = [readBrandDto1, readBrandDto2];

      jest
        .spyOn(brandRepo, 'findAndCount')
        .mockResolvedValueOnce([mockResult, mockTotal]);

      jest.spyOn(brandService, 'getWorkspaceCount').mockResolvedValue(2);

      const paginatedResultArray = await brandService.getBrands(
        '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a',
        {
          offset: 0,
          perPage: 10,
        },
      );

      const readBrandDtos = paginatedResultArray?.items;

      expect(paginatedResultArray).toBeDefined();
      expect(paginatedResultArray).toBeInstanceOf(PaginatedResultArray);
      expect(readBrandDtos.length).toEqual(2);
      expect(readBrandDtos[0]).toBeInstanceOf(ReadBrandDto);
      expect(readBrandDtos[0].id).toEqual(
        'daddc0ad-d678-434f-8015-9817f5d39bae',
      );
      expect(readBrandDtos[1]).toBeInstanceOf(ReadBrandDto);
      expect(readBrandDtos[1].id).toEqual(
        '011fd28b-24fd-4e62-a29b-19525c449046',
      );
    });
  });

  describe('createBrand', () => {
    it('should create a new brand', async () => {
      const id = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const name = 'Foo';
      const description = 'Foo description';
      const userId = 123;

      const savedBrand = new Brand();
      savedBrand.id = id;
      savedBrand.updatedByPersonId = userId;
      savedBrand.organizationId = organizationId;
      savedBrand.name = name;
      savedBrand.description = description;
      savedBrand.createdByPersonId = userId;
      savedBrand.createdByPerson = new User();

      const createBrandResultDto = new CreateBrandResultDto();
      createBrandResultDto.id = id;
      createBrandResultDto.organizationId = organizationId;
      createBrandResultDto.name = name;

      jest.spyOn(brandRepo, 'save').mockResolvedValueOnce(savedBrand);
      jest.spyOn(brandRepo, 'findOne').mockResolvedValueOnce(undefined);
      jest.spyOn(brandRepo, 'findOne').mockResolvedValueOnce(savedBrand);

      const result = await brandService.createBrand(userId, organizationId, {
        name: name,
        description: description,
      });

      expect(result).toBeDefined();
      expect(result).toBeInstanceOf(CreateBrandResultDto);
      expect(result.name).toEqual(name);
      expect(result.description).toEqual(description);
      expect(result.organizationId).toEqual(organizationId);
      expect(result.id).toEqual(id);
    });
  });

  describe('updateBrand', () => {
    it('should update a new brand', async () => {
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const name = 'Foo';
      const description = 'Foo description';
      const userId = 123;

      const savedBrand = new Brand();
      savedBrand.id = brandId;
      savedBrand.updatedByPersonId = userId;
      savedBrand.organizationId = organizationId;
      savedBrand.name = name;
      savedBrand.description = description;

      const updateBrandDto = new UpdateBrandDto();
      updateBrandDto.name = name;
      updateBrandDto.description = description;

      jest.spyOn(brandRepo, 'save').mockResolvedValueOnce(savedBrand);
      jest.spyOn(brandRepo, 'findOne').mockResolvedValueOnce(undefined);
      jest.spyOn(brandRepo, 'findOneBy').mockResolvedValueOnce(savedBrand);
      jest.spyOn(brandService, 'getWorkspaceCount').mockResolvedValue(2);

      const result = await brandService.updateBrand(
        userId,
        organizationId,
        brandId,
        updateBrandDto,
      );

      expect(result).toBeDefined();
      expect(result).toBeInstanceOf(ReadBrandDto);
      expect(result.name).toEqual(name);
      expect(result.description).toEqual(description);
      expect(result.organizationId).toEqual(organizationId);
      expect(result.id).toEqual(brandId);
    });
  });

  describe('deleteBrand', () => {
    it('should soft delete a brand', async () => {
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const userId = 123;

      const savedBrand = new Brand();
      savedBrand.id = brandId;
      savedBrand.updatedByPersonId = userId;
      savedBrand.organizationId = organizationId;

      jest.spyOn(brandRepo, 'save').mockResolvedValueOnce(savedBrand);
      jest.spyOn(brandRepo, 'findOneBy').mockResolvedValueOnce(savedBrand);
      jest.spyOn(workspaceBrandMapRepo, 'count').mockResolvedValueOnce(0);
      jest.spyOn(brandService, 'getWorkspaceCount').mockResolvedValue(1);

      const result = await brandService.deleteBrand(
        userId,
        organizationId,
        brandId,
      );

      expect(result).toBeDefined();
      expect(result).toBeInstanceOf(ReadBrandDto);
      expect(result.organizationId).toEqual(organizationId);
      expect(result.id).toEqual(brandId);
    });
  });
});
