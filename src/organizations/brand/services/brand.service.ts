import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { In, Like, Not, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { ReadBrandDto } from '../dto/read-brand.dto';
import { CreateBrandResultDto } from '../dto/create-brand-result.dto';
import { Brand } from '../entities/brand.entity';
import { WorkspaceBrandMap } from '../../../workspaces/brand/entities/workspace-brand-map.entity';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { UpdateBrandDto } from '../dto/update-brand.dto';
import { CreateBrandDto } from '../dto/create-brand.dto';
import { BrandByNameDto } from '../dto/brand-by-name.dto';
import {
  WorkspaceAdAccountMap
} from "../../../workspaces/workspace-ad-account/entities/workspace-ad-account-map.entities";
import {PlatformAdAccountBrandMap} from "../../../ad-accounts/entities/platform-ad-account-brand-map.entity";
import {ReadMarketDto} from "../../../markets/dto/read-market.dto";
import {Market} from "../../../markets/entities/market.entity";
import {ReadAdAccountBrandDto} from "../dto/read-ad-account-brand.dto";

@Injectable()
export class BrandService {
  constructor(
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
    @InjectRepository(WorkspaceBrandMap)
    private workspaceBrandMapRepository: Repository<WorkspaceBrandMap>,

    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  /**
   * get number of workspaces associated with a brand
   * @param brandId
   */

  async getWorkspaceCount(brandId: string): Promise<number> {
    const count = await this.workspaceBrandMapRepository.count({
      where: { brandId: brandId },
    });
    return count;
  }

  /**
   * find all brands
   *
   * @returns
   */
  async getBrands(
    organizationId: string,
    paginationOptions: PaginationOptions,
    search?: string,
  ): Promise<PaginatedResultArray<ReadBrandDto>> {
    const whereConditions: any = {
      organizationId,
      deleted: false,
    };

    if (search && search.trim() !== '') {
      whereConditions.name = Like(`%${search}%`);
    }

    const [result, total] = await this.brandRepository.findAndCount({
      where: whereConditions,
      relations: ['updatedByPerson', 'createdByPerson'],
      skip: paginationOptions.offset,
      take: paginationOptions.perPage,
      order: {
        name: 'ASC',
      },
    });

    const ReadBrandDtos: ReadBrandDto[] = this.classMapper.mapArray(
      result,
      Brand,
      ReadBrandDto,
    );

    await Promise.all(
      ReadBrandDtos.map(async (dto) => {
        dto.workspaceCount = await this.getWorkspaceCount(dto.id);
      }),
    );

    return new PaginatedResultArray(ReadBrandDtos, total);
  }
  /**
   * find all brands
   *
   * @returns
   */
  async getBrandsByName(
    organizationId: string,
    paginationOptions: PaginationOptions,
    brandNames: string,
  ): Promise<BrandByNameDto> {
    const brandNamesArray = brandNames.split(',');
    const whereConditions: any = {
      name: In(brandNamesArray),
      organizationId,
      deleted: false,
    };

    const [result, total] = await this.brandRepository.findAndCount({
      where: whereConditions,
      skip: paginationOptions.offset,
      take: paginationOptions.perPage,
      order: {
        name: 'ASC',
      },
    });

    const transformedResult: BrandByNameDto = result.reduce((acc, brand) => {
      acc[brand.name] = {
        id: brand.id,
        description: brand.description,
        name: brand.name,
      };
      return acc;
    }, {});

    return transformedResult;
  }

  /**
   * Create a brand
   *
   * @param userId
   * @param organizationId
   * @param brandDTO with name and description
   * @returns
   */
  async createBrand(
    userId: number,
    organizationId: string,
    brandDTO: CreateBrandDto,
  ) {
    // Check if a brand with the same name already exists within the given organization
    const existingBrand = await this.getExistingBrandByName(
      organizationId,
      brandDTO.name,
    );

    if (existingBrand) {
      throw new ConflictException(
        'Brand name already exists within the organization.',
      );
    }

    const brand = new Brand();
    brand.updatedByPersonId = userId;
    brand.organizationId = organizationId;
    brand.name = brandDTO.name;
    brand.description = brandDTO.description;
    brand.createdByPersonId = userId;

    const savedBrand = await this.brandRepository.save(brand);

    // After saving, fetch the saved entity with its relations
    const savedBrandWithRelations = await this.brandRepository.findOne({
      where: {
        id: savedBrand.id,
        deleted: false,
      },
      relations: ['createdByPerson'],
    });

    return this.classMapper.mapAsync(
      savedBrandWithRelations,
      Brand,
      CreateBrandResultDto,
    );
  }

  /**
   * Update a brand
   *
   * @param userId
   * @param organizationId
   * @param brandId
   * @param updateBrandDto
   * @returns
   */
  async updateBrand(
    userId: number,
    organizationId: string,
    brandId: string,
    updateBrandDto: UpdateBrandDto,
  ) {
    const brand = await this.getBrandEntity(brandId, organizationId);

    const { name, description } = updateBrandDto;
    brand.name = name;
    brand.description = description || '';
    brand.updatedByPersonId = userId;

    const existingBrand = await this.getExistingBrandByName(
      organizationId,
      name,
      brandId,
    );

    if (existingBrand) {
      throw new ConflictException(
        'Brand name already exists within the organization.',
      );
    }

    const result = await this.classMapper.mapAsync(
      await this.brandRepository.save(brand),
      Brand,
      ReadBrandDto,
    );

    result.workspaceCount = await this.getWorkspaceCount(result.id);
    return result;
  }

  /**
   * Delete a brand
   *
   * @param userId
   * @param organizationId
   * @param brandId
   * @returns
   */
  async deleteBrand(userId: number, organizationId: string, brandId: string) {
    const brand = await this.getBrandEntity(brandId, organizationId);

    const isBrandAssociatedWithWorkspace =
      await this.isBrandAssociatedWithWorkspace(brandId);

    if (isBrandAssociatedWithWorkspace) {
      throw new BadRequestException(
        'Cannot delete brand. It is associated with one or more workspaces.',
      );
    }

    brand.deleted = true;
    brand.updatedByPersonId = userId;

    const result = await this.classMapper.mapAsync(
      await this.brandRepository.save(brand),
      Brand,
      ReadBrandDto,
    );

    result.workspaceCount = await this.getWorkspaceCount(result.id);
    return result;
  }

  /**
   * Check if the brand is associated with any workspace
   *
   * @param brandId
   * @returns
   */
  async isBrandAssociatedWithWorkspace(brandId: string): Promise<boolean> {
    const associatedWorkspaces = await this.workspaceBrandMapRepository.count({
      where: { brandId },
    });

    return associatedWorkspaces > 0;
  }

  /**
   * Get a brand entity by id for a specific organization. Throws NotFoundException if not found.
   *
   * @param id Find a brand by id
   * @param organizationId Find a brand within a specific organization
   * @returns
   */
  async getBrandEntity(id: string, organizationId: string) {
    const brand = await this.brandRepository.findOneBy({ id, organizationId });

    if (!brand) {
      throw new NotFoundException(
        `Brand with ID ${id} not found in the organization with ID ${organizationId}`,
      );
    }
    return brand;
  }

  /**
   * Get a brand by name for a specific organization.
   * @param organization_id
   * @param name
   * @param updateBrandId
   */
  async getExistingBrandByName(
    organization_id: string,
    name: string,
    updateBrandId?: string,
  ) {
    const whereConditions: any = {
      name: name,
      organizationId: organization_id,
    };

    if (updateBrandId) {
      whereConditions.id = Not(updateBrandId);
    }

    const existingBrand = await this.brandRepository.findOne({
      where: whereConditions,
    });

    return existingBrand;
  }

  async getAdAccountBrandsByWorkspaces(
    organizationId: string,
    workspaceIds: number[],
    paginationOptions?: PaginationOptions,
  ){
    const [result, total] = await this.brandRepository
      .createQueryBuilder('brand')
      .select(['brand.id', 'brand.name'])
      .innerJoin(
        PlatformAdAccountBrandMap,
        'adAccountBrandMap',
        'adAccountBrandMap.brandId = brand.id',
      )
      .innerJoin(WorkspaceAdAccountMap, 'workspaceAdAccountMap', 'workspaceAdAccountMap.platformAdAccountId = adAccountBrandMap.platformAdAccountId')
      .innerJoin('workspaceAdAccountMap.workspace', 'workspace',
          'workspace.organizationId = :organizationId AND workspace.id IN (:...workspaceIds)',
          { organizationId, workspaceIds })
      .orderBy('brand.name', 'ASC')
      .take(paginationOptions.perPage)
      .skip(paginationOptions.offset)
      .getManyAndCount();

    const readAdAccountBrandDtos: ReadAdAccountBrandDto[] =
        this.classMapper.mapArray(result, Brand, ReadAdAccountBrandDto);

    return new PaginatedResultArray(readAdAccountBrandDtos, total);
  }
}
