/**
 * Data Transfer Object (DTO) representing the brand entity
 */
import { AutoMap } from '@automapper/classes';
import { BrandUserDto } from './brand-user.dto';

export class ReadBrandDto {
  /**
   * System assigned Id of the Brand
   * @example 123e4567-e89b-12d3-a456-426614174000
   */
  @AutoMap()
  id: string;

  /**
   * Name of the Brand
   * @example "Acme Inc"
   */
  @AutoMap()
  name: string;

  /**
   * Description of the Brand
   */
  @AutoMap()
  description: string;

  /**
   * System assigned Id of the Organization
   * @example "Acme Inc"
   */
  @AutoMap()
  organizationId: string;

  /**
   * User who created the Brand
   */
  @AutoMap()
  createdByPerson: BrandUserDto;

  /**
   * User who updated the Brand
   */
  @AutoMap()
  updatedByPerson: BrandUserDto;

  /**
   * Creation date of the Brand. This is of the format YYYY-MM-DD
   */
  @AutoMap()
  dateCreated: Date;

  /**
   * Updated date of the Brand
   */
  @AutoMap()
  lastUpdated: Date;

  @AutoMap()
  /**
   * The number of workspaces associated with the brand
   */
  workspaceCount: number;
}
