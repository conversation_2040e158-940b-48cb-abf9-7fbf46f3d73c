import { IsString, IsDefined, IsOptional } from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class UpdateBrandDto {
  /**
   * The name of the brand
   * @example "My Brand"
   */
  @AutoMap()
  @IsDefined({ message: 'Name must be defined.' })
  @IsString({ message: 'Name must be a string.' })
  name: string;

  /**
   * The description of the brand
   */
  @AutoMap()
  @IsOptional()
  @IsString({ message: 'Description must be a string.' })
  description?: string;
}
