import {
  Controller,
  Get,
  Put,
  Post,
  Param,
  ValidationPipe,
  Body,
  Request,
  Delete,
  Query,
} from '@nestjs/common';
import { BrandService } from '../services/brand.service';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadBrandDto } from '../dto/read-brand.dto';
import { UpdateBrandDto } from '../dto/update-brand.dto';
import { CreateBrandDto } from '../dto/create-brand.dto';
import {
  ApiConflictResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { BrandByNameDto } from '../dto/brand-by-name.dto';
import {ReadMarketDto} from "../../../markets/dto/read-market.dto";
import {AdAccountBrandsRequestDto} from "../dto/ad-account-brands-request.dto";

@ApiTags('Brand')
@Controller('organization/:organizationId/brand')
/**
 * This endpoint returns brands from organization
 */
export class BrandController {
  constructor(private brandService: BrandService) {}
  @VmApiOkPaginatedArrayResponse({
    type: ReadBrandDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term for brands',
    required: false,
    type: String,
  })
  @Get()
  async getBrands(
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('search') search?: string,
  ): Promise<PaginatedResultArray<ReadBrandDto>> {
    return this.brandService.getBrands(
      organizationId,
      paginationOptions,
      search,
    );
  }

  @VmApiOkResponse({
    type: BrandByNameDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiQuery({
    name: 'brandNames',
    description: 'Names of the brands',
    required: true,
    type: String,
  })
  @Get('details')
  async getBrandsByName(
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('brandNames') brandNames?: string,
  ): Promise<BrandByNameDto> {
    return this.brandService.getBrandsByName(
      organizationId,
      paginationOptions,
      brandNames,
    );
  }

  /**
   * Create a new brand to a organization.
   * @param organizationId - The id of the Organization.
   * @param name - The name of Brand.
   */
  @ApiConflictResponse({
    description: 'Brand name already exists',
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @ApiParam({ name: 'name', description: 'The name of Brand' })
  @ApiParam({ name: 'description', description: 'The description of Brand' })
  @ApiParam({ name: 'userId', description: 'The id of the user' })
  @Post('user/:userId')
  async createBrand(
    @Param('organizationId') organizationId: string,
    @Param('userId') userId: number,
    @Body(new ValidationPipe()) param: CreateBrandDto,
  ) {
    return this.brandService.createBrand(userId, organizationId, param);
  }

  /**
   * Update a brand by id
   *
   * @param id
   * @param updateWorkspaceDto
   * @returns
   */
  @ApiConflictResponse({
    description: 'Brand name already exists.',
  })
  @VmApiOkResponse({
    type: ReadBrandDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({ name: 'brandId', description: 'The id of the brand' })
  @ApiParam({ name: 'userId', description: 'The id of the user' })
  @Put(':brandId/user/:userId')
  updateBrand(
    @Param('organizationId') organizationId: string,
    @Param('brandId') brandId: string,
    @Param('userId') userId: number,
    @Body(new ValidationPipe()) updateBrandDto: UpdateBrandDto,
  ) {
    return this.brandService.updateBrand(
      userId,
      organizationId,
      brandId,
      updateBrandDto,
    );
  }

  /**
   * Soft Delete a brand by id
   *
   * @param id
   * @returns
   */
  @VmApiOkResponse({
    type: ReadBrandDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({ name: 'brandId', description: 'The id of the brand' })
  @Delete(':brandId')
  deleteBrand(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('brandId') brandId: string,
  ) {
    const { userId } = req;
    return this.brandService.deleteBrand(userId, organizationId, brandId);
  }

  @Post('adAccountBrand')
  getBrandsByWorkspaces(
      @Body() body: AdAccountBrandsRequestDto,
      @Param('organizationId') organizationId: string,
      @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return this.brandService.getAdAccountBrandsByWorkspaces(organizationId, body.workspaceIds, paginationOptions);
  }
}
