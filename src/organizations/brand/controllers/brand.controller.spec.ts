import { Test, TestingModule } from '@nestjs/testing';
import { BrandController } from './brand.controller';
import { BrandService } from '../services/brand.service';
import { CreateBrandDto } from '../dto/create-brand.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { ReadBrandDto } from '../dto/read-brand.dto';
import { UpdateBrandDto } from '../dto/update-brand.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Brand } from '../entities/brand.entity';
import { BrandProfile } from '../mappers/brand.profile';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { Request } from 'express';
import { WorkspaceBrandMap } from '../../../workspaces/brand/entities/workspace-brand-map.entity';
import { BadRequestException } from '@nestjs/common';
import { CreateBrandResultDto } from '../dto/create-brand-result.dto';

describe('BrandController', () => {
  let brandController: BrandController;
  let brandService: BrandService;
  let workspaceBrandMapRepo: Repository<WorkspaceBrandMap>;
  let brandRepo: Repository<Brand>;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [BrandController],
      providers: [
        BrandService,
        BrandProfile,
        {
          provide: getRepositoryToken(Brand),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(WorkspaceBrandMap),
          useClass: Repository,
        },
      ],
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
    }).compile();

    workspaceBrandMapRepo = moduleRef.get<Repository<WorkspaceBrandMap>>(
      getRepositoryToken(WorkspaceBrandMap),
    );
    brandRepo = moduleRef.get<Repository<Brand>>(getRepositoryToken(Brand));

    brandController = moduleRef.get<BrandController>(BrandController);
    brandService = moduleRef.get<BrandService>(BrandService);
  });

  describe('getBrands', () => {
    it('should return a paginated array of ReadBrandDto', async () => {
      const organizationId = '123';
      const paginationOptions: PaginationOptions = { offset: 0, perPage: 10 };
      const expectedResult: PaginatedResultArray<ReadBrandDto> = {
        totalCount: 1,
        items: [
          {
            id: 'daddc0ad-d678-434f-8015-9817f5d39bae',
            name: 'Coke',
            description: null,
            organizationId: 'daddc0ad-d678-434f-8015-9817f5d39bae',
            dateCreated: new Date(Date.parse('2023-06-30T20:37:35.000Z')),
            lastUpdated: new Date(Date.parse('2023-06-30T20:37:35.000Z')),
            workspaceCount: 0,
            updatedByPerson: {
              id: 1,
              username: 'Foo',
            },
            createdByPerson: {
              id: 1,
              username: 'Foo',
            },
          },
        ],
        queryId: undefined,
      };

      jest.spyOn(brandService, 'getBrands').mockResolvedValue(expectedResult);

      const result = await brandController.getBrands(
        organizationId,
        paginationOptions,
        undefined,
      );

      expect(result).toEqual(expectedResult);
      expect(brandService.getBrands).toHaveBeenCalledWith(
        organizationId,
        paginationOptions,
        undefined,
      );
    });
  });

  describe('createBrand', () => {
    it('should call brandService.createBrand with the correct parameters', async () => {
      const userId = 123;
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const brandName = 'Coke';

      const request: Request = {
        userId: userId,
      } as unknown as Request;

      const createBrandDto: CreateBrandDto = {
        name: brandName,
        description: 'test description',
      };

      const expectedResult = new CreateBrandResultDto();
      expectedResult.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      expectedResult.name = brandName;
      expectedResult.organizationId = organizationId;
      expectedResult.dateCreated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );
      expectedResult.lastUpdated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );
      expectedResult.createdByPerson = {
        id: 1,
        username: 'Foo',
      };

      const brand = new Brand();
      brand.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      brand.organizationId = organizationId;
      brand.dateCreated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand.lastUpdated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));

      jest.spyOn(brandRepo, 'save').mockResolvedValue(brand);
      jest.spyOn(brandRepo, 'findOne').mockResolvedValueOnce(brand);
      jest.spyOn(brandService, 'createBrand').mockResolvedValue(expectedResult);

      await brandController.createBrand(organizationId, userId, createBrandDto);

      expect(brandService.createBrand).toHaveBeenCalledWith(
        userId,
        organizationId,
        createBrandDto,
      );
    });
  });

  describe('updateBrand', () => {
    it('should call brandService.updateBrand with the correct parameters', async () => {
      const userId = 123;
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const brandName = 'Pepsi';
      const brandDescription = 'Test Description';
      const brandId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';

      const updateBrandDto: UpdateBrandDto = {
        name: brandName,
        description: brandDescription
      };

      const expectedResult = new Brand();
      expectedResult.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      expectedResult.name = brandName;
      expectedResult.organizationId = organizationId;
      expectedResult.dateCreated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );
      expectedResult.lastUpdated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );

      jest.spyOn(brandService, 'updateBrand').mockResolvedValue(expectedResult);

      await brandController.updateBrand(
        organizationId,
        brandId,
        userId,
        updateBrandDto,
      );

      expect(brandService.updateBrand).toHaveBeenCalledWith(
        userId,
        organizationId,
        brandId,
        updateBrandDto,
      );
    });
  });

  describe('deleteBrand', () => {
    it('should call brandService.deleteBrand with the correct parameters', async () => {
      const userId = '123';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const brandId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';

      const request: Request = {
        userId: userId,
      } as unknown as Request;

      const expectedResult = new Brand();
      expectedResult.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      expectedResult.organizationId = organizationId;
      expectedResult.dateCreated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );
      expectedResult.lastUpdated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );

      jest.spyOn(workspaceBrandMapRepo, 'find').mockResolvedValueOnce([]);

      jest.spyOn(brandService, 'deleteBrand').mockResolvedValue(expectedResult);

      await brandController.deleteBrand(
        request as Request,
        organizationId,
        brandId,
      );

      expect(brandService.deleteBrand).toHaveBeenCalledWith(
        userId,
        organizationId,
        brandId,
      );
    });

    it('should throw BadRequestException when deleting a brand associated with a workspace', async () => {
      // Your test data
      const userId = '123';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const brandId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';

      const request: Request = {
        userId: userId,
      } as unknown as Request;

      const brand = new Brand();
      brand.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      brand.organizationId = organizationId;
      brand.dateCreated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand.lastUpdated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));

      const expectedResult = new WorkspaceBrandMap();
      expectedResult.brandId = brandId;
      expectedResult.workspaceId = 123;

      jest.spyOn(brandRepo, 'findOneBy').mockResolvedValueOnce(brand);

      jest
        .spyOn(workspaceBrandMapRepo, 'find')
        .mockResolvedValueOnce([expectedResult]);
      jest
        .spyOn(brandService, 'isBrandAssociatedWithWorkspace')
        .mockResolvedValue(true);

      // Wrap the function call inside an async function and use expect().rejects.toThrow()
      await expect(
        brandController.deleteBrand(
          request as Request,
          organizationId,
          brandId,
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });
});
