import { ArgumentMetadata, BadRequestException } from '@nestjs/common';
import { UUIDPipe } from './uuid.pipe';

describe('UUIDPipe', () => {
  let pipe: UUIDPipe;

  beforeEach(() => {
    pipe = new UUIDPipe();
  });

  describe('transform', () => {
    it('should transform valid UUID', () => {
      const value = '123e4567-e89b-12d3-a456-************';
      const metadata = { data: 'id' } as ArgumentMetadata;

      const result = pipe.transform(value, metadata);

      expect(result).toEqual(value);
    });

    it('should throw BadRequestException for invalid UUID', () => {
      const value = 'invalid-uuid';
      const metadata = { data: 'id' } as ArgumentMetadata;

      expect(() => pipe.transform(value, metadata)).toThrowError(
        BadRequestException,
      );
    });

    it('should throw BadRequestException with correct error message', () => {
      const value = 'invalid-uuid';
      const metadata = { data: 'id' } as ArgumentMetadata;
      const expectedErrorMessage = 'Invalid UUID format for id';

      expect(() => pipe.transform(value, metadata)).toThrowError(
        BadRequestException,
      );
      expect(() => pipe.transform(value, metadata)).toThrowError(
        expectedErrorMessage,
      );
    });
  });
});
