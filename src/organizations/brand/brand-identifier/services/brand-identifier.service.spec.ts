import { Test, TestingModule } from '@nestjs/testing';
import { BrandIdentifierService } from './brand-identifier.service';
import { Repository } from 'typeorm';
import { Brand } from '../../entities/brand.entity';
import { BrandIdentifier } from '../entities/brand-identifier.entity';
import { CreateBrandIdentifierResultDto } from '../dto/create-brand-identifier-result.dto';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BrandIdentifierProfile } from '../mappers/brand-identifier.profile';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { NotFoundException } from '@nestjs/common';
import { UpdateBrandIdentifierResultDto } from '../dto/update-brand-identifier-result.dto';
import { UpdateBrandIdentifierDto } from '../dto/update-brand-identifier.dto';
import { DeleteBrandIdentifierDto } from '../dto/delete-brand-identifier.dto';
import { BrandService } from '../../../brand/services/brand.service';

describe('BrandIdentifierService', () => {
  let brandIdentifierService: BrandIdentifierService;
  let brandService: BrandService;
  let brandIdentifierRepository: Repository<BrandIdentifier>;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      providers: [
        BrandIdentifierService,
        {
          provide: BrandService,
          useValue: {
            getBrandEntity: jest.fn(),
          },
        },
        BrandIdentifierProfile,
        {
          provide: getRepositoryToken(BrandIdentifier),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    brandIdentifierService = moduleRef.get<BrandIdentifierService>(
      BrandIdentifierService,
    );
    brandService = moduleRef.get<BrandService>(BrandService);
    brandIdentifierRepository = moduleRef.get<Repository<BrandIdentifier>>(
      getRepositoryToken(BrandIdentifier),
    );
  });

  describe('createBrandIdentifier', () => {
    it('should create a new Brand Identifier', async () => {
      const brandId = '6c1f2ad4-15a7-42be-9be5-980d23c14287';
      const identifier = 'testIdentifier';

      const brandIdentifier = new BrandIdentifier();
      brandIdentifier.brandId = brandId;
      brandIdentifier.identifier = identifier;

      jest
        .spyOn(brandIdentifierRepository, 'save')
        .mockResolvedValueOnce(brandIdentifier);

      const result = await brandIdentifierService.createBrandIdentifier(
        brandId,
        identifier,
      );

      expect(brandIdentifierRepository.save).toHaveBeenCalledWith(
        brandIdentifier,
      );
      expect(result).toBeInstanceOf(CreateBrandIdentifierResultDto);
    });
  });

  describe('softDeleteBrandIdentifierById', () => {
    it('should soft delete a Brand Identifier', async () => {
      const brandIdentifierId = '6c1f2ad4-15a7-42be-9be5-980d23c14287';
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';

      const brandIdentifier = new BrandIdentifier();
      brandIdentifier.id = brandIdentifierId;

      const brand = new Brand();
      brand.id = brandId;

      jest
        .spyOn(brandIdentifierRepository, 'createQueryBuilder')
        .mockReturnValueOnce({
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getOne: jest.fn().mockResolvedValueOnce(brandIdentifier),
        } as any);
      jest
        .spyOn(brandIdentifierRepository, 'save')
        .mockResolvedValueOnce(brandIdentifier);

      const result = await brandIdentifierService.softDeleteBrandIdentifierById(
        organizationId,
        brandId,
        brandIdentifierId,
      );

      expect(brandIdentifierRepository.createQueryBuilder).toHaveBeenCalled();
      expect(brandIdentifier.deleted).toBe(true);
      expect(brandIdentifierRepository.save).toHaveBeenCalledWith(
        brandIdentifier,
      );
      expect(result).toBeInstanceOf(DeleteBrandIdentifierDto);
    });

    it('should throw NotFoundException when Brand Identifier not found', async () => {
      const brandIdentifierId = '6c1f2ad4-15a7-42be-9be5-980d23c14287';
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';

      jest
        .spyOn(brandIdentifierRepository, 'createQueryBuilder')
        .mockReturnValueOnce({
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getOne: jest.fn().mockResolvedValueOnce(undefined),
        } as any);

      await expect(
        brandIdentifierService.softDeleteBrandIdentifierById(
          organizationId,
          brandId,
          brandIdentifierId,
        ),
      ).rejects.toThrowError(NotFoundException);
    });
  });

  describe('updateBrandIdentifier', () => {
    it('should update a Brand Identifier', async () => {
      const id = '14a1d02a-5f0c-4e37-84fe-vidmob';
      const updateBrandIdentifierDto: UpdateBrandIdentifierDto = {
        identifier: 'Pepsi',
      };
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';

      const brandIdentifier = new BrandIdentifier();
      brandIdentifier.id = id;
      brandIdentifier.identifier = 'Old Identifier';

      jest
        .spyOn(brandIdentifierRepository, 'findOne')
        .mockResolvedValueOnce(brandIdentifier);
      jest
        .spyOn(brandIdentifierRepository, 'save')
        .mockResolvedValueOnce(brandIdentifier);

      jest
        .spyOn(brandIdentifierRepository, 'findOneBy')
        .mockResolvedValueOnce(brandIdentifier);

      const result = await brandIdentifierService.updateBrandIdentifier(
        organizationId,
        brandId,
        id,
        updateBrandIdentifierDto,
      );

      expect(brandIdentifierRepository.findOneBy).toHaveBeenCalledWith({
        id,
        brandId,
      });

      expect(brandIdentifierRepository.save).toHaveBeenCalledWith(
        brandIdentifier,
      );
      expect(result).toBeInstanceOf(UpdateBrandIdentifierResultDto);
      expect(result.identifier).toBe(updateBrandIdentifierDto.identifier);
    });

    it('should throw NotFoundException when brand identifier is not found', async () => {
      const id = '14a1d02a-5f0c-4e37-84fe-vidmob';
      const updateBrandIdentifierDto: UpdateBrandIdentifierDto = {
        identifier: 'Pepsi',
      };
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';

      jest
        .spyOn(brandIdentifierRepository, 'findOne')
        .mockResolvedValueOnce(null);
      jest
        .spyOn(brandIdentifierRepository, 'findOneBy')
        .mockResolvedValueOnce(null);

      await expect(
        brandIdentifierService.updateBrandIdentifier(
          organizationId,
          brandId,
          id,
          updateBrandIdentifierDto,
        ),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
