import { IsString, IsDefined, <PERSON><PERSON>ength } from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class UpdateBrandIdentifierDto {
  /**
   * The Identifier of the brand Identifier
   * @example "My Brand Identifier Vidmob"
   */
  @AutoMap()
  @IsDefined({ message: 'Identifier must be defined.' })
  @IsString({ message: 'Identifier must be a string.' })
  @MaxLength(255, { message: 'Identifier must be less than 255 characters.' })
  identifier: string;
}
