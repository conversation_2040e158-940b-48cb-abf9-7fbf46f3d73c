import {
  Body,
  Controller,
  Delete,
  Param,
  Post,
  Put,
  ValidationPipe,
} from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { BrandIdentifierService } from '../services/brand-identifier.service';
import { CreateBrandIdentifierDto } from '../dto/create-brand-identifier.dto';
import { VmApiOkResponse } from '@vidmob/vidmob-nestjs-common';
import { DeleteBrandIdentifierDto } from '../dto/delete-brand-identifier.dto';
import { UpdateBrandIdentifierDto } from '../dto/update-brand-identifier.dto';
import { CreateBrandIdentifierResultDto } from '../dto/create-brand-identifier-result.dto';
import { UUID } from 'crypto';

@ApiTags('Brand Identifier')
@Controller('organization/:organizationId/brand/:brandId/brand-identifier')
export class BrandIdentifierController {
  constructor(private brandIdentifierService: BrandIdentifierService) {}

  /**
   * Create a new Brand Identitifer.
   * @param brandId - The ID of the Brand that this Brand Identifier belongs to.
   * @param identifier - The Brand Identifier description.
   */
  @VmApiOkResponse({
    type: CreateBrandIdentifierResultDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to create a brand identifier for.',
  })
  @ApiParam({
    name: 'brandId',
    description: 'The id of the brand to create a brand identifier for.',
  })
  @ApiParam({
    name: 'identifier',
    description: 'The Brand Identifier description',
  })
  @Post()
  async createBrandIdentifier(
    @Param('brandId') brandId: UUID,
    @Body(new ValidationPipe()) param: CreateBrandIdentifierDto,
  ) {
    return this.brandIdentifierService.createBrandIdentifier(
      brandId,
      param.identifier,
    );
  }

  /**
   * Soft Delete a Brand Identifier by id
   *
   * @param organizationId
   * @param brandId
   * @param id
   * @returns
   */
  @VmApiOkResponse({
    type: DeleteBrandIdentifierDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to update a brand identifier for.',
  })
  @ApiParam({
    name: 'brandId',
    description: 'The id of the brand Identifier',
  })
  @ApiParam({
    name: 'id',
    description: 'The id of the brand Identifier to be soft deleted.',
  })
  @Delete(':id')
  async softDeleteBrandIdentifierById(
    @Param('organizationId') organizationId: UUID,
    @Param('brandId') brandId: UUID,
    @Param('id') id: UUID,
  ) {
    return this.brandIdentifierService.softDeleteBrandIdentifierById(
      organizationId,
      brandId,
      id,
    );
  }

  /**
   * Update a Brand Identifier by id
   *
   * @param organizationId
   * @param brandId
   * @param id
   * @param UpdateBrandIdentifierDto
   * @returns
   */
  @VmApiOkResponse({
    type: UpdateBrandIdentifierDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to update a brand identifier for.',
  })
  @ApiParam({
    name: 'brandId',
    description: 'The id of the brand Identifier',
  })
  @ApiParam({
    name: 'identifier',
    description: 'Description of the brand identifier to update.',
  })
  @Put(':id')
  async updateBrandIdentifier(
    @Param('organizationId') organizationId: UUID,
    @Param('brandId') brandId: UUID,
    @Param('id') id: UUID,
    @Body(new ValidationPipe()) updateBrandIdentifier: UpdateBrandIdentifierDto,
  ) {
    return this.brandIdentifierService.updateBrandIdentifier(
      organizationId,
      brandId,
      id,
      updateBrandIdentifier,
    );
  }
}
