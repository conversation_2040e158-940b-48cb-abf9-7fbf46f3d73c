import { SplitAndTrimPipe } from './split-and-trim-pipe';
import { ArgumentMetadata } from '@nestjs/common';

describe('SplitAndTrimPipe', () => {
  let pipe: SplitAndTrimPipe;

  beforeEach(() => {
    pipe = new SplitAndTrimPipe();
  });

  it('should transform a comma-separated string with ISO codes to an array of trimmed strings', () => {
    const value = 'col, bra, usa';
    const metadata: ArgumentMetadata = { type: 'query', metatype: String };

    const result = pipe.transform(value, metadata);

    expect(result).toEqual(['col', 'bra', 'usa']);
  });

  it('should return an empty array if the value is falsy', () => {
    const falsyValues = ['', null, undefined];
    const metadata: ArgumentMetadata = { type: 'query', metatype: String };

    for (const value of falsyValues) {
      const result = pipe.transform(value, metadata);
      expect(result).toEqual([]);
    }
  });
});
