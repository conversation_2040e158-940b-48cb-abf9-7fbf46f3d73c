import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Industry } from './entities/industry.entity';
import { IndustryService } from './industry.service';
import { IndustryProfile } from './mapper/industry.profile';

@Module({
  imports: [TypeOrmModule.forFeature([Industry])],
  exports: [IndustryService],
  providers: [IndustryService, IndustryProfile],
})
export class IndustryModule {}
