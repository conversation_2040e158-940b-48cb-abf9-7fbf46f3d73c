import { Entity } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

@Entity('industry')
export class IndustryDTO {
  /**
   * System assigned Id of the industry
   */
  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  id: number;

  /**
   * Industry Name
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  name: string;

  /**
   * Industry Identifier
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  identifier: string;

  /**
   * Parent Industry Id
   */
  @AutoMap()
  @IsOptional()
  @IsNumber()
  parentId: number;

  /**
   * Root Industry Id
   */
  @AutoMap()
  @IsOptional()
  @IsNumber()
  rootId: number;
}
