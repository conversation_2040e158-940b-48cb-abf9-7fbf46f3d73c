import { InjectRepository } from '@nestjs/typeorm';
import { Industry } from './entities/industry.entity';
import { IsNull, Repository } from 'typeorm';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { Injectable } from '@nestjs/common';
import { IndustryDTO } from './dto/industry.dto';

@Injectable()
export class IndustryService {
  static INDUSTRY_GROUP_ENTITY = 'Industry group';
  static INDUSTRY_ENTITY = 'Industry';
  static SUB_INDUSTRY_ENTITY = 'Sub industry';
  constructor(
    @InjectRepository(Industry)
    private industryRepository: Repository<Industry>,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  async getIndustryGroups(): Promise<IndustryDTO[]> {
    const industries = await this.industryRepository.find({
      where: {
        parentId: IsNull(),
        rootId: IsNull(),
      },
      order: {
        name: 'AS<PERSON>',
      },
    });
    return this.classMapper.mapArray(industries, Industry, IndustryDTO);
  }

  async getIndustries(industryGroupId: number): Promise<IndustryDTO[]> {
    const industries = await this.industryRepository.find({
      where: {
        parentId: industryGroupId,
      },
      order: {
        name: 'ASC',
      },
    });
    return this.classMapper.mapArray(industries, Industry, IndustryDTO);
  }

  async getSubIndustries(industryId: number): Promise<IndustryDTO[]> {
    const industries = await this.industryRepository.find({
      where: {
        parentId: industryId,
      },
      order: {
        name: 'ASC',
      },
    });
    return this.classMapper.mapArray(industries, Industry, IndustryDTO);
  }
}
