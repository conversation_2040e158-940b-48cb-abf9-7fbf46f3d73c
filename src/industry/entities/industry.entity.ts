import { Column, <PERSON>tity, <PERSON>in<PERSON><PERSON>umn, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';

@Entity()
export class Industry {
  /**
   * System assigned Id of the industry
   */
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Industry Name
   */
  @AutoMap()
  @Column({ length: 50 })
  name: string;

  /**
   * Industry Identifier
   */
  @AutoMap()
  @Column({ length: 190 })
  identifier: string;

  /**
   * Parent ID
   */
  @AutoMap()
  @Column({ name: 'parent_id', type: 'bigint', nullable: true })
  parentId: number;

  /**
   * Parent Industry
   */
  @AutoMap()
  @OneToOne(() => Industry, { nullable: true })
  @JoinColumn({ name: 'parent_id' })
  parentIndustry: Industry;

  /**
   * Root ID
   */
  @AutoMap()
  @Column({ name: 'root_id', type: 'bigint', nullable: true })
  rootId: number;

  /**
   * Root Industry
   */
  @AutoMap()
  @OneToOne(() => Industry, { nullable: true })
  @JoinColumn({ name: 'root_id' })
  rootIndustry: Industry;
}
