import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { createMap, Mapper } from '@automapper/core';
import { IndustryDTO } from '../dto/industry.dto';
import { Industry } from '../entities/industry.entity';

@Injectable()
export class IndustryProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper) => {
      createMap(mapper, Industry, IndustryDTO);
    };
  }
}
